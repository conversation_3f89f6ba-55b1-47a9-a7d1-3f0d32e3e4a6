<!-- @format -->

<template>
    <div class="tabs1-con">
        <div class="gap"></div>
        <div class="qiye-tu">
            <TendencyChart
                ref="tendencyChart"
                :curEquit="curEquit"
                :dateStr="dateStr"
            ></TendencyChart>
        </div>
        <div class="qiye-board">
            <div class="zy-line ac jb" v-if="productList.length != 0">
                <p class="qiye-til1">生产设备</p>
            </div>

            <div class="qiye-shebei" v-if="productList.length != 0">
                <radio-group
                    @change="radioChange($event, item, index)"
                    v-for="(item, index) in productList"
                    :key="item.SBXH"
                    :data-item="item"
                >
                    <label class="item radiogroup">
                        <div class="left">
                            <div class="ic" style="background-color: #3873ff">
                                <i style="background-color: #3873ff"></i>
                            </div>
                            <view>{{ item.SBMC }}</view>
                        </div>
                        <view>
                            <radio
                                style="transform: scale(0.7)"
                                :value="item.SBXH"
                                :checked="item.SBXH === curEquit.SBXH"
                            />
                        </view>
                    </label>
                </radio-group>
            </div>

            <div class="gap"></div>
            <div class="zy-line ac jb" v-if="polluteList.length != 0">
                <p class="qiye-til1">治污设备</p>
            </div>
            <div class="qiye-shebei" v-if="polluteList.length != 0">
                <radio-group
                    @change="radioChange($event, item, index)"
                    v-for="(item, index) in polluteList"
                    :key="item.SBXH"
                    :data-item="item"
                >
                    <label class="item radiogroup">
                        <div class="left">
                            <div class="ic" style="background-color: #3873ff">
                                <i style="background-color: #3873ff"></i>
                            </div>
                            <view>{{ item.SBMC }}</view>
                        </div>
                        <view>
                            <radio
                                style="transform: scale(0.7)"
                                :value="item.SBXH"
                                :checked="item.SBXH === curEquit.SBXH"
                            />
                        </view>
                    </label>
                </radio-group>
            </div>
            <div class="gap"></div>
            <div class="zy-line ac jb" v-if="otherEquipList.length != 0">
                <p class="qiye-til1">其他设备</p>
            </div>
            <div class="qiye-shebei" v-if="otherEquipList.length != 0">
                <radio-group
                    @change="radioChange($event, item, index)"
                    v-for="(item, index) in otherEquipList"
                    :key="item.SBXH"
                    :data-item="item"
                >
                    <label class="item radiogroup">
                        <div class="left">
                            <div class="ic" style="background-color: #3873ff">
                                <i style="background-color: #3873ff"></i>
                            </div>
                            <view>{{ item.SBMC }}</view>
                        </div>
                        <view>
                            <radio
                                style="transform: scale(0.7)"
                                :value="item.SBXH"
                                :checked="item.SBXH === curEquit.SBXH"
                            />
                        </view>
                    </label>
                </radio-group>
            </div>
            <div class="gap"></div>
            <div class="gap"></div>
            <div class="gap"></div>
        </div>

        <div class="gap"></div>
    </div>
</template>
<script>
import { debounce } from 'lodash';
import TendencyChart from './TendencyChart.vue';
export default {
    components: {
        TendencyChart
    },
    props: {
        currentscx: {
            type: String,
            default: ''
        },
        dateStr: {
            type: String,
            default: ''
        }
    },

    data() {
        return {
            trendData: [],
            productList: [],
            polluteList: [],
            otherEquipList: [],
            equipList: [],
            curEquit: {} //当前设备
        };
    },
    mounted() {
        uni.$on('sendTrendData', (payload) => {
            this.$nextTick(() => {
                this.equipList = payload.equipList;
            });
        });
    },
    methods: {
        // 初始化生产线
        initProductList() {
            this.productList = this.equipList?.scList?.reverse() || [];
            this.polluteList = this.equipList?.zwList?.reverse() || [];
            this.otherEquipList = this.equipList?.qtList?.reverse() || [];
            // 如果之前有选中的设备就使用之前选中的设备
            // 如果没有选中过设备,默认拿生产线第一个设备并高亮
            // if (!Object.keys(this.curEquit).length) {
            this.curEquit = this.productList.length
                ? this.productList[0]
                : this.polluteList.length
                ? this.polluteList[0]
                : this.otherEquipList[0];
            // }

            this.$nextTick(() => {
                this.$refs.tendencyChart.getTrendPic();
            });
        },
        radioChange($event, item) {
            this.curEquit = item;
            // this.$nextTick(()=>{
            // 	this.$refs.tendencyChart.getTrendPic();
            // })
        }
    },
    beforeDestroy() {
        uni.$off('sendTrendData');
    }
};
</script>

<style scoped>
.qiye-board {
    position: relative;
}

.radiogroup {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 28rpx;
}

.radiogroup .left {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.radiogroup .radio {
    transform: scale(0.5);
}
</style>
