<!-- @format -->
<template>
    <div class="tabs1-con">
        <div class="qiye-tu">
            <TendencyChart
                ref="tendencyChart"
                :queryTime="queryTime"
                :curEquit="curEquit"
                :dateStr="dateStr"
                :curIndex="curIndex"
                :warnStart="warnStart"
                :warnEnd="warnEnd"
                :x="x"
            ></TendencyChart>
        </div>
        <div class="nodata" v-show="!productList.length && !polluteList.length">
            暂无数据
        </div>
        <div
            class="qiye-board"
            v-show="productList.length || polluteList.length"
        >
            <div v-show="productList.length">
                <div class="zy-line ac jb">
                    <p class="qiye-til1">生产设备</p>
                </div>
                <div class="qiye-shebei">
                    <radio-group
                        @change="radioChange($event, item, index)"
                        v-for="(item, index) in productList"
                        :key="item.SBXH"
                        :data-item="item"
                    >
                        <label class="item radiogroup">
                            <div class="left">
                                <div
                                    class="ic"
                                    style="background-color: #3873ff"
                                >
                                    <i style="background-color: #3873ff"></i>
                                </div>
                                <view>{{ item.SBMC }}</view>
                            </div>
                            <view>
                                <radio
                                    style="transform: scale(0.7)"
                                    :value="item.SBXH"
                                    :checked="item.SBXH === curEquit.SBXH"
                                />
                            </view>
                        </label>
                    </radio-group>
                </div>
            </div>

            <div class="gap"></div>
            <div class="zy-line ac jb" v-if="polluteList.length != 0">
                <p class="qiye-til1">治污设备</p>
            </div>
            <div class="qiye-shebei" v-if="polluteList.length != 0">
                <radio-group
                    @change="radioChange($event, item, index)"
                    v-for="(item, index) in polluteList"
                    :key="item.SBXH"
                    :data-item="item"
                >
                    <label class="item radiogroup">
                        <div class="left">
                            <div class="ic" style="background-color: #3873ff">
                                <i style="background-color: #3873ff"></i>
                            </div>
                            <view>{{ item.SBMC }}</view>
                        </div>
                        <view>
                            <radio
                                style="transform: scale(0.7)"
                                :value="item.SBXH"
                                :checked="item.SBXH === curEquit.SBXH"
                            />
                        </view>
                    </label>
                </radio-group>
            </div>
            <div class="gap"></div>
            <div class="gap"></div>
            <div class="gap"></div>
        </div>
        <div class="gap"></div>
    </div>
</template>
<script>
import { sbsxzt } from '@/api/iot/warning.js';
import { debounce } from 'lodash';
import TendencyChart from './TendencyChart.vue';
export default {
    components: {
        TendencyChart
    },
    props: {
        currentscx: {
            type: String,
            default: ''
        },
        dateStr: {
            type: String,
            default: ''
        }
    },
    watch: {
        currentscx: {
            handler: function (nv) {
                this.$nextTick(() => {
                    this.gettjt();
                });
            }
        }
    },
    data() {
        return {
            trendData: [],
            productList: [],
            polluteList: [],
            curEquit: {}, //当前设备
            queryTime: {}, //查询时间
            curIndex: 0,
            warnStart: '',
            warnEnd: '',
            x: [],
            debouncedGettjt: null
        };
    },
    created() {
        this.debouncedGettjt = debounce(this.gettjt, 1500); // 设置防抖延迟时间为800毫秒
    },
    mounted() {
        uni.$on('sendData', (payload) => {
            this.$nextTick(() => {
                this.queryTime.startT = this.$dayjs(payload.startTime).format(
                    'YYYY-MM-DD HH:mm'
                );
                this.queryTime.endT = this.$dayjs(payload.endTime).format(
                    'YYYY-MM-DD HH:mm'
                );
                this.warnStart = payload.warnStart;
                this.warnEnd = payload.warnEnd;
                this.x = payload.x;
                let { scList, zwList } = payload.arrSendData;
                zwList = zwList.filter((e) => e.SBMC != '');
                this.productList = scList?.reverse() || [];
                this.polluteList = zwList?.reverse() || [];
                this.curEquit =
                    this.productList.length > 0
                        ? this.productList[0]
                        : this.polluteList[0];
                this.isControlEquit =
                    this.productList.length > 0 ? false : true;
                this.curSBXH = this.curEquit.SBXH;
                this.curIndex = 0;
                this.$nextTick(() => {
                    this.debouncedGettjt();
                });
            });
        });
    },
    methods: {
        //获取设备列表
        gettjt() {
            sbsxzt({
                SCXID: this.currentscx,
                DATE: this.$dayjs(this.queryTime.startT).format('YYYY-MM-DD')
            }).then((res) => {
                this.productList = res.data?.scList?.reverse() || [];
                this.polluteList = res.data?.zwList?.reverse() || [];
                this.$nextTick(() => {
                    this.curEquit =
                        this.productList.length > 0
                            ? this.productList[0]
                            : this.polluteList[0];
                });
            });
        },
        radioChange($event) {
            this.curEquit = $event.currentTarget.dataset.item;
        }
    }
};
</script>

<style scoped>
.tabs1-con {
    height: calc(100% - 80rpx);
}
.radiogroup {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 28rpx;
}
.radiogroup .left {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}
.radiogroup .radio {
    transform: scale(0.5);
}
.qiye-board {
    background-color: #fff;
    margin: 0 13px;
    border-radius: 9px;
    padding: 11px;
    height: calc(100% - 540rpx);
    overflow-y: auto;
    position: relative;
}

.qiye-tu {
    padding: 0 16rpx;
}
.nodata {
    text-align: center;
    color: #888;
    padding: 20rpx;
}
</style>
