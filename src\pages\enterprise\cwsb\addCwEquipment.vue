<template>
	<!-- 新增生产设备 -->
	<div style="background: #f1f1f1;height:calc(100% - 60rpx);overflow:scroll;">
		<section class="main" style='padding-bottom: 0; margin-bottom: 30.1932rpx;'>
			<div class="inner">
				<div class="gap"></div>
				<div class="zy-form">
					<div class="item">
						<p class="label star">设备名称</p>
						<div class="rp">
							<input type="text" v-model="model.SBMC" class="zy-input1" placeholder="请填写设备名称"
								@confirm="matchEquipType" @click="hideTabbar" @focus="hideTabbar"
								@blur="matchEquipType">
						</div>
					</div>
					<div class="item">
						<p class="label star">IMEI号</p>
						<div class="rp">
							<input type="text" :disabled="imeiShow || canNotEditIMEI" v-model="model.IMEI" class="zy-input1"
								placeholder="请扫描或手动输入后6位数" style="margin-right: 18rpx;font-size:31rpx;"
								@input="search(model.IMEI)" @click="dealCanNotEditIMEI" @focus="hideTabbar" @blur="showTabbar">
							<image src="../../../static/app/images/sysic.png" class="pd-sysbtn" @click="scanCode()" />
						</div>
					</div>
					<div class="item">
						<p class="label star">设施种类</p>
						<div class="rp">
							<div class="zy-selectBox" @click='showSelectorProductEquipType'>
								<p v-if="!model.GYLX_SBLX_CH" class="res">请选择生产设施种类</p>
								<p else class="res">{{model.GYLX_SBLX_CH}}</p>
							</div>
						</div>
					</div>
					<div class="item">
						<p class="label">设施图片预览</p>
						<div class="rp">
							<div class="zy-selectBox" @click='showSelectorEquipPicture'>
								<p v-if="!model.SST_WJID" class="res">请选择设备图片</p>
								<p else class="res" v-if="model.SST_WJID">
									<image class="pic" mode="aspectFit" :src="getFileUrlPic(model)" alt="" />
								</p>
							</div>
						</div>
					</div>
					<div class="item">
						<p class="label star">
							设备安装时间
						<div class="rp">
							<p-mui-date-picker
                            v-show="!canNotEditIMEI"
                             @confirm="sdConfirm" dateType="SECOND" format="YYYY-MM-DD HH:mm:ss">
								<input type="text" v-model="model.AZSJ" placeholder="请选择时间" class="date-ipt" disabled />
							</p-mui-date-picker>
                            <span class="date-ipt" v-show="canNotEditIMEI">
                               {{model.AZSJ }}
                            </span>
						</div>
					</div>
					<div class="item">
						<p class="label star">设备安装状态</p>
						<div class="rp pd-btn1">
							<button type="button" :class="model.YXZT == item.value ? 'on' : ''"
								v-for="(item,index) in sbztList" :key='item.value'
								@click="changeYXZT(item)">{{item.label}}</button>
						</div>
					</div>
					<div class="item">
						<p class="label star">设备类型</p>
						<div class="rp pd-btn1">
							<button type="button" :class="model.ZNSBLX == item.value ? 'on' : ''"
								v-for="(item,index) in equipTypeList" :key='item.value'
								@click="changeEquipType">{{item.label}}</button>
						</div>
					</div>
					<div class="item" v-if="model.ZNSBLX == 'ZD'">
						<p class="label">振动能量</p>
						<div class="rp pd-btn1">
							<button type="button" :class="model.ZDQD == item.value ? 'on' : ''"
								v-for="(item,index) in shakeList" :key='item.value'
								@click="changeShakeType(item)">{{item.label}}</button>
						</div>
					</div>
					<div class="item" v-if="model.ZNSBLX == 'ZD'">
						<p class="label star">是否受周边影响</p>
						<div class="rp pd-btn1">
							<button type="button" :class="model.SFSYX == item.value ? 'on' : ''"
								v-for="(item,index) in influenceList" :key='item.value'
								@click="changeInfluenceType(item)">{{item.label}}</button>
						</div>
					</div>

					<div class="item" v-if="model.ZNSBLX == 'ZD'">
						<p class="label star">设备运行规律</p>
						<div class="rp pd-btn1">
							<button type="button" v-for="(item,index) in yxglList" :key='item.value'
								@click="changeYXGL(item)"
								v-bind:class="model.type == item.value ? 'on' : ''">{{item.label}}
							</button>
						</div>
					</div>

					<div class="item" v-if="model.type == '2'">
						<p class="label star">间歇周期</p>
						<div class="rp">
							<!-- <input type="text" v-model="model.YXGL.circle" class="zy-input1" placeholder="请填写间歇周期"
								> -->
							<input type="text" v-model="model.circle" class="zy-input1" placeholder="请填写间歇周期">
						</div>
					</div>
					<div class="item">
						<p class="label star">绑定生产线</p>
						<div class="rp">
							<div class="zy-selectBox" @click='showScx()'>
								<p v-if="!model.SCXMC" class="res">请选择</p>
								<p else class="res">{{model.SCXMC}}</p>
							</div>
						</div>
					</div>
					<div class="item" v-if="model.SFFC == '1'">
						<p class="label star" @click="togglePreventIllustration">防拆功能 <image
								src="../../../static/app/images/whic.png" class="pd-whic1">
						</p>
						<div class="rp pd-btn1">
							<button type="button" :class="{on: model.FCBJZT  == '2'}"
								@click="changePreventRemoveType('2')">报警中</button>
							<button type="button" :class="{on: model.FCBJZT  == '1'}"
								@click="changePreventRemoveType('1')">启用</button>
							<button type="button" :class="{on: model.FCBJZT  == '0'}"
								@click="changePreventRemoveType('0')">未启用</button>
						</div>
					</div>
					<div class="item" v-show="isShowSetCircle">
						<p class="label">设置设备参数</p>
						<div class="rp">
							<div class="zy-selectBox" @click="toSetImeiCircle(model.IMEI)">
								<p class="res link">前往设置</p>
							</div>
						</div>
					</div>
					<div class="item nfx">
						<p class="label star" @click="toggleInstallIllustration">安装照片<image
								src="@/static/app/images/whic.png" class="pd-whic1">
						</p>
						<div class="gap"></div>
						<ul class="pd-ulpic1">
							<li v-for="(item,index) in azFileList">
								<div style='position: relative;width: 213.76815rpx;'>
									<image mode="scaleToFill" :src="getFileUrl(item)" alt=""
										@click="previewImage(azFileList,index)" />
									<image mode="scaleToFill" src="../../../static/app/images/cls.png" class="delImg"
										@click="delFile(item)" />
								</div>
							</li>
							<li>
								<image @click="addFile('ZDSB')" src="../../../static/app/images/addpic.png"
									class="pd-addpic1" />
							</li>
						</ul>
					</div>
					<div class="item remark">
						<p class="label">备注信息</p>
						<div class="rp">
							<textarea auto-height type="text" v-model="model.BZ" row="3" :class="model.BZ ? '':'two' "
								@click="hideTabbar" @focus="hideTabbar" @blur="showTabbar"
								class="zy-textarea1 uni-input" cursor-spacing="20"
								placeholder="请输入备注信息如设备运行规律区别于“平稳”“间歇”，可特别说明下。"></textarea>

							<!-- <u-input v-model="model.BZ" type="textarea" row="3" :class="model.BZ ? '':'two'" adjust-position :clearable="false" class="zy-textarea1" placeholder-style="#c1c1c1"  placeholder="请输入备注信息如设备运行规律区别于“平稳”“间歇”，可特别说明下。" auto-height  /> -->
						</div>
					</div>
					<div class="item">
						<p class="label" style="color: #4874ff;flex: auto;" @click="changeShowThreeList">查看最近三次采集结果</p>
						<div class="rp">
							<p class="pd-txt2" v-show="showThreeList">
								<span class="show-newData">已展示采集结果</span>
								<u-icon name="reload" color="#ddd" size="22" @click="getnewStateList()"></u-icon>
							</p>
						</div>
					</div>
				</div>
				<div class="gap"></div>
				<!-- 采集结果 -->
				<div v-show="showThreeList">
					<EquipStateList :list="threeList"></EquipStateList>
				</div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="zy-bot-btn1" @click="save">保存</div>
				<div class="gap"></div>
				<div class="gap"></div>
			</div>
		</section>

		<div class="mymask" v-show="showMask">

		</div>
		<div class="pd-botdlg" v-show="showPreventIllustration">
			<i class="dlgcls" @click="togglePreventIllustration"></i>
			<div class="gap"></div>
			<div class="gap"></div>
			<div class="pd-con1">
				<div class="pd-tit1">防拆功能说明</div>
				<div class="gap"></div>
				<div class="gap"></div>

				<div class="gap"></div>
				<dl class="pd-dltxt1">
					<dt></dt>
					<dd>1、启用防拆功能后，如果防拆开关弹起将会触发防拆报警;</dd>
					<dd>2、新增设备防拆功能默认开启。</dd>
				</dl>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
			</div>
		</div>

		<div class="pd-botdlg" v-show="showInstallIllustration">
			<i class="dlgcls" @click="toggleInstallIllustration"></i>
			<div class="gap"></div>
			<div class="gap"></div>
			<div class="pd-con1">
				<div class="pd-tit1">拍照示例</div>
				<div class="gap"></div>
				<div class="gap"></div>
				<ul class="sample">
					<li>
						<image class="pic" mode="widthFix" src="../../../static/images/sample/sb1.jpg" alt="">
							<p>近景</p>
					</li>
					<li>
						<image class="pic" mode="widthFix" src="../../../static/images/sample/sb3.jpg" alt="">
							<p>中景</p>
					</li>
					<li>
						<image class="pic" mode="widthFix" src="../../../static/images/sample/sb2.jpg" alt="">
							<p>远景</p>
					</li>
				</ul>
				<div class="gap"></div>
				<div class="gap"></div>
				<dl class="pd-dltxt1">
					<dt>说明：</dt>
					<dd>1、近景尽量把设备IMEI号拍出来；</dd>
					<dd>2、尽量把设备所安装的设备与设备的大概位置展示出来。</dd>
				</dl>
				<div class="gap"></div>
				<div class="gap"></div>
			</div>
		</div>

		<!-- 选择图片 -->
		<selectEquipPicturePop :SBID="model.SBID" v-show="isShowAddEquipPicture"
			:isShowAddEquipPicture.sync="isShowAddEquipPicture" :showMask.sync="showMask"
			@updateDiagramEquipPicture="updateDiagramEquipPicture"></selectEquipPicturePop>

		<u-select value-name="value" label-name="label" mode="single-column" :list="arrProductType"
			v-model="isShowSelectorProductType" @confirm="confirmProductType"></u-select>
		<u-select value-name="SCXID" label-name="SCXMC" mode="single-column" :list="scxList" v-model="scxShow"
			@confirm="selectProduceLine"></u-select>
		<u-select value-name="IMEI" label-name="IMEI" mode="single-column" :list="imeiList" v-model="imeiShow"
			@confirm="selectImei"></u-select>
		<!-- 返回上页是否存草稿弹窗 -->
		<PageBackRemindModal v-show="isShowBackPop" :isShowBackPop.sync="isShowBackPop"  :popContent="popContent" 
		@movePage="movePage" @saveDialog="saveDialog" @stateOnPage="stateOnPage"></PageBackRemindModal>
	</div>
</template>

<script>
	import minix from '../minix.js';
	import equipmentCard from '@/pages/realtime/components/equipmentCard';
	import {
		getGgdmz
	} from '@/api/iot/ggdmz.js';
	import {
		getMaterialLegendList,
		matchEquipPicture
	} from '@/api/iot/planeFigure.js';

	import {
		saveCwsb,
		getScxList,
		addScsbList,
		newStateList,
		getFilelist,
		deletefile,
		znsb,
		commocode,
		editScsb,
		queryEquipPics,
		getValidate,
		getProductContorlRelationeEditHistory,
        getScxsbList
	} from '@/api/iot/enterprise.js';
	import {
		DOWNLOAD_URLZDY,
		UPLOAD_URL,
		LOGIN_ULR_BASE,
	} from '@/common/config.js';
	import {
		guid
	} from '@/common/uuid.js';
	import EquipStateList from '@/pages/component/EquipStateList';
	import selectEquipPicturePop from '@/pages/enterprise/components/selectEquipPicturePop';
	import PageBackRemindModal from '../components/PageBackRemindModal';
	export default {
		mixins: [minix],
		data() {
			return {
				autoUpload: false,
				uploadUrl: UPLOAD_URL,
				token: '',
				enterpriseInfo: {},
				info: {},
				model: {
					SBID: '',
					SBMC: '', //设备名称
					IMEI: '', //IMEI
					YXZT: '1', //运行状态
					YXGL: {
						type: "",
					}, //运行规律
					SCXID: '', //生产线
					AZSJ: '', //安装时间
					BZ: '', //备注内容
					USER: '', //用户
					ORGID: '', //企业的ORGID,
					SCXMC: '', //生产线id
					circle: '', //间歇周期
					type: '1',
					ZDQD: '', //振动能量
					SFSYX: '1', //是否受周边影响
					ZNSBLX: '', //设备类型  （ZD-振动，DL-电流）
					SFFC: '0', //是否防拆SFFC 1展示0不展示
					FCBJZT: '0', //0不启用,  1  启用 ，默认启用 是否防拆
					SST_WJID: '', //图片文件id
					GYLX_SBLX_CH: '', //生产设施种类
				},
				HYFL: '', //产污设备组织方式
				SBLX: '', //治污设备组织方式
				SSSCX: '',
				selectWrwList: [], //选中的污染物list
				hylxShow: false,
				sblxShow: false,
				sblxList: [],
				zywrShow: false,
				sbztList: [{
						label: "工作",
						value: "1"
					},
					{
						label: "待机",
						value: "2"
					},
					{
						label: "关闭",
						value: "3"
					}
				],
				yxglList: [{
						label: "平稳",
						value: "1"
					},
					{
						label: "间歇",
						value: "2"
					}
				],
				scxShow: false,
				scxList: [],
				rules: {
					SBMC: {
						required: true,
						message: '请填写设备名称',
						trigger: 'change'
					},
					IMEI: {
						required: true,
						message: '请扫描获取IMEI'
					},
					YXZT: {
						required: true,
						message: '请选择设备安装状态'
					},
					SFSYX: {
						required: true,
						message: '请选择是否受周边影响'
					},
					YXGL: {
						required: true,
						message: '请选择运行规律'
					},
					circle: {
						required: false,
						message: '请填写间歇周期'
					},
					SCXID: {
						required: true,
						message: '请选择生产线'
					},
					AZSJ: {
						required: true,
						message: '请获取安装时间'
					},
				},

				border: false, //input 是否显示边框, 默认false
				labelWidth: 200,
				selectShow: false,
				labelPosition: 'left', //表单域提示文字的位置，left-左侧，top-上方
				errorType: ['border-bottom'], //错误的提示方式，数组形式, 可选值为：
				copy: false,
				showThreeList: false, //采集结果列表
				threeList: [], //最近三次采集结果
				isNormal: ['正常', '异常'],
				showMask: false,
				showInstallIllustration: false,
				showPreventIllustration: false,
				azFileList: [], // 安装上传图片
				threeData: [{
						name: '检测时间',
						value: 'JCSJ'
					},
					{
						name: '设备状态',
						value: 'ERR'
					},
					{
						name: '设备电压',
						value: 'VOL_ZT'
					},
					{
						name: '信号功率',
						value: 'SP_ZT',
					},
					{
						name: '信号质量',
						value: 'RSRQ_ZT'
					},
					{
						name: '振动能量',
						value: 'NL_LV'
					},
				],
				fixedLine: false,
				backPage: false,
				timer: null,
				imeiShow: false,
				imeiList: [],
				fromScan: false,
				focus: true,
				isGetIMEI: false, //是否匹配到IMEI
				shakeList: [ //振动能量
					// {
					// 	label:'弱',
					// 	value:'1'
					// },
					// {
					// 	label:'中',
					// 	value:'2'
					// },
					// {
					// 	label:'强',
					// 	value:'3'
					// },
				],
				influenceList: [ //周边影响
					// {
					// 	label:'是',
					// 	value:'1'
					// },
					// 	{
					// 		label:'否',
					// 		value:'0'
					// 	},
				],
				equipTypeList: [{
						value: 'ZD',
						label: '振动'
					},
					{
						value: 'DL',
						label: '电流'
					},
				],
				keyHeight: false,
				pageType: '', //页面状态
				arrProductType: [],
				isShowSelectorProductType: false,
				isShowSetCircle: false,
				isShowAddEquipPicture: false,
				addEquipPictureWJID: '', //当前选中的图片id
				initArrMaterialLibrary: [], //全部图片
				arrMaterialLibrary: [], //查询到的图片
				oldRelateLine: '', //旧的关联的产线
				isHaveEditProductContorlRelationeEditHistory:false,//是否存在产治污关系配置记录
                canNotEditIMEI:false,//超过24小时不可以编辑IMEI
                relativeEquips:[],//产线对应的设备列表
				popContent:'当前内容未保存，是否离开？',
				isShowBackPop:false
			};
		},
		components: {
			equipmentCard,
			EquipStateList,
			selectEquipPicturePop,
			PageBackRemindModal
		},
		watch: {
			'model.IMEI': {
				handler(newV, oldV) {
					if (newV == '') {
						this.threeList = [];
						this.model.SFFC = '0';
						this.model.FCBJZT = '1';
					}
				},
				immediate: true
			},
			model: {
				handler(newVal, oldVal) {
					if (this.pageType == 'add') {
						this.backPage = true;
					}
				},
				deep: true,
			},
		},
		onLoad(option) {
			this.info = JSON.parse(decodeURIComponent(option.info)) || {};
			this.enterpriseInfo = uni.getStorageSync('userInfo');
			this.editType = option.type;
			//从详情页进入编辑
			if (option.type && option.type == 'edit') {
				this.pageType = 'edit';
				this.isShowSetCircle = true;
				uni.setNavigationBarTitle({
					title: '编辑生产设备'
				});
				this.model = Object.assign(this.model, JSON.parse(decodeURIComponent(option.detail)));
				if (this.model.YXGL) {
					this.model.type = this.model.YXGL.type;
					this.model.circle = this.model.YXGL.circle;
				}
                //判断按安装时间是否距离编辑是否大于24小时，大于24小时，不可以编辑IMEI号
                let eidtTime = this.$dayjs().format('YYYY-MM-DD HH:mm:ss');
                let diffDays = this.$dayjs(eidtTime).diff(this.$dayjs(this.model.AZSJ), 'days');
                if(diffDays>1){
                    this.canNotEditIMEI = true;
                }
				//存下进入编辑页时的关联产线
				this.oldRelateLine = this.model.SCXID;
				//请求设备安装图片
				this.getFileList();
			}

			//从列表页进入新增页面
			if (option.type && option.type == 'add') {
				this.model.AZSJ = this.$dayjs().format('YYYY-MM-DD HH:mm:ss');
				uni.setNavigationBarTitle({
					title: '新增生产设备'
				});
				this.pageType = 'add';
				this.model = uni.getStorageSync('equitDraft') || this.model;
				this.azFileList = uni.getStorageSync('equitDraftFIleList') || this.azFileList;
				//新增需要生成新得随机设备id
				this.model.SBID = guid();
				//只要是新增就获取当前时间
				this.model.AZSJ = this.$dayjs().format('YYYY-MM-DD HH:mm:ss');
				this.$nextTick(()=>{
					this.backPage = false
				})
				
			}

			//判断是否从生产线跳转过来，从生产线跳转过来需要固定生产线
			if (option.type == 'addEquit') {
				this.model.AZSJ = this.$dayjs().format('YYYY-MM-DD HH:mm:ss');
				uni.setNavigationBarTitle({
					title: '新增生产设备'
				});
				this.pageType = 'addEquit';
				this.model = uni.getStorageSync('equitDraft') || this.model;
				this.azFileList = uni.getStorageSync('equitDraftFIleList') || this.azFileList;
				this.model.SCXID = this.info.SCXID;
				this.model.SCXMC = this.info.SCXMC;
				this.fixedLine = true;
				//新增需要生成新得随机设备id
				this.model.SBID = guid();
				//只要是新增就获取当前时间
				this.model.AZSJ = this.$dayjs().format('YYYY-MM-DD HH:mm:ss');
				this.backPage = false
			}
			//监听键盘抬起
			// #ifdef APP-PLUS
			uni.onKeyboardHeightChange((res) => {
				console.log('res', res);
				// 监听软键盘的高度，页面隐藏后一定要取消监听键盘
				if (res.height == 0) {
					// 我这里仅考虑键盘的出现和隐藏
					console.log('height', this.keyHeight);
					this.keyHeight = false;
				} else {
					this.keyHeight = true;
					console.log('height', this.keyHeight);
				}
			});
			// #endif
		},
		onHide() {
			// #ifdef APP-PLUS
			// 取消监听键盘高度
			uni.offKeyboardHeightChange((res) => {});
			// #endif
		},
		created() {},
		mounted() {
            // 获取缓存中的企业信息
			this.enterpriseInfo = uni.getStorageSync('userInfo');
			this.initGetGgdmz();
			//获取公共代码
			this.getCommonCode();
			this.getProductEquipType();
			this.queryProductContorlRelationeEditHistory();

		},
		onBackPress() {
			if (this.editType == 'add') {
				 console.log('this.backPage',this.backPage)
				let self = this;
				if (this.backPage) {
					uni.hideKeyboard();
					this.isShowBackPop = true;
					return true;
				// 	uni.showModal({
				// 		title: '提示',
				// 		cancelText: '存为草稿',
				// 		content: '当前内容未保存，是否离开?',
				// 		success: function(res) {
				// 			if (res.confirm) {
				// 				console.log('用户点击确定');
				// 				self.backPage = false;
				// 				uni.navigateBack({
				// 					delta: 1
				// 				});
				// 			} else if (res.cancel) {
				// 				console.log('用户点击取消');
				// 				uni.setStorageSync('equitDraft', self.model);
				// 				if (self.azFileList.length > 0) {
				// 					uni.setStorageSync('equitDraftFIleList', self.azFileList);
				// 				}
				// 				uni.showToast({
				// 					title: '内容已保存为草稿',
				// 					icon: 'none'
				// 				});
				// 				self.backPage = false;
				// 				setTimeout(() => {
				// 					uni.navigateBack({
				// 						delta: 1
				// 					});
				// 				}, 1000);

				// 			}
				// 		}
				// 	});
				
				}
			
				// return this.backPage;
			}
		},
		methods: {
			//离开页面不存草稿
			movePage(){
			    this.backPage = false;
				setTimeout(() => {
					uni.navigateBack({
						delta: 1
					});
				}, 200);
			},
			//离开页面存草稿
			saveDialog(){
				let self = this;
				uni.setStorageSync('equitDraft', self.model);
				if (self.azFileList.length > 0) {
					uni.setStorageSync('equitDraftFIleList', self.azFileList);
				}
				uni.showToast({
					title: '内容已保存为草稿',
					icon: 'none'
				});
				self.backPage = false;
				setTimeout(() => {
					uni.navigateBack({
						delta: 1
					});
				}, 200);
			},
			//不离开页面
			stateOnPage(){
				this.isShowBackPop = false;
			},
			//查询产治污关系编辑历史记录
			async queryProductContorlRelationeEditHistory(){
			const arrData = await getProductContorlRelationeEditHistory(this.info.WRYBH)
				this.isHaveEditProductContorlRelationeEditHistory = arrData.length > 0;
			},
			showSelectorEquipPicture() {
				this.isShowAddEquipPicture = true;
				this.showMask = true;
			},
			updateDiagramEquipPicture(payload) {
				this.model.SST_WJID = payload.WJID;
			},

			//匹配设备类型
			async matchEquipType() {
                //如果已经有产线，先校验产线上是否已经有重名设备
                if(this.model.SCXID){
                      try {
                        await this.validIsHadEquipName(this.model.SCXID);
                        this.toQueryEquipPics();
                    } catch (error) {
                        //若重名则清空设备名称
                        console.log('error');
                        this.model.SBMC = '';
                    }
                }else{
                    this.toQueryEquipPics();
                }
			},
			//获取设备种类
			async getProductEquipType() {
				let {
					data
				} = await getGgdmz({
					code: 'SCSBLX'
				});
				if (data && data.length > 0) {
					this.arrProductType = data;
				}
			},
			//根据类型适配图片
			async matchPicture(value = '') {
				//如果类型是其他或者为空就不需要请求
				if (value == 'QT' || '') {
					this.model.SST_WJID = ''
					return;
				}
				let params = {
					sblx: '生产',
					sszl: value
				}
				try{
					const data = await matchEquipPicture(params)
					this.model.SST_WJID = data.WJID;
				}catch(error){
					console.log(error)
				}
			},
			//切换设备种类
			confirmProductType(v) {
				this.model.GYLX_SBLX = v[0].value;
				this.model.GYLX_SBLX_CH = v[0].label;
				this.matchPicture(v[0].value)

			},
			//查询相关类型
			async toQueryEquipPics() {
				let params = {
					SBMC: this.model.SBMC,
					SBLX: '生产'
				};
				let {
					data: {
						data = {},
						message
					}
				} = await queryEquipPics(params);
				if (data.length > 0 && data[0].MC != '其他') {
					this.arrProductType.sort((a, b) => {
						const aIndex = data.findIndex(obj => obj.BM === a.value);
						const bIndex = data.findIndex(obj => obj.BM === b.value);
						if (aIndex === -1) {
							return 1;
						} else if (bIndex === -1) {
							return -1;
						} else {
							return aIndex - bIndex;
						}
					});
					this.model.GYLX_SBLX = this.arrProductType[0].value;
					this.model.GYLX_SBLX_CH = this.arrProductType[0].label;
				} else if (data.length > 0 && data[0].MC == '其他') {
					this.model.GYLX_SBLX = 'QT'
					this.model.GYLX_SBLX_CH = '其他';
				}

			},

			//设备类型选择器
			showSelectorProductEquipType() {
				this.isShowSelectorProductType = true;
			},
			//去设置阈值
			toSetImeiCircle(IMEI) {
				uni.navigateTo({
					url: `/pages/realtime/SetImeiCircle?IMEI=${IMEI}`
				});
			},
			sdConfirm(obj) {
				this.model.AZSJ = obj.time;
			},
			//获取公共代码
			getCommonCode() {
				//生产设备--振动能量
				commocode({
					code: 'ZDSB_ZDQD'
				}).then(res => {
					if (res.data && res.data.length) {
						this.shakeList = res.data;
					}
				});
				//生产设备--周边影响
				commocode({
					code: 'ZDSB_SFSYX'
				}).then(res => {
					if (res.data && res.data.length) {
						this.influenceList = res.data;
					}
				});
			},

			//获取当前时间
			getCurrentTime() {
				this.model.AZSJ = this.$dayjs().format('YYYY-MM-DD HH:mm:ss');
			},
			//选择振动能量
			changeShakeType(item) {
				// 非必填，如果当前没有值说明用户要选中
				// 如果当前有值但是不等于选中的值说明用户要切换状态
				if (this.model.ZDQD !== item.value) {
					this.$set(this.model, 'ZDQD', item.value);
				} else {
					this.$set(this.model, 'ZDQD', '');
				}
			},
			//修改防拆功能
			changePreventRemoveType(v) {
				if (this.pageType == 'add' || this.pageType == 'addEquit') {
					uni.showToast({
						title: '新增设备防拆功能默认未开启',
						icon: 'none'
					});
					return;
				}
				this.model.FCBJZT = v;
			},
			//是否受周边影响
			changeInfluenceType(item) {
				this.$set(this.model, 'SFSYX', item.value);
			},
			//选择设备状态
			changeYXZT(item) {
				this.$set(this.model, 'YXZT', item.value);
			},
			//设备类型
			changeEquipType() {
				uni.showToast({
					title: '当前设备类型不可切换',
					icon: 'none'
				});

			},
			//选择运行规律
			changeYXGL(item) {
				this.$set(this.model, 'type', item.value);
				this.$set(this.model, 'YXGL', {
					type: item.value
				});


			},
			initGetGgdmz() {
				// 设备状态
				// getGgdmz({
				// 	code: 'ZDSB_YXZT'
				// }).then(res => {
				// 	if (res.data && res.data.length > 0) {
				// 		this.sbztList = res.data;
				// 		console.log(this.sbztList);
				// 	}
				// });
				// 运行规律
				// getGgdmz({
				// 	code: 'ZDSB_YXGL'
				// }).then(res => {
				// 	if (res.data && res.data.length > 0) {
				// 		this.yxglList = res.data;
				// 		console.log(this.yxglList);
				// 	}
				// });
				// 组织方式
				// let {
				// 	sjqx
				// } = this.enterpriseInfo;
				let {
					WRYBH
				} = this.info;
				getScxList({
					ORGID: uni.getStorageSync('ORGID') || '',
					WRYBH: WRYBH
				}).then(res => {
					if (res.data && res.data.length > 0) {
						this.scxList = res.data;
					}
				});
			},
            //安装时间超过24小时不可以编辑
            async dealCanNotEditIMEI(){
                return new Promise((resolve,reject)=>{

                    if(this.canNotEditIMEI){
                    uni.showModal({
                            title: '提示',
                            content: '安装时间已经超过24小时，IMEI号不可以编辑！',
                            showCancel:false,
                            success: function(res) {
                                console.log('用户点击确定');
                            }
                        });
                        reject('安装时间已经超过24小时，IMEI号不可以编辑');
                    }else{
                        resolve();

                    }
                });

            },

			//搜索
			async search(imei) {
                try {
                    await this.dealCanNotEditIMEI();
                    if (imei && imei.length < 5) {
                        if (this.timer) {
                            clearTimeout(this.timer);
                        }
                        this.timer = setTimeout(() => {
                            uni.showToast({
                                title: '请先输入5位以上的IMEI号',
                                icon: 'none'
                            });
                        }, 500);
                        return;

                    }
                    if (imei && imei.length >= 5) {
                        if (this.timer) {
                            clearTimeout(this.timer);
                        }
                        this.timer = setTimeout(() => {
                            this.getImei(imei);
                        }, 500);
                    }
                } catch (error) {
                    console.log('error',error)
                }

			},

			// 查询完整imei码
			getImei(imei) {
				znsb({
					IMEI: imei
					// IMEI: '513710'
					// IMEI: '34040'
				}).then((res) => {
					if (!res.data || res.data.length === 0) {
						uni.showToast({
							title: "未匹配到IMEI号，请检查输入！",
							icon: 'none'
						});
						this.isGetIMEI = false;
						this.isShowSetCircle = false;
					} else {
						uni.hideKeyboard(); //隐藏软键盘
                        this.imeiList = res?.data?.filter(
                        (item) => item.ZNSBLX !== 'PH' && item.ZNSBLX !== 'RAD'
                        );
                        console.log('  this.imeiList', this.imeiList);
						this.imeiShow = true;
						this.isGetIMEI = true;
					}
				});
			},

			// 选择查出来的imei码
			selectImei(v) {
				// 先校验选择的码是不是已经被绑定了
				getValidate({
					IMEI: v[0].value
					// IMEI:'865257111065919'
					//IMEI:'865257111065919'
				}).then(res => {
					// 选择的码有效
					if (res.data.length === 0) {
						this.model.IMEI = v[0].value;
						//获取到正确的imei之后可以设置阈值
						this.isShowSetCircle = true;
						let obj = this.imeiList.find(e => e.IMEI == this.model.IMEI);
						this.model.ZNSBLX = obj.ZNSBLX;
						this.model.SFFC = obj.SFFC;
						this.model.FCBJZT = '0';
						this.changeShowThreeList();
					}
					// 码已经被绑了
					else if (res.data && res.data.length > 0) {
						this.model.IMEI = '';
						this.isShowSetCircle = false;
						this.isGetIMEI = false;
						let msg = res.data[0].TEXT;
						uni.showToast({
							title: msg,
							icon: "none",
							duration: 2000
						});
						// 查出多个码时，继续弹下拉框
						if (this.imeiList.length > 1) {
							setTimeout(() => {
								this.imeiShow = true;
							}, 1000);
						}
					}
				});
			},

			async scanCode() {
                try {
                    await this.dealCanNotEditIMEI();
                    uni.scanCode({
                        scanType: ['qrCode'],
                        success: res => {
                            console.log('res', res);
                            this.fromScan = true;
                            let imei = res.result.split(';')[0];
                            if (imei != '') {
                                this.search(imei);
                            }
                            // this.getnewStateList(this.model.IMEI)
                        },
                        fail: (err) => {
                        uni.showToast({
                            title: '未识别到二维码！',
                            icon: 'none'
                        });
                    }
                    });

                } catch (error) {

                }

        },

			//展示列表最新3条数据数据
			changeShowThreeList() {
				if (this.model.IMEI.length == '') {
					uni.showToast({
						title: '请先扫码获取IMEI号',
						icon: 'none'
					});
					return;
				}
				if (!this.threeList || !this.threeList.length) {
					this.getnewStateList();
				}
				this.showThreeList = !this.showThreeList;

			},
			//获取最新的3条数据
			getnewStateList() {
				this.threeList = [];
				newStateList({
					IMEI: this.model.IMEI
				}).then(res => {
					this.threeList = [...res.data];
				});
			},

			cancel() {
				this.zywrShow = false;
				this.selectWrwList = [];
			},

			//添加文件
			addFile(zlx) {
				if (this.azFileList.length >= 3) {
					uni.showToast({
						title: "最多只能上传3张照片",
						icon: 'none'
					});
					return;
				}

				let self = this;
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
					success: function(res) {
						console.log('reschooseImage', res);
						//console.log(JSON.stringify(res.tempFilePaths));
						self.uploadFile(res, zlx);
					}
				});
			},
			uploadFile(res, zlx) {
				console.log('resuploadFile', res);
				console.log('this.model.SBID', this.model.SBID);
				let f = res.tempFilePaths[0];
				let {size,type} = res.tempFiles[0];
				let self = this;
				uni.showLoading({
					title: "上传中"
				});
				uni.uploadFile({
					url: UPLOAD_URL,
					filePath: f,
					name: f.name,
					formData: {
						WJDX:size/1024,
						WJLX:type,
						LXDM: 'ZDFJ',
						ZLXDM: zlx,
						YWSJID: this.model.SBID,
						WJMC: f.name
					},
					timeout: 60000,
					success: function(r) {
						uni.showToast({
							title: '上传成功',
							icon: 'none'
						});
						//上传成功掉获取文件列表接口
						self.getFileList();
						uni.hideLoading();
					},
					fail: function(err) {
						uni.showToast({
							title: '上传失败',
							icon: 'none'
						});
						uni.hideLoading();
					}
				});
			},
			//获取上传后的文件列表，看接口文档要传什么参数,
			getFileList() {
				getFilelist({
					"pageSize": 100000,
					"pageNum": 1,
					"YWSJID": this.model.SBID,
					"LXDMS": "ZDFJ",
					"ZLXDMS": "WRYZP,GYLCT,ZDSB"
				}).then(res => {
					let fileData = res[0];
					if (fileData && fileData.zlxList && fileData.zlxList.length > 0) {
						fileData.zlxList.forEach(list => {
							if (list.ZLXDM == 'ZDSB') {
								this.azFileList = list.fileList;
							}
						});
					}
				});
			},
			getFileUrl(item) {
				return DOWNLOAD_URLZDY + item.WJID;
			},
			getFileUrlPic(item) {
				return DOWNLOAD_URLZDY + item.SST_WJID;
			},
			showScx() {
				if (this.fixedLine) {
					uni.showToast({
						title: '当前生产线已绑定，不可选择',
						icon: 'none'
					});
					return;
				}
				this.scxShow = true;
			},
            //校验生产线是否已经存在设备名称
            validIsHadEquipName(scxid){
                return new Promise((resolve,reject)=>{
                    //选择生产线前校验生产线上是否已经存在相同的名称的设备
                    getScxsbList({
                        SCXID: scxid
                    }).then((res) => {
                        this.relativeEquips = res.data || [];
                        let objSameNameEquip = this.relativeEquips.find(item=>item.SBMC === this.model.SBMC);
                        if(objSameNameEquip){
                             uni.showToast({
                                title:'该生产线已有设备重名，请重新输入',
                                icon:'none'
                            });
                            return reject();
                        }else{
                            return resolve();
                        }
                    });
                });

            },
            //选择生产线
			async selectProduceLine(v) {
                try {
                    await this.validIsHadEquipName(v[0].value);
                    this.model.SCXID = v[0].value;
                    this.model.SCXMC = v[0].label;
                } catch (error) {
                    console.log('error');
                    this.model.SCXID = '';
                    this.model.SCXMC = '';
                }
            },

			//预览图片
			previewImage(fileList, index) {
				let self = this;
				let fileUrls = fileList.map((file) => {
					return this.getFileUrl(file);
				});
				console.log('fileUrls', fileUrls);
				// 预览图片
				uni.previewImage({
					current: index,
					urls: fileUrls,
					//长按保存到本地
					longPressActions: {
						itemList: ["保存图片到本地"],
						success: (data) => {
							// console.log('data', data)
							if (data.tapIndex == 0) {
								let imgurl = fileUrls[data.index];
								self.saveImage(imgurl);
							}

						},
						fail: function(err) {
							console.log(err.errMsg);
						},
					},

				});

			},

			//保存图片
			saveImage(imgurl) {
				// console.log(imgurl)
				uni.downloadFile({
					url: imgurl,
					success(res) {
						let url = res.tempFilePath;
						uni.saveImageToPhotosAlbum({
							filePath: url,
							success() {
								uni.showToast({
									title: '已存至系统相册',
									icon: "success"
								});
							},
							fail(err) {
								uni.showToast({
									title: '保存失败',
									icon: "error"
								});
							}
						});
					}
				});
			},

			delFile(file) {
				let self = this;
				uni.showModal({
					title: '提示',
					content: '确认删除照片?',
					success: function(res) {
						if (res.confirm) {
							console.log('用户点击确定');
							deletefile(file.WJID).then(res => {
								uni.showToast({
									title: '删除成功',
									duration: 500
								}).then(() => {
									setTimeout(() => {
										self.getFileList();
									}, 500);

								});

							});
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},

			save() {
				let self = this;
				if (this.timer) {
					clearTimeout(this.timer);
				}
				this.timer = setTimeout(() => {
					//设置运行规律，间歇周期
					if (this.model.type == '1') {
						this.$set(this.model, 'YXGL', {
							type: this.model.type
						});
					} else if (this.model.type == '2') {
						this.$set(this.model, 'YXGL', {
							type: this.model.type,
							circle: this.model.circle
						});
					}
					// 如果选择运行规律优先校验运行规律的必填项，间歇周期只有在选中了运行规律并且是间歇时才去校验
					this.rules.circle.required = this.model.type == "2" ? true : false;

					//判断设备类型，重置相关校验规则,
					//如果振动，周边影响，运行规律，间歇周期都不是必填,并清空数据项
					if (this.model.ZNSBLX == 'ZD') {
						this.rules.SFSYX.required = true;
						this.rules.YXGL.required = true;
					} else if (this.model.ZNSBLX == 'DL') {
						this.rules.SFSYX.required = false;
						this.rules.YXGL.required = false;
						this.rules.circle.required = false;
						this.model.SFSYX = '';
						this.model.YXGL = '';
						this.model.circle = '';
					}

					this.model.USER = this.enterpriseInfo.id;
					this.model.ORGID = uni.getStorageSync('ORGID') || '';
					this.model.CJR = this.enterpriseInfo.name;

					let rules = Object.keys(this.rules);
					for (let i = 0; i < rules.length; i++) {
						let field = rules[i];
						let requires = this.rules[field];
						// 单独校验运行规律
						if (this.model.ZNSBLX == 'ZD' && field === 'YXGL' && !this.model['YXGL']['type']) {
							uni.showToast({
								icon: 'none',
								title: '请选择运行规律',
							});
							return;
						}
						if (field !== 'YXGL' && (!this.model[field] || !this.model[field].length) && requires
							.required) {
							uni.showToast({
								icon: 'none',
								title: requires.message,
							});
							return;
						}
					}


					// 传给后端需要转格式
					this.model.YXGL = JSON.stringify(this.model.YXGL);

					//保存前判断是否匹配到IMEI
					if (!this.isGetIMEI && this.model.IMEI.length != 15) {
						uni.showToast({
							title: "未匹配到IMEI号，请检查输入！",
							icon: 'none'
						});
						return;
					}
					//校验图片上传
					if (!this.azFileList.length) {
						uni.showToast({
							title: '请上传安装图片',
							icon: 'none',
							duration: 1000
						});
						return;
					}

					//从列表页进入新增页面
					if (this.editType && this.editType == 'add' || this.editType == 'addEquit') {
						znsb({
							IMEI: this.model.IMEI
						}).then((res) => {
							if (!res.data || res.data.length === 0) {
								uni.showToast({
									title: "未匹配到IMEI号，无法保存，请检查输入！",
									icon: 'none'
								});
							} else {
								addScsbList(this.model).then(res => {
									if (res.data && res.data.status != '000') {
										uni.showToast({
											title: '添加失败',
											duration: 1000
										});
										return;
									}
									uni.showToast({
										title: '添加成功',
										duration: 800
									}).then(() => {
											if(self.isHaveEditProductContorlRelationeEditHistory){
                                                setTimeout(() => {
                                                    //如果有产治污配置历史记录提示需重新配置产治污关系
                                                    uni.showModal({
                                                        title: '提示',
                                                        content: '需重新配置产治污关系',
                                                        showCancel: false,
                                                        success: function(res) {
                                                            self.addSuccessCallback()
                                                        },
                                                    })
                                                }, 900)
											}else{
												self.addSuccessCallback()
											}

									});

								}).catch(err => {
									uni.showToast({
										title: '添加失败'
									});
								});
							}
						});


					}

					//从详情页进入编辑页
					if (this.editType && this.editType == 'edit') {
						editScsb(this.model).then(res => {
							if (res.data && res.data.status != '000') {
								uni.showToast({
									title: '编辑失败',
									duration: 1000
								});
								return;
							}
							uni.showToast({
								title: '操作成功'
							}).then(() => {
								setTimeout(() => {
									if (self.isHaveEditProductContorlRelationeEditHistory&&self.oldRelateLine != self.model.SCXID) {


										uni.showModal({
											title: '提示',
											content: '需重新配置产治污关系',
											showCancel: false,
											success: function(res) {
												self.editSuccessCallback()
											}
										})
									} else {
										self.editSuccessCallback()
									}
								}, 1000)

							});
						}).catch(err => {
							uni.showToast({
								title: '操作失败'
							});
						});
					}

				}, 500);

			},
			//添加设备成功回调
			addSuccessCallback(){
				this.backPage =false;
				//保存成功清除草稿
				uni.removeStorageSync(
					"equitDraft"
					);
				uni.removeStorageSync(
					"equitDraftFIleList"
					);
				setTimeout(() => {
					let pages =
						getCurrentPages(); // 当前页面
					//刷新上一页面的设备列表
					let beforePage =pages[pages.length -2]; // 上一页
					if (beforePage.$vm) {
						beforePage.$vm.getScsbList &&beforePage.$vm.getScsbList();
						beforePage.$vm.getScx &&beforePage.$vm.getScx();
					} else {
						beforePage
							.getScsbList &&
							beforePage
							.getScsbList();
						beforePage
							.getScx &&
							beforePage
							.getScx();
					}

					//从生产线详情进入时，需要刷新产线详情页的其他设备列表
					if (this
						.fixedLine ===
						true
						) {
						let parentPage =
							pages[
								pages
								.length -
								3
								];
						if (parentPage
							.$vm
							) {
							parentPage
								.$vm
								.getScx &&
								parentPage
								.$vm
								.getScx();
							parentPage
								.$vm
								.getScsbList &&
								parentPage
								.$vm
								.getScsbList();
						} else {
							parentPage
								.getScx();
							parentPage
								.getScsbList();
						}
					}
					uni.navigateBack({
						delta: 1
					});
				}, 600);

			},
			//保存编辑成功回调
			editSuccessCallback() {
				setTimeout(() => {
					let pages = getCurrentPages(); // 当前页面
					let beforePage = pages[pages.length - 2]; // 上一页
					if (beforePage.$vm) {
						beforePage.$vm.initFormData();
						beforePage.$vm.getFileList();
					} else {
						beforePage.initFormData();
						beforePage.getFileList();
					}

					//刷新企业信息页面的生产线线列表、生产设备列表
					let parentPage = pages[pages.length - 3];
					if (parentPage.$vm) {
						parentPage.$vm.getScx && parentPage.$vm.getScx();
						parentPage.$vm.getScsbList && parentPage.$vm
							.getScsbList();
					} else {
						parentPage.getScx && parentPage.getScx();
						parentPage.getScsbList && parentPage.getScsbList();
					}

					uni.navigateBack({
						delta: 1
					});
				}, 1000);
			},
			back() {
				uni.navigateBack({
					delta: 1
				});
			},
			toggleInstallIllustration() {
				this.showMask = !this.showMask;
				this.showInstallIllustration = !this.showInstallIllustration;
			},
			togglePreventIllustration() {
				this.showMask = !this.showMask;
				this.showPreventIllustration = !this.showPreventIllustration;
			}

		}
	};
</script>

<style scoped lang="less">
	html {
		-webkit-tap-highlight-color: transparent;
		height: 100%;
	}

	body {
		-webkit-backface-visibility: hidden;
		height: 100%;
	}

	.inputbox {
		display: flex;
	}

	.main {
		overflow: unset;
	}

	.pd-tablebx image {
		height: 30rpx;
	}

	.bznr {
		display: inline-block;
		height: 60rpx;
		line-height: 60rpx;
	}

	/deep/ .u-form-item--left__content__label {
		font-size: 30rpx;
		color: #2c323f;
	}

	/deep/ .u-input__textarea {
		border-radius: 8rpx;
		height: 100rpx;
		background-color: rgb(243, 245, 249);
		padding-left: 10rpx;
	}

	/deep/ .u-list-item {
		margin: 0;
	}

	/deep/ .uni-input-placeholder {
		/* padding: 0 20rpx 0 0; */
		text-align: right;
		font-size: 26rpx;
	}

	.listWarp {
		width: 100%;
		height: 100%;
		overflow-y: scroll;
		padding-top: 80rpx;
	}

	.listWarp p {
		width: 100%;
		padding: 20rpx;
		text-align: center;
		border-bottom: 1px solid #efefef;
	}

	.opration {
		padding: 20rpx;
		display: flex;
		justify-content: space-between;
		position: absolute;
		width: 100%;

		background-color: #fff;
	}

	.confirm {
		color: rgb(60, 170, 255);
	}

	.on {
		color: rgb(60, 170, 255);
	}

	/deep/ .u-icon {
		padding: 0 0 0 10rpx;
	}

	.tip-title {
		text-align: center;
		border-bottom: 1px solid #efefef;
		padding: 20rpx;
		font-weight: 600;
	}

	.tip-content {
		padding: 20rpx;
	}

	.tip-content p {
		/* padding: 20rpx 0; */
		font-size: 26rpx;

		color: #666;
	}

	.tip-know {
		position: absolute;
		bottom: 0;
		padding: 20rpx;
		text-align: center;
		border-top: 1px solid #efefef;
		color: rgb(60, 170, 255);
		width: 100%;
	}

	.mask {
		display: static;
	}

	.pd-ullst1 {
		margin-bottom: 10px;
	}

	.date-ipt.pd-txt1 {
		position: relative;
		top: 5px;
		left: 5px;
		display: inline-block;
	}

	::v-deep .uni-input-input {
		text-align: right;
	}

	.uni-textarea.two {
		min-height: 46px;
	}

	.pd-ulpic1 {
		flex-wrap: wrap;
		align-items: flex-start;
	}

	.pd-ulpic1 li {
		position: relative;
		width: 213.76815rpx;
	}

	.pd-ulpic1 li {
		margin-left: 0;
		margin-top: 12rpx;
	}

	.pd-ulpic1.sample li+li {
		margin-top: 0;
	}

	.pd-ulpic1.sample li image {
		display: block;
		width: 213.76815rpx;
		height: 360rpx;
		margin: 0 auto;
	}

	.pd-ulpic1 li .delImg {
		position: absolute;
		right: 0;
		top: 0;
		width: 48rpx;
		height: 48rpx;
	}

	.pd-ulpic1 {
		flex-wrap: wrap;
		align-items: flex-start;
	}



	.pd-ulpic1 li+li {
		margin-left: 0;
		margin-top: 12rpx;
	}

	.btn-box {
		position: absolute;
		width: 100%;
		padding: 0rpx 30rpx;
	}

	.zy-bot-btn1 {
		/* margin-bottom: 30rpx; */
	}

	uni-button {
		outline: none;
	}

	.show-newData {
		position: relative;
		top: -4rpx;
	}

	.inner {
		padding: 0;
	}

	.normal {
		color: #3ab918;
	}

	.abnormal {
		color: #ff0000;
	}

	.mymask {
		position: absolute;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, .4);
		z-index: 1000;
		width: 100%;
		height: 100%;
	}

	.zy-selectBox .link {
		color: #4874ff;
	}

	.zy-selectBox .pic {
		width: 100rpx;
		height: 100rpx;
		background: #edf0f3;
		padding: 6rpx;

	}
	.pd-botdlg{
		.sample{
			display:flex;
			justify-content: space-between;
		}
		.sample li{
		    width: 30%;
			border-radius:8rpx;
			overflow:hidden;
		}
		.sample li .pic{
			width:100%;
		}
		.sample li p{
			text-align:center;
		}
		
	}
		
</style>
