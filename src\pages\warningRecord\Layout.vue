<template>
	<div style="background-color: #f5f5f5;height:100%">
		<header class="header bluebg">
			    <i class="ic-back" @click="back()"></i>
				<h1 class="title">预警详情</h1>
			</header>
			<section class="main">
				<div class="inner">
					<ul class="yy-ultbs1">
						<li :class="{'on':curType == item.value }" v-for="item in tabArr" @click="changeTab(item)">{{item.name}}</li>
					</ul>
					<div class="gap"></div>
					<!-- 预警内容 -->
					<ContentList v-if="curType == 'content'" :warning="warning"></ContentList>
					<!-- 预警分析 -->
					<AnalysEchart refs="analysEchart" v-if="curType == 'analysis'" :warning="warning"></AnalysEchart>
					<!-- 历史记录 -->
					<History refs="history" v-if="curType == 'history'" :warning="warning"></History> 
					<!-- 内容反馈 -->
					<Feedback  v-if="curType == 'feedback'" :warning="warning" :ID="id"></Feedback> 
				</div>
			</section>
	</div>
</template>

<script>
	import ContentList from './components/ContentList.vue'
	 import AnalysEchart from './components/AnalysEchart.vue'
	import History from './components/History.vue'
	import Feedback from './components/Feedback.vue'
	import {yjxxInfo} from '@/api/iot/warning.js'
	export default {
		components:{
			ContentList,
			AnalysEchart,
			History,
			Feedback
		},
		data() {
			return {
				tabArr: [
					{
						name: '预警内容',
						value: 'content'
					},
					{
						name: '预警分析',
						value: 'analysis'
					},
					{
						name: '历史记录',
						value: 'history'
					},
					{
						name: '内容反馈',
						value: 'feedback'
					}
				],
				curType: 'content',
				id:'',
				warning:{},//预警内容
				dataInfo:{},
			
			}
		},
		onLoad(option){
			this.id = option.YJID;
			this.getWarnInfo();
		}, 	
		created() {
		},
		methods: {

			//tab切换
			changeTab(item){
				this.curType = item.value;
				switch(this.curType)
				{
				    case 'content':
				     this.getWarnInfo()
				        break;
				    case 'analysis':
					    this.getWarnInfo()
						uni.$emit('changeTab')
				        break;
					case 'history':
					    this.getWarnInfo()
						break;
				    default:
						break;
				}
			},
			//预警内容
			getWarnInfo(){
				let obj = {
					ID:this.id
				}		
				yjxxInfo(obj).then(res =>{
					if(res.status == '000'){
						this.warning = res.data[0];
						this.warning.YJBH = this.warning.YJBH || '--' //预警编号
					    // uni.setStorageSync('warningData', this.warning);
						this.date = this.warning.DATE;
						this.scxid = this.warning.SCXID;
						this.$store.commit('SET_WARNING_INFO',res.data[0])
					}
				})
			},
			back() {
			    uni.navigateBack({
			        delta: 1
			    });
			}
		}
	}
</script>

<style scoped>
	html { -webkit-tap-highlight-color: transparent; height: 100%; }
	body { -webkit-backface-visibility: hidden; height: 100%;}
	.main{padding-top: 0;}
	.inner {
	    padding-top: 80rpx;
		overflow:hidden;
		padding-bottom:0rpx;
	}
</style>
