<!-- @format -->

<template>
    <div class="main-page">
        <ul class="yy0706-navtab1">
            <li
                :class="{ on: currentTab === 'warn' }"
                @click="currentTab = 'warn'"
            >
                预警查询
            </li>
            <li
                :class="{ on: currentTab === 'realTime' }"
                @click="currentTab = 'realTime'"
            >
                实时查询
            </li>
        </ul>
        <div class="main-content">
            <Warning v-if="currentTab === 'warn'"></Warning>
            <RealTime v-if="currentTab === 'realTime'"></RealTime>
        </div>
    </div>
</template>

<script>
import Warning from '@/pages/warning/Index';
import RealTime from '@/pages/realtime/index';

export default {
    name: 'DataCollectionAppIndex',
    data() {
        return {
            currentTab: 'warn'
        };
    },
    components: {
        Warning,
        RealTime
    },
    mounted() {},
    methods: {}
};
</script>

<style lang="scss" scoped>
.main-page {
    height: 100vh;
    overflow: hidden;
    .yy0706-navtab1 {
        background: #4874ff;
        li {
            color: rgba(255, 255, 255, 0.8);
        }
        li.on {
            color: #fff;
        }
        li.on::after {
            content: '';
            display: none;
            width: 56px;
            position: absolute;
            left: 50%;
            -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
            bottom: 0;
            text-align: center;
            height: 2px;
            border-radius: 1px;
            background-color: #fff;
        }
    }
    .main-content {
        height: calc(100vh - 50.7222499rpx);
        overflow-y: auto;
        background: #ddd;
    }
}
</style>
