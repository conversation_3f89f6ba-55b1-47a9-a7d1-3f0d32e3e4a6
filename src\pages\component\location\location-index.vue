<template>
	<Page title="请选择">
		<mi-loading ref="Loading" title="定位中" :hasMask="true" id="loadingBtn"></mi-loading>
		<template v-slot:bar>
			<NaviBar title="请选择" textRight="确定" @optionClick="onSelectConfirm" />
		</template>
		<view class="location">
			<view>经度: <span id="lng" ref="lng"></span></view>
			<view>纬度: <span id="lat" ref="lat"></span></view>
		</view>
		<view class="location-btn" @click="resetLocation"><i><em></em></i></view>

		<!-- #ifdef APP-PLUS || H5 -->
		<view @click="locationMap.onClick" id="locationMap" :anginType ="anginType" :prop="option" :change:prop="locationMap.updateEcharts" :change:anginType="locationMap.receiveMsg" style="width:100%; height:95%"></view>
		<!-- #endif -->
		<!-- #ifndef APP-PLUS || H5 -->
		<view>非 APP、H5 环境不支持</view>
		<!-- #endif -->
		<!-- <el-amap vid="amap" :plugin="plugin" class="amap-demo" :center="center">
      </el-amap> -->
	</Page>
</template>

<script>
import miLoading from '@/components/mi-loading/mi-loading.vue';
import NaviBar from '@/pages/component/NaviBar.vue';
import Page from '@/pages/component/Page.vue';
export default {
	components: {
		NaviBar,
		Page,
		miLoading
	},
	data() {
		return {
			option: {
				lng: 11,
				lat: 11
			},
			marker: null,
			anginType:false,
			map: null,
			lng: 116.412251,
			lat: 39.840609
		};
	},

	// watch: {
	// 	lng(){
	// 		// this.map.remove(this.marker);
	// 		// this.getMarker()
	// 	},

	// 	lat(){
	// 		// this.map.remove(this.marker);
	// 		// this.getMarker()
	// 	}
	// },

	onLoad(options) {
		if (options.lng !== 'undefined' && options.lng !== '') {
			this.option.lng = options.lng;
		} else {
			this.option.lng = '';
		}
		if (options.lat !== 'undefined' && options.lat !== '') {
			this.option.lat = options.lat;
		} else {
			this.option.lat = '';
		}
	},

	mounted() {},

	methods: {
		resetLocation(){
			this.anginType = !this.anginType
		},
		onViewClick(option) {
			this.option = JSON.parse(option.option);
		},

		onSelectConfirm() {
			let self = this;
			// 判断经纬度是否有，有的话，就用经纬度的
			let view = uni
				.createSelectorQuery()
				.in(this)
				.select('#lng');
			
			view.fields(
				{
					context: true,
					dataset: true,
					size: true,
					scrollOffset: true
				},
				data => {
					self.option.lng = JSON.stringify(data.dataset.lng).replace(/\"/g, '');
					self.option.lat = JSON.stringify(data.dataset.lat).replace(/\"/g, '');
					
					// 搜索是否在中国内
					this.searchMap();
				}
			).exec();
			
			
		},
		
		searchMap(){
			if (parseFloat(this.option.lng) > 135.4) {
				this.filterTips(this.option.lng);
			} else if (parseFloat(this.option.lng) < 73.4) {
				this.filterTips(this.option.lng);
			} else if (parseFloat(this.option.lat) > 53.6) {
				this.filterTips(this.option.lat);
			} else if (parseFloat(this.option.lat) < 3.8) {
				this.filterTips(this.option.lat);
			} else {
				let location = {};
				location.lng = this.option.lng;
				location.lat = this.option.lat;
				uni.$emit('onLocationSelect', location);
				uni.navigateBack({
					delta: 1
				});
			}
		},

		filterTips(index) {
			uni.showToast({
				icon: 'none',
				duration: 2000,
				title: '该经纬度格式不对或超出中国的经纬度范围,请重新输入正确的经纬度'
			});
		}
	}
};
</script>

<script module="locationMap" lang="renderjs">
const selectedStart = 'static/ITkoala-amap/selectedStart.png' //选中的图片

export default {
	data() {
		return {
			marker: null,
			map: null,
			ownerInstanceObj: null//service层对象
		}
	},

	mounted() {
		if (typeof window.AMap === 'function') {
			this.initAmap()
		} else {
			document.getElementById('loadingBtn').style.display = 'block';
			// 动态引入较大类库避免影响页面展示
			const script = document.createElement('script')
			script.src = 'https://webapi.amap.com/maps?v=1.4.15&key=b698e8fc43715a16053e9fa156601706&plugin=AMap.Geocoder'
			script.onload = this.initAmap.bind(this)
			document.head.appendChild(script)
		}
	},

	methods: {
		initAmap() {
			let self = this;
			// 如果沒有经纬度，那么就开启定位
			if(this.option.lng == ''){
				AMap.plugin('AMap.Geolocation', function() {
					var geolocation = new AMap.Geolocation({
						enableHighAccuracy: true,//是否使用高精度定位，默认:true
						timeout: 10000,          //超过10秒后停止定位，默认：5s
						buttonPosition:'RB',    //定位按钮的停靠位置
						buttonOffset: new AMap.Pixel(10, 20),//定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
						zoomToAccuracy: true,   //定位成功后是否自动调整地图视野到定位点

					});
					// map.addControl(geolocation);
					geolocation.getCurrentPosition(function(status,result){
						if(status=='complete'){
							self.option.lng = result.position.lng;
							self.option.lat = result.position.lat;
							
							

							self.updatePosition();
						}else{
							onError(result)
						}
					});
				});
			}else{
				this.updatePosition();
			}
		},

		againLocation(){
			document.getElementById('loadingBtn').style.display = 'block';
			this.option.lng = ''
			this.anginType = false
			this.initAmap()
		},

		receiveMsg(newValue, oldValue){
				// if(oldValue){
					// this.getMapData()
					this.againLocation()
				// }
			},

		updatePosition(){
			let self = this;
			this.map = new AMap.Map('locationMap', {
				resizeEnable: true,
				center: [self.option.lng, self.option.lat],
				layers: [ //使用多个图层
					// new AMap.TileLayer.Satellite() //使用卫星图
				],
				zooms: [4, 18], //设置地图级别范围
				zoom: 16
			})

			this.getMarker();


			this.map.on('click', function(e) {
				self.option.lng = e.lnglat.lng
				self.option.lat = e.lnglat.lat
				
				// 设置点位信息
				self.setPoint(e.lnglat.lng, e.lnglat.lat);
				
				self.map.remove(self.marker);
				self.getMarker()
			});

			// 设置点位信息
			self.setPoint(self.option.lng, self.option.lat);

			
			document.getElementById('loadingBtn').style.display = 'none';
		},
		
		setPoint(lng, lat){
			try{
				document.getElementById('lng').setAttribute('data-lng',   lng)
				document.getElementById('lng').setAttribute('data-lat',  lat)
				document.getElementById('lng').innerHTML =  lng;
				document.getElementById('lat').innerHTML =  lat;
			}catch(e){
			}
		},

		updateEcharts(){

		},

		// 点击传经纬度
		onClick(event, ownerInstance) {
			let option = this.option;
			// 调用 service 层的方法
			ownerInstance.callMethod('onViewClick', {
				option: JSON.stringify(option)
			})
		},

		getMarker(){
			let that = this
			this.marker = new AMap.Marker({
				position: [that.option.lng, that.option.lat],
				map: that.map
			});
		}
	}
}
</script>

<style scoped>
.location {
	display: flex;
	width: 100%;
	align-items: center;
	justify-content: center;
	padding: 10upx;
}

.location view {
	width: 50%;
}

.location-btn{
	position: absolute;
	right: 20rpx;
	top: 80%;
	z-index: 500;
	background: #4874ff;
	border-radius: 20rpx;
	padding: 10rpx;
	color: #fff;
	width: 80rpx;
	height: 80rpx;
	background-color: #fff;
	display: flex;
    justify-content: center;
    align-items: center;
}
.location-btn i{
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	position: relative;
	border: 10rpx solid #4874ff;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #fff;
}
.location-btn i em{
	width: 20rpx;
	height: 20rpx;
	background: #4874ff;
	border-radius: 50%;
}

</style>
