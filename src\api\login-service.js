/** @format */

import http from '@/common/net/http.js';
import Vue from 'vue';
import dayjs from '@/components/dayjs/dayjs.min.js';

const CACHE_KEY_USER_INFO = 'user_info';

const CACHE_KEY_TOKEN = 'token';
const CACHE_KEY_TOKEN_TIME = 'token_time';
const CACHE_KEY_ISLOGIN = 'is_login';
const CACHE_KEY_USER_ID = 'user_id';

const CACHE_KEY_PASSWORD = 'password';

export const LOGIN_CACHE_KEYS = {
    userInfo: CACHE_KEY_USER_INFO,
    token: CACHE_KEY_TOKEN,
    token_time: CACHE_KEY_TOKEN_TIME,
    userId: CACHE_KEY_USER_ID,
    password: CACHE_KEY_PASSWORD,
    is_login: CACHE_KEY_ISLOGIN
};

import { userCustoms } from '@/api/iot/version.js';
export const loginByPassword = async (userId, password) => {
    //console.log('obj',userId, password)

    clearAuthInfoBeforeLogin(userId);
    // #ifdef APP-PLUS
    try {
        const resp = await http.post(`${http.loginUrl}/mobileLogin`, {
            userid: userId,
            password,
            ssxt: '智能运维App'
        });
        console.log('我请求了登录-app');
        uni.setStorageSync('userInfo', resp.userInfo);
        uni.setStorageSync('password', password);
        // 重置登录状态
        uni.setStorageSync('IS_LOGIN', true);
        onLoginSuccess(userId, resp);
        uni.$emit('onLoginSuccess', resp.userInfo);
        resetLocalStorageORGID(resp);
    } catch (error) {
        uni.$emit('onLoginFail', error || error.errMsg);
    }

    // #endif
    // #ifdef H5

    try {
        const res = await http.post(
            `${http.loginUrl}/mobileLogin`,
            {
                userid: userId,
                password,
                ssxt: '智能运维App'
            },
            { contentType: 'application/json; charset=utf-8' }
        );
        console.log('触发了登陆');
        uni.setStorageSync('password', password);
        // 重置登录状态
        uni.setStorageSync('IS_LOGIN', true);
        uni.setStorageSync('userInfo', res.userInfo);
        uni.setStorageSync('user_info', res.userInfo);
        onLoginSuccess(userId, res);
        uni.$emit('onLoginSuccess', res.userInfo);
        resetLocalStorageORGID(res);
    } catch (error) {
        uni.$emit('onLoginFail', error || error.errMsg);
    }

    // #endif
};

//重置orgid
const resetLocalStorageORGID = async (resp) => {
    //登录时登录信息的sjqx不为空，用登录信息的sjqx作为orgid
    if (resp.userInfo.sjqx != null) {
        uni.setStorageSync('ORGID', resp.userInfo.sjqx);
        uni.setStorageSync('ORGID_LABEL', resp.userInfo.department);
    } else {
        //登录时登录信息的sjqx为空, 查询客户列表接口
        //     2.1、缓存的orgid在列表里面，用缓存的orgid
        //     2.2、缓存的orgid不在列表里面，用列表第一个orgid
        const { data } = await userCustoms({});
        let customORGID = uni.getStorageSync('ORGID');
        let flag = data.some((e) => e.value == customORGID);
        if (!flag) {
            uni.setStorageSync('ORGID', data[0].value);
            uni.setStorageSync('ORGID_LABEL', data[0].label);
        }
    }
};

const onLoginSuccess = (userId, loginResp) => {
    //缓存用户ID
    uni.setStorage({
        key: CACHE_KEY_USER_ID,
        data: userId,
        fail(error) {
            console.log('设置useriderror', error);
        }
    });

    //缓存token
    uni.setStorageSync(CACHE_KEY_TOKEN, loginResp.jwtToken);

    //缓存获取token时间
    uni.setStorage({
        key: CACHE_KEY_TOKEN_TIME,
        data: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        fail(error) {}
    });
    //缓存用户基本信息
    uni.setStorage({
        key: CACHE_KEY_USER_INFO,
        data: loginResp.userInfo,
        fail(error) {}
    });

    // #ifdef APP-PLUS
    setAuthorityUserOnAndroid(userId, loginResp);
    // #endif
};

// #ifdef APP-PLUS
const setAuthorityUserOnAndroid = (userId, loginResp) => {
    if (plus.android) {
        let AuthorityUser = plus.android.importClass(
            'com.bovosz.webapp.auth.AuthorityUser'
        );
        if (AuthorityUser) {
            let authorityUser = new AuthorityUser();
            authorityUser.setUserId(userId);
            authorityUser.setToken(loginResp.jwtToken);
            let AuthAgent = plus.android.importClass(
                'com.bovosz.webapp.auth.AuthAgent'
            );
            let authAgent = AuthAgent.getInstance();
            authAgent.setAuthorityUser(authorityUser);
        }
    }
};
// #endif

/**
 * 登录前清除用户缓存
 */
const clearAuthInfoBeforeLogin = (userId) => {
    let cachedUserId = uni.getStorageSync(CACHE_KEY_USER_ID);
    if (cachedUserId && cachedUserId !== userId) {
        for (let p in LOGIN_CACHE_KEYS) {
            try {
                uni.removeStorageSync(LOGIN_CACHE_KEYS[p]);
            } catch (error) {}
        }
        uni.removeStorageSync('userInfo');
    }
};

export default {
    loginByPassword,
    getUserOrgid() {
        let userInfo = uni.getStorageSync('userInfo');
        return userInfo.orgid;
    },

    getAuthUser() {
        return uni.getStorageSync(CACHE_KEY_USER_INFO) || {};
    },

    getAuthUserId() {
        return this.getAuthUser().id || '';
    },

    //获取用户部门ID
    getUserDepartmentId() {
        return this.getAuthUser().bmbh || '';
    }
};
