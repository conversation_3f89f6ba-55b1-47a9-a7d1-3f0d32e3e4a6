<template>
	<view class="datetime-picker">
		<view class="wrap">
			<view class="picker-body">
				<picker-view :value="value" @change="_onChange" >
					<picker-view-column>
						<view class="column-item" v-for="item in years" :key="item">
							{{ item + "年" }}
						</view>
					</picker-view-column>
					<picker-view-column>
						<view class="column-item" v-for="item in months" :key="item">
							{{ formatNum(item) + "月" }}
						</view>
					</picker-view-column>
					<picker-view-column v-if="type == 'Date'">
						<view class="column-item" v-for="item in days" :key="item">
							{{ formatNum(item) + "日" }}
						</view>
					</picker-view-column>
				</picker-view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "simple-date-picker",
		props: {
			startYear: {
				type: Number,
				default: 1970,
			},
			endYear: {
				type: Number,
				default: 2090,
			},
			startDate: {
				type: String,
				default: ''
			},
			type: { //时间类型，日期、月份
				type: String,
				default: 'Date'
			},
			date:{
				type: String,
				default: ''
			}
		},

		data() {
			return {
				open: false,
				years: [],
				months: [],
				days: [],
				hours: [],
				minutes: [],
				currentDate: new Date(),
				year: "",
				month: "",
				day: "",
				hour: "",
				minute: "",
				value: [0, 0, 0, 0, 0],
			};
		},

		mounted() {
			this.init();
		},

		watch: {
			month() {
				this.initDays();
			},
			type(val){
				// if(val == 'Date'){
				// 	this._onSubmit();
				// }
			},
			date: {
				immediate: true,
				handler(newVal, oldVal) {
					this.currentDate = newVal ? new Date(newVal) : new Date();
				}
			}
		},

		methods: {
			init() {
				this.initYears();
				this.initMonths();
				this.initDays();
				this.initHours();
				this.initMinutes();
				this.setSelectValue();
			},

			initYears() {
				const years = [];
				for (let year = this.startYear; year <= this.endYear; year++) {
					years.push(year);
					if (this.currentDate.getFullYear() === year) {
						this.$set(this.value, 0, year - this.startYear);
					}
				}
				this.years = years;
			},

			initMonths() {
				const months = [];
				for (let month = 1; month <= 12; month++) {
					months.push(month);
					if (this.currentDate.getMonth() + 1 === month) {
						this.$set(this.value, 1, month - 1);
					}
				}
				this.months = months;
			},

			initDays() {
				const value = this.value;
				const selectedYear = this.years[value[0]];
				const selectedMonth = this.months[value[1]];
				const days = [];
				const totalDays = new Date(selectedYear, selectedMonth, 0).getDate();
				for (let day = 1; day <= totalDays; day++) {
					days.push(day);
					if (this.currentDate.getDate() === day) {
						this.$set(value, 2, day - 1);
					}
				}
				this.days = days;
			},

			initHours() {
				const hours = [];
				for (let hour = 0; hour <= 23; hour++) {
					hours.push(hour);
					if (this.currentDate.getHours() === hour) {
						this.$set(this.value, 3, hour);
					}
				}
				this.hours = hours;
			},

			initMinutes() {
				const minutes = [];
				for (let minute = 0; minute < 60; minute++) {
					minutes.push(minute);
					if (this.currentDate.getMinutes() === minute) {
						this.$set(this.value, 4, minute);
					}
				}
				this.minutes = minutes;
			},
			renderData(date){
				date = new Date(date.replace('-', '/').replace('-', '/'))
				this.currentDate = date;
				this.value = [date.getFullYear() - this.startYear,date.getMonth() ,date.getDate() - 1,0,0]
			},
			show() {
				this.open = true;
			},

			hide() {
				this.open = false;
			},

			_onChange(e) {
				if(this.startDate){
					const v = e.detail.value;
					
					const year = this.years[v[0]];
					const month = this.months[v[1]];
					const day = this.days[v[2]];
					if(!this.dateCompare(new Date(this.startDate.replace('-', '/').replace('-', '/')), new Date(year, month - 1, day))){
						uni.showToast({
							title: "结束时间不能早于开始时间",
							icon: 'none'
						})
						this.value = [...this.value]
						return;
					}
				}
				
				this.value = e.detail.value;
				this.setSelectValue();

			},
			/**
			 * 比较日期大小
			 */
			dateCompare(startDate, endDate) {
				// // 计算截止时间
				// startDate = new Date(startDate.replace('-', '/').replace('-', '/'))
				// // 计算详细项的截止时间
				// endDate = new Date(endDate.replace('-', '/').replace('-', '/'))
				if (startDate <= endDate) {
					return true
				} else {
					return false
				}
			},
			setSelectValue() {
				const v = this.value;

				this.year = this.years[v[0]];
				this.month = this.months[v[1]];
				this.day = this.days[v[2]];
				this._onSubmit();
			},

			_onSubmit() {
				const {
					year,
					month,
					day,
					hour,
					minute,
					formatNum
				} = this;
				// const result = {
				//   year: formatNum(year),
				//   month: formatNum(month),
				//   day: formatNum(day),
				//   hour: formatNum(hour),
				//   minute: formatNum(minute),
				// };
				const result = new Date(year, month - 1, day);
				this.$emit("change", result);
			},

			formatNum(num) {
				return num < 10 ? "0" + num : num + "";
			},
		},
	};
</script>

<style lang="scss">
	$transition: all 0.3s ease;
	$primary: #488ee9;

	.datetime-picker {
		position: relative;
		width: 100%;
		z-index: 999;

		picker-view {
			height: 100%;
		}

		.mask {
			position: relative;
			z-index: 1000;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			background-color: rgba(0, 0, 0, 0.6);
			visibility: hidden;
			opacity: 0;
			transition: $transition;

			&.show {
				visibility: visible;
				opacity: 1;
			}
		}

		.wrap {
			z-index: 1001;
			position: relative;
			bottom: 0;
			left: 0;
			width: 100%;
			transition: $transition;

			// transform: translateY(100%);
			&.show {
				transform: translateY(0);
			}
		}

		.picker-header {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			padding: 8px 8px;
			background-color: darken(#fff, 2%);
			background-color: #fff;
		}

		.picker-body {
			width: 100%;
			height: 420rpx;
			overflow: hidden;
			background-color: #fff;
		}

		.column-item {
			text-overflow: ellipsis;
			white-space: nowrap;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.btn-picker {
			position: relative;
			display: inline-block;
			padding-left: 10px;
			padding-right: 10px;
			box-sizing: border-box;
			text-align: center;
			text-decoration: none;
			line-height: 2;
			-webkit-tap-highlight-color: transparent;
			overflow: hidden;
			background-color: #eee;
			font-size: 14px;
			// border-radius: 2px;
			color: #000;
			cursor: pointer;
		}

		.btn-picker.submit {
			background-color: $primary;
			color: #fff;
		}
	}
</style>
