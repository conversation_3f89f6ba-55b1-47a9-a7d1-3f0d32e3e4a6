<template>
	<body>
		<div class="zx-page">
			<header class="header">
				<i class="pd-backbtn" @click="back()"></i>
				<h1 class="title">{{ sbmc }}</h1>
			</header>
			<section class="main">
				<div class="inner" style='overflow: unset;'>
					<div class="gap"></div>
					<div class="gap"></div>
					<ul class="zy-data1">
						<li>
							<p class="p1">所属生产线：</p>
							<p class="p2">{{scxmc}}</p>
						</li>
						<li>
							<p class="p1">智能设备ID：</p>
							<p class="p2"><span  style='display: block;' >{{equipmentInfo.IMEI||'--'}}</span></p>
						</li>
						<li>
							<p class="p1">设备用途:</p>
							<p class="p2">{{equipmentInfo.SBLX||'--'}}</p>
						</li>
						<li>
							<p class="p1">污染物：</p>
							<p class="p2">{{equipmentInfo.ZYWR||'--'}}</p>
						</li>
						<li>
							<p class="p1">安装时间：</p>
							<p class="p2">{{equipmentInfo.AZSJ&&equipmentInfo.AZSJ.slice(0,16)||'--'}}</p>
						</li>
					</ul>
					<div class="gap"></div>
					<div class="gap"></div>
					<div class="zy-data2">
						<div class="box">
							<p class="zy-til1 ic1">现场照片</p>
							<u-upload  @on-preview='clickPreview' :fileList="fileList" :deletable="false" :maxCount="0" :previewFullImage="true" :show-progress="false"></u-upload>
							<u-empty mode="data" text='暂无现场照片' v-if='!fileList.length'></u-empty>
						
							<div class="gap"></div>
						</div>
						<div class="box"><shakeTendencyChart :imei="imei" :sbid="sbid" :sbmc="sbmc" :markLine="equipmentInfo.QTYZ"></shakeTendencyChart></div>
					</div>
				</div>
			</section>
		</div>
		
	</body>
</template>

<script>
import { getSbxq, getZdsj } from '../../api/iot/realtime.js';
import { DOWNLOAD_URLZDY } from '@/common/config.js';
import shakeTendencyChart from './shakeTendencyChart.vue';
export default {
	components: { shakeTendencyChart },
	data() {
		return {
			sbid: '',
			sbmc: '',
			scxmc:'',
			equipmentInfo: {},
			imgList: [],
			fileList: [],
			markLine:'',
			pre:false
		};
	},
	mounted() {},
	onLoad(options) {
		this.sbid = options.SBID;
		this.imei = options.IMEI;
		this.sbmc = options.SBMC;
		this.scxmc = options.SCXMC
		this.initEquipmentInfo();
	},
	onBackPress(options) {
		// 退出改页面时解除横屏
		// #ifdef APP-PLUS
		plus.screen.unlockOrientation();
		// #endif
	},
	methods: {
		// 初始化设备信息
		initEquipmentInfo() {
			getSbxq({ SBID: this.sbid }).then(res => {
				this.fileList = [];
				// 企业信息
				this.equipmentInfo = res.data.sbjbxx;
				// 处理附件地址
				let imgList = res.data.wjList;
				for (let i in imgList) {
					this.fileList.push({ url: `${DOWNLOAD_URLZDY + imgList[i].WJID}` });
				}
			});
		},
		clickPreview(url, lists, name){
		
		},
		back() {
			uni.navigateBack({
				delta: 1
			});
		}
	}
};
</script>
<style scoped>

@keyframes move {
	0% {
		transform: translate(0, 0);
	}
	10% {
		transform: translate(0, 0);
	}
	100% {
		transform: translate(-100%, 0);
	}
}
.trans {
	white-space: nowrap;
	animation: move infinite 10s linear;
}

</style>
