<!-- @format -->

<template>
    <div>
        <div class="item">
            <p class="label" :class="{ star: options.type === 'add' }">
                新IMEI号
            </p>
            <div class="rp">
                <input
                    type="text"
                    :disabled="isDisabled"
                    v-model="info.imei"
                    class="zy-input1"
                    placeholder="请扫描或手动输入后6位数"
                    style="font-size: 31rpx"
                    @input="search(info.imei)"
                />
                <image
                    v-show="!isDisabled"
                    src="@/static/app/images/sysic.png"
                    class="pd-sysbtn"
                    @click="scanCode()"
                />
            </div>
        </div>
        <u-select
            value-name="IMEI"
            label-name="IMEI"
            mode="single-column"
            :list="imeiList"
            v-model="imeiShow"
            @confirm="selectimei"
        ></u-select>
    </div>
</template>

<script>
import { getIMEIData } from '@/api/iot/equipmentMaintenance.js';
import { znsb, getValidate } from '@/api/iot/enterprise.js';
export default {
    props: {
        options: {
            type: Object,
            default: () => {}
        },
        isDisabled: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            imeiShow: false,
            imeiList: [],
            info: {
                imei: ''
            },
            rules: {
                imei: {
                    required: true,
                    message: '请扫描获取IMEI'
                }
            }
        };
    },
    watch: {},
    onHide() {},
    created() {},
    mounted() {},
    onBackPress() {},
    methods: {
        //更新表单
        updateInfo(newInfo) {
            this.$set(this.info, 'imei', newInfo['new_imei'] || '-');
        },
        //选择IMEI设备
        selectimei(v) {
            this.validImei(v);
        },
        // 校验imei码是否已经被绑定，如果已绑定，提示该imei号已绑定
        //如果未绑定，说明设备未被安装，可以使用
        validImei(v) {
            // 先校验选择的码是不是已经被绑定
            getValidate({
                IMEI: v[0].value
            }).then((res) => {
                // 选择的码未被绑定
                if (res.data.length === 0) {
                    this.setNewIMEI(v);
                }
                // 码已经被绑定
                else if (res.data && res.data.length > 0) {
                    uni.showModal({
                        title: '该IMEI已经绑定产治污设备',
                        showCancel: false
                    });
                    this.info.imei = '';
                }
            });
        },
        //未绑定的imei号放入表单里
        setNewIMEI(v) {
            this.info.imei = v[0].value;
            this.$emit('updateNewIMEI', v[0].value);
        },
        //搜索
        search(imei) {
            if (imei.length < 5) {
                if (this.timer) {
                    clearTimeout(this.timer);
                }
                this.timer = setTimeout(() => {
                    uni.showToast({
                        title: '请先输入5位以上的IMEI号',
                        icon: 'none'
                    });
                }, 1000);
                return;
            }
            if (imei.length >= 5) {
                if (this.timer) {
                    clearTimeout(this.timer);
                }
                this.timer = setTimeout(() => {
                    this.getImei(imei);
                }, 1000);
            }
        },

        // 查询完整imei码
        getImei(imei) {
            znsb({
                IMEI: imei
                // IMEI: '513710'
                // IMEI: '34040'
            }).then((res) => {
                if (res?.data?.length) {
                    uni.hideKeyboard(); //隐藏软键盘
                    this.imeiList = res.data;
                    this.imeiShow = true;
                    this.isGetIMEI = true;
                } else {
                    uni.showToast({
                        title: '未匹配到IMEI号，请检查输入！',
                        icon: 'none'
                    });
                    this.isGetIMEI = false;
                }
            });
        },
        //扫码
        scanCode() {
            uni.scanCode({
                scanType: ['qrCode'],
                success: (res) => {
                    this.fromScan = true;
                    let imei = res.result.split(';')[0];
                    if (imei != '') {
                        this.search(imei);
                    }
                },
                fail: (err) => {
                    uni.showToast({
                        title: '未识别到二维码！',
                        icon: 'none'
                    });
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.pd-sysbtn {
    margin-left: 18rpx;
}
</style>
