<template>
	<bottom-sheet 
		ref="sheet"
		operate
		@confirm="onClickConfirm">
		<cascade-picker
			ref="districtCascade"
			displayField="name"
			:column="level"
			:value="value"
			@change="onValueChange"
			@mounted="onPickerMounted"
		/>
	</bottom-sheet>
</template>

<script>
	import bottomSheet from '@/pages/component/bottom-sheet.vue';
	import cascadePicker from './cascade-picker.vue';
	
	import districtService from '@/api/district-service.js'
	
	let _self = this;
	
	export default {
		name: 'DistrictCascadePicker',
		components: {
			bottomSheet, cascadePicker
		},
		
		props: {
			level: {
				type: Number,
				default: 3
			}
		},
		
		data() {
			return {
				value: [],
				topLevelDistricts: null,
				columns: null
			}
		},
		
		mounted() {
			_self = this;
			//提前准备第一级数据
			let chain = new Promise((resolve, reject) => {
				resolve();
			});
			return chain.then(districtService.getRootDistrict)
				.then(districtService.getChildDistricts)
				.then(children => {
					_self.topLevelDistricts = children;
				})
		},
		
		methods: {
			
			show() {
				this.$refs['sheet'].show();
			},
			
			dismiss() {
				this.$refs['sheet'].dismiss();
			},
			
			setLoader() {
				let cascade = null;
				// #ifdef MP-ALIPAY
				cascade = this.$refs['sheet'].$refs['districtCascade'];
				// #endif
				
				// #ifndef MP-ALIPAY
				cascade = this.$refs['districtCascade'];
				// #endif
				
				cascade.setLoader(parent => {
					if(parent) {
						return districtService.getChildDistricts(parent);
					} else {
						return new Promise((resolve, reject) => {
							resolve(_self.topLevelDistricts);
						});
					}
				});
			},
			
			onClickConfirm() {
				this.$emit('confirm', this.value, this.columns);
			},
			
			onValueChange(indexs, columns) {
				this.value = indexs;
				this.columns = columns;
			},
			
			/**
			 * 需要在窗口实例化之后才能设置数据加载函数，否则拿不到相应的实例
			 */
			onPickerMounted() {
				this.setLoader()
			}
		}
	}
</script>