<!-- @format -->

<template>
    <div
        class="pd-pg1a"
        style="background: #f1f1f1; height: 100%; overflow-y: auto"
    >
        <header class="header">
            <div class="pd-row aic">
                <input
                    type="text"
                    class="pd-inpsrh"
                    placeholder=" 请输入企业名称或IMEI号查询"
                    v-model="searchText"
                    @input="search"
                />
                <image
                    src="../../static/app/images/addic1.png"
                    class="pd-add1"
                    @click="add"
                />
            </div>
        </header>
        <ul class="pd-ultbs2">
            <li
                :class="dataType == '0' ? 'on' : ''"
                @click="change({ value: '0' })"
            >
                未安装<sub>（{{ count.WAZ_COU || '0' }}家）</sub>
            </li>
            <li
                :class="dataType == '1' ? 'on' : ''"
                @click="change({ value: '1' })"
            >
                已安装<sub>（{{ count.AZ_COU || '0' }}家）</sub>
            </li>
            <li
                :class="dataType == '2' ? 'on' : ''"
                @click="change({ value: '2' })"
            >
                信号弱<sub>（{{ count.XHR_COU || '0' }}家）</sub>
            </li>
        </ul>
        <div class="main">
            <scroll-view
			    ref="scrollView"
                style="height:100%"
                v-show="list.length"
				:scroll-top="scrollTop" 
                scroll-y
				@scroll="scroll"
                @scrolltolower="getMore"
            >

			<!-- <u-loading-icon text="加载中" textSize="18"></u-loading-icon> -->
                <div v-for="(item, index) in list" :key="index">
                    <div class="gap"></div>
                    <dl class="pd-dlbx1">
                        <dt @click="toEnterpriseInfo(item)">
                            <strong>{{ item.WRYMC }}</strong>
                            <image
                                src="../../static/app/images/arwrt1.png"
                                class="pd-arw1"
                            />
                        </dt>
                        <dd>
                            <p class="p1" @click="opLocaltion(item)">
                                污染源地址：<i style="color: #4874ff">{{
                                    item.DWDZ
                                }}</i>
                            </p>
                            <p class="p2">环保联系人：{{ item.HBLXR }}</p>
                            <p class="p3">
                                环保联系人电话：<i
                                    style="color: #4874ff"
                                    @click="makeCall(item.HBLXRDH)"
                                    >{{ item.HBLXRDH }}</i
                                >
                            </p>
                            <p class="p4" v-if="dataType == '1'">
                                生产设备：{{ item.SCSB_COU || '0' }}
                            </p>
                            <p class="p4" v-if="dataType == '1'">
                                治污设备：{{ item.ZWSB_COU || '0' }}
                            </p>
                            <p class="p4" v-if="dataType == '2'">
                                信号弱设备数：{{ item.XHR_COUNT || '0' }}
                            </p>
                        </dd>
                    </dl>
                </div>
            </scroll-view>
            <u-empty
                class="nodatabox"
                mode="data"
                v-show="!list.length"
            ></u-empty>
        </div>
    </div>
</template>

<script>
/** @format */
import appWatch from '@/utils/app-watch.js';
import { getQyCount, getQyxx, updataQyxx } from '@/api/iot/enterprise.js';
import { qyxxlist } from '@/api/iot/weakSignal.js';
import gcoord from 'gcoord';

export default {
    data() {
        return {
            triggered: false,
            isOpenRefresh: true, // 是否开启下拉
            dataTypeArr: [
                {
                    name: '全部',
                    value: ''
                },
                {
                    name: '已安装',
                    value: 'Y'
                },
                {
                    name: '未安装',
                    value: 'N'
                }
            ],
			scrollTop:0,
            searchText: '',
            pageNum: 1,
            pageSize: 20,
            dataType: '0',
            total: 0,
            // 企业列表
            list: [],
            count: {},
            enterpriseInfo: {},
            timer: null,
            screenHeight: 0,
            weakList: [],
			topItem:'top',
			old: {
								scrollTop: 0
							}
        };
    },
    onShow() {
		console.log('触发ohshow');
		// 获取缓存中的企业信息
		this.enterpriseInfo = uni.getStorageSync('userInfo');
		this.list = [];
		this.total = 0;
		 this.pageNum = 1;
		this.initList();
		this.onpageTime();
    },
    created() {},
    mounted() {
		
    },
    watch: {
        searchText: {
            handler: function (val) {
                this.searchText = val;
            }
        }
    },

    methods: {
		scroll: function(e) {
			// console.log(e)
			this.old.scrollTop = e.detail.scrollTop
		},
		goTop: function(e) {
			console.log('goTop')
			// 解决view层不同步的问题
			this.scrollTop = this.old.scrollTop
			this.$nextTick(function() {
				this.scrollTop = 0
			});
		},
		// handleBackTop(){
		//     this.topItem = 'top'
		// 	console.log('topItem',this.topItem)
		// },
		// setScrollTop(){
		// 	console.log('setScrollTop')
		// 	this.$nextTick(()=>{
		// 		this.scrollTop = 0;
		// 	})
			
		// },
        //监听当前页面停留时间
        onpageTime() {
            let time = 0;
            let timer = null;
            timer = setInterval(function () {
                time += 1;
                if (time >= 20) {
                    clearInterval(timer);
                    let params = {
                        jwtToken: uni.getStorageSync('token')
                    };
                }
            }, 1000);
        },
        //重置查询条件
        resetQueryCondition() {
            this.list = [];
            this.pageNum = 1;
            this.isLastPage = false;
            this.total = 0;
        },
        // 获取企业信息列表
        async initList() {
			
            let { sjqx } = this.enterpriseInfo;
            getQyCount({
                ORGID: sjqx,
                ALLSEARCH: this.searchText
            }).then((res) => {
                this.count = res.data[0];
            });

            const {
                data: { list, isLastPage, total, pageNum }
            } = await getQyxx({
                ORGID: sjqx,
                pageNum: this.pageNum,
                pageSize: this.pageSize,
                ALLSEARCH: this.searchText,
                ZT: this.dataType
            });
            this.list = [...this.list, ...list];
            this.total = total;
            this.pageNum = pageNum;
            this.isLastPage = isLastPage;
				//如果是第一页面，回到顶部
			if(pageNum == 1){
				this.goTop();
			}
			
        },
        getMore() {
            if (this.isLastPage) {
                uni.showToast({
                    title: '没有更多了',
                    icon: 'none'
                });
            } else {
                this.pageNum++;
                this.initList();
            }
        },
        //查询信号弱列表
        getWeakList() {
            let { sjqx } = this.enterpriseInfo;
            qyxxlist({
                ORGID: sjqx,
                ALLSEARCH: this.searchText
            }).then((res) => {
                if (res.status === '000') {
                    this.list = res.data;
                    console.log('weakList', this.weakList);
                }
            });
        },
        // tab切换
        change(option) {
            this.dataType = option.value;
            this.resetQueryCondition();
            if (this.dataType === '2') {
                appWatch.getClickWatchAPP({
                    url: 'pages/enterprise/Index',
                    clickModule: '信号弱'
                });
                this.getWeakList();
            } else {
                this.initList();
            }
        },
        // 信息设置
        setInformation(option) {
            uni.navigateTo({
                url:
                    '/pages/enterprise/editEnterpriseInfo?type=edit&info=' +
                    encodeURIComponent(JSON.stringify(option))
            });
        },
        // 搜索
        search() {
            if (this.timer) {
                clearTimeout(this.timer);
            }
            this.timer = setTimeout(() => {
                this.resetQueryCondition();
                this.initList();
            }, 400);
        },
        // 唤起系统导航app进行导航
        opLocaltion(item) {
            //转换为高德坐标第item
            console.log('转换前', item);
            // var result = gcoord.transform(
            //     [item.JD, item.WD], // 经纬度坐标
            //     gcoord.GCJ02, // 当前坐标系
            //     gcoord.WGS84 // 目标坐标系
            // );
            // console.log("转换后", result)
            // uni.getLocation({
            // 	success: res => {
            uni.openLocation({
                type: 'gcj02',
                latitude: parseFloat(item.WD),
                longitude: parseFloat(item.JD),
                name: item.WRYMC,
                scale: 8
            });
            // uni.openLocation({
            //     latitude: parseFloat(result[1]),
            //     longitude: parseFloat(result[0]),
            //     name: item.WRYMC,
            //     scale: 8
            // });
            // 	}
            // });
        },
        makeCall(phone) {
            uni.makePhoneCall({
                phoneNumber: phone //仅为示例
            });
        },
        // evt （radio 点击事件 item 点击的企业信息 ）
        radioChange(evt, item) {
            let { name } = this.enterpriseInfo;
            updataQyxx({
                WRYBH: item.WRYBH,
                YYCJ: evt.target.value,
                XGR: name
            });
        },
        // 到企业详情页
        toEnterpriseInfo(item) {
            let url =
                '/pages/enterprise/enterpriseInfo?info=' +
                encodeURIComponent(JSON.stringify(item));
            if (this.dataType == '2') {
                url =
                    '/pages/enterprise/enterpriseInfo?type=weakSignal&info=' +
                    encodeURIComponent(JSON.stringify(item));
            }
            uni.navigateTo({
                url
            });
        },
        //添加污染源
        add() {
            uni.navigateTo({
                url: '/pages/enterprise/editEnterpriseInfo?type=add'
            });
        }
    }
};
</script>

<style scoped>
/** @format */

html {
    -webkit-tap-highlight-color: transparent;
    height: 100%;
}
body {
    -webkit-backface-visibility: hidden;
    height: 100%;
}

.main {
    overflow: scroll;
    height: calc(100% - 230rpx);
	padding: 0 20rpx;
}
.pd-pg1a .inner {
    height: auto;
    padding: 0rpx 20rpx 20rpx 20rpx;
}
.pd-ultbs2 {
    width: 100%;
    background: #fff;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: space-evenly;
    -webkit-justify-content: space-evenly;
    justify-content: space-evenly;
    padding-top: 140rpx;
}
.pd-ultbs2 li {
    font-size: 28.985475rpx;
    color: #666;
    line-height: 78.502425rpx;
    width: 50%;
    text-align: center;
}
.pd-ultbs2 li.on {
    color: #4874ff;
    background: url(~@/static/app/images/botbar.png) no-repeat center bottom;
    background-size: 84.54105rpx;
    font-weight: bold;
}
.pd-ultbs2 li sub {
    font-size: 22.94685rpx;
}
.pd-ultbs2 li.on sub,
.pd-ultbs2 li.on p {
    font-weight: bold;
}
.pd-pg1a .pd-ultbs2 {
    top: 0;
}

.header {
    z-index: 1;
}
.pd-dlbx1 dd p.p1 {
    background-position: left 4px;
}
.radio {
    transform: scale(0.7);
    font-size: 26rpx;
}

.pd-add1 {
    height: 45rpx;
}
.max-height-scroll {
    max-height: 20rpx;
}
.nodatabox ::v-deep .u-iconfont {
    font-size: 124rpx !important;
}

.nodatabox ::v-deep .u-icon__label {
    font-size: 28rpx !important;
}
</style>
