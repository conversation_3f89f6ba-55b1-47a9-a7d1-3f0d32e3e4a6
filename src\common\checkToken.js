import dayjs from '@/components/dayjs/dayjs.min.js';
export default function checkToken() {
	let startTime = uni.getStorageSync('token_time')
	let endTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
	let diff = dayjs(endTime).diff(dayjs(startTime), 'minutes')
	const loginStatus = uni.getStorageSync('IS_LOGIN')
	// console.log('diff',diff,loginStatus)
	//token大于27分钟就需要重新请求
	return diff >  27 && loginStatus
}
