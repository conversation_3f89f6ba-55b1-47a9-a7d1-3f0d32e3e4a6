<template>
	<div class="warp">
		<div class="img-box"><image src="../../../static/images/success.png" mode=""></image></div>
		<div class="name-box">{{ lastTimeCwsbName }}{{isXg?'修改':'添加'}}成功</div>

		<div class="btn-box">
			<div class="btn qd" @click="toIndex">确定</div>
			<div class="gap"></div>
			<div class="gap"></div>
			<div class="btn copyAdd" @click="back" v-if='!isXg'>复制新增</div>
		</div>

		<div class="more-box">
			<div class="line-box">
				<div class="line"></div>
				<div class="more">更多操作</div>
				<div class="line"></div>
			</div>
		</div>

		<div class="add-box">
			<div @click="toAddZwEquipment">添加生产线 >></div>
			<div @click="toAddZwEquipment">添加治污设备 >></div>
		</div>
	</div>
</template>

<script>
export default {
	data() {
		return {
			info: {},
			lastTimeCwsbName: '',
			isXg:false
		};
	},
	onLoad(option) {
		console.log(option);
		this.lastTimeCwsbName = option.cwsbName;
		this.info = JSON.parse(decodeURIComponent(option.info));
		this.isXg = option.xg=='1'?true:false
	},
	methods: {
		//
		toAddProductionLine() {
			uni.navigateTo({
				url: './pages/enterprise/scx/addProductionLine?info=' + encodeURIComponent(JSON.stringify(this.info))
			});
		},
         //
		toAddZwEquipment() {
			uni.navigateTo({
				url: './pages/enterprise/zwsb/addZwEquipment?info=' + encodeURIComponent(JSON.stringify(this.info))
			});
		},
		//
		toIndex() {
			uni.reLaunch({
				url: '../Index'
			});
		},

		back() {
			uni.navigateBack({
				delta: 1
			});
		}
	}
};
</script>

<style scoped lang="scss">
.warp {
	padding: 80rpx 100rpx;
	.add-box {
		display: flex;
		align-items: center;
		color: #41a3ff;
		justify-content: space-between;
		font-size: 16px;
	}
	.img-box {
		width: 100%;
		display: flex;
		justify-content: center;
		image {
			width: 128rpx;
			height: 128rpx;
		}
	}
	.name-box {
		font-size: 40rpx;
		text-align: center;
		font-weight: 600;
		padding: 100rpx 40rpx;
	}
	.btn-box {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
		.btn {
			height: 80rpx;
			border-radius: 5px;
			border: 1px solid #5290ff;
			color: #fff;
			line-height: 80rpx;
			text-align: center;
			width: 80%;
		}
		.qd {
			background-color: #5290ff;
		}
		.copyAdd {
			color: #5290ff;
		}
	}
	.more-box {
		position: relative;
		height: 100rpx;
		margin-top: 100rpx;
		.line-box {
			height: 100%;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.line {
				height: 2px;
				background-color: #efefef;
				width: 30%;
				top: 50%;
				margin-top: -1px;
			}
			.more {
				color: #ccc;
			}
		}
	}
}
</style>
