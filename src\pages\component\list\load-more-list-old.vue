<!-- @format -->

<template>
    <view class="load-more-list">
        <view :style="totalTipStyle" v-if="showDetailTotal">
            共<text style="color: #fba72e; margin-right: 8px"
                >{{ total }}条</text
            >
            已加载<text style="color: #fba72e">{{ list.length }}条</text>
        </view>

        <scroll-view
            :style="scrollListStyle"
            :scroll-y="true"
            @scrolltolower="onLoadMore"
        >
            <component
                :showDetailTotal="showDetailTotal"
                :is="itemVue"
                v-for="(item, index) in list"
                :key="index"
                :data="item"
                :style="itemStyle(index)"
            />
        </scroll-view>
        <uni-load-more
            v-show="showStatus"
            :style="loadStatusStyle"
            :class="loadStatusClass"
            :status="status"
        />
    </view>
</template>

<script>
import loadMore from './load-more.js';
import uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue';

export default {
    mixins: [loadMore],
    components: {
        uniLoadMore
    },
    props: {
        //父容器的内边距
        parentPadding: {
            type: Number,
            default: 8
        },

        //父容器需要修正的高度
        reviseHeight: {
            type: Number,
            default: 0
        },

        //是否在组件挂载完毕后自动加载数据
        autoload: {
            type: Boolean,
            default: true
        }
    },

    data() {
        return {
            itemVue: 'text',
            total: 0,
            pageIndex: 0,
            pageSize: 10,
            hasMore: false,
            status: 'noMore',
            showStatus: false,
            loadStatusClass: '',
            list: [],
            parentHeight: 500,
            totalHeight: 24,
            isLoading: false
        };
    },

    computed: {
        totalTipStyle: function () {
            return {
                'line-height': `${this.totalHeight}px`,
                'font-size': '14px',
                color: '#999'
            };
        },

        scrollListStyle: function () {
            let listHeight = 0;

            if (this.showDetailTotal) {
                listHeight =
                    this.parentReviseHeight -
                    this.parentPadding * 2 -
                    this.totalHeight;
            } else {
                listHeight = this.parentReviseHeight;
            }
            return {
                width: '100%',
                height: `${listHeight}px`
            };
        },

        loadStatusStyle: function () {
            return {
                position: 'fixed',
                bottom: '0px',
                left: `0px`,
                width: '100%',
                'background-color': '#fff'
            };
        },

        parentReviseHeight: function () {
            return this.parentHeight - this.reviseHeight;
        }
    },

    mounted() {
        //动态计算滚动列表的高度
        let parentDom = this.$el.parentNode || this.$el.parentElement;
        this.parentHeight = parentDom.clientHeight;

        if (this.autoload) {
            this.status = 'loading';
            this.showStatus = true;
            this.loadMore(this.pageIndex + 1);
        }

        //暴露一个刷新事件给客户端（通常是父组件）
        this.$nextTick(function () {
            this.$on('refresh', () => {
                this.refresh();
            });
        });
    },

    methods: {
        onLoadFinish() {
            this.isLoading = false;
            this.hasMore = this.list.length < this.total;
            this.status = this.hasMore ? 'more' : 'noMore';
            //如果没有更多，显示提示1.5s后再关闭提示
            let delay = this.status === 'more' ? 0 : 1500;
            setTimeout(() => {
                this.loadStatusClass = 'power-load-more-status';
                this.showStatus = false;
            }, delay);
        },

        appendMore(more) {
            if (Array.isArray(more) && more.length > 0) {
                let index = this.list.length;
                more.forEach((item) => {
                    this.$set(this.list, index, item);
                    index++;
                });
                this.pageIndex++;
            } else {
                console.log(`加载更多数据类型不是Array或数据长度为0`);
            }
        },

        onLoadMore() {
            if (this.isLoading) {
                return;
            }

            if (this.hasMore) {
                this.isLoading = true;
                this.status = 'loading';
                this.loadStatusClass = '';
                this.showStatus = true;
                this.loadMore(this.pageIndex + 1);
            }
        },

        refresh() {
            this.total = 0;
            this.status = 'loading';
            this.pageIndex = 0;
            this.hasMore = false;
            this.showStatus = true;
            this.list = [];
            this.loadMore(this.pageIndex + 1);
        },

        itemStyle(index) {
            if (index === 0) {
                return;
            }
            return {
                'margin-top': '10px'
            };
        }
    }
};
</script>

<style scoped>
.load-more-list {
    height: 100%;
}

.power-load-more-status {
    animation: statusFadeOut 0.5 ease-out;
}

@keyframes statusFadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}
</style>
