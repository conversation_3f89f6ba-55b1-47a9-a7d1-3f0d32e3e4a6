<template>
    <div class="warn-anysise">
		<div class="pictit">
			<div class="left-tit">
				{{info.SCXMC == ''?info.ZWXMC:info.SCXMC}}
			</div>
			<div class="qiye-tabs3">
				<span :class="curPic==item.value?'cur':''" 
				 @click="changePic(item)" v-for="(item, index) in picTabArr" :key="index">{{item.name}}</span>
			</div>
		</div>
		 <StatePic ref="statePic"  v-show="curPic == 'state'" :warning="warning"></StatePic>
		 <TrendPic ref="trendPic"  v-show="curPic == 'trend'" :dateStr="dateStr" :currentscx="currentscx"></TrendPic>
		 
	</div>
	
</template>

<script>
	
	import { DOWNLOAD_URLZDY } from '@/common/config.js';
	import {monitorCxzt,monitorPicture} from '@/api/warning.js'
	import TrendPic from './TrendPic'
	import StatePic from './StatePic'
	export default {
		components:{
			StatePic,
			TrendPic
		},
		props:{
			warning:{
				type:Object,
				default:function(){}
			}
		},
		watch:{
			warning:{
				handler(newVal,oldVal){
					this.info = newVal;
					this.currentscx = newVal.SCXID;
					if(this.info){
						//ZW-治污，TC-停产，GZ-故障
						// if(this.info.YJLX == 'ZW' ||this.info.YJLX == 'TC'){
						// }else if(this.info.YJLX == 'GZ'){
						// }
					}
					
		     	},
				//immediate: true,
				deep:true
				
			}
			
		},
		data() {
			return {
				info:{},
				picTabArr:[
					{
						name:'状态图',
						value:'state'
					},
					{
						name:'趋势图',
						value:'trend'
					},
				],
				curPic:'state',
                currentscx:'',//生产线
				dateStr: this.$dayjs().format('YYYY-MM-DD'),
			
			};
		},
		mounted() {
		
		},
		methods: {
				//切换图表
				changePic(item){
					this.curPic = item.value;
					switch (item.value){
						case 'state':
					    this.$refs.statePic.getPollutionWarning();
						break;
						case 'trend':
						this.$nextTick(()=>{
							this.$refs.trendPic.gettjt();
						})
						break;
						default:
						break;
					}
				},

		}
	};
</script>



<style scoped>
	.warn-anysise{
		height: 100%;
			overflow-y: auto;
	}
	.qiye-tabs3{
	    display: flex;
		padding: 10px;
		background: #fff;
	}
	.qiye-tabs3 span{
	    font-size: 30rpx;
	    color: #666;
	    line-height: 10rpx;
	    padding: 10rpx;
	}
	.qiye-tabs3 span:first-child{
	    padding-left: 0;
	}
	.qiye-tabs3 span+span{
	    border-left: 4rpx solid #ddd;
	}
	.qiye-tabs3 span.cur{
	    color: #4874ff;
	}
	.pictit{
		display: flex;
		justify-content: space-between;
		background-color: #fff;
	}
	.left-tit{
		padding: 0 20rpx;
		font-weight: bold;
		line-height: 2;
	}

</style>