<template>
	<div>
		<dl class="pd-dlbx1">
			<dt>上报数据</dt>
			<dd>展示最新上传的三组状态数据</dd>
		</dl>
		<div v-for="(item,index) in list" :key='index'>
			<div class="gap"></div>
			<ul class="pd-ulbx1" >
				<sup>栏目{{index+1}}</sup>
				<li><em>检测时间</em><i>{{item.JCSJ||'-'}}</i></li>
				<li><em>上报时间</em><i>{{item.SBSJ||'-'}}</i></li>
				<li><em>X振幅</em><i>{{item.X||'-'}}</i></li>
				<li><em>Y振幅</em><i>{{item.Y||'-'}}</i></li>
				<li><em>Z振幅</em><i>{{item.Z||'-'}}</i></li>
				<li><em>X频率</em><i>{{item.X_FREQ||'-'}}</i></li>
				<li><em>Y频率</em><i>{{item.Y_FREQ||'-'}}</i></li>
				<li><em>Z频率</em><i>{{item.Z_FREQ||'-'}}</i></li>
			</ul>
		</div>
		
	</div>
	
</template>

<script>
	import { getZxsj } from '@/api/iot/enterprise.js';
	export default{
		props:{
			info:{
				type:Object,
				default:()=>{
					return{}
				}
			},
			sbInfo:{
				type:Object,
				default:()=>{
					return{}
				}
			}
		},
		data(){
			return{
				pageSize:'',
				enterpriseInfo:{},
				list:[]
			}
		},
		mounted() {
			this.enterpriseInfo = uni.getStorageSync('userInfo');
			this.getZxsj()
		},
		methods:{
			getZxsj(){
				let {IMEI} = this.sbInfo
				getZxsj({IMEI:IMEI,pageSize:this.pageSize}).then(res=>{
					if (res.data_array && res.data_array.length > 0) {
						this.list = res.data_array;
					}
				})
			}
		}
	}
</script>

<style scoped>
	.pd-dlbx1 {
    background: #fff;
    border-radius: 9px;
    margin: 0 ;
    padding: 9px;
}
.pd-ulbx1{
	 margin: 0 ;
}
</style>
