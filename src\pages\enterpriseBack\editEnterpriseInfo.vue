<template>
	<body style="background: #f1f2f6;font-size: 24rpx;" class="warp">
		<header class="header">
			<i class="pd-backbtn" @click="back"></i>
			<h1 class="title">编辑企业信息</h1>
		</header>
		<section class="main">
			<div class="inner">
			<!-- 	<div class="zy-data1 rw-data1">
	                <div class="rwrow1 border-b">
	                    <span class="rw1">企业名称</span>
	                    <span class="des">{{model.WRYMC||'-'}}</span>
	                </div>
	
	                <div class="rwnamebox border-b" @click='toMap'>
	                    <div class="rw1">企业地址</div>
	                    <div class="jgbox">
	                        <p class="fdes addrico">{{model.DWDZ||'-'}}</p>
	                    </div>
	                </div>
	
	                <div class="rwrow1 border-b">
	                    <span class="rw1">环保联系人</span>
	                    <span class="des">{{model.HBLXR||'-'}}</span>
	                </div>
	
	                <div class="rwrow1 border-b">
	                    <span class="rw1">联系方式</span>
	                    <span class="des">{{model.HBLXRDH||'-'}}</span>
	                </div>
	
	                <div class="rwrow1">
	                    <span class="rw1">应用场景</span>
	                    <span class="arr1">{{model.YYCJ||'-'}}</span>
	                </div>
	            </div> -->
				<u-form :model="model" :rules="rules" ref="uForm" :errorType="errorType">
					<u-form-item required :label-position="labelPosition" label="企业名称" prop="WRYMC" :label-width="labelWidth">
						<u-input :border="border" placeholder="请填写企业名称" v-model="model.WRYMC" />
					</u-form-item>

					<u-form-item required label-position="top" label="企业地址" prop="DWDZ" :label-width="labelWidth">
						<u-input @click='toMap()' disabled  type="textarea" height="100" :border="border" placeholder="请填写企业地址" v-model="model.DWDZ" />
					</u-form-item>

					<u-form-item required :label-position="labelPosition" label="环保联系人" prop="HBLXR" :label-width="labelWidth">
						<u-input :border="border"   placeholder="请填写环保联系人" v-model="model.HBLXR" />
					</u-form-item>

					<u-form-item required :label-position="labelPosition" type='number' label="联系方式" prop="HBLXRDH" :label-width="labelWidth">
						<u-input :border="border" type='number' placeholder="请填写联系方式" v-model="model.HBLXRDH" />
					</u-form-item>

					<u-form-item :label-position="labelPosition" label="应用场景" required prop="YYCJ" :label-width="labelWidth">
						<u-input :border="border" type="select"  :select-open="yycjShow" v-model="model.YYCJ" placeholder="请选择应用场景" @click="yycjShow = true"></u-input>
					</u-form-item>
				</u-form>
				<div class="gap"></div>
				<div class="servebtn" @click='save'><button type="button" class="log-btn">保存</button></div>
			</div>
		</section>
		<u-select value-name="DM" label-name="DMNR" mode="single-column" :list="yycjList" v-model="yycjShow" @confirm="getYycj"></u-select>
	</body>
</template>

<script>
import enterprise_store from './enterprise.store.js';

import { getGgdmz } from '@/api/iot/ggdmz.js';
import { updateQyxx } from '@/api/iot/enterprise.js';
export default {
	watch: {
		JWD: {
			handler: function(v) {
				this.model.JD = v.JWD.longitude;
				this.model.WD = v.JWD.latitude;
				this.model.DWDZ = v.JWD.DZ;
			},
			deep: true //深度监听
		}
	},
	data() {
		return {
			enterpriseInfo:{},
			JWD: enterprise_store.state,
			border: false, //input 是否显示边框, 默认false
			model: {
				WYYBH:'',
				WRYMC:'',
				DWDZ:'',
				HBLXR:'',
				HBLXRDH:'',
				YYCJ:"",
				XGR:"",
				JD:'',
				WD:''
			},
			labelWidth: 200,
			labelPosition: 'left', //表单域提示文字的位置，left-左侧，top-上方
			errorType: ['border-bottom'], //错误的提示方式，数组形式, 可选值为：
			DWDZ:'',
			rules: {
				WRYMC: [
					{
						required: true,
						message: '填写企业名称',
						trigger: 'change'
					}
				],
				DWDZ: [
					{
						required: true,
						message: '填写企业地址',
						trigger: 'change'
					}
				],
				HBLXR: [
					{
						required: true,
						message: '填写联系人',
						trigger: 'change'
					}
				],
				HBLXRDH: [
					{
						required: true,
						message: '填写联系人电话',
						trigger: 'change'
					}]
				,
				YYCJ: [
					{
						required: true,
						message: '填写应用场景',
						trigger: 'change'
					}
				]
			},
			yycjShow:false,
			yycjList:[],
		};
	},
	onLoad(option) {
			this.enterpriseInfo = uni.getStorageSync('userInfo');
		this.model = JSON.parse(decodeURIComponent(option.info));
		this.getGgdmz()
	},
	methods: {
		getGgdmz(){
			getGgdmz({"DMJBH":"QYXX_YYCJ"}).then(res=>{
				if (res.data_array && res.data_array.length > 0) {
					this.yycjList = res.data_array;
				}
			})
		},
	
		getYycj(v){
			this.model.YYCJ = v[0].value;
		},
		save(){
			this.model.XGR = this.enterpriseInfo.name;
			this.$refs.uForm.validate(valid => {
				if(valid){
					updateQyxx(this.model).then(res=>{
						uni.showToast({
							title:'修改成功'
						})
						uni.setStorageSync('qyxxIsUpdate',true)
					})
				}
			})
		},
		toMap() {
			uni.navigateTo({
				url: './map'
			});
		},
		back() {
			uni.navigateBack({
				delta: 1
			});
		}
	}
};
</script>

<style scoped>
	/deep/ .u-form {
		font-size: 26rpx
	}
/deep/ .u-input__textarea {
	border-radius: 8rpx;
	height: 100rpx;
	
	padding-left: 40rpx;
	background: url(../../static/app/images/addr.png) no-repeat;
	background-color: rgb(243, 245, 249);
	background-size: 20rpx auto;
	background-position: 12rpx 16rpx;
}
/deep/ .uni-textarea-wrapper {
	
	height: 100% !important;
}
</style>
