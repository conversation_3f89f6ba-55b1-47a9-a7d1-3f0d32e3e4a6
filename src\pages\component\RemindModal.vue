<!-- @format -->
<!-- 说明提示弹窗 -->

<template>
    <div>
        <div class="mask" style="display: block"></div>
        <div class="yy0706-alert1" :style="{height:height}">
            <div class="hd">
                <h1 class="tit1">{{ title }}</h1>
                <image
                    src="~@/static/equipmentMaintenance/images/yy0706-cls.png"
                    alt=""
                    class="yy0706-cls"
                    @click="close"
                />
            </div>
            <div class="bd">
                <div>{{ content }}</div>
            </div>
			<slot></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: 'DataCollectionAppRemindModal',
    props: {
        title: {
            type: String,
            default: '提示说明'
        },
        content: {
            type: String,
            default: '说明的内容。'
        },
		height: {
		    type: String,
		    default: '340rpx'
		}
    },
    data() {
        return {
		};
    },

    mounted() {
		
	},
    methods: {
		close(){
			this.$emit('close')
		}
	}
};
</script>

<style lang="scss" scoped>
.yy0706-alert1 .bd {
    padding: 0rpx 20rpx 0 20rpx;
}
.yy0706-alert1 {
    height: 340rpx;
}
</style>
