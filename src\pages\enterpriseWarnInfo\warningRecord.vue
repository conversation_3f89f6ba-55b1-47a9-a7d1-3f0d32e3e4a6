<template>
	<body style="background-color:#f5f5f5;">
		<header class="header">
			<i class="pd-backbtn" @click="back()"></i>
			<h1 class="title">{{wrymc}}</h1>
		</header>

		<section class="main">
			<div class="inner">
				<div class="zy-qiehuan">
					<div class="cell">
						<p
							@click="
								showWarningType = !showWarningType;
								showTimeType = false;
							"
						>
							{{ currentWarningLabel }}
						</p>
						<ul v-if="showWarningType">
							<li
								v-for="(item, index) in warningType"
								style="margin-left: 20rpx;"
								:key="index"
								@click="getWarningType(item)"
								:class="item.value == currentWarningType ? 'cur' : ''"
							>
								{{ item.label }}
							</li>
						</ul>
					</div>

					<div class="cell">
						<p
							@click="
								showTimeType = !showTimeType;
								showWarningType = false;
							"
						>
							{{ currentTimeLabel }}
						</p>
						<ul v-if="showTimeType">
							<li
								v-for="(item, index) in TimeType"
								style="margin-left: 20rpx;"
								:key="index"
								@click="getTimeType(item)"
								:class="item.value == currentTimeType ? 'cur' : ''"
							>
								{{ item.label }}
							</li>
						</ul>
					</div>
				</div>
				<u-empty mode="data" v-if="nodata"></u-empty>
				<ul class="zy-data5">
					<li v-for="(item, index) in warningList" :key="index">
						<div class="zy-line jb ac">
							<p class="zy-til2">{{getContent(item.YJLX)}}</p>
							<div class="zy-clock">发生时间：{{ item.FSSJ }}</div>
						</div>
						<div class="box" @click="goToDetail(item)">
							<!-- <div class="zy-line jb ac">
								<p>生产设备【{{ item.SCSBMC }}】</p>
								<img src="../../static/app/images/type1.png" alt="" />
							</div>
							<div class="zy-line jb ac">
								<p>治污设备【{{ item.ZWSBMC }}】</p>
								<img src="../../static/app/images/type2.png" alt="" />
							</div> -->
							<p>{{strSlice(item.YJNR)}}</p>
							<!-- <p>{{ item.YJGZ }}。</p> -->
							<p>持续时间：{{ calcTime(item.CXSJ) }}</p>
						</div>
						<div class="gap"></div>
					</li>
				</ul>
			</div>
		</section>
	</body>
</template>

<script>
import { getYjjl,getCode } from '../../api/iot/realtime.js';
export default {
	data() {
		return {
			wrybh: '',
			wrymc:'',
			warningType: [{ label: '全部类型', value: '' }, { label: '停产预警', value: 'TC' }, { label: '治污预警', value: 'ZW' }],
			showWarningType: false,
			currentWarningType: '',
			currentWarningLabel: '全部类型',
			TimeType: [
				{ label: '今天', value: 'today' },
				{ label: '昨天', value: 'yesterday' },
				{ label: '近7天', value: 'seven' },
				{ label: '近1个月', value: 'month' },
				{ label: '全部记录', value: 'all' }
			],
			showTimeType: false,
			currentTimeType: 'today',
			currentTimeLabel: '今天',
			endTime: '',
			startTime: '',
			pagesize: 10,
			pagenum: 1,
			warningList: [],
			nodata: false
		};
	},
	onLoad(option) {
		this.wrybh = option.wrybh;
		this.wrymc = option.wrymc
	},
	mounted() {
		this.getTime('today');
		this.getCode();
		this.initList();
	},
	methods: {
		goToDetail(item){
			console.log('item',item);
			uni.navigateTo({
					url: `/pages/warningRecord/Layout?YJID=${item.ID}`
			});
		
		},
		// 选择预警类型
		getWarningType(v) {
			this.currentWarningType = v.value;
			this.currentWarningLabel = v.label;
			this.showWarningType = false;
			this.initList();
		},
		// 选择时间类型
		getTimeType(v) {
			this.showTimeType = false;
			this.currentTimeType = v.value;
			this.currentTimeLabel = v.label;
			this.getTime(v.value);
			this.initList();
		},
		// 初始化列表
		initList() {
			getYjjl({
				endT: this.endTime,
				startT: this.startTime,
				pageNum: this.pagenum,
				pageSize: this.pagesize,
				YJLX: this.currentWarningType,
				WRYBH: this.wrybh
			}).then(res => {
				this.warningList = res.data;
				this.nodata = false;
				if (!res.data.length) {
					this.nodata = true;
				}
				console.log(res);
			});
		},
		getCode(){
			getCode({
				code: 'YJGZ_YJLX'
			}).then(res=>{
				this.warningType = res.data;
			})
		},
		getContent(yjlx){
			for(let i=0;i<this.warningType.length;i++){
				let item = this.warningType[i];
				if(item.value == yjlx){
					return item.label;
				}
			}
			return "";
		},
		getTime(value) {
			switch (value) {
				case 'today':
					this.startTime = this.$dayjs().format('YYYY-MM-DD 00:00');
					this.endTime = this.$dayjs().format('YYYY-MM-DD HH:mm');
					break;
				case 'yesterday':
					this.startTime = this.$dayjs()
						.subtract(1, 'day')
						.format('YYYY-MM-DD 00:00');
					this.endTime = this.$dayjs()
						.subtract(1, 'day')
						.format('YYYY-MM-DD 23:59');
					break;
				case 'seven':
					this.startTime = this.$dayjs()
						.subtract(7, 'day')
						.format('YYYY-MM-DD 00:00');
					this.endTime = this.$dayjs().format('YYYY-MM-DD HH:mm');
					break;
				case 'month':
					this.startTime = this.$dayjs()
						.subtract(30, 'day')
						.format('YYYY-MM-DD 00:00');
					this.endTime = this.$dayjs().format('YYYY-MM-DD HH:mm');
					break;
				default:
					this.startTime = '';
					this.endTime = '';
					break;
			}
		},
		calcTime(v) {
			if (!v) {
				return '';
			}
			if (Number(v) < 60) {
				return `0小时${v}分钟`;
			} else {
				return `${parseInt(v / 60)}小时${v % 60}分钟`;
			}
		},
		back() {
			uni.navigateBack({
				delta: 1
			});
		},
		strSlice(v){
			let index = v.indexOf('】')
			return v.slice(index+1)
		console.log(e);
		}
	}
};
</script>

<style scoped>

.zy-data5{
	height: calc(100% - 15rpx);
}

.zy-til2{
	min-height: 94rpx;
}
</style>
