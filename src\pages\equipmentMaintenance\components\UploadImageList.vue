<!-- @format -->

<template>
    <div v-show="isNeedShow">
        <div class="zy-form">
            <div class="item nfx">
                <p
                    class="label"
                    :class="{ star: options.type === 'add' }"
                    @click="showInstallIllustration = !showInstallIllustration"
                >
                    {{ title
                    }}
					<image
                        src="@/static/app/images/whic.png"
                        class="pd-whic1"
                    />
                </p>
                <div class="gap"></div>
                <ul class="pd-ulpic1">
                    <li v-for="(item, index) in arrUploadImage" :key="index">
                        <div style="position: relative; width: 213.76815rpx">
                            <image
                                mode="scaleToFill"
                                :src="getFileUrl(item)"
                                alt=""
                                @click="previewImage(arrUploadImage, index)"
                            />
                            <image
                                v-show="!isDisabled"
                                mode="scaleToFill"
                                src="@/static/app/images/cls.png"
                                class="delImg"
                                @click="delFile(item)"
                            />
							
                        </div>
                    </li>
                    <li
                        v-show="
                            !isDisabled || arrUploadImage.lenght === maxLength
                        "
                    >
                        <div class="innerbox">
                            <image
                                @click="addFile(childTypeKeyword)"
                                src="@/static/app/workbench/images/addtu.png"
                                class="addpic"
                            />
                        </div>
                    </li>
                    <!-- <li v-for="(item, index) in arrUploadImage" :key="index">
                        <div class="pic-box">
                            <image
                                v-show="item.WJID != ''"
                                mode="scaleToFill"
                                :src="getFileUrl(item)"
                                alt=""
                                @click="previewImage(arrUploadImage, index)"
                            />
                            <image
                                v-show="!isDisabled && item.WJID != ''"
                                mode="scaleToFill"
                                src="@/static/app/images/cls.png"
                                class="delImg"
                                @click="delFile(item)"
                            />
                            <image
                                v-show="!isDisabled && item.WJID == ''"
                                mode="heightFix"
                                @click="addFile(item.ZLXDM)"
                                src="@/static/equipmentMaintenance/images/yy0706-photo.png"
                                class="pd-addpic1"
                            />
                        </div>
                        <div class="name">{{ item.name }}</div>
                    </li> -->
                </ul>
            </div>
        </div>
        <div class="mymask" v-show="showInstallIllustration"></div>
        <div class="pd-botdlg" v-show="showInstallIllustration">
            <i
                class="dlgcls"
                @click="showInstallIllustration = !showInstallIllustration"
            ></i>
            <div class="gap"></div>
            <div class="gap"></div>
            <div class="pd-con1">
                <div class="pd-tit1">拍照示例</div>
                <div class="gap"></div>
                <div class="gap"></div>
                <slot>
                    <ul class="pd-ulpic1 sample">
                        <li>
                            <image
                                mode="aspectFit"
                                src="@/static/images/sample/sb1.jpg"
                                alt=""
                            />
                            <p>近景</p>
                        </li>
                        <li>
                            <image
                                mode="aspectFit"
                                src="@/static/images/sample/sb3.jpg"
                                alt=""
                            />
                            <p>中景</p>
                        </li>
                        <li>
                            <image
                                mode="aspectFit"
                                src="@/static/images/sample/sb2.jpg"
                                alt=""
                            />
                            <p>远景</p>
                        </li>
                    </ul>
                    <div class="gap"></div>
                    <div class="gap"></div>
                </slot>

                <div class="gap"></div>
                <div class="gap"></div>
            </div>
        </div>
        <!-- #ifdef APP-PLUS -->
        <bowo-watermark ref="watermarkPainter" :fontSize="14" color="#fff" />
        <!-- #endif -->
        <!-- #ifndef APP-PLUS -->
        <bowo-watermark ref="watermarkPainter" :fontSize="14" color="#fff" />
        <!-- #endif -->
    </div>
</template>

<script>
import { getFilelist, deletefile, downloadFile } from '@/api/iot/appendix.js';
import {
    API_LOGIN_SERVICE_URL,
    LOGIN_ULR_BASE,
    UPLOAD_URL,
    DOWNLOAD_URLZDY,
    PREVIEW_FILE_URL
} from '@/common/config.js';
export default {
    name: 'DataCollectionAppUploadImage',
    props: {
        options: {
            type: Object,
            default: () => {}
        },
        // arrUploadType: {
        //     type: Array,
        //     default: () => []
        // },
        childTypeKeyword: {
            type: String,
            default: ''
        },
        fileTypeKeyword: {
            type: String,
            default: ''
        },
        maxLength: {
            type: Number,
            default: 3
        },
        uploadId: {
            type: String,
            default: ''
        },
        title: {
            type: String,
            default: '安装照片'
        },
        isDisabled: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            arrUploadImage: [],
            showInstallIllustration: false,
            isNeedShow: false
        };
    },

    mounted() {
        if (this.options.type === 'add') {
            this.isNeedShow = true;
        }
    },

    methods: {
        //获取文件list
        getImageFileList() {
            let obj = {
                pageSize: 100000,
                pageNum: 1,
                YWSJID: this.uploadId,
                LXDMS: this.fileTypeKeyword,
                ZLXDMS: ''
            };
            getFilelist(obj)
                .then((res) => {
                    let fileData = res[0];
                    if (fileData?.zlxList?.length) {
                        fileData.zlxList.forEach((list) => {
                            if (list.ZLXDM === this.childTypeKeyword) {
                                this.arrUploadImage = list.fileList;
                            }

                            if (
                                !this.arrUploadImage.length &&
                                this.options.type == 'detail'
                            ) {
                                this.isNeedShow = true;
                            }
                        });
                    }
                })
                .catch((err) => {
                    console.log('err', err);
                });
        },

        //删除文件
        delFile(file) {
            if (this.timer) {
                clearTimeout(this.timer);
            }
            this.timer = setTimeout(() => {
                let self = this;
                uni.showModal({
                    title: '提示',
                    content: '确认删除?',
                    success: function (res) {
                        if (res.confirm) {
                            console.log('用户点击确定');
                            deletefile(file.WJID).then((res) => {
                                uni.showToast({
                                    title: '删除成功',
                                    duration: 500
                                }).then(() => {
                                    setTimeout(() => {
                                        self.getImageFileList();
                                    }, 500);
                                });
                            });
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    }
                });
            }, 500);
        },
        //添加文件
        addFile(zlx) {
            let self = this;
            let resultAddress = '';
            let latitudeAndLongitude = '';
            // const appAuthorizeSetting = uni.getAppAuthorizeSetting();
            // console.log('appAuthorizeSetting', appAuthorizeSetting);
            //上传图片前先获取定位信息
            /*#ifdef APP-PLUS*/
            uni.getLocation({
                type: 'gcj02',
                geocode: true,
                success: function (result) {
                    const {
                        latitude,
                        longitude,
                        address: {
                            province,
                            city,
                            district,
                            street,
                            streetNum,
                            poiName,
                            cityCode
                        }
                    } = result;
                    resultAddress = `${province}${city}${district}${street}${streetNum}${poiName}`;
                     
                      latitudeAndLongitude = `${latitude.toFixed(5)}°N ${longitude.toFixed(5)}°E`;
                    uni.chooseImage({
                        count: 1, //默认值
                        sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
                        success: function (res) {
                            let len = res.tempFilePaths.length;
                            for (let i = 0; i < len; i++) {
                                self.uploadFile(
                                    res,
                                    zlx,
                                    i,
                                    resultAddress,
                                    latitudeAndLongitude
                                );
                            }
                        }
                    });
                },
                fail: (res) => {
                    uni.showModal({
                        title: '请开启获取定位权限',
                        showCancel: false
                    });
                    // uni.chooseImage({
                    //     count: 1, //默认值
                    //     sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
                    //     success: function (res) {
                    //         let len = res.tempFilePaths.length;
                    //         for (let i = 0; i < len; i++) {
                    //             self.uploadFile(
                    //                 res,
                    //                 zlx,
                    //                 i,
                    //                 resultAddress,
                    //                 latitudeAndLongitude
                    //             );
                    //         }
                    //     }
                    // });
                }
            });
            /*#endif*/
            /*#ifdef H5*/
            uni.chooseImage({
                count: 1, //默认值
                sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
                success: function (res) {
                    let len = res.tempFilePaths.length;
                    for (let i = 0; i < len; i++) {
                        self.uploadFile(
                            res,
                            zlx,
                            i,
                            resultAddress,
                            latitudeAndLongitude
                        );
                    }
                }
            });
            /*#endif*/
        },

        //上传图片
        async uploadFile(
            res,
            zlx,
            i,
            resultAddress = '',
            latitudeAndLongitude = ''
        ) {
            let f = res.tempFilePaths[i];
			let {size,type} = res.tempFiles[0];
            uni.showLoading({
                title: '上传中'
            });
            let self = this;
            let multiLineTexts = []; //水印文本
            // imageUrl: 图片bloburl
            // multilineTexts: 水印文字数组，一行文字是就是一个item,
            // fileName，文件名称
            if (!resultAddress || !latitudeAndLongitude) {
                multiLineTexts = [
                    `时间：${self.$dayjs().format('YYYY-MM-DD HH:mm:ss')}`
                ];
            } else {
                multiLineTexts = [
                    `时间：${self.$dayjs().format('YYYY-MM-DD HH:mm:ss')}`,
                    `地点：${resultAddress}`,
                    `经纬度：${latitudeAndLongitude}`
                ];
            }
            const result = await self.$refs.watermarkPainter.paintWatermark(
                f,
                multiLineTexts,
                ''
            );
            let watermarkPainter = result.path;

            uni.uploadFile({
                url: UPLOAD_URL,
                filePath: watermarkPainter,
                name: 'file',
                formData: {
					WJDX:size/1024,
					WJLX:type,
                    pageSize: 1000,
                    pageNum: 1,
                    YWSJID: self.uploadId,
                    LXDM: this.fileTypeKeyword,
                    ZLXDM: zlx
                },
                timeout: 6000,
                success: function (res) {
                    uni.showToast({
                        title: '上传成功',
                        icon: 'none'
                    });
                    console.log('上传成功');
                    self.getImageFileList();
                    uni.hideLoading();
                },
                fail: function (err) {
                    console.log('上传失败');
                    uni.showToast({
                        title: '上传失败',
                        icon: 'none'
                    });
                    uni.hideLoading();
                }
            });
        },
        //获取文件路径
        getFileUrl(item) {
            return DOWNLOAD_URLZDY + item.WJID;
        },

        //预览
        previewImage(fileList, index) {
            let fileUrls = fileList.map((file) => {
                return this.getFileUrl(file);
            });
            // 预览图片
            uni.previewImage({
                current: index,
                urls: fileUrls
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.mymask {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1000;
    width: 100%;
    height: 100%;
}

.pd-ulpic1 li {
    width: 115px;
    height: auto;
    background-color: #fff;
    border-radius: 6px;
    margin: 0 2%;
    .delImg {
        width: 40rpx;
        height: 40rpx;
    }
    .pic-box {
        position: relative;
        width: 213.76815rpx;
    }
    .name {
        text-align: center;
        color: #999;
        font-size: 22rpx;
        line-height: 44rpx;
    }
    .addpic {
        border-radius: 0;
    }
}
.pd-dltxt1 dt,
.pd-dltxt1 dd {
    white-space: wrap;
}
</style>
