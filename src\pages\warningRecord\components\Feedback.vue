<!-- @format -->
<template>
	<div class="lr-wrap">
		<div class="lr-main-all">
			<div class="zy-yujing">
				<!-- 反馈处理 -->
				<div class="yujing-con">
					<div class="yj-fankui">
						<div class="zy-line ac jb">
							<p class="yj-txt1">
								共<span>{{ feedback&&feedback.length }}</span>个反馈记录
							</p>
							<p class="yj-add" @click="add">添加</p>
						</div>
						<div class="gap"></div>
						<div class="nodatabox" v-if="feedback.length == 0">
							<u-empty text="暂无反馈记录" mode="list" style="font-size:60rpx;"></u-empty>
						</div>
						<ul class="fk-details">
							<li v-for="(item, index) in feedback" :key="index">
								<div class="zy-line jb">
									<h3 class="fk-list-title">
										<span class="s1">{{item.FKDX_CH || '-'}}</span>
										<span class="s2" style="margin-left:20rpx;">{{ item.CJSJ || '-' }}</span>
									</h3>
								</div>

								<!--  反馈对象 FKDX  客户端1  运维端4   小程序2   -->
								<!-- 运维端 运维核实（线上核实） -->
								<div v-if="item.FKDX == 4 && item.FKLX == 1">
									<div class="zy-line jb">
										<p class="p1">反馈人员：</p>
										<p class="p2">{{ item.FKR || '-' }}</p>
									</div>
									<div class="zy-line jb">
										<p class="p1">反馈时间：</p>
										<p class="p2">{{ item.CJSJ || '-' }}</p>
									</div>
									<div class="zy-line jb">
										<p class="p1">是否有效：</p>
										<p class="p2">
											<span>{{item.SFYX == 1?'有效':'无效'}}</span>
										</p>
									</div>
									<div class="zy-line jb">
										<p class="p1">
											反馈内容：
										</p>
										<p class="p2">{{ item.FKNR || '-' }}</p>
									</div>
									<div class="zy-line jb">
										<p class="p1">
											<span>处理意见：</span>
										</p>
										<p class="p2">{{ item.CZCS || '-' }}</p>
									</div>
								</div>
								
								<!-- 运维端 运维核实（企业核实）-->
								<div v-if="item.FKDX == 4 && item.FKLX == 2">
									<div class="zy-line jb">
										<p class="p1">核实人员：</p>
										<p class="p2">{{ item.FKR || '-' }}</p>
									</div>
									<div class="zy-line jb">
										<p class="p1">核实时间：</p>
										<p class="p2">{{ item.CJSJ || '-' }}</p>
									</div>
									<div class="zy-line jb">
										<p class="p1">是否有效：</p>
										<p class="p2">
											<span>{{item.SFYX == 1?'有效':'无效'}}</span>
										</p>
									</div>
									<div class="zy-line jb">
										<p class="p1">
											核实结论：
										</p>
										<p class="p2">{{ item.FKNR || '-' }}</p>
									</div>
								</div>
								
								<!-- 运维端  运维核实（反馈客户）-->
								<div v-if="item.FKDX == 4 && item.FKLX == 3">
									<div class="zy-line jb">
										<p class="p1">反馈人员：</p>
										<p class="p2">{{ item.FKR || '-' }}</p>
									</div>
									<div class="zy-line jb">
										<p class="p1">反馈方式：：</p>
										<p class="p2">{{ item.HSFS || '-' }}</p>
									</div>
									<div class="zy-line jb">
										<p class="p1">反馈时间：</p>
										<p class="p2">{{ item.CJSJ || '-' }}</p>
									</div>
								
									<div class="zy-line jb">
										<p class="p1">
											反馈内容：
										</p>
										<p class="p2">{{ item.FKNR || '-' }}</p>
									</div>
								</div>
								
								
								<!-- 客户端 -->
								<div v-if="item.FKDX == 1">
									<div class="zy-line jb">
										<p class="p1">核实人员：</p>
										<p class="p2">{{ item.FKR || '-' }}</p>
									</div>
									<div class="zy-line jb">
										<p class="p1">联系电话：</p>
										<p class="p2">{{ item.FKRDH || '-' }}</p>
									</div>
									<div class="zy-line jb">
										<p class="p1">核实情况：</p>
										<p class="p2">
											<span>{{item.SFYX == 1?'情况属实':'情况不符'}}</span>
										</p>
									</div>
									<div class="zy-line jb">
										<p class="p1">
											处置情况：
										</p>
										<p class="p2">{{ item.FKNR || '-' }}</p>
									</div>
									<div class="zy-line jb">
										<p class="p1">
											<span>处置措施：</span>
										</p>
										<p class="p2">{{ item.CZCS || '-' }}</p>
									</div>
								</div>
								<!-- 企业端 -->
								<div v-if="item.FKDX == 2">
									<div class="zy-line jb">
										<p class="p1">反馈人员：</p>
										<p class="p2">{{ item.FKR || '-' }}</p>
									</div>
									<div class="zy-line jb">
										<p class="p1">联系电话：</p>
										<p class="p2">{{ item.FKRDH || '-' }}</p>
									</div>
									<div class="zy-line jb">
										<p class="p1">反馈结论：</p>
										<p class="p2">
											<span>{{item.SFYX == 1?'情况属实':'情况不符'}}</span>
										</p>
									</div>
									<div class="zy-line jb">
										<p class="p1">
											反馈内容：
										</p>
										<p class="p2">{{ item.FKNR || '-' }}</p>
									</div>
								</div>
								<div class="nfx" v-if="item.piclist.length != 0">
									<em class="zmzl">相关附件：
									</em>
									<ul class="ul-pic1">
										<li class="imgli" v-for="(i, indexId) in item.piclist" :key="indexId">
											<div class="innerbox">
												<image class="img" mode="scaleToFill" :src="getFileUrl(i)" alt=""
													@click="
											            previewImage(item.piclist, indexId)
											        " />

											</div>
										</li>
									</ul>
									<div class="gap"></div>
								</div>



							</li>
						</ul>
					</div>
				</div>
			</div>
		</div>

		<AddDialog :ID="ID" v-if="showAdd" @hide="hideAdd" :warning="warn"></AddDialog>

	</div>
</template>

<script>
	import {
		DOWNLOAD_URLZDY,
	} from '@/common/config.js'
	import {
		getWarningBaseList,
		getWarningFeedbackList,
		getWarningFeedbackAdd
	} from '@/api/iot/warningRecord.js';
	import AddDialog from './AddDialog';
	export default {
		components: {
			AddDialog
		},
		data() {
			return {
				tab: 1,
				showAdd: false,
				YJSJID: this.warning.YJSJID, //预警编号
				info: {},
				feedback: [],
				FKNR: '', //反馈内容
				warn: this.warning,
				fileList: []
			};
		},
		props: {
			ID: {
				type: String,
				default: ''
			},
			warning: {
				type: Object,
				default: () => {
					return null
				}
			},
		},
		computed: {
			equitments() {
				let SCSBMC = '';
				let ZWSBMC = '';
				if (this.info.ZWSBMC) {
					SCSBMC =
						this.info.SCSBMC == null || this.info.SCSBMC == '' ?
						'' :
						this.info.SCSBMC + ',';
					ZWSBMC =
						this.info.ZWSBMC == null || this.info.ZWSBMC == '' ?
						'' :
						this.info.ZWSBMC;
				} else {
					SCSBMC =
						this.info.SCSBMC == null || this.info.SCSBMC == '' ?
						'--' :
						this.info.SCSBMC;
					ZWSBMC = '';
				}
				return SCSBMC + ZWSBMC;
			}
		},
		created() {
			this.getFeedbackList();

		},
		mounted() {

		},
		methods: {
			//获取文件路径
			getFileUrl(item) {
				return DOWNLOAD_URLZDY + item;
			},

			//预览图片
			previewImage(fileList, index) {
				let self = this;
				let fileUrls = fileList.map((file) => {
					return this.getFileUrl(file);
				});
				// 预览图片
				uni.previewImage({
					current: index,
					urls: fileUrls,
					//长按保存到本地
					longPressActions: {
						itemList: ["保存图片到本地"],
						success: (data) => {
							// console.log('data', data)
							if (data.tapIndex == 0) {
								let imgurl = fileUrls[data.index];
								self.saveImage(imgurl)
							}
			
						},
						fail: function(err) {
							console.log(err.errMsg);
						},
					},
			
				});
			
			},
			
			//保存图片
			saveImage(imgurl) {
				uni.downloadFile({
					url: imgurl,
					success(res) {
						let url = res.tempFilePath
						uni.saveImageToPhotosAlbum({
							filePath: url,
							success() {
								uni.showToast({
									title: '已存至系统相册',
									icon: "success"
								})
							},
							fail(err) {
								uni.showToast({
									title: '保存失败',
									icon: "error"
								})
							}
						})
					}
				})
			},
			//预警基本信息
			getBaseList() {
				let obj = {
					ID: this.ID
				};
				getWarningBaseList(obj).then((res) => {
					if (res.status === '000') {
						this.info = res.data[0];

					}
				});
			},
			//预警反馈列表
			getFeedbackList() {
				let obj = {
					YJSJID: this.YJSJID,
				};
				getWarningFeedbackList(obj).then((res) => {
					if (res.status === '000') {
						this.feedback = res.data;
						this.feedback.forEach(item => {
							let arr = item.WJIDS && item.WJIDS.split(',') || []
							this.$set(item, 'piclist', arr)
						})
					}
				});
			},
			add() {
				this.showAdd = true;
			},
			hideAdd() {
				this.showAdd = false;
				this.getFeedbackList();
			}
		}
	};
</script>

<style scoped>
	.lr-wrap {
		height: 100%;
	}

	.lr-main-all,
	.zy-yujing,
	.yj-fankui {
		height: 100%;
	}

	.yujing-con {
		height: calc(100% - 170rpx);
	}

	.fk-details {
		height: 100%;
		overflow: auto;
	}

	.fk-details li .p1 {
		color: #666;
		line-height: 60rpx;
		text-align: left;
		width: 160rpx;
	}

	.fk-details li .p2 {
		color: #333;
		line-height: 60rpx;
		width: calc(100% - 180rpx);
		white-space: pre-wrap;
		word-wrap: break-word;
		word-break: break-all;
	}




	.nfx .zmzl {
		color: #666;
		line-height: 31px;
		text-align: left;
		width: 83px;
		font-size: 29.166675rpx;
	}

	.ul-pic1 {
		flex-wrap: wrap;
		align-items: space-between;
		display: flex;
		padding-top: 10rpx;
	}

	.ul-pic1 li {
		position: relative;
		width: 33.3%;
		margin-left: 0;
		margin-top: 0px;
		padding: 8rpx;
		border-top: 0
	}

	.ul-pic1 li.imgli {
		padding: 0
	}

	.pd-pic {
		height: 360rpx;
	}

	.ul-pic1 li.imgli {
		backgroung: none;
		border-top: none;
		width: 31%;
		margin: 6rpx;
	}

	.ul-pic1 li .innerbox {
		width: 100%;
		background-color: #f1f1f1;
		padding: 16rpx;
		height: 160rpx;
	}

	.ul-pic1 li.imgli .innerbox {
		padding: 0rpx;
		background: #fff;
	}

	.ul-pic1 li .img {
		width: 100%;
		height: 100%;
	}

	.nodatabox ::v-deep .u-iconfont {
		font-size: 124rpx !important;
	}

	.nodatabox ::v-deep .u-icon__label {
		font-size: 28rpx !important;
	}
</style>
