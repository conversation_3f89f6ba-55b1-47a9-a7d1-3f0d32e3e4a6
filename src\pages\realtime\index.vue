<!-- @format -->

<template>
    <div class="pd-pg1a" style="background: #f1f1f1; position: relative">
        <header class="header realtime">
            <div class="searchbox">
                <input
                    type="text"
                    class="pd-inpsrh"
                    placeholder="推荐输入IMEI号后5位查询"
                    v-model="searchText"
                    @input="debouncedSearch"
                    @confirm="search"
                />
                <i class="searchbtn" @click="search"></i>
                <image
                    src="../../static/app/images/scan.png"
                    class="pd-sysbtn"
                    @click="scanCode()"
                />
            </div>
        </header>
        <ul class="pd-ultbs1" style="z-index: 9999">
            <li
                :class="dataType == item.value ? 'on' : ''"
                v-for="(item, index) in dataTypeArr"
                :key="index"
                @click="changeTab(item)"
            >
                <p>{{ item.name }}</p>
            </li>
        </ul>
        <section class="main">
            <div class="inner" style="height: calc(100vh - 100rpx)">
                <div class="gap"></div>
                <scroll-view
                    v-show="stateList.length"
                    scroll-y
                    style="height: 100%"
                    @scrolltolower="getMore"
                >
                    <equipment-card
                        :list="stateList"
                        @setImeiCircle="toSetImeiCircle"
                    ></equipment-card>
                </scroll-view>
                <scroll-view
                    scroll-y
                    style="height: 100%"
                    v-show="vibrationList.length"
                    @scrolltolower="getMore"
                >
                    <ul
                        class="pd-ullst1"
                        v-for="(item, index) in vibrationList"
                        :key="index"
                    >
                        <li class="to-detail">
                            <em>设备IMEI</em>
                            <div>
                                <i>{{ item.IMEI || '-' }} </i>
                                <image
                                    @click="toSetImeiCircle(item)"
                                    class="gray-icon-reverse"
                                    src="@/static/app/images/gray-arrow.png"
                                    mode="widthFix"
                                >
                                </image>
                            </div>
                        </li>
                        <li>
                            <em>检测时间</em><i>{{ item.JCSJ || '-' }}</i>
                        </li>
                        <li>
                            <em>上报时间</em
                            ><i style="color: rgb(248, 190, 69)">{{
                                item.SBSJ || '-'
                            }}</i>
                        </li>
                        <template v-if="item.ZNSBLX == 'RAD'">
                            <li>
                                <em>辐射剂量率</em
                                ><i>{{ item.POWER.toString() || '-' }}</i>
                            </li>
                        </template>
                        <template v-if="item.ZNSBLX == 'PH'">
                            <li>
                                <em>pH值</em
                                ><i>{{ item.POWER.toString() || '-' }}</i>
                            </li>
                        </template>
                        <template v-if="item.ZNSBLX == 'ZD'">
                            <li>
                                <em>X轴振幅</em><i>{{ item.X || '-' }}</i>
                            </li>
                            <li>
                                <em>Y轴振幅</em><i>{{ item.Y || '-' }}</i>
                            </li>
                            <li>
                                <em>Z轴振幅</em><i>{{ item.Z || '-' }}</i>
                            </li>
                            <li>
                                <em>X轴频率</em><i>{{ item.X_FREQ || '-' }}</i>
                            </li>
                            <li>
                                <em>Y轴频率</em><i>{{ item.Y_FREQ || '-' }}</i>
                            </li>
                            <li>
                                <em>Z轴频率</em><i>{{ item.Z_FREQ || '-' }}</i>
                            </li>
                        </template>
                        <template v-if="item.ZNSBLX == 'DL'">
                            <li>
                                <em>电流量（A）</em
                                ><i>{{ item.POWER || '0' }}</i>
                            </li>
                        </template>
                    </ul>
                </scroll-view>
                <u-empty
                    class="nodatabox"
                    mode="data"
                    v-show="!stateList.length && !vibrationList.length"
                    :text="text"
                    font-size="28rpx"
                ></u-empty>
            </div>
        </section>
    </div>
</template>

<script>
import { debounce } from 'lodash';
import equipmentCard from './components/equipmentCard.vue';
import EquipStateList from '@/pages/component/EquipStateList';
import { znsb } from '@/api/iot/enterprise.js';
import { getState, getVibration } from '@/api/iot/realtime.js';

export default {
    components: {
        equipmentCard,
        EquipStateList
    },
    data() {
        return {
            dataTypeArr: [
                {
                    name: '状态数据',
                    value: 'state'
                },
                {
                    name: '工况数据',
                    value: 'vibration'
                }
            ],
            dataType: 'state',
            stateList: [], //状态数据列表
            vibrationList: [], //震动数据列表
            searchText: '',
            pageNum: 1,
            pageSize: 20,
            total: 0,
            isLastPage: false,
            timer: null,
            text: '数据为空,推荐输入IMEI号后5位查询',
            red: false,
            textcolor: '#ff0000',
            stateData: [
                {
                    name: '开关状态',
                    value: 'ADS'
                },
                {
                    name: '设备IMEI',
                    value: 'IMEI'
                },
                {
                    name: '检测时间',
                    value: 'JCSJ'
                },
                {
                    name: '上报时间',
                    style: 'color: #ff0000',
                    value: 'SBSJ'
                },
                {
                    name: '设备状态',
                    style: 'color: #3ab918',
                    value: 'ERR'
                },
                {
                    name: '设备电压',
                    style: 'color: #3ab918',
                    value: 'VOL'
                },
                {
                    name: '信号功率',
                    style: 'color: #3ab918',
                    value: 'SP'
                },
                {
                    name: '总功率',
                    style: 'color: #3ab918',
                    value: 'TP'
                },
                {
                    name: '信噪比',
                    style: 'color: #3ab918',
                    value: 'SINR'
                },
                {
                    name: '信号质量',
                    style: 'color: #3ab918',
                    value: 'RSRQ'
                }
            ],
            vibrationData: [
                {
                    name: '设备IMEI',
                    value: 'IMEI'
                },
                {
                    name: '检测时间',
                    value: 'JCSJ'
                },
                {
                    name: '上报时间',
                    value: 'SBSJ',
                    style: 'color:#efa31e'
                },
                {
                    name: 'X轴振幅',
                    value: 'X'
                },
                {
                    name: 'Y轴振幅',
                    value: 'Y'
                },
                {
                    name: 'Z轴振幅',
                    value: 'Z'
                },
                {
                    name: 'X轴频率',
                    value: 'X_FREQ'
                },
                {
                    name: 'Y轴频率',
                    value: 'Y_FREQ'
                },
                {
                    name: 'Z轴频率',
                    value: 'Z_FREQ'
                }
            ],
            debouncedSearch: null
        };
    },
    created() {
        this.debouncedSearch = debounce(this.search, 1500);
    },
    methods: {
        //扫码
        scanCode() {
            uni.scanCode({
                scanType: ['qrCode'],
                success: (res) => {
                    this.fromScan = true;
                    let imei = res.result.split(';')[0];
                    this.searchText = imei;
                    if (imei) {
                        this.search();
                    }
                },
                fail: (err) => {
                    uni.showToast({
                        title: '未识别到二维码！',
                        icon: 'none'
                    });
                }
            });
        },
        //去设置阈值
        toSetImeiCircle(item) {
            uni.navigateTo({
                url: `/pages/realtime/SetImeiCircle?IMEI=${item.IMEI}`
            });
        },
        //获取最新上报的状态数据
        getStateList() {
            //console.log('sta',this.pageNum);
            getState({
                IMEI: this.searchText,
                pageSize: this.pageSize,
                pageNum: this.pageNum
            })
                .then((res) => {
                    if (res.status === '000') {
                        this.stateList = [...this.stateList, ...res.data.list];
                        this.total = res.data?.total;
                        this.isLastPage = res.data?.isLastPage;
                        if (res.data?.list?.length == 0) {
                            this.text = '请输入正确的IMEI号后5位查询';
                        }
                    }
                })
                .catch((err) => {
                    //console.log(err);
                });
        },

        //最新上报的震动数据
        getVibrationList() {
            getVibration({
                IMEI: this.searchText,
                pageSize: this.pageSize,
                pageNum: this.pageNum
            })
                .then((res) => {
                    if (res.status === '000') {
                        this.vibrationList = [
                            ...this.vibrationList,
                            ...res.data?.list
                        ];
                        this.total = res.data?.total;
                        this.isLastPage = res.data?.isLastPage;
                        if (!res.data.list || !res.data.list.length) {
                            this.text = '请输入正确的IMEI号后5位查询';
                        }
                    }
                })
                .catch((err) => {
                    console.log(err);
                });
        },
        //搜索
        search() {
            uni.hideKeyboard();
            this.total = 0;
            this.isLastPage = false;
            this.pageNum = 1;
            this.stateList = [];
            this.vibrationList = [];
            if (this.searchText.length >= 5) {
                if (this.dataType == 'state') {
                    this.getStateList();
                } else if (this.dataType == 'vibration') {
                    this.getVibrationList();
                }
            } else {
                uni.showToast({
                    title: '请输入5位以上imei号',
                    icon: 'none'
                });
            }
        },
        getMore() {
            if (this.isLastPage) {
                uni.showToast({
                    title: '没有更多了',
                    icon: 'none'
                });
                return;
            } else if (this.dataType == 'state') {
                this.pageNum++;
                this.getStateList();
            } else if (this.dataType == 'vibration') {
                this.pageNum++;
                this.getVibrationList();
            }
        },
        changeTab(item) {
            this.dataType = item.value;
            this.search();
        }
    }
};
</script>

<style scoped lang="less">
html {
    -webkit-tap-highlight-color: transparent;
    height: 100%;
}

body {
    -webkit-backface-visibility: hidden;
    height: 100%;
}
.pd-pg1a .header {
    height: 110.90825rpx;
    padding: 10.289875rpx 28.985475rpx 0;
    box-sizing: border-box;
}
.realtime {
    .searchbox {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .pd-inpsrh {
        width: calc(100% - 70rpx);
    }
    .pd-sysbtn {
        width: 40rpx;
        height: 40rpx;
    }
}
.pd-ullst1 {
    margin-bottom: 10px;
}

.red {
    color: #f00 !important;
}

.tabBox {
    position: absolute;
    top: 140rpx;
    left: 0;
    width: 100%;
    z-index: 9;
}

.tab-content {
    height: 100%;
}

.nodatabox ::v-deep .u-iconfont {
    font-size: 124rpx !important;
}

.nodatabox ::v-deep .u-icon__label {
    font-size: 28rpx !important;
}
.pd-ullst1 > li {
    padding: 0rpx;
    line-height: 70rpx;
}
.pd-ullst1 > li + li {
    border: none;
}
.pd-ullst1 li em {
    font-size: 13px;
    color: #999;
    width: 140rpx;
    display: inline-block;
}

.chilun {
    position: absolute;
    bottom: 20rpx;
    right: 30rpx;
    z-index: 9999;
}
.chilun .icon {
    width: 76rpx;
}
.to-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.to-detail .gray-icon-reverse {
    width: 26rpx;
    transform: rotate(180deg);
}
</style>
