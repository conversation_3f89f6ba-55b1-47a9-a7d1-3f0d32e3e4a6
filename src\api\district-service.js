import http from '@/common/net/http.js';
import districtLocal from './district.json';
import { deepCopyObject } from '@/common/merge.js';

const CACHE_PREFFIX = 'district';

const createCacheKey = (code) => {
	if(code){
		return `${CACHE_PREFFIX}@${code}`;
	} else {
		return `${CACHE_PREFFIX}@root`;
	}
}

/**
 * 从服务端同步行政区划数据
 */
const syncDistrictData = (code) => {
	return new Promise((resolve, reject) => {
		http.post(`${http.url}`, {
			XZQHDM: code,
			service: "SEARCH_XZQH",
			version: "1"
		}).then(resp => {
			let district = resp.map(item => {
				return {
					code: item.XZQHDM,
					name: item.XZQH,
					parent: item.FDM,
					level: item.XZJB,
					order: parseInt(item.XZQHDM)
				}
			});
			saveToLocal(code, district);
			resolve(deepCopyObject(district));
		})
		.catch(error => {
			reject(error)
		});
	});
}

/**
 * 从本地缓存加载行政区划数据
 */
const loadDistrictFromLocal = (code) => {
	let cacheKey = createCacheKey(code);
	uni.removeStorageSync(cacheKey);
	return uni.getStorageSync(cacheKey);
}

const saveToLocal = (code, data) => {
	uni.setStorage({
		key: createCacheKey(code),
		data: data
	})
}

/**
 * 加载全省的行政区划数据
 * @param {Object} code
 */
const loadDistrictSource = (code) => {
	return new Promise((resolve, reject) => {
		let localCache = loadDistrictFromLocal(code);
		if(localCache){
			resolve(deepCopyObject(localCache));
		} else {
			resolve(syncDistrictData(code));
		}
	});
}

/**
 * 检索从根节点到到指定行政区划代码的所有层次节点
 * 比如：根据320101检索结果为：
 * [
		{code: '32000', ...},
		{code: '320100', ...},
		{code: '320101', ...}
	]
 */
const retrieveDistrictChainByCode = (code) => {
	return new Promise((resolve, reject) => {
		loadDistrictSource()
			.then(district => {
				let root = resolveRootDistrict(code, district);
				if(root){
					let chain = [];
					recursiveDistrictChain(code, root, chain)
					resolve(chain);
				}else{
					resolve(null);
				}
			})
			.catch(error => {
				reject(error);
			})
	});
}

/**
 * 获取根节点行政区划
 * @param {Object} code
 * @param {Object} districtSource
 */
const resolveRootDistrict = (code, districtSource) => {
	if(districtSource === null || typeof districtSource === 'undefined'){
		return;
	}
	if(Array.isArray(districtSource)){//类似/common/city.data.js这样的数据
		if(districtSource.length > 0){
			let matchDistrict = districtSource.filter(item => {
				let preffix = trimZero(item.code);
				return code.startsWith(preffix);
			});
			matchDistrict.sort((aDistrict, another) => {
				return parseInt(aDistrict.code) - parseInt(another.code);
			});
			return matchDistrict.length > 0 ? matchDistrict[0] : null;
		}
		return null;
	} else {
		//从`api/appservercontroller/getXzqhTree`这个接口返回的树状结构数据
		let preffix = trimZero(districtSource.code);
		return code.startsWith(preffix) ? districtSource : null;
	}
}

/**
 * 去掉首尾的0
 */
export const trimZero = (code) => {
	let preffix = code.replace(/0/g, ' ').trim().replace(/[ ]/g, '0');
	return preffix;
}

/**
 * 根据行政区划代码的层级规则提取指定行政区划代码@param code的各层级行政区划
 * 如32000是320100的上级，320100是320101的上级
 */
const recursiveDistrictChain = (code, district, chain) => {
	let preffix = trimZero(district.code);
	if(code.startsWith(preffix)){
		chain.push({
			code: district.code,
			name: district.name,
			parent: district.parent,
			order: district.order
		});
		if(district.children && district.children.length > 0){
			for(let i = 0; i < district.children.length; i++){
				let child = district.children[i];
				let childPreffix = trimZero(child.code);
				if(code.startsWith(childPreffix)){
					recursiveDistrictChain(code, child, chain);
					break;
				}
			}
		}
	}
}

/**
 * 获取指定代码的行政区划
 */
const getDistrictByCode = (code) => {
	return new Promise((resolve, reject) => {
		loadDistrictSource()
			.then(districtSource => {
				let result = null;
				if(districtSource){
					let filterDistricts = districtSource.filter(item => {
						return item.code === code;
					});
					result = filterDistricts.length > 0 ? filterDistricts[0] : null;
				}
				resolve(result);
			})
			.catch(error => {
				
			})
	});
}

/**
 * 获取指定代码的行政区划
 */
const getDistrictByCodeSync = (code, districtSource) => {
	let result = null;
	if(districtSource){
		let filterDistricts = districtSource.filter(item => {
			return item.code === code;
		});
		result = filterDistricts.length > 0 ? filterDistricts[0] : null;
	}
	return result;
}

const recursiveFindDistrict = (code, parent) => {
	if(code === parent.code){
		return parent;
	}
	
	let preffix = trimZero(parent.code);
	if(code.startsWith(preffix)){
		let children = parent.children;
		if(children && children.length > 0){
			for(let i = 0; i < children.length; i++){
				let child = children[i];
				let childPreffix = trimZero(child.code);
				if(code.startsWith(childPreffix)){
					return recursiveFindDistrict(code, child);
				}
			}
		}
	}else{
		return null;
	}
}

/**
 * 获取根节点
 */
const getRootDistrict = () => {
	return new Promise((resolve, reject) => {
		loadDistrictSource()
			.then(districtSource => {
				let result = null;
				if(districtSource && districtSource.length > 0){
					//按行政区划排序从小到大排序
					districtSource.sort((aDistrict, another) => {
						return parseInt(aDistrict.code) - parseInt(another.code);
					});
					
					//根据行政区划代码规则，代码值最小的是根节点
					let districtOfMinCode = districtSource[0];
					result = districtOfMinCode;
					
					let parentCode = districtOfMinCode.parent;
					if(parentCode){
						let children = getChildDistricts(parentCode);
						//如果行政区划最小的节点存在兄弟节点，说明录入的行政区划未录入根节点，
						//此时需要虚拟一个根节点
						if(children.length > 1){
							result = {
								code: parentCode,
								name: '行政区划',
								parent: '',
								order: 0
							}
						}
					}
					result.hasChild = true;
				}
				resolve(result);
			})
			.catch(error => {
				reject(error);
			})
	});
}

/**
 * 获取子节点行政区划
 */
const getChildDistricts = (parent) => {
	return new Promise((resolve, reject) => {
		loadDistrictSource()
			.then(districtSource => {
				let children = getChildDistrictSync(parent, districtSource);
				for(let district of children){
					district.hasChild = getChildDistrictSync(district, districtSource).length > 0;
				}
				resolve(children);
			})
			.catch(error => {
				reject(error);
			})
	});
}

/**
 * 同步获取子节点
 */
const getChildDistrictSync = (parent, districtSource) => {
	let children = null;
	if(districtSource){
		let parentCode = parent.code || parent;
		children = districtSource.filter(item => {
			return item.parent === parentCode && item.name !== '市辖区';
		});
	}
	return children;
}

const selectDistrict = (multi, selected) => {
	uni.navigateTo({
		url: `/pages/component/tree/district-tree?multi=${multi}&selected=${selected}`
	})
}

export default {
	loadDistrictSource,
	retrieveDistrictChainByCode,
	getDistrictByCode,
	getDistrictByCodeSync,
	getRootDistrict,
	getChildDistricts,
	getChildDistrictSync,
	selectDistrict
};

