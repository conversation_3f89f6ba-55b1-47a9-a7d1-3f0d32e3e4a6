<template>
	<RemindModal @close="closeBackPop" height="458rpx" :content="popContent" >
		<div class="dialog-slot-btn">
			<div class="gap"></div>
			<div class="bottom-btn" @click="movePage">是</div>
			<div class="bottom-btn" @click="saveDialog">存草稿</div>
			<div class="bottom-btn" @click="stateOnPage">否</div>
		</div>
	</RemindModal>
</template>


<script>
import RemindModal from '@/pages/component/RemindModal'
export default {
    name: 'DataCollectionAppReminderPop',
    props: {
		popContent: {
		    type: String,
		    default: '说明的内容。'
		}
    },
	components: {
		RemindModal
	},
    data() {
        return {
		}
    },

    mounted() {},

    methods: {
		movePage(){
			this.$emit('movePage')
		},
		saveDialog(){
			this.$emit('saveDialog')
		},
		stateOnPage(){
			this.$emit('stateOnPage')
		},
		closeBackPop(){
			//关闭弹窗
			this.$emit('update:isShowBackPop',false)
		}
	}
};
</script>

<style lang="scss" scoped>
	.dialog-slot-btn{
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		.bottom-btn{
			color: #fff;
			background: #4874ff;
			border-radius: 6rpx;
			padding: 8rpx 15rpx;
			font-size: 28rpx;
			line-height: 50rpx;
			text-align:center;
			width: 90%;
			margin: 13rpx;
		}
	}
</style>
