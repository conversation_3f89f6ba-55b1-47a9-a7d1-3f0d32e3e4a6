<template>
	<!-- 监管信息 -->
	<div class="tabs1-con">
	    <ul class="qiye-info">
	        <li>
	            <div class="lp">
	                <p class="p1">企业状态：</p>
	            </div>
	            <div class="rp">
					<p>
						<image class="pic"
						   mode="widthFix"
						    :src="getFilePath(enterpriseState)"
						></image>
					</p>
	            </div>
	        </li>
	        <li>
	            <div class="lp">
	                <p class="p1">法定代表：</p>
	            </div>
	            <div class="rp">
	                <p class="p1">{{enterpriseInfo.HBLXR}}</p>
	            </div>
	        </li>
	        <li>
	            <div class="lp">
	                <p class="p1">联系方式：</p>
	            </div>
	            <div class="rp">
	                <p class="p1 tel" @click="call(enterpriseInfo.HBLXRDH)">{{enterpriseInfo.HBLXRDH || '-'}}</p>
	            </div>
	        </li>
	        <li>
	            <div class="lp">
	                <p class="p1">企业地址：</p>
	            </div>
	            <div class="rp">
	                <p class="p1 local" @click="opLocaltion">
					{{ enterpriseInfo.DWDZ&&enterpriseInfo.DWDZ.slice(0, 14) || '-' }}{{ enterpriseInfo.DWDZ&&enterpriseInfo.DWDZ.length > 13 ? '...' : '' }}
					</p>
	            </div>
	        </li>
	    </ul>
		
	    <div class="gap"></div>
	    <div class="qiye-board">
	        <ul class="qiye-data1">
				<li>
				    <div class="ic">
				        <image class="icon" mode="widthFix" src="@/static/app/enterpriseDetail/images/qiye-data1-ic1.png" alt="">
				    </div>
				    <div class="rp">
				        <p class="p1">生产线</p>
				        <p class="p2">{{lineInfo.SCX_COUNT || 0}}</p>
				    </div>
				</li>
				<li>
				    <div class="ic">
				        <image class="icon" mode="widthFix" src="@/static/app/enterpriseDetail/images/qiye-data1-ic2.png" alt="">
				    </div>
				    <div class="rp">
				        <p class="p1">治污线</p>
				        <p class="p2">{{lineInfo.ZWX_COUNT || 0}}</p>
				    </div>
				</li>
				<li>
				    <div class="ic">
				        <image class="icon" mode="widthFix" src="@/static/app/enterpriseDetail/images/qiye-data1-ic3.png" alt="">
				    </div>
				    <div class="rp">
				        <p class="p1">生产设备</p>
				        <p class="p2">{{lineInfo.SCSB_COUNT || 0}}</p>
				    </div>
				</li>
				<li>
				    <div class="ic">
				        <image class="icon" mode="widthFix" src="@/static/app/enterpriseDetail/images/qiye-data1-ic4.png" alt="">
				    </div>
				    <div class="rp">
				        <p class="p1">治污设备</p>
				        <p class="p2">{{lineInfo.ZWSB_COUNT || 0}}</p>
				    </div>
				</li>


	        </ul>
	    </div>
		
	    <div class="gap"></div>
	    <div class="qiye-board">
	        <div class="zy-line ac jb">
	            <p class="qiye-til1">企业平面图</p>
	           <i class="qiye-quanping">
					<image v-if="enterpriseData.QYPMT_WJID"
					  class="quanping" mode="widthFix" src="@/static/app/enterpriseDetail/images/qiye-quanping.png" 
					@click="goFullScreen" 
					alt="">
				</i>
	        </div>
	        <div class="gap"></div>
	        <div class="qiye-tu">
				<image v-if="enterpriseData.QYPMT_WJID" class="pingmian" mode="widthFix" style="width:100%;height:auto;" 
				:src="getFileUrl(enterpriseData.QYPMT_WJID)"
				/>
					<image v-if="!enterpriseData.QYPMT_WJID" mode="widthFix" style="width:40%;margin:30rpx auto 80rpx auto;display: block;"  src="@/static/app/enterpriseDetail/images/empty.png" alt="">
	        </div>
		
	    </div>
		
	    <div class="gap"></div>
	    <div class="qiye-board">
	        <div class="zy-line">
	            <p class="qiye-til1">工艺流程图</p>
	        </div>
	        <div class="gap"></div>
	        <div class="qiye-tabs2" >
	            <div class="item" :class="currentscx==item.SCXID?'cur':''"
				  @click="changeProductLine(item,index)" v-for="(item, index) in scxlist">
	                <p>{{ item.SCXMC }}</p>
	                <!-- <image v-if="item.ISYC == 'true'" src="@/static/app/enterpriseDetail/images/qiye-lamp.png" class="lamp"> -->
	            </div>
	        </div>
	        <div class="gap"></div>
	        <div class="qiye-tu">
				<image 	@click="toFullScreen(webviewSrc)" v-if="webviewSrc" mode="widthFix" style="width:100%;height:auto;"
				  :src="webviewSrc" alt="">
				<image v-if="!webviewSrc" mode="widthFix" style="width:40%;margin:40rpx auto 80rpx auto;display: block;"  src="@/static/app/enterpriseDetail/images/empty.png" alt="">
	        </div>
	    </div>

	    <div class="gap"></div>
	</div>
		    
</template>

<script>
	import {
		getBasecount,
		getDwzb
	} from '@/api/iot/runningData.js';
	import { DOWNLOAD_URLZDY } from '@/common/config.js';
	export default {
	    components: {
		
	    },
		props:{
			enterpriseData:{
				type:Object,
				default:()=>{}
			},
			enterpriseState:{
				type:String,
				default:''
			},
			wrybh :{
				type:String,
				default:''
			},
			
		},
		watch: {
			wrybh: {
				handler: function(nv) {
					this.getlineInfo()
				},
			},

		},
	    data() {
	        return {
	            tabArr: [
	                {
	                    name: '监管信息',
	                    value: 'regulatoryInfo'
	                },
	                {
	                    name: '实时监控',
	                    value: 'realTimeMonitor'
	                },
	                {
	                    name: '预警信息',
	                    value: 'earlyWarningInfo'
	                }
	            ],
	            curType: 'regulatoryInfo',
				enterpriseInfo:{},//企业基本信息
				lineInfo:{},//生产线信息
				previewList:[],//预览列表
				scxlist:[],//生产线
				webviewSrc:'',
				currentscx:'',//当前生产线
				flowPicList:[],//工艺流程图

	        };
	    },
		watch: {
			enterpriseData:{
				handler: function(newVal) {
					console.log('newVal',newVal);
					this.enterpriseInfo = newVal.qyjbxx;
					this.previewList.push(this.enterpriseData.QYPMT_WJID);
					this.getflowPic();
					this.scxlist = newVal.scxList;
					let scxLen = newVal.scxList.length;
					if (scxLen) {
						this.currentscx = newVal.scxList[0].SCXID;
						this.currentscxname = newVal.scxList[0].SCXMC;
						let url = '';
						if(this.scxlist[0].GYLCT_WJID){
							url = this.getFileUrl(this.scxlist[0].GYLCT_WJID)
							this.webviewSrc = url
						}
					
					}
				},
				deep:true
			}
		
		},
	    onLoad(option) {
	       let userinfo = uni.getStorageSync('user_info');
	    },
	    created() {
			this.getlineInfo()
		},
	    methods: {
			//切换生产线
			changeProductLine(val,index){
				this.currentscx = val.SCXID
				this.currentscxname = val.SCXMC
				let url = '';
				if(this.scxlist[0].GYLCT_WJID){
					url = this.getFileUrl(this.scxlist[index].GYLCT_WJID)
					this.webviewSrc = url
				}
			
			},
			//平面图全屏
			goFullScreen(){
				let picId = ''
				if(this.enterpriseData.QYPMT_WJID){
					picId = this.enterpriseData.QYPMT_WJID
				}
				uni.navigateTo({
				   url: `/pages/realtime/detail/FullScreenPlanPic?PICID=${picId}`
				})
			},
			//工艺全屏
			toFullScreen(src){
				uni.navigateTo({
				   url: `/pages/realtime/detail/FullScreenGongyiPic?SRC=${src}`
				})
			},
			//获取平面图点位Id
			getflowPic(){
				if(this.enterpriseData.QYPMT_WJID){
					let obj = {
						WJID:this.enterpriseData.QYPMT_WJID
					}
					getDwzb(obj).then(res=>{
						//console.log('res',res.data);
					})
				}

				
			},

			//获取图片路径
			getFileUrl(item){
				if(item){
					return DOWNLOAD_URLZDY + item;
				}else{
					return ''
				}
			},

			//预览图片
			previewImage(fileList, index) {
				let self = this;
				let fileUrls = fileList.map((file) => {
					return this.getFileUrl(file);
				});
				// 预览图片
				uni.previewImage({
					current: index,
					urls: fileUrls,
					//长按保存到本地
					longPressActions: {
						itemList: ["保存图片到本地"],
						success: (data) => {
							// console.log('data', data)
							if (data.tapIndex == 0) {
								let imgurl = fileUrls[data.index];
								self.saveImage(imgurl)
							}
			
						},
						fail: function(err) {
							console.log(err.errMsg);
						},
					},
			
				});
			
			},
			
			//保存图片
			saveImage(imgurl) {
				uni.downloadFile({
					url: imgurl,
					success(res) {
						let url = res.tempFilePath
						uni.saveImageToPhotosAlbum({
							filePath: url,
							success() {
								uni.showToast({
									title: '已存至系统相册',
									icon: "success"
								})
							},
							fail(err) {
								uni.showToast({
									title: '保存失败',
									icon: "error"
								})
							}
						})
					}
				})
			},
			//获取状态图片
			getFilePath(v) {
			    return v == '0'
			        ? require('@/static/app/images/mk3a.png')
			        : v == '1'
			        ? require('@/static/app/images/mk1a.png')
			        : v == '2'
			        ? require('@/static/app/images/mk2a.png')
			        : require('@/static/app/images/tingzhi.png');
			},
			//生产线，生产设备信息
			getlineInfo(){
				let obj = {
					WRYBH:this.wrybh
				}
				getBasecount(obj).then(res =>{
					this.lineInfo = res.data[0];
					console.log('lineInfo',this.lineInfo);
				})
			},
			//获取样式
			getStateClass(state){
				switch(state)
				{
				    case '1':
				       return 'p2'
				        break;
				    case '2':
				         return 'p2'
				        break;
				    default:
				    return 'p3'
				}
			},
			// 唤起系统导航app进行导航
			opLocaltion() {
				uni.getLocation({
					success: res => {
						uni.openLocation({
							latitude: parseFloat(this.enterpriseInfo.WD),
							longitude: parseFloat(this.enterpriseInfo.JD),
							name: this.enterpriseInfo.WRYMC,
							scale: 8
						});
					}
				});
			},
			call(phone) {
						console.log('传入的电话',phone);
						const res = uni.getSystemInfoSync();
			
						// ios系统默认有个模态框
						if(res.platform=='ios'){
							uni.makePhoneCall({
							phoneNumber:phone,
							success(){
								console.log('拨打成功了');
							},
							fail() {
								console.log('拨打失败了');
							}
						})
						}else{
						//安卓手机手动设置一个showActionSheet
							uni.showActionSheet({
							        itemList: [phone,'呼叫'],
							        success:function(res){
										console.log(res);
							           if(res.tapIndex==1){
							            uni.makePhoneCall({
							              phoneNumber: phone,
							            })
							          }
							        }
							      })
						}
			
		    },
	        //tab切换
	        changeTab(item) {
	            this.curType = item.value;
	        },
	        back() {
	            uni.navigateBack({
	                delta: 1
	            });
	        }
	    }
	}
</script>

<style>
	@import '@/static/app/enterpriseDetail/css/common.css';
	@import '@/static/app/enterpriseDetail/css/reset.css';
	@import '@/static/app/enterpriseDetail/css/zy08-20.css';
	.ic .icon{
		width: 90rpx;
	}
	.qiye-quanping .quanping{
		width: 30rpx;
	}
	.pic{
		width: 130rpx;
		border-radius: 20rpx;
		position: relative;
		bottom:-8rpx;
	}
	.qiye-tu .pingmian{
		width: 100%;
	}
</style>