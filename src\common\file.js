const parseFileSuffix = (filePathOrName) => {
	let dotIndex = filePathOrName.lastIndexOf('.');
	if(dotIndex !== -1){
		return filePathOrName.substring(dotIndex + 1);
	} else {
		return '';
	}
}

const parseFileName = (filePathOrName) => {
	let uniformPath = filePathOrName.replace('\\', '/')
	let slashIndex = uniformPath.lastIndexOf('/');
	
	if(slashIndex !== -1){
		return uniformPath.substring(slashIndex + 1);
	} else {
		return uniformPath;
	}
}

const STORE_SIZE_GRADE = [
	['B', 1024],
	['KB', 1024 * 1024],
	['MB', 1024 * 1024 * 1024],
	['TB', 1024 * 1024 * 1024 * 1024],
	['PB', 1024 * 1024 * 1024 * 1024 * 1024]
]

/**
 * 字节长度转换成易读的格式
 */
export const bitSizeToElegantSize = (fileLength) => {
	let result = '--';
	if(fileLength){
		let maxGrade = STORE_SIZE_GRADE.slice(-1)[0];
		let maxLimit = maxGrade[1];
		let unit = maxGrade[0];
		let limit = maxLimit;
		if(fileLength < maxLimit){
			for(let grade of STORE_SIZE_GRADE){
				let gradeLimit = grade[1];
				if(fileLength < gradeLimit){
					unit = grade[0];
					limit = gradeLimit;
					break;
				}
			}
		}
		let factor = limit / 1024;
		let elegantSize = (parseFloat(fileLength) / factor).toFixed(1);
		result = `${elegantSize}${unit}`;
	}
	return result;
}
	
	
export default {
	parseFileName,
	parseFileSuffix,
	bitSizeToElegantSize
}