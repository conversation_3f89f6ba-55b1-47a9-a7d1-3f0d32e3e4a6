<template>
	<div>
		<!-- 解决横屏后的样式问题 -->
	</div>
</template>

<script>
export default {
	data() {
		return {};
	},
	onLoad(options) {
		// 退出页面时解除横屏
		// #ifdef APP-PLUS
		plus.screen.lockOrientation('portrait-primary'); //
		uni.navigateBack()
		uni.navigateBack()
		// #endif
		// setTimeout(()=>{
		// 	uni.navigateBack({
		// 		delta: 2
		// 	});
		// },200)
		
	},
	onBackPress() {
		// // #ifdef APP-PLUS
		// plus.screen.lockOrientation('portrait-primary'); //;
		// // #endif
	}
};
</script>

<style></style>
