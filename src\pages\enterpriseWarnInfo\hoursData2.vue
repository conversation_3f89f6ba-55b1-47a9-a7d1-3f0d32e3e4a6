<template>
	<!-- 企业信息 -->
	<div class='hour-box-wrapper' style="padding: 0rpx 30rpx;">
		<p class="zy-til1 ic4">近24小时振动数据</p>
		<div class="gap"></div>
		<!-- <web-view src="http://www.sthjjtxfj.com:8094/views/buffos/share.html?menuId=6dc4f93322e0456e81334f2c9aed68af"></web-view> -->
		<!-- 选择生产线 -->
		<ul class="zy-tabs1">
			<li :class="currentscx==item.SCXID?'cur':''" @click="changescx(item)" v-for="(item, index) in scxlist">
				{{ item.SCXMC }}
				<!-- <div class="zy-qipao" v-if='item.YCSL > 0'>!</div> -->
			<image v-if="item.ISYC == 'true'" src="../../static/warningRecord/images/jinggao.png" class="icon-img"></image>
			</li>
		</ul>
		<div class="gap"></div>
		<div class="title">
			<div class="legend">
				<div class="cell">
					<div class="icon">启动</div>
				</div>
				<div class="cell">
					<div class="icon" style="background-color: #ff7048;">停止</div>
				</div>
				<div class="cell">
					<div class="icon" style="background-color: #888888;">离线</div>
				</div>
			</div>
			<div style="position: absolute;right: 24rpx;float:right;z-index:220;">
				<text class="date-input" @click="open">{{dateStr}}</text>
			</div>
		</div>
		<div class="time-data-wrap"
			 id="table">
			<view class='equi-title' v-if="data2.length != 0">生产设备</view>
			<view>
				<!-- #ifdef APP-PLUS || H5 -->
				<view style="width: 100%;height:500rpx" :prop2="option2" :change:prop2="echarts.updateEcharts2"
					id="echarts2" class="echarts" ref="echarts2"></view>
				<!-- #endif -->
				<!-- #ifndef APP-PLUS || H5 -->
				<view>非 APP、H5 环境不支持</view>
				<!-- #endif -->
			</view>

			<view class='equi-title' v-if="data1.length != 0">治污设备</view>
			<view>
				<!-- #ifdef APP-PLUS || H5 -->
				<view style="width: 100%;height:400rpx" :prop1="option1" :change:prop1="echarts.updateEcharts1"
					id="echarts1" class="echarts" ref="echarts1"></view>
				<!-- #endif -->
				<!-- #ifndef APP-PLUS || H5 -->
				<view>非 APP、H5 环境不支持</view>
				<!-- #endif -->
			</view>

			<div class="ic-full" @click="toRunningData"></div>
		</div>
		<!-- 无每日时间状态显示 -->
    	<u-calendar v-model="show" :mode="mode" @change="changeDate"></u-calendar>
	</div>
</template>

<script>
	import {
		getInfo,
		getXssj
	} from '../../api/iot/realtime.js';
	import {
		sbsxzt,
		qyztrl,
		getScrl,
		getDwzb
	} from '../../api/iot/runningData.js';
	export default {
		data() {
			return {
				  webviewStyles: {
					progress: {
						color: '#FF3333',
					},
					width:'100px',
					height:'100'
				},
				WRYBH: '',
				userinfo: uni.getStorageSync('user_info'),
				enterpriseInfo: {},
				xzqhdm: '',
				scxlist: [],
				currentscx: '',
				currentscxname: '',
				datetimerange: [
					this.$dayjs()
					.subtract(1, 'day')
					.format('YYYY-MM-DD HH:mm'),
					this.$dayjs().format('YYYY-MM-DD HH:mm')
				],
				header: [],
				hoursData: [],
				hourBoxHeight: 'auto',
				date: this.$dayjs().format('YYYY-MM-DD'),
				dateStr: this.$dayjs().format('YYYY-MM-DD'),
				calendarData: {},
				showCalender: false,
				pickerOptions: {
					disabledDate(time) {
						return time.getTime() > Date.now();
					},
					cellClassName(date) {}
				},
				CXID: '1eaeda4a-e900-11ea-b918-286ed888c994',
				option1: {},
				option2: {},
				data1: {},
				data2: {},
				stateColors: {
					1: {
						color: '#888888',
						label: '停止'
					},
					3: {
						color: '#ff7054',
						label: '异常'
					},
					2: {
						color: '#7dcd27',
						label: '启动'
					},
				},
				show: false, //控制日历的弹出与收起, 默认false
				mode: 'date', //single-单个日期(默认值)，multiple-可以选择多个日期，range-选择日期范围（多个月需配合monthNum属性使用） 
				list: [
						{
							"JCSJ": "2022-08-20 00:02:14",  // 检测时间
							"NL": "40.84"                   // 振动能量
						},
						{
							"JCSJ": "2022-08-20 00:07:14",
							"NL": "44.65"
						},
						{
							"JCSJ": "2022-08-20 00:12:14",
							"NL": "42.93"
						}
					]
			};
		},
		props: {
			wrybh: {
				type: String,
				default: ''
			},
			headerHeight: {
				type: Number,
				default: 0
			},
			scrollHeight: {
				type: Number,
				default: 0
			},
			houseData2:{
				type:Object,
				default:()=>{}
			}
		},
		watch: {
			wrybh: {
				handler: function(nv) {
					this.WRYBH = nv;
					//this.initScxInfo();
				},
				immediate: true
			},
			houseData2:{
				handler: function(newVal) {
					console.log('newVal',newVal);
					this.scxlist = newVal.scxList;
					let scxLen = newVal.scxList.length;
					if (scxLen) {
						this.currentscx = newVal.scxList[0].SCXID;
						this.currentscxname = newVal.scxList[0].SCXMC;
					}
					//this.initXssj();
					
					this.handleRect();
					
					this.initChartData();
					
					//获取振动趋势
					this.getShakeTrend();
				},
					deep:true
			}

		},
		computed: {
			cellClass: function() {
				return function(data) {
					if (this.calendarData[data.day] === '0') {
						return 'date-cell0';
					} else if (this.calendarData[data.day] === '1') {
						return 'date-cell1';
					} else if (this.calendarData[data.day] === '2') {
						return 'date-cell2';
					} else if (this.calendarData[data.day] === '3') {
						return 'date-cell3';
					} else {
						return '';
					}
				};
			}
		},
		mounted() {
		},
		methods: {
			// 獲取生产线信息
			initScxInfo() {
				getInfo({
					WRYBH: this.WRYBH,
					XZQHDM: this.userinfo.orgid
				}).then(res => {
					this.scxlist = res.data.scxList;
					let scxLen = res.data.scxList.length;
					if (scxLen) {
						this.currentscx = res.data.scxList[0].SCXID;
						this.currentscxname = res.data.scxList[0].SCXMC;
					}
					//this.initXssj();

					this.handleRect();

					this.initChartData();
					
					//获取振动趋势
					this.getShakeTrend();
				});
			},
			//获取振动趋势
			getShakeTrend(){
				let obj = {
					 SCXID:this.currentscx,
                     DATE:this.dateStr
				}
				getDwzb(obj).then(res=>{
					console.log('res',res.data);
				})
			},
			// 小时数据
			initXssj() {
				getXssj({
					SCXID: this.currentscx,
					startT: this.datetimerange[0],
					endT: this.datetimerange[1]
				}).then(res => {
					const qtztMap = {
						0: '离线',
						1: '停止',
						2: '启动',
						'': ''
					};
					let scsbList = res.data.scxssjList;
					let zwxssjList = res.data.zwxssjList;

					let allequipment = scsbList.concat(zwxssjList);

					let columns = [];
					let columns1 = {};
					let colIndex = 1;
					let data = [];

					allequipment.forEach(item => {
						//
						if (!columns1[item.SBMC]) {
							columns1[item.SBMC] = colIndex++;
						}
						let index = columns.findIndex(items => {
							return items.SBMC == item.SBMC;
						});
						if (index == -1) {
							columns.push({
								SBMC: item.SBMC,
								SBLX: item.SBLX,
								ZBGX: item.ZBGX,
								SBID: item.SBID,
								IMEI: item.IMEI
							});
						}

						var hours = item.SJSJ && item.SJSJ.slice(11, 16);
						if (!data[hours]) {
							data[hours] = {
								key: item.SJSJ && item.SJSJ.slice(11, 16),
								hours: []
							};
						}
						var curColIndex = columns1[item.SBMC] - 1;

						data[hours]['hours'][curColIndex] = {
							val: item.QTZT,
							sbmc: item.SBMC,
							label: qtztMap[item.QTZT]
						};
					});
					console.log(data);
					// 表头数据
					this.header = columns;
					this.hoursData = []
					// 解决视图不更新
					for (let i in data) {
						this.hoursData.push(data[i]);
					}
				});
			},
			toRunningData() {
				uni.navigateTo({
					url: `/pages/runningData/Index?SCXID=${this.currentscx}&DATE=${this.dateStr}`
				})
			},
			// 生产线点击
			changescx(val) {
				this.currentscx = val.SCXID;
				this.currentscxname = val.SCXMC;
				//this.initXssj();
				this.gettjt();
				this.getDayData();
			},
			// 时间控件change事件
			change(v) {
				console.log('v',this.datetimerange);
				this.datetimerange = v;
				this.initXssj();
			},
			// 到生产线详情页
			toEquipmentInfo(v) {
				uni.navigateTo({
					url: `./equipmentInfo?SBID=${v.SBID}&IMEI=${v.IMEI}&SBMC=${v.SBMC}&SCXMC=${this.currentscxname}`
				});
			},
			handleRect() {
				const self = this;
				this.$nextTick(() => {
					setTimeout(function() {
						let queryDom = uni.createSelectorQuery().in(self) // 使用api并绑定当前组件
						let promiseTop = new Promise((resolve) => {
							queryDom
								.select('.hour-box-wrapper')
								.boundingClientRect((res) => {
									let top = res.top;
									// console.log(res)

									resolve(top);
								})
								.exec()
						})

						let promiseBottom = new Promise((resolve) => {
							queryDom
								.select('.time-data-wrap')
								.boundingClientRect((res) => {
									let bottom = res.top;
									// console.log(res)

									resolve(bottom);
								})
								.exec()

						})

						Promise.all([promiseTop, promiseBottom]).then(res => {
							// let hourBoxHeight = (self.screenHeight - (res[1] - res[0]) - self.headerHeight);

							// if(!plus.navigator.hasNotchInScreen()){
							// 	hourBoxHeight = hourBoxHeight - plus.navigator.getStatusbarHeight();
							// }

							let hourBoxHeight = (self.scrollHeight - (res[1] - res[0]));
							self.hourBoxHeight = hourBoxHeight + 'px';
						})
					}, 500);

				})

			},
			open() {
				//打开有日期状态的日历
				//this.$refs.calendar.open();
				//打开如日期状态的日历
				this.show = true;
			},
			changeDate(e){
				this.dateStr = `${e.year}-${e.month<10?'0'+e.month:e.month}-${e.day<10?'0'+e.day:e.day}`
				this.gettjt();
			},
			confirm(e) {
				console.log(e);
				this.dateStr = e.fulldate
				this.gettjt();
			},
			onMonthChange(e) {
				let month = e.year + "-" + (e.month <= 9 ? '0' + e.month : e.month)
				this.date = month;
				this.getDayData();
			},
			initChartData() {
				this.getDayData();
				this.gettjt();
			},
			getDayData() {
				getScrl({
					CXID: this.currentscx,
					MON: this.$dayjs(this.date).format('YYYY-MM')
				}).then(res => {
					let data = res.data;
					let map = {};
					if (data && data.length > 0) {
						data.forEach(item => {
					    	let date = this.$dayjs(this.date).format('YYYY-MM')+'-'+item.days
							map[date] = item.zt.toString();
							//map[item.days.toString()] = item.zt.toString();
						});
					}
					this.calendarData = map;
					console.log('calendarData',this.calendarData);
				});
			},
			gettjt(scrq) {
				let that = this;
				sbsxzt({
					 SCXID: this.currentscx,
					DATE: this.dateStr,
				}).then(res => {
					res.data.zwList.forEach(x => {
						x.name = x.SBMC;
						// x.ztList = JSON.parse(x.ztList);
						if(x.ztList && x.ztList.length > 0){
							x.list = x.ztList;
							x.list.forEach(y => {
								y.end = that
									.$dayjs(y.end)
									.format('YYYY-MM-DD HH:mm');
							});
						}
					});
					res.data.scList.forEach(x => {
						x.name = x.SBMC;
						// x.ztList = JSON.parse(x.ztList);
						if(x.ztList && x.ztList.length > 0){
							x.list = x.ztList;
							x.list.forEach(y => {
									// y.end = that
									// 	.$dayjs(y.end)
									// 	.add(1, 'minute')
									// 	.format('YYYY-MM-DD HH:mm');
									y.end = that
										.$dayjs(y.end)
										.format('YYYY-MM-DD HH:mm');
							});
						}
					});
					this.data2 = res.data.scList;
					this.data1 = res.data.zwList;
					this.option2 = this.setChart(res.data.scList);
					this.option1= this.setChart(res.data.zwList);
				});
			},			
			setChart(data) {
										let statusObj = {
											1: {
												color: '#ff7054',
												label: '停止'
											},
						
											2: {
												color: '#7dcd27',
												label: '开启'
											},
						
											0: {
												color: '#edf0f3',
												label: '离线'
											},
										};
						
										let x = [];
						
										let minutes = 0;
						
										let jszj = '';
						
										if (this.dateStr == this.$dayjs().format('YYYY-MM-DD')) {
											minutes = this.getcurrentallminute();
						
						 					jszj = this.getcurrenthhmm();
											
											//minutes = 1440;
														
											//jszj = '23:59';
										} else {
											minutes = 1440;
						
											jszj = '23:59';
										}
						
										for (let i = 0; i < minutes; i++) {
											let num = i;
						
											let h = Math.floor(num / 60);
						
											let min = num % 60;
						
											h = (h <= 9 ? '0' : '') + h;
						
											min = (min <= 9 ? '0' : '') + min;
											x.push(h + ':' + min);
						
										}
						
										let y = [];
						
										let lines = [];
						
										data.forEach((v, i) => {
											y.push(v.name);
											//背景状态（如果数据中有可以去掉）
											lines.push({
												name: '断电',
						
												z: 1,
						
												type: 'lines',
						
												coordinateSystem: 'cartesian2d',
						
												silent: true,
						
												lineStyle: {
													width: 24,
						
													color: '#edf0f3',
						
													opacity: 1
												},
						
												data: [{
													name: v.name,
						
													coords: [
														['00:00', v.name],
														[jszj, v.name]
													]
												}]
											});
						
											v.list && v.list.forEach((item, j) => {
												let xVal1 = item.start.slice(11, 16);
						
												let xVal2 = item.end.slice(11, 16);
						
												let obj = statusObj[v.list[j].status];
						
												lines.push({
													name: obj.label,
						
													z: 2,
						
													type: 'lines',
						
													coordinateSystem: 'cartesian2d',
						
													lineStyle: {
														width: 24,
						
														color: obj.color || '#888888',
						
														opacity: 1
													},
						
													data: [{
														name: v.name,
						
														start: item.start,
						
														end: item.end,
						
														statusLabel: obj.label,
						
														coords: [
															[xVal1, v.name],
															[xVal2, v.name]
														]
													}]
												});
											});
										});
						
						
										
										let option = {
											tooltip: {
												show: true,
												axisPointer: {
													type: 'cross',
													crossStyle: {
														color: '#4874ff'
													},
													label: {
														 color: '#fff',
														 backgroundColor : '#4874ff'
													 }
												},
												backgroundColor: "rgba(255,255,255,0)",
												padding: [0, 0],
										
											
											},
											grid: {
												top: 10,
												left: 15
											},
											xAxis: {
												type: 'category',
												data: x,
												interval: 0,
												axisLabel: {
													// formatter(v) {
													//     let s = v.split(':')[1];
													//     if (s % 15 === 0) {
													//         return v;
													//     } else {
													//         return '';
													//     }
													// },
													show: true,
													textStyle: {
														color: '#999',
												    }
												},
												axisLine: {
													show: true,
													lineStyle: {
														color: '#999'
													}
												},
												axisTick: {
													show: true
												}
											},
											yAxis: {
												type: 'category',
												data: y,
												axisLabel: {
													interval: 0,
													show: true,
													inside: true,
													textStyle: {
														color: '#fff'
													},
													padding: [2, 0, 0, 0]
												},
												axisLine: {
													show: false,
													lineStyle: {
														color: 'black'
													}
												},
												axisTick: {
													show: false
												},
												z:3
											},
											series: lines
										};
					
										
										//this.option1 = option;
										return option
										//let str = JSON.stringify(this.option2)
										//console.log('str',str);
			
            },
			
			getcurrentallminute() {
				let hour = this.$dayjs().format('HH');
				let minutes = this.$dayjs().minute();
				return hour * 60 + minutes +1;
			},
			getcurrenthhmm() {
				return this.$dayjs().add(1, 'minute').format("HH:mm");
			},
			getClass(date) {
				console.log(date);
			},
		
		}
	};
</script>
<script module="table" lang="renderjs">
	export default {
		data() {
			return {}
		},
		methods: {
			scrollEvent() {
				// 头部随滚动条滚动
				this.$refs.th.style.transform = `translate(-${this.$refs.table.scrollLeft}px)`
			}
		},
		mounted() {}
	}
</script>

<script module="echarts" lang="renderjs">
	let myChart1;
	let myChart2;
	export default {
		data() {
			return {
				chartw: "",
				charth: '',
				flag: false,
			}
		},
		onLoad() {
			

		},
		mounted() {
			if (typeof window.echarts === 'function') {
				this.initEcharts()
			} else {
				// 动态引入较大类库避免影响页面展示
				const script = document.createElement('script')
				// view 层的页面运行在 www 根目录，其相对路径相对于 www 计算
				script.src = 'static/echarts4.9.0.js'
				script.onload = this.initEcharts.bind(this)
				document.head.appendChild(script)
			}
			
		},
		methods: {
			initEcharts() {
				let echarts1 = document.getElementById('echarts1');
				let echarts2 = document.getElementById('echarts2');
				myChart1 = echarts.init(echarts1)
				myChart2 = echarts.init(echarts2)
			
				let option1 = {};
				if (this.option1 && this.option1.tooltip && this.option1.tooltip.yAxis) {
					option1 = {
						...this.option1
					};
			
					option1.tooltip.formatter = function(obj) {
						let data = obj.data;
						return `<p>${data.name}</p>
				              <p>开始时间：${data.start}</p>
				              <p>结束时间：${data.end}</p>
				              <p>状态：${data.statusLabel}</p>
				            `;
					}
					option1.tooltip.confine = true;
				}
				myChart1 && myChart1.setOption(option1)
				
				let option2 = {};
				if (this.option2 && this.option2.tooltip){
					option2 = {
						...this.option2
					};
					option2.tooltip.formatter = function(obj) {
						let data = obj.data;
						return `<p >${data.name}</p>
				              <p>开始时间：${data.start}</p>
				              <p>结束时间：${data.end}</p>
				              <p>状态：${data.statusLabel}</p>
				            `;
					}
				}
				myChart2 && myChart2.setOption(option2)
			},
			updateEcharts1(newValue, oldValue, ownerInstance, instance) {
				let echarts1 = document.getElementById('echarts1');
				echarts1.style.height = newValue.yAxis.data.length * 32 + 70 + 'px';
				if (!newValue) {
					return;
				}
				//自定义状态的文字和颜色   
				let option = JSON.parse(JSON.stringify(newValue));
				// 渲染后触发的option1
				option.tooltip.formatter = function(obj) {
					let data = obj.data;
					return `<div style="background:${data.statusLabel== '停止'?'#ff7054':(data.statusLabel== '开启'?'#7dcd27':'#ccc')};color:#fff;padding:6px;opacity:0.9;border-radius:6px;border:1px solid #ddd;">          ${data.name}</br>
				       开始时间：${data.start.slice(0,16)} </br>
				       结束时间：${data.end} </br>
				       状态：${data.statusLabel} </br>
				     </div>`;
				}
				option.tooltip.confine = true;
				myChart1 && myChart1.clear()
				myChart1 && myChart1.setOption(option)
				myChart1 && myChart1.resize()
			},

			updateEcharts2(newValue, oldValue, ownerInstance, instance) {
				let echarts2 = document.getElementById('echarts2');
				echarts2.style.height = newValue.yAxis.data.length * 32 + 70 + 'px';
				//自定义状态的文字和颜色
				if (!newValue) {
					return;
				}
				let option = JSON.parse(JSON.stringify(newValue));
				// 渲染后触发的option2
				option.tooltip.formatter = function(obj) {
					let data = obj.data;
				 return `<div style="background:${data.statusLabel== '停止'?'#ff7054':(data.statusLabel== '开启'?'#7dcd27':'#888')};color:#fff;padding:6px;opacity:0.9;border-radius:6px;border:1px solid #ddd;">          ${data.name}</br>
				       开始时间：${data.start.slice(0,16)} </br>
				       结束时间：${data.end} </br>
				       状态：${data.statusLabel} </br>
				     </div>`;
				}
				option.tooltip.confine = true;
				myChart2 && myChart2.clear()
				myChart2 && myChart2.setOption(option)
				myChart2 && myChart2.resize()
			},

			format(date, fmt) {
				var o = {
					"M+": date.getMonth() + 1, //月份 
					"d+": date.getDate(), //日 
					"h+": date.getHours(), //小时 
					"m+": date.getMinutes(), //分 
					"s+": date.getSeconds(), //秒 
					"q+": Math.floor((date.getMonth() + 3) / 3), //季度 
					"S": date.getMilliseconds() //毫秒 
				};
				if (/(y+)/.test(fmt)) {
					fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
				}
				for (var k in o) {
					if (new RegExp("(" + k + ")").test(fmt)) {
						fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k])
							.length)));
					}
				}
				return fmt;
			},
		}
	}
</script>

<style scoped lang="scss">
	.title {
		display: flex;
		position: relative;
		padding: 18rpx 0;
		align-items: center;
	}

	.title .legend {
		display: flex;
		border-radius: 18rpx;
	}

	.title .legend .cell {
		display: flex;
		align-items: center;
		color: #000000;
	}


	.title .legend .cell .icon {
		height: 48rpx;
		line-height: 48rpx;
		width: 72rpx;
		background-color: #7dcd27;
		color: #fff;
		font-size: 26rpx;
	}

	.title .legend .cell:first-child .icon,
		{
		border-radius: 10rpx 0 0 10rpx;
	}

	.title .legend .cell:last-child .icon {
		border-radius: 0 10rpx 10rpx 0;
	}

	.title .legend .cell .text {
		margin: 0 16rpx;
		font-size: 28rpx;
	}

	.date-cell0 {
		background-color: #b7b5b6;
	}

	.date-cell1 {
		background-color: #0ff;
	}

	.date-cell2 {
		background-color: #f00;
	}

	.date-cell3 {
		background-color: #ff6100;
	}

	.time-data-wrap {
		display: flex;
		flex-direction: column;
		width: 100%;
		position: relative;
	}

	.time-data-wrap .left {
		width: 120rpx;
		position: relative;
		z-index: 1;
	}


	/* #ifdef APP-PLUS */
	// .time-data-wrap .left div {
	// 	width: 120rpx;
	// 	height: 48rpx;
	// 	box-sizing: border-box;
	// 	background-color: #fff;
	// 	margin: 10rpx 0;
	// 	border: 1px solid #0000FF;
	// }
	/* #endif */
	/* #ifdef H5 */
	// .time-data-wrap .left div {
	// 	width: 120rpx;
	// 	height: 48rpx;
	// 	box-sizing: border-box;
	// 	background-color: #fff;
	// 	margin: 8rpx 0;
	// }
	/* #endif */
.icon-img{
    width: 60rpx;
    height: 40rpx;
    position: relative;
    right: -16rpx;
    top: -24rpx;
}


	.time-data-wrap .right .tb {
		width: calc(100vw - 146rpx);
		overflow: auto;
	}

	.time-data-wrap .left .tb div {
		float: left;
		height: 48rpx;
		box-sizing: border-box;
		margin: 10rpx 5rpx;
	}

	.time-data-wrap .right {}

	.time-data-wrap .right .tb div.tr {
		float: left;
		height: 48rpx;
		box-sizing: border-box;
		margin: 10rpx 5rpx;
	}

	.time-data-wrap .right .tb div.td {
		margin: 0 5rpx;
	}

	.th {
		position: sticky;
		top: 120rpx;
	}

	.th div {
		width: 200rpx;
		white-space: nowrap;
		margin: 10rpx 5rpx;
		overflow: hidden;
		height: 48rpx;
		display: flex;
		align-items: center;
	}

	.hours-data {
		width: 200rpx;
		border-radius: 12.077325rpx;
		flex-shrink: 0;
	}

	.mark {
		background-color: #fff;
		color: #fff;
		width: 28rpx;
		height: 40vh;
		right: 0;
		z-index: 666;
		position: absolute;
	}

	.th-item {
		box-sizing: border-box;
	}

	.th-item image {
		height: 35rpx;
		width: 35rpx;
	}

	.zy-tabs1 {
		flex-wrap: wrap;

		li {
			margin-top: 10rpx;
		}
	}

	.date-input {
		float: right;
		width: 200rpx;
		height: 50rpx;
		color: #000000;
		line-height: 50rpx;
		font-size: 26rpx;
		cursor: pointer;
		padding-left: 10rpx;
		border-radius: 10rpx;
		border: 2rpx solid rgb(50, 131, 183);
		outline: none;
	}

	.equi-title {
		font-size: 27rpx;
		font-weight: bold;
	}
		
	.cell{
		color:#333!important;
	}
</style>
