import fileUtil from './file.js'

/**
 * 根据路径或文件名判断是否图片文件 
 */
const isPicture = (path) => {
	let suffix = fileUtil.parseFileSuffix(path)
	let regex = /jpg|png|jpeg|svg|bmp|gif/i
	return regex.test(suffix)
}

/**
 * 根据路径或文件名判断是否视频文件 
 */
const isVideo = (path) => {
	let suffix = fileUtil.parseFileSuffix(path)
	let regex = /avi|mp4|mpg|mpeg|swf|flv/i
	return regex.test(suffix)
}

/**
 * 根据路径或文件名判断是否音频文件 
 */
const isAudio = (path) => {
	let suffix = fileUtil.parseFileSuffix(path)
	let regex = /mp3/i
	return regex.test(suffix)
}

export default {
	isPicture,
	isVideo,
	isAudio
}

