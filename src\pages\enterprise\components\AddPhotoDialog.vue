<!-- @format -->

<template>
	<div style="width: 100%; height: 100%;position:absolute;top:0;left:0;" v-show="showAddPhotoDialog">
		<div class="mask" style="display: block"></div>
		<div class="yj-alert" :style="{top:scrollTop + 'px'}">
			<h3>企业图片上传</h3>

			<div class="item nfx">
				<p class="label">
					治污线全景图
				</p>
				<div class="gap"></div>
				<AppendixImage ref="appendix" :list.sync="fileList" fileTypeKeyword="ZDFJ" childTypeKeyword="ZWQJT"
					 :uploadId="WRYBH" :isCanEdit="true" :maxImagesLength="1" ></AppendixImage>
			</div>

			<p class="p1">图片名称:</p>
			<input type="text" v-model="wjmc" placeholder="请输入图片名称">

			<div class="zy-line ac jc">
				<p class="btn1" @click="cancel">取消</p>
				<p class="btn2" @click="edit">编辑</p>
				<p class="btn2" @click="save">保存</p>
			</div>
			<div class="warn">
				<i class="star">*</i> 注意：可编辑数据为优化数据最近的一条数据
			</div>
		</div>
		<u-top-tips ref="uTips"></u-top-tips>
	</div>
</template>

<script>
	import AppendixImage from '@/pages/component/AppendixImage'
	import {
		getFilelist,
		deletefile,
		downloadFile
	} from '@/api/iot/appendix.js';
	import {
		API_LOGIN_SERVICE_URL,
		LOGIN_ULR_BASE,
		UPLOAD_URL,
		DOWNLOAD_URLZDY,
		PREVIEW_FILE_URL
	} from '@/common/config.js'
	export default {
		props: {
			info: {
				type: Object,
				default: () => null
			},
			showAddPhotoDialog: {
				type: Boolean,
				default: false
			},
			WRYBH:{
				type:String,
				default:''
			}
		},
		data() {
			return {
				scrollTop: 0,
				halfWindowHeight: 0
			};
		},
		mounted() {
			this.userinfo = uni.getStorageSync('userInfo')
			let self = this;
			uni.$on('onPageScroll', function(data) { //接收参数
				self.scrollTop = data;
			});
			uni.getSystemInfo({
				success: function(res) {
					self.halfWindowHeight = res.windowHeight / 2;

				}
			})

		},
		methods: {
			cancel() {
				this.$emit('update:showAddPhotoDialog', false)
				this.FKNR = ''
			},
			save() {
				if (this.FKNR == '') {
					uni.showToast({
						title: '请填写反馈内容',
						icon: "none",
						duration: 1000
					})
					return
				}

				if (this.saveType) {
					//编辑
					this.saveEdit()
				} else {
					//保存
					this.add()
				}
			},
			//预警反馈录入
			add() {
				let obj = {
					FKNR: this.FKNR, //反馈内容
					FKR: this.userinfo.name, //反馈人 
					FXID: this.info.FXID, //分析ID
					WRYBH: this.info.WRYBH, //污染源编号
				};

				yhjladd(obj).then((res) => {
					if (res.data.status == '000') {
						uni.showToast({
							title: '添加成功',
							duration: 1000
						}).then(res => {
							setTimeout(() => {
								this.$emit('update:showAddPhotoDialog', false)
								//信号弱组件更新优化记录列表
								uni.$emit('getOptimization');
								this.FKNR = ''
							}, 300);

						})
					}
				});
			},
			//编辑第一条数据
			edit() {
				if (!this.firstSituation) {
					uni.showToast({
						title: '没有可编辑内容',
						icon: "none",
						duration: 1000
					})
					return
				}
				//点击获取第一条数据
				this.FKNR = this.firstSituation.FKNR;
				this.saveType = 1
			},
			saveEdit() {
				let obj = {
					FKNR: this.FKNR, //反馈内容
					FKR: this.userinfo.name, //反馈人             
					FKID: this.firstSituation.FKID
				};
				yhjledit(obj).then((res) => {
					if (res.data.status == '000') {
						uni.showToast({
							title: '修改成功',
							duration: 1000
						}).then(res => {
							setTimeout(() => {
								this.$emit('update:showAddDialog', false)
								//信号弱组件更新优化记录列表
								uni.$emit('getOptimization');
								this.FKNR = ''
							}, 300);
						})
					}
				});
			},
			radioChange(v) {
				this.curValid = v.target.value;
			}
		}
	};
</script>

<style scoped>
	.yj-alert {
		height: 650rpx;
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		margin: auto;
		width: 600rpx;
		background: #FFFFFF;
		border-radius: 12px;
		z-index: 1111;
		box-sizing: border-box;
		padding: 20rpx;
	}

	.star {
		color: #f00;
		padding-right: 10rpx;
	}

	.warn {
		padding: 30rpx 20rpx;
		color: #bbb;
		font-size: 26rpx;
	}

	.yj-alert input {
		width: 100%;
		background: #FCFCFC;
		border-radius: 4rpx;
		border: 2rpx solid #E3E3E3;
		font-size: 30rpx;
		color: #333;
		line-height: 60rpx;
		height: 60rpx;
		box-sizing: border-box;
		padding: 14rpx 20rpx;
		resize: none;
		margin-bottom: 30rpx;
		font-family: "Microsoft YaHei";
	}

	.uni-label-pointer {
		cursor: pointer;
		display: flex;
		flex-direction: row;
		margin-right: 60rpx;
	}

	.radio-group {
		display: flex;
	}

	.radio-group .uni-list-cell {
		margin-right: 10rpx;
	}

	radio {
		transform: scale(0.7)
	}

	/deep/.uni-textarea-textarea {
		text-align: left;
	}

	.yj-alert .btn1,
	.yj-alert .btn2 {
		height: 60rpx;
		line-height: 60rpx;
	}

	.yj-alert .textarea1 {
		margin-bottom: 60rpx;
	}
</style>
