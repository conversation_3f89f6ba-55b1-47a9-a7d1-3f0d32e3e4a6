/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-01-28 11:03:28
 * @LastEditTime: 2021-07-30 14:42:26
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /SmartFormWeb/api/print-template.js
 */
import {
	postPrintForm,
} from '@/api/record.js'
import CryptoJS from 'crypto-js';
import {
	LOGIN_ULR_BASE,
	DOWNLOAD_URL
} from '@/common/config.js'
import attachFile from '@/api/attach.js'
import fileService from '@/api/file-service.js';

export default {
	/**
	 * @description: 预览图片功能，通过两个服务获取预览图，先获取 WJID，再用WJID去加载图片，通过BASE64转译可获得最终的图片数组
	 * @param { String } recordId 表单唯一编号
	 * @param { String } templateId 模板号
	 * @param { Boolean } type 表单是否重新加载的状态，true为重新加载最新模板，false为用缓存的模板
	 * @return { urlList } 返回的预览图片的数组
	 */
	getPrintUrl(data, type) {
		return new Promise(function(resolve) {
			postPrintForm({
				printlist:data,
				regenerate: true,
			}).then((res) => {
				let id = res.WJID;
				uni.showLoading({
					title: '加载表单中'
				})
				uni.request({
					url: LOGIN_ULR_BASE + '/file/prveview/' + id,
					success: (data) => {
						let url = LOGIN_ULR_BASE + '/file/showimagefile/';
						let filePath = data.data.data; //文件数组、
						let urlList = []
						filePath.forEach(element => {
							urlList.push(url + CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(element)))
						});
						let fanalUrl = CryptoJS.enc.Utf8.parse(filePath[0]);
						fanalUrl = CryptoJS.enc.Base64.stringify(fanalUrl);
						uni.hideLoading()
						resolve(urlList);
					}
				})
			}).catch((res)=>{
				uni.showToast({
					icon: 'none',
					title: res.error_msg,
					duration: 2000
				});
			})
		});
	},

	/**
	 * @description: 打印文件 获取预览图片后保存在本地，之后保存到本地之后调用方法获取本地的地址后连接打印机
	 * @param {String} recordId 表单唯一编号
	 * @param {String} templateId 模板号
	 * @return {*}
	 */
	getCallPrinter(data,type) {
		postPrintForm({
			printlist:data,
			regenerate: type,
		}).then((res) => {
			uni.downloadFile({
				url: DOWNLOAD_URL + '?wdbh=' + res.WJID, //仅为示例，并非真实的资源
				success: (ress) => {
					let filePath = ress.tempFilePath;
					uni.saveFile({
						tempFilePath: filePath,
						success: function(r) {
							let Printer = plus.android.importClass('com.bovosz.webapp.print.Printer')
							Printer.printAppPlusPDFFile(plus.android.runtimeMainActivity(), r.savedFilePath)
						}
					});
				}
			});
		}).catch((res)=>{
			uni.showToast({
				icon: 'none',
				title: res.error_msg,
				duration: 2000
			});
		})
	},

	/**
	 * @description: 分享APP类型的笔录
	 * @param {*}
	 * @return {*}
	 */	
	getShareInfo(data,name){
		postPrintForm({
			printlist: data
		})
		.then((res) => {
			let urls = LOGIN_ULR_BASE + '/file/showimagefilebywjid/' + res.WJID
			uni.shareWithSystem({
				summary: name,
				href: urls,
				success() {
					// 分享完成，请注意此时不一定是成功分享
				},
				fail() {
					// 分享失败
				},
			});
		})
		.catch(() => {});
	},

	/**
	 * @description: 分享二维码的信息
	 * @param {*}
	 * @return {*}
	 */	
 	getShareQrcode(data){
		return new Promise(function(resolve) {
			postPrintForm({
				printlist:data,
			}).then((res) => {
				let downLoadUrl = DOWNLOAD_URL + "?wdbh=" + res.WJID;
				let urls = LOGIN_ULR_BASE + '/file/showimagefilebywjid/' + res.WJID
				resolve(urls);	
			}).catch((res)=>{
				uni.showToast({
					icon: 'none',
					title: res.error_msg,
					duration: 2000
				});
			})
		});
	 },

	/**
	 * @description: 打开文件
	 * @param {*} data
	 * @return {*}
	 */	
	getOpenInfo(data){
		postPrintForm({
			printlist: data
		}).then((res)=>{
			fileService.openDocument(res.WJID,"name")
		})
	}
}
