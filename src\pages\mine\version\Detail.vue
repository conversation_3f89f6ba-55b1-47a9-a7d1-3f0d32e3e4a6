<template>
	<div>
		<div v-if="info.BBSM" class="detail-form" >
			{{info.BBSM}}
		</div>
		<u-empty v-else text="暂无数据" mode="list" ></u-empty>
	</div>

</template>

<script>
	export default {
		data() {
			return {
				info: {},
			};
		},
		onLoad(options) {
			this.info = JSON.parse(decodeURIComponent(options.item))
		},
		mounted() {},
		methods: {
		}
	};
</script>


<style scoped>
	.detail-form {
		padding: 20rpx;
		white-space: pre-line;
		line-height: 90rpx;
		font-size: 31rpx;
		color: #333;
	}
</style>
