@charset "utf-8";


/** 
 * mobile reset
 **/
html, body, div, span, ol, ul, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video { margin: 0; padding: 0; border: 0; font-family: inherit; font-style: normal; font-weight: normal; font-size: 100%; vertical-align: baseline; }
ol, ul { list-style: none; }
input, button, textarea, select {-webkit-appearance: none; appearance: none; box-sizing: border-box; border-radius: 0; padding: 0; margin: 0; border: none; outline: none;}
table { border-collapse: collapse; border-spacing: 0; }
caption, th, td { font-weight: normal; vertical-align: middle; }
q, blockquote { quotes: none; }
q:before, q:after, blockquote:before, blockquote:after { content: ""; content: none; }
a img { border: none; }
article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section, summary { display: block; }
a { text-decoration: none; }
a:hover, a:active { outline: none; }
html { font-family: Helvetica, "STHeiti STXihei", "Microsoft JhengHei", "Microsoft YaHei", "Noto Sans CJK SC", "Source Han Sans CN" Tohoma, Arial; -webkit-text-size-adjust: 100%; -webkit-tap-highlight-color: transparent; height: 100%; overflow-x: hidden;}
body { color: #333; background-color: #fff; -webkit-backface-visibility: hidden; /*line-height: 1;*/ height: 100%;}
