<!-- @format -->

<template>
    <body style="background-color: #f1f2f6" class="warp">
        <header class="header">
            <div class="title">添加治污设备</div>
            <i class="pd-edtbtn" @click="back">关闭</i>
        </header>
        <scroll-view
            scroll-y
            style="height: calc(100vh - 78rpx)"
            @scrolltolower="getMore"
        >
            <section class="main">
                <div class="inner">
                    <div class="pd-tit1"><strong>基本信息</strong></div>
                    <u-form
                        :model="model"
                        :rules="rules"
                        ref="uForm"
                        :errorType="errorType"
                    >
                        <!-- 	<u-form-item :label-position="labelPosition" label="所属企业" required prop="SSQY" :label-width="labelWidth">
							<u-input :border="border" v-model="model.SSQY" placeholder="请填写所属企业"></u-input>
						</u-form-item> -->

                        <u-form-item
                            required
                            :label-position="labelPosition"
                            label="设备名称"
                            prop="SBMC"
                            :label-width="labelWidth"
                        >
                            <u-input
                                :border="border"
                                placeholder="请填写设备名称"
                                v-model="model.SBMC"
                            />
                        </u-form-item>

                        <u-form-item
                            required
                            :label-position="labelPosition"
                            label="所属生产线"
                            prop="SSSCX"
                            :label-width="labelWidth"
                        >
                            <u-input
                                type="select"
                                :select-open="scxShow"
                                @click="scxShow = true"
                                :border="border"
                                placeholder="请填写所属生产线"
                                v-model="SSSCX"
                            />
                        </u-form-item>
                        <u-form-item
                            :label-position="labelPosition"
                            label="工艺类型"
                            required
                            prop="GYLX"
                            :label-width="labelWidth"
                        >
                            <u-input
                                :border="border"
                                type="select"
                                :select-open="gylxShow"
                                v-model="model.GYLX"
                                placeholder="请选择行业分类"
                                @click="gylxShow = true"
                            ></u-input>
                        </u-form-item>

                        <u-form-item
                            :label-position="labelPosition"
                            label="设备类型"
                            required
                            prop="GYLX_SBLX"
                            :label-width="labelWidth"
                        >
                            <u-input
                                :border="border"
                                type="select"
                                :select-open="sblxShow"
                                v-model="model.GYLX_SBLX"
                                placeholder="请选择治污设备组织方式"
                                @click="selectSblx()"
                            ></u-input>
                        </u-form-item>
                        <u-form-item
                            required
                            :label-position="labelPosition"
                            label="设备位置"
                            prop="SBWZ"
                            :label-width="labelWidth"
                        >
                            <u-input
                                disabled
                                :border="border"
                                placeholder="请获取位置"
                                v-model="SBWZ"
                            />
                            <u-icon name="map" @click="getJwd"></u-icon>
                        </u-form-item>

                        <u-form-item
                            :label-position="labelPosition"
                            label="绑定智能设备"
                            prop="IMEI"
                            :label-width="labelWidth"
                        >
                            <u-input
                                :border="border"
                                placeholder="可扫码获取设备id"
                                v-model="model.IMEI"
                            />
                            <u-icon name="scan" @click="scanCode"></u-icon>
                        </u-form-item>

                        <u-form-item
                            label-position="top"
                            label="备注内容"
                            prop="BZNR"
                            :label-width="labelWidth"
                        >
                            <u-input
                                type="textarea"
                                :border="border"
                                height="200"
                                placeholder="填写备注内容"
                                v-model="model.BZ"
                            />
                        </u-form-item>
                        <u-form-item
                            :label-position="labelPosition"
                            label-width="150"
                        >
                            <u-upload
                                width="160"
                                height="160"
                                :action="uploadUrl"
                                :auto-upload="autoUpload"
                                ref="uUpload"
                                max-count="3"
                                :show-progress="true"
                                :header="{
                                    jwtToken: token
                                }"
                                :before-upload="beforeUpload"
                                @on-remove="deleteFile"
                                :form-data="{
                                    LXDM: 'ZDFJ',
                                    ZLXDM: 'ZDFJLB',
                                    YWSJID: ''
                                }"
                            ></u-upload>
                        </u-form-item>
                    </u-form>
                    <ul class="pd-ulbtn1">
                        <li class="on" @click="save">保存</li>
                    </ul>
                </div>

                <u-select
                    value-name="SCXID"
                    label-name="SCXMC"
                    mode="single-column"
                    :list="scxList"
                    v-model="scxShow"
                    @confirm="getScx"
                ></u-select>
                <u-select
                    value-name="DM"
                    label-name="DMNR"
                    mode="single-column"
                    :list="gylxList"
                    v-model="gylxShow"
                    @confirm="getGylx"
                ></u-select>
                <u-select
                    value-name="DM"
                    label-name="DMNR"
                    mode="single-column"
                    :list="sblxList"
                    v-model="sblxShow"
                    @confirm="getSblx"
                ></u-select>
            </section>
        </scroll-view>
    </body>
</template>

<script>
import { getGgdmz } from '@/api/iot/ggdmz.js';
import { saveZwsb, getScx } from '@/api/iot/enterprise.js';
import { DOWNLOAD_URLZDY, UPLOAD_URL } from '@/common/config.js';
import enterprise_store from '../enterprise.store.js';
export default {
    watch: {
        JWD: {
            handler: function (v) {
                this.model.JD = v.JWD.longitude;
                this.model.WD = v.JWD.latitude;
                this.SBWZ = `${v.JWD.longitude} ~ ${v.JWD.latitude}`;
            },
            deep: true //深度监听
        }
    },
    data() {
        return {
            JWD: enterprise_store.state,
            autoUpload: false,
            uploadUrl: UPLOAD_URL,
            token: '',
            enterpriseInfo: {},
            info: {},
            model: {
                SBMC: '',
                ORGID: '',
                CJR: '',
                SCXID: '',
                GYLX: '',
                GYLX_SBLX: '',
                IMEI: '',
                JD: '',
                WD: '',
                BZ: '' //备注内容
            },
            GYLX: '', //产污设备组织方式
            SBLX: '', //治污设备组织方式
            SSSCX: '',
            SBWZ: '',

            gylxShow: false,
            gylxList: [],
            sblxShow: false,
            sblxList: [],
            scxShow: false,
            scxList: [],
            rules: {
                SSQY: [
                    {
                        required: true,
                        message: '请选择所属企业',
                        trigger: 'change'
                    }
                ],
                SCXID: [
                    {
                        required: true,
                        message: '请选择所属生产线'
                    }
                ],
                GYLX: [
                    {
                        required: true,
                        message: '请选择工艺类型'
                    }
                ],
                SBLX: [
                    {
                        required: true,
                        message: '请选择设备类型'
                    }
                ]
            },
            border: false, //input 是否显示边框, 默认false
            labelWidth: 200,
            selectShow: false,
            labelPosition: 'left', //表单域提示文字的位置，left-左侧，top-上方
            errorType: ['border-bottom'], //错误的提示方式，数组形式, 可选值为：
            copy: false
        };
    },
    onLoad(option) {
        this.enterpriseInfo = uni.getStorageSync('userInfo');
        this.token = uni.getStorageSync('token');
        this.info = JSON.parse(decodeURIComponent(option.info));
    },
    onHide() {},
    mounted() {
        this.$refs.uForm.setRules(this.rules);
        uni.getLocation({
            type: 'wgs84',
            success: (res) => {
                this.model.JD = res.longitude;
                this.model.WD = res.latitude;
                this.SBWZ = `${res.longitude.toFixed(
                    2
                )} ~ ${res.latitude.toFixed(2)}`;
            }
        });
        this.getGgdmz();
    },
    methods: {
        //删除文件
        deleteFile(r) {},
        beforeUpload() {
            // this.$refs.uUpload.formData.YWSJID = this.formData.XH;
            // this.$refs.uUpload.formData.file = this.$refs.uUpload.lists[0].file.path;
            // console.log(this.$refs.uUpload.lists);
        },
        getGgdmz() {
            // 工艺类型
            getGgdmz({ DMJBH: 'ZWSB_GYLX', FDM: 'ZWSB_GYLX' }).then((res) => {
                if (res.data_array && res.data_array.length > 0) {
                    this.gylxList = res.data_array;
                }
            });
            // 组织方式
            let { orgid } = this.enterpriseInfo;
            let { WRYBH } = this.info;
            getScx({
                ORGID: orgid,
                pageSize: 666,
                orderBy: 'CJSJ',
                orderWay: 'DESC',
                WRYBH: WRYBH
            }).then((res) => {
                if (res.data_array && res.data_array.length > 0) {
                    this.scxList = res.data_array;
                }
            });
        },

        // 产污设备组织方式 u-select @confirm 事件
        getGylx(v) {
            this.model.GYLX = v[0].label;
            this.model.GYLX_SBLX = '';
            getGgdmz({ DMJBH: 'ZWSB_GYLX', FDM: v[0].value }).then((res) => {
                if (res.data_array && res.data_array.length > 0) {
                    this.sblxList = res.data_array;
                }
            });
        },
        // 治污设备组织方式 u-select @confirm 事件
        getSblx(v) {
            this.model.GYLX_SBLX = v[0].label;
        },
        selectSblx() {
            if (!this.model.GYLX) {
                uni.showToast({
                    title: '请先选择工艺类型',
                    icon: 'none'
                });
                return;
            }
            this.sblxShow = true;
        },
        getScx(v) {
            this.model.SCXID = v[0].value;
            this.SSSCX = v[0].label;
        },

        getJwd() {
            uni.navigateTo({
                url: '../map'
            });
        },

        scanCode() {
            uni.scanCode({
                scanType: ['qrCode'],
                success: (res) => {
                    this.model.IMEI = res.result.split(';')[0];
                },
                fail: (err) => {
                    uni.showToast({
                        title: '未识别到二维码！',
                        icon: 'none'
                    });
                }
            });
        },

        getMore() {},

        save() {
            this.model.ORGID = this.enterpriseInfo.orgid;
            this.model.CJR = this.enterpriseInfo.name;

            this.$refs.uForm.validate((valid) => {
                if (valid) {
                    saveZwsb(this.model).then((res) => {
                        uni.showToast({
                            title: '保存成功',
                            duration: 2000
                        }).then(() => {
                            this.$refs.uUpload.formData.YWSJID = res.id;
                            this.$refs.uUpload.upload();
                            setTimeout(() => {
                                uni.navigateTo({
                                    url:
                                        './saveZwsbSuccess?zwsbName=' +
                                        this.model.SBMC +
                                        '&info=' +
                                        encodeURIComponent(
                                            JSON.stringify(this.info)
                                        )
                                });
                            }, 2000);
                        });
                        uni.setStorageSync('isAddEquipment', true);
                    });
                }
            });
        },
        back() {
            uni.navigateBack({
                delta: 1
            });
        }
    }
};
</script>

<style scoped>
.pd-tablebx image {
    height: 30rpx;
}
.bznr {
    display: inline-block;
    height: 60rpx;
    line-height: 60rpx;
}
/deep/ .u-form-item--left__content__label {
    font-size: 30rpx;
    color: #2c323f;
}

/deep/ .u-input__textarea {
    border-radius: 8rpx;
    height: 100rpx;
    background-color: rgb(243, 245, 249);
    padding-left: 10rpx;
}
/deep/ .uni-textarea-wrapper {
    height: 100% !important;
}
/deep/ .u-list-item {
    margin: 0;
}
/deep/ .uni-input-placeholder {
    padding: 0 20rpx 0 0;
    /* text-align: right; */
    font-size: 26rpx;
}
.listWarp {
    width: 100%;
    height: 100%;
    overflow-y: scroll;
    padding-top: 80rpx;
}
.listWarp p {
    width: 100%;
    padding: 20rpx;
    text-align: center;
    border-bottom: 1px solid #efefef;
}
.opration {
    padding: 20rpx;
    display: flex;
    justify-content: space-between;
    position: absolute;
    width: 100%;

    background-color: #fff;
}
.confirm {
    color: rgb(60, 170, 255);
}
.on {
    color: rgb(60, 170, 255);
}
/deep/ .u-icon {
    padding: 0 0 0 10rpx;
}
.tip-title {
    text-align: center;
    border-bottom: 1px solid #efefef;
    padding: 20rpx;
    font-weight: 600;
}
.tip-content {
    padding: 20rpx;
}
.tip-content p {
    /* padding: 20rpx 0; */
    font-size: 26rpx;

    color: #666;
}
.tip-know {
    position: absolute;
    bottom: 0;
    padding: 20rpx;
    text-align: center;
    border-top: 1px solid #efefef;
    color: rgb(60, 170, 255);
    width: 100%;
}
</style>
