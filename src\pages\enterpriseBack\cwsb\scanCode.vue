<template>
	<scan @getCode="getScanCode"/>
</template>

<script>
	export default{
		data(){
			return {
				page:'ScanCode'
			}
		},
		onLoad() {
			var pages = getCurrentPages();
			var prevPage = pages[pages.length - 2]; //上一个页面
			
		},
		methods:{
			getScanCode(v){
				// this.model.IMEI = v.split(';')[0]
				// console.log(v.split(';')[0]);
				// this.scanCodeShow=false
			if(v){
				var pages = getCurrentPages();
				
				var prevPage = pages[pages.length - 2]; //上一个页面
			
				prevPage.model.IMEI=v.split(';')[0]
				uni.navigateBack({
					delta:1
				})
			}
			},
		}
	}
</script>

<style>
</style>
