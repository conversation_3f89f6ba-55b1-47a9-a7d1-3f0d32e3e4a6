.ic-back {
    position: absolute;
    left: 30.1932rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 30rpx;
    height: 30rpx;
    background: url(~@/static/app/images/ic-back.png) center center no-repeat;
    background-size: 21.7391249rpx 36.2319rpx;
}

.zy-form {
    padding-left: 24.1545749rpx;
    background-color: #fff;
}

.zy-form .item {
    display: flex;
    justify-content: space-between;
    padding: 21.1353rpx 0;
    padding-right: 24.1545749rpx;
    border-bottom: 1px solid #eee;
}

.zy-form .item .label {
    font-size: 28rpx;
    color: #333;
    flex: 3;
    line-height: 39.855075rpx;
    position: relative;
}

.zy-form .item .detail {
    font-size: 28rpx;
}

.zy-form .item .label.star::after {
    content: "*";
    position: absolute;
    left: -12.077325rpx;
    top: 0;
    color: red;
}

.zy-form .item .rp {
    display: flex;
    flex: 7;
}

.equipment-maintenance .zy-form .item .label {
    flex: 4;
}

.equipment-maintenance .zy-form .item .rp {
    display: flex;
    flex: 6;
}

.zy-form .item .rp i {
    font-size: 28rpx;
    color: #333;
}

.zy-form .item .rp i.pd-txt1 {
    color: #4874ff;
    padding-left: 8rpx;
}

.zy-form .item .rp i.pd-txt2 {
    color: #666;
}

.zy-input1 {
    font-size: 28rpx;
    color: #333;
    line-height: 39.855075rpx;
    text-align: right;
}

.zy-input1.pd-txt2 {
    color: #666;

}

.zy-textarea1::placeholder,
.zy-input1::placeholder {
    color: #999;
}

.zy-textarea1 {
    font-size: 28rpx;
    color: #333;
    line-height: 39.855075rpx;
    font-family: "Microsoft YaHei";
    width: 483.0918rpx;
    height: auto;
    resize: none;
}

.zy-textarea1.pd-txt2 {
    color: #666;
}

/* .zy-selectBox {
    position: relative;
} */

.zy-selectBox .res {
    font-size: 28rpx;
    color: #999;
    /* text-align: center;
    line-height: 39.855075rpx; */
    padding: 0 30.1932rpx;
    background: url(~@/static/app/images/select-right.png) right center no-repeat;
    background-size: 11.473425rpx;
}

.detail-type .zy-selectBox .res {
    padding: 0;
    background: none;
}

.zy-selectBox .res.link {
    background: url(~@/static/app/images/select-right-blue.png) right center no-repeat;
    background-size: 11.473425rpx;
}

.zy-selectBox .res.selected {
    color: #333;
}

.zy-selectBox .options {
    display: none;
    position: absolute;
    top: 110%;
    left: 0;
    right: 0;
    box-shadow: 0 0 12.077325rpx rgba(0, 0, 0, 0.05);
    background-color: #fff;
    padding: 0 30.1932rpx;
}

.zy-selectBox .options li {
    font-size: 28rpx;
    color: #333;
    line-height: 66.4251rpx;
    white-space: nowrap;
}

.zy-bot-btn1 {
    /*  position: absolute;
    bottom: 30.1932rpx;
    left: 30.1932rpx;
    right: 30.1932rpx; */
    height: 96.618375rpx;
    border-radius: 48.3091499rpx;
    background-color: #4874ff;
    font-size: 33.81645rpx;
    color: #fff;
    text-align: center;
    line-height: 96.618375rpx;
}

/** 我的页面 **/
.topbox {
    background-color: #FFFFFF;
}

.caozuo li {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 27.77775rpx;
    color: #666;
    height: 90.5789999rpx;
    border-bottom: 1px solid #ddd;
    padding: 0 27rpx;
}

.caozuo {
    padding: 0 15.0966rpx;
}

.caozuo li .s1 {
    font-size: 27.77775rpx;
    line-height: 96.618375rpx;
    color: #666;
}

.caozuo li .s2 {
    position: relative;
    height: 90.5789999rpx;
    color: #666;
    font-size: 27.77775rpx;
    line-height: 90.5789999rpx;
}

.caozuo li .arr {
    padding-right: 32.608725rpx;
    background: url(~@/static/app/images/ic-more.png) right center no-repeat;
    background-size: 12.681rpx 21.73875rpx;
}

.caozuo li:last-child {
    border-bottom: none;
}

.sign-out {
    height: 99.63765rpx;
    line-height: 99.63765rpx;
    color: #fff;
    text-align: center;
    font-size: 33.81645rpx;
    background-color: #4874ff;
    border-radius: 48.3091499rpx;
    margin: 2rem 30rpx 0;
}
