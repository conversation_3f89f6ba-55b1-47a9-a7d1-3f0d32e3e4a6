<!-- @format -->

<template>
    <!-- 筛选 -->
    <div v-if="showFilterBox">
        <div class="mask" style="display: block"></div>
        <div class="pd-botalt" style="height: calc(90vh)">
            <div class="pd-althd">
                <strong>全部筛选</strong>
                <i class="altcls" @click="close"></i>
            </div>
            <scroll-view scroll-y="true" class="scoll-wrapper">
                <div class="pd-altbd">
                    <div
                        class="pd-tit1"
                        v-if="cityList.length > 0 || townList.length > 0"
                    >
                        所属区域
                    </div>
                    <div class="gap"></div>
                    <ul class="pd-ulbtn2" v-if="userinfo.orgid.length == 2">
                        <li
                            :class="currentCity == item.XZH ? 'on' : ''"
                            v-for="(item, index) in cityList"
                            :key="item.XZQHDM"
                            @click="getXzqhdm(item, 2)"
                        >
                            <p>{{ item.XZQH }}</p>
                        </li>
                    </ul>
                    <div class="gap"></div>
                    <ul class="pd-ulbtn2">
                        <li
                            :class="currentTown.includes(item.XZH) ? 'on' : ''"
                            v-for="(item, index) in townList"
                            :key="index"
                            @click="getXzqhdm(item, 3)"
                        >
                            <p>{{ item.XZQH }}</p>
                        </li>
                    </ul>
                    <div class="gap"></div>
                    <div class="gap"></div>
                    <div class="pd-tit1">预警类型</div>
                    <div class="gap"></div>
                    <ul class="pd-ulbtn2">
                        <li
                            :class="select.includes(item.value) ? 'on' : ''"
                            :key="item.value"
                            v-for="(item, index) in qyztList"
                            @click="getQyzt(item)"
                        >
                            <p>{{ item.label }}</p>
                        </li>
                    </ul>
                </div>
            </scroll-view>
            <ul class="pd-botbtn1">
                <li @click="reset">重置</li>
                <li class="on" @click="confirm">确定</li>
            </ul>
        </div>
    </div>
</template>

<script>
import {  getCode } from '@/api/iot/realtime.js';
import { getXzqh } from '@/api/iot/xzqh.js';
export default {
    data() {
        return {
            currentTab: '1',
            tabs: [
                {
                    label: '所属区域',
                    value: '1'
                },
                {
                    label: '企业状态',
                    value: '2'
                }
            ],
            xzqh: [],
            // 0: 离线， 1 生产正常 2 治污异常  3 生产停止
            qyztList: [],
            select: [],
            zt: [],
            params: {
                FDM: '',
                XZQHDM: '',
                ZT: ''
            },
            cityList: [],
            townList: [],
            userinfo: {},
            currentCity: '',
            currentTown: []
        };
    },
    props: {
        showFilterBox: {
            type: Boolean,
            default: false
        },
        ZT: {
            type: String,
            default: '1,2'
        }
        // xzqhList: {
        // 	type: Array,
        // 	default: () => {
        // 		return [];
        // 	}
        // }
    },
    computed: {
        showFilter: {
            get() {
                return this.$props.showFilterBox;
            },
            set(newValue) {
                return newValue;
            }
        }
    },
    watch: {
        // xzqhList: {
        // 	handler: function(nv) {
        // 		this.cityList = [];
        // 		this.townList = [];
        // 		for (let i in nv) {
        // 			// if (nv[i].XZH.length == 4) {
        // 				this.cityList.push(nv[i]);
        // 			// } else {
        // 			// 	this.townList.push(nv[i]);
        // 			// }
        // 		}
        // 		if (this.userinfo.orgid.length == 2) {
        // 			this.currentCity = this.cityList[0].XZQ;
        // 			//为市级查询区县为多个
        // 			this.initRegion(this.cityList[0].XZQHDM)
        // 		}else if (this.userinfo.orgid.length == 4) {
        // 			this.currentCity = this.userinfo.orgid;
        // 			//为市级查询区县为多个
        // 			this.initRegion(this.cityList[0].XZQHDM)
        // 		}else if (this.userinfo.orgid.length == 6) {
        // 			this.currentCity = this.userinfo.orgid.substr(0,4);
        // 			this.currentTown = this.userinfo.orgid;
        // 			this.params.XZQHDM = this.currentTown;
        // 			//为区县级查询区县为一个
        // 			this.initRegion(this.userinfo.orgid)
        // 		}
        // 	},
        // 	immediate: true
        // }
    },
    mounted() {
        this.userinfo = uni.getStorageSync('user_info');
        if (this.userinfo.orgid.length == 4) {
            this.currentCity = this.userinfo.orgid;
            this.params.FDM = this.currentCity;
        } else if (this.userinfo.orgid.length == 6) {
            this.currentCity = this.userinfo.orgid.substr(0, 4);
            this.currentTown = [this.userinfo.orgid];
            this.params.FDM = this.currentCity;
            this.params.XZQHDM = this.userinfo.orgid;
        } else if (this.userinfo.orgid.length > 6) {
            this.currentTown = [this.userinfo.orgid];
            this.params.XZQHDM = this.userinfo.orgid;
        }

        this.initRegion();
        this.getCode();
    },
    methods: {
        change(v) {
            this.currentTab = v;
        },
        getCode() {
            getCode({
                code: 'YJGZ_YJLX'
            }).then((res) => {
                this.qyztList = res.data;
                this.select = res.data.map((item) => {
                    return item.value;
                });
            });
        },
        getXzqhdm(v, level) {
            //市级、县级用户只显示当前市，不用点击
            if (
                level == 2 &&
                (this.userinfo.orgid.length == 4 ||
                    this.userinfo.orgid.length == 6)
            ) {
                return;
            }

            if (level == 2) {
                this.currentCity = v.XZH;
                this.params.FDM = v.XZH;
                this.getTownList(v);
                return;
            }

            if (
                this.userinfo.orgid.length == 6 &&
                this.townList.length == 1 &&
                this.townList[0].XZH == this.userinfo.orgid
            ) {
                return;
            }

            // if (this.userinfo.orgid.length == 6 && this.userinfo.orgid != v.XZH) {
            // 	uni.showToast({
            // 		icon: 'error',
            // 		title: '无法查看其他区县'
            // 	});
            // 	return;
            // }
            if (this.currentTown.includes(v.XZH)) {
                this.currentTown = this.currentTown.filter((item) => {
                    return item != v.XZH;
                });
            } else {
                this.currentTown.push(v.XZH);
            }

            this.params.XZQHDM = this.currentTown
                .filter((item) => {
                    return item != this.userinfo.orgid;
                })
                .join(',');
            // this.$emit('getFilterParams', this.params);
        },
        getQyzt(v) {
            let index = this.select.findIndex((item) => {
                return item == v.value;
            });
            if (index != -1) {
                if (this.select.length == 1) {
                    uni.showToast({
                        icon: 'none',
                        title: '需至少选中一种状态'
                    });
                    return;
                }
                this.select.splice(index, 1);
            } else {
                this.select.push(v.value);
            }
            this.params.ZT = this.select.join(',');
            // this.$emit('getFilterParams', this.params);
        },
        reset() {
            //区域重置需要根据用户权限
            if (this.userinfo.orgid.length === 4) {
                //市级重置，市为当前orgid，区县为空
                this.params.XZQHDM = '';
                this.currentTown = [];
            } else if (this.userinfo.orgid.length === 2) {
                //省级，市为列表中第一个，区县为空
                this.currentCity = this.cityList[0].XZH;
                this.params.FDM = this.currentCity;
                this.params.XZQHDM = '';
                this.currentTown = [];
            } else if (
                this.userinfo.orgid.length === 6 &&
                ((this.townList.length == 1 &&
                    this.townList[0].XZH != this.userinfo.orgid) ||
                    this.townList.length > 1)
            ) {
                this.params.XZQHDM = this.userinfo.orgid;
                this.currentTown = [];
            }

            //企业状态重置需要根据外部传入的状态
            this.select = this.qyztList.map((item) => {
                return item.value;
            });
            this.params.ZT = this.select.join(',');
        },
        confirm() {
            this.$emit('getFilterParams', this.params);
            this.close();
        },
        close() {
            this.$parent.showFilterBox = false;
        },
        //初始化行政区域列表
        initRegion() {
            let _this = this;
            let xzqhdm = this.userinfo.orgid;
            this.cityList = [];
            if (xzqhdm.length == 2) {
                //1. 获取市级信息
                getXzqh({
                    FDM: xzqhdm.substr(0, 2) + '0000'
                }).then((res) => {
                    let data = res.data;
                    //2. 省、市、区县角色
                    //省级角色获取全部地市信息，获取后默认获取第一个地市的区县信息？
                    //市级获取全部地市信息后，处理只显示当前市，并获取区县信息
                    //区县角色前面步骤与市级一样
                    //省级用户查询地市
                    _this.cityList = [];
                    if (data && data.length > 0) {
                        if (xzqhdm.length == 2) {
                            _this.cityList = data;
                        } else {
                            _this.cityList = data.filter((item) => {
                                return item.XZH == xzqhdm.substr(0, 4);
                            });
                        }
                    }
                });
            } else if (xzqhdm.length == 4) {
                let city = xzqhdm + '00';
                getXzqh({
                    FDM: city
                }).then((res) => {
                    _this.townList = res.data;
                });
            } else if (xzqhdm.length == 6) {
                getXzqh({
                    FDM: xzqhdm
                }).then((res) => {
                    if (!res.data || res.data.length == 0) {
                        getXzqh({
                            FDM: xzqhdm.substr(0, 4) + '00'
                        }).then((res) => {
                            _this.townList = res.data.filter((item) => {
                                return item.XZH == xzqhdm;
                            });
                        });

                        return;
                    }
                    _this.townList = res.data;
                });
            }
        },
        getTownList(region) {
            getXzqh({
                FDM: region.XZQHDM
            }).then((res) => {
                this.townList = res.data;
            });
        }
    }
};
</script>

<style scoped>
.allFilter {
    font-size: 35rpx;
    font-weight: bolder;
    display: inline-block;
    width: 100%;
    text-align: center;
}

.filter-box {
}

.pd-siderbar {
    height: 100vh;
}

.pd-navtree ul + ul {
    flex: 1;
    background: unset;
}

.townTree {
    height: 100%;
    overflow: auto;
}

.pd-botbtn1 {
    width: initial;
}

.scoll-wrapper {
    height: calc(90vh - 220rpx);
}
</style>
