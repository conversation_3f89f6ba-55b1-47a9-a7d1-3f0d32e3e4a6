/** @format */

import axios from '@/common/ajaxRequest.js';
import {
    ULR_BASE,
    LOGIN_ULR_BASE,
    QUERY_EQUIP_TYPE_URL,
    IOTMANAGE_URL
} from '@/common/config.js';

export const getQyCount = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/az/qyxx/list/count',
        params: data
    });
};

export const getQyxx = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/az/qyxx/list',
        params: data
    });
};
// 新增企业信息
export const addQyxx = (data) => {
    return axios.request({
        method: 'POST',
        url: ULR_BASE + '/az/qyxx/add',
        data: data
    });
};
// 企业详情信息
export const getQyxxInfo = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/az/qyxx/info',
        params: data
    });
};

// 修改企业信息
export const updataQyxx = (data) => {
    return axios.request({
        method: 'post',
        url: ULR_BASE + '/az/qyxx/modify',
        data: data
    });
};

// 企业详情-生产线
export const getScxList = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/az/scx/list',
        params: data
    });
};
//删除生产线
export const delScx = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/az/scx/del',
        params: data
    });
};
//删除治污线
export const delZwx = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/az/zwx/del',
        params: data
    });
};

// 企业详情-行业类型
export const getCommoHylx = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/commo/hylx',
        params: data
    });
};

// 企业详情-治污线
export const getZwxList = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/az/zwx/list',
        params: data
    });
};

// 生产线设备
export const getScxsbList = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/az/scsb/list',
        params: data
    });
};

// 删除生产设备
export const delScsb = (data) => {
    return axios.request({
        method: 'POST',
        url: ULR_BASE + '/az/scsb/del',
        data: data
    });
};

// 添加生产设备
export const addScsbList = (data) => {
    return axios.request({
        method: 'POST',
        url: ULR_BASE + '/az/scsb/add',
        data: data
    });
};

// 生产设备信息
export const ScsbInfo = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/az/scsb/info',
        params: data
    });
};

// 生产设备--公告代码
export const commocode = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/commo/commocode',
        params: data
    });
};

// 生产设备修改
export const editScsb = (data) => {
    return axios.request({
        method: 'POST',
        url: ULR_BASE + '/az/scsb/modify',
        data: data
    });
};

//最新上报的3条状态数据
export const newStateList = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/az/zdsb/ztxxgsh',
        params: data
    });
};

// 治污设备列表
export const getZwsbList = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/az/zwsb/list',
        params: data
    });
};
// export const getZwsbList = (data) => {
//     return axios.request({
//         method: 'GET',
//         url: ULR_BASE + '/az/zwsb/list',
//         data: data
//     });
// };

// 治污设备删除
export const delZwsb = (data) => {
    return axios.request({
        method: 'POST',
        url: ULR_BASE + '/az/zwsb/del',
        data: data
    });
};

// 治污设备新增
export const addZwsb = (data) => {
    return axios.request({
        method: 'POST',
        url: ULR_BASE + '/az/zwsb/add',
        data: data
    });
};

// 治污设备信息
export const zwsbInfo = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/az/zwsb/info',
        params: data
    });
};

// 治污设备修改
export const editZwsb = (data) => {
    return axios.request({
        method: 'POST',
        url: ULR_BASE + '/az/zwsb/modify',
        data: data
    });
};

// 产治污关系绑定列表
export const getRelationList = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/az/czwgx/list',
        params: data
    });
};

// 新增产治污关系绑定
export const addRelation = (data) => {
    return axios.request({
        method: 'POST',
        url: ULR_BASE + '/az/czwgx/add',
        data: data
    });
};

// 删除产治污关系绑定
export const deleteRelation = (data) => {
    return axios.request({
        method: 'POST',
        url: ULR_BASE + '/az/czwgx/del',
        data: data
    });
};

//删除生产设备
export const deleteScsb = (data) => {
    return axios.request({
        method: 'POST',
        url: ULR_BASE + '/az/scsb/del',
        data: data
    });
};

// 生产线信息
export const scxInfo = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/az/scx/info',
        params: data
    });
};

// 生产线信息
export const zwxInfo = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/az/zwx/info',
        params: data
    });
};

// 生产线保存
export const scxSave = (data) => {
    return axios.request({
        method: 'post',
        url: ULR_BASE + '/az/scx/add',
        data: data
    });
};

// 生产线修改
export const scxUpdata = (data) => {
    return axios.request({
        method: 'post',
        url: ULR_BASE + '/az/scx/modify',
        data: data
    });
};

// 治污线保存
export const zwxSave = (data) => {
    return axios.request({
        method: 'post',
        url: ULR_BASE + '/az/zwx/add',
        data: data
    });
};

// 治污线修改
export const zwxUpdata = (data) => {
    return axios.request({
        method: 'post',
        url: ULR_BASE + '/az/zwx/modify',
        data: data
    });
};

// 保存治污设备
export const saveZwsb = (data) => {
    return axios.request({
        method: 'post',
        url: ULR_BASE + '/az/zwsb/add',
        data: data
    });
};

// 查询治污设备信息
export const getZwsbInfo = (data) => {
    return axios.request({
        method: 'post',
        url: ULR_BASE + '/az/zwsb/info',
        data: data
    });
};

// 删除治污设备
export const deleteZwsb = (data) => {
    return axios.request({
        method: 'post',
        url: ULR_BASE + '/az/scsb/del',
        data: data
    });
};

// 查询治污设备
export const getZwsb = (data) => {
    return axios.request({
        method: 'post',
        url: ULR_BASE + '/mobile/zwsb',
        data: data
    });
};

// 查询产污设备
export const getCwsb = (data) => {
    return axios.request({
        method: 'post',
        url: ULR_BASE + '/mobile/cwsb',
        data: data
    });
};

// 修改
export const updateZwsb = (data) => {
    return axios.request({
        method: 'post',
        url: ULR_BASE + '/az/zwsb/modify',
        data: data
    });
};

// 匹配IMEI号
export const znsb = (data) => {
    return axios.request({
        method: 'post',
        url: ULR_BASE + '/az/znsb',
        data: data
    });
};

// 检测IMEI号
export const getValidate = (data) => {
    return axios.request({
        method: 'post',
        url: ULR_BASE + '/baseinfo/imei/validate',
        data: data
    });
};

// 修改
export const updateCwsb = (data) => {
    return axios.request({
        method: 'post',
        url: ULR_BASE + '/mobile/cwsb/update',
        data: data
    });
};
// 产污设备保存
export const saveCwsb = (data) => {
    return axios.request({
        method: 'post',
        url: ULR_BASE + '/mobile/cwsb/save',
        data: data
    });
};
// 最新上报数据
export const getZxsj = (data) => {
    return axios.request({
        method: 'post',
        url: ULR_BASE + '/mobile/znsb/zxsj',
        data: data
    });
};

// 最新上报状态
export const getZtxx = (data) => {
    return axios.request({
        method: 'post',
        url: ULR_BASE + '/mobile/znsb/ztxx',
        data: data
    });
};

export const getFilelist = (data) => {
    return axios.request({
        method: 'post',
        url:
            LOGIN_ULR_BASE +
            '/platform/file/filemanagecontroller/queryfileinfos',
        data: data,
        post: {
            'Content-Type': 'application/json; charset=utf-8'
        }
    });
};

export const deletefile = (data) => {
    return axios.request({
        method: 'get',
        url:
            LOGIN_ULR_BASE +
            '/platform/file/filemanagecontroller/deletefile/' +
            data,
        params: {}
    });
};

export const downloadFile = (data) => {
    return axios.request({
        method: 'get',
        url:
            LOGIN_ULR_BASE +
            '/platform/file/filemanagecontroller/downloadfilebyid/' +
            data,
        params: {}
    });
};

//查询平面图点位信息
export const queryPmtxx = (wrybh) => {
    return axios.request({
        url: LOGIN_ULR_BASE + '/pmt/queryPmtxx/' + wrybh,
        method: 'get',
        params: {}
    });
};

//查询设备相关图片
export const queryEquipPics = (data) => {
    return axios.request({
        url: QUERY_EQUIP_TYPE_URL,
        method: 'get',
        params: data
    });
};

//查询企业产治污关系配置历史记录
export const getProductContorlRelationeEditHistory = (data) => {
    return axios.request({
        url: IOTMANAGE_URL + '/czwgx/czjl/list/' + data,
        method: 'get',
        params: data
    });
};

//解除脱落预警接口
export const getDropWarn = (data) => {
    return axios.request({
        url: IOTMANAGE_URL + '/ccyj/relieveByDeviceId/' + data,
        method: 'get',
    });
};
