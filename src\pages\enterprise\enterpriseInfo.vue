<!-- @format -->

<template>
    <div style="background: #f1f1f1">
        <section class="main">
            <div class="inner">
                <div class="pd-modhd">
                    <strong>基本信息</strong>
                    <image
                        src="../../static/app/images/edtic.png"
                        class="pd-edt1"
                        @click="editJbxx"
                    />
                </div>
                <div class="zy-form info">
                    <div class="item">
                        <p class="label">企业名称</p>
                        <div class="rp">
                            <i class="pd-txt2">{{ info.WRYMC }}</i>
                        </div>
                    </div>

                    <!-- <div class="item">
                        <p class="label">所属行业</p>
                        <div class="rp">
                            <i class="pd-txt2">{{ info.HYLX_CH || '-' }}</i>
                        </div>
                    </div> -->
                    <!-- <div class="item">
                        <p class="label">信用代码</p>
                        <div class="rp">
                            <i class="pd-txt2">{{ info.TYSHXYDM || '-' }}</i>
                        </div>
                    </div> -->
                    <div class="item">
                        <p class="label">企业地址</p>
                        <div class="rp">
                            <i class="pd-txt2">
                                {{
                                    (info.DWDZ && info.DWDZ.slice(0, 14)) ||
                                    '--'
                                }}{{
                                    info.DWDZ && info.DWDZ.length > 13
                                        ? '...'
                                        : ''
                                }}
                            </i>
                        </div>
                    </div>
                    <!-- <div class="item">
                        <p class="label">所属区域</p>
                        <div class="rp">
                            <i class="pd-txt2">{{ getXzqh() || '-' }}</i>
                        </div>
                    </div> -->
                    <div class="item">
                        <p class="label">环保联系</p>
                        <div class="rp">
                            <i class="pd-txt2">{{ info.HBLXR || '-' }}</i>
                        </div>
                    </div>
                    <div class="item">
                        <p class="label">联系电话</p>
                        <div class="rp">
                            <i class="pd-txt2">{{ info.HBLXRDH || '-' }}</i>
                        </div>
                    </div>
                    <div class="item">
                        <p class="label">监管联系</p>
                        <div class="rp">
                            <i class="pd-txt2">{{ info.JGLXR || '-' }}</i>
                        </div>
                    </div>
                    <div class="item">
                        <p class="label">联系电话</p>
                        <div class="rp">
                            <i class="pd-txt2">{{ info.JGLXRDH || '-' }}</i>
                        </div>
                    </div>
                    <div class="item">
                        <p class="label">安装人员</p>
                        <div class="rp">
                            <i class="pd-txt2">{{ info.AZLXR || '-' }}</i>
                        </div>
                    </div>
                    <div class="item">
                        <p class="label">联系电话</p>
                        <div class="rp">
                            <i class="pd-txt2">{{ info.AZLXRDH || '-' }}</i>
                        </div>
                    </div>
                    <!-- <div class="item">
                        <p class="label">备注</p>
                        <div class="rp">
                            <span class="pd-txt2">{{ info.BZ || '-' }}</span>
                        </div>
                    </div> -->
                    <div class="item" v-if="isWeakSignalDetail">
                        <p class="label">信号弱设备数</p>
                        <div class="rp">
                            <i class="pd-txt2">{{ info.XHR_COUNT || '-' }}</i>
                        </div>
                    </div>
                </div>
                <div class="gap"></div>
				<div v-show="info.ZT == 1">
					<div  class="pd-modhd" @click="toRealTime">
					    <strong>实时监控</strong>
					    <image
					        src="../../static/app/images/arwrt1.png"
					        class="pd-arw1"
					    />
					</div>
					<div class="gap"></div>
				</div>
                <!-- 信号弱设备展示信息 -->
                <WeakSignal
                    v-if="isWeakSignalDetail"
                    :info="info"
                    :showAddDialog.sync="showAddDialog"
                ></WeakSignal>

                <!-- 非信号弱设备展示信息 -->
                <div v-if="!isWeakSignalDetail">
                    <div class="pd-modhd">
                        <strong>相关附件</strong>
                    </div>
                    <div class="zy-form">
                        <div class="item nfx">
                            <p class="label upload-title">
                                <span>企业大门图</span>
                                <image
                                    @click="addFile('WRYZP')"
                                    src="../../static/app/images/addic2.png"
                                    class="pd-add2"
                                />
                            </p>
                            <div class="gap"></div>
                            <ul class="pd-ulpic1">
                                <li
                                    v-for="(item, index) in wryFileList"
                                    :key="item.WJID"
                                >
                                    <div class="img-content">
                                        <image
                                            class="pic"
                                            mode="aspectFill"
                                            :src="getFileUrl(item)"
                                            alt=""
                                            @click="
                                                previewImage(wryFileList, index)
                                            "
                                        />
                                        <image
                                            mode="scaleToFill"
                                            src="../../static/app/images/cls.png"
                                            class="delImg"
                                            @click="delFile(item)"
                                        />
                                    </div>
                                </li>
                            </ul>
                        </div>
                        <div class="item nfx">
                            <p class="label upload-title">
                                企业平面图
                                <image
                                    @click="addFile('QYPMT')"
                                    src="../../static/app/images/addic2.png"
                                    class="pd-add2"
                                />
                            </p>
                            <div class="gap"></div>
                            <ul class="pd-ulpic1">
                                <li
                                    v-for="(item, index) in enterpriseFileList"
                                    :key="item.WJID"
                                >
                                    <div class="img-content">
                                        <image
                                            class="pic"
                                            mode="aspectFill"
                                            :src="getFileUrl(item)"
                                            alt=""
                                            @click="
                                                previewImage(
                                                    enterpriseFileList,
                                                    index
                                                )
                                            "
                                        />
                                        <image
                                            mode="scaleToFill"
                                            src="../../static/app/images/cls.png"
                                            class="delImg"
                                            @click="delFile(item)"
                                        />
                                    </div>
                                </li>
                                <!-- <li>
									<image @click="addFile('QYPMT')" src="../../static/app/images/addpic.png"
										class="pd-addpic1" />
								</li> -->
                            </ul>
                        </div>

                        <div class="item nfx" v-if="arrPlanDispose.length">
                            <p class="label">平面点位图</p>
                            <ul class="pd-ulpic1">
                                <li
                                    v-for="(item, index) in arrPlanDispose"
                                    :key="item.pmtxh"
                                >
                                    <div class="img-content">
                                        <image
                                            class="pic"
                                            :src="getPmtFileUrl(item)"
                                            alt=""
                                            @click="toPmtFullScreen(item)"
                                        />
                                        <div
                                            style="
                                                position: absolute;
                                                bottom: 0;
                                                left: 0;
                                                background: rgba(0, 0, 0, 0.6);
                                                color: #fff;
                                                white-space: nowrap;
                                                overflow: hidden;
                                                text-overflow: ellipsis;
                                                text-align: center;
                                                line-height: 50rpx;
                                                height: 50rpx;
                                                width: 100%;
                                                font-size: 24rpx;
                                                border-radius: 0 0 6rpx 6rpx;
                                            "
                                        >
                                            {{ item.pmtmc }}
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                        <div class="item nfx">
                            <p class="label upload-title">
                                <span>生产车间全景图</span>
                                <image
                                    @click="addFile('SCQJT')"
                                    src="../../static/app/images/addic2.png"
                                    class="pd-add2"
                                />
                            </p>
                            <div class="gap"></div>
                            <ul class="pd-ulpic1">
                                <li
                                    v-for="(
                                        item, index
                                    ) in productWorkshopFileList"
                                    :key="item.WJID"
                                >
                                    <div class="img-content">
                                        <image
                                            class="pic"
                                            mode="aspectFill"
                                            :src="getFileUrl(item)"
                                            alt=""
                                            @click="
                                                previewImage(
                                                    productWorkshopFileList,
                                                    index
                                                )
                                            "
                                        />
                                        <image
                                            mode="scaleToFill"
                                            src="../../static/app/images/cls.png"
                                            class="delImg"
                                            @click="delFile(item)"
                                        />
                                    </div>
                                </li>
                            </ul>
                        </div>
                        <div class="item nfx">
                            <p class="label upload-title">
                                <span>治污线全景图</span>
                                <image
                                    @click="addFile('ZWQJT')"
                                    src="../../static/app/images/addic2.png"
                                    class="pd-add2"
                                />
                            </p>
                            <div class="gap"></div>
                            <ul class="pd-ulpic1">
                                <li
                                    v-for="(
                                        item, index
                                    ) in pollutControlFileList"
                                    :key="item.WJID"
                                >
                                    <div class="img-content">
                                        <image
                                            class="pic"
                                            mode="aspectFill"
                                            :src="getFileUrl(item)"
                                            alt=""
                                            @click="
                                                previewImage(
                                                    pollutControlFileList,
                                                    index
                                                )
                                            "
                                        />
                                        <image
                                            mode="aspectFit"
                                            src="../../static/app/images/cls.png"
                                            class="delImg"
                                            @click="delFile(item)"
                                        />
                                    </div>
                                </li>
                            </ul>
                        </div>

                        <!-- 其他附件 -->
                        <div class="otherFile">
                            <div>
                                <image
                                    src="../../static/app/images/file.png"
                                    class="file-icon"
                                />
                                <span class="title">其他附件</span>
                            </div>
                            <lsj-upload
                                ref="lsjUpload"
                                childId="upload1"
                                :width="width"
                                :height="height"
                                :option="option"
                                :size="size"
                                :formats="formats"
                                :debug="debug"
                                :instantly="instantly"
                                @progress="onprogress"
                                @change="onChange"
                                @uploadEnd="onuploadEnd"
                            >
                                <view
                                    class="btn fjbtn"
                                    :style="{ width: width, height: height }"
                                >
                                    <image
                                        src="../../static/app/images/addic2.png"
                                        class="pd-add2"
                                    />
                                </view>
                            </lsj-upload>
                        </div>

                        <ul class="filelistbox">
                            <li
                                class="filelist"
                                v-for="item in fileList"
                                :key="item.WJID"
                            >
                                <div class="left">
                                    <span class="file-icon">
                                        <image
                                            class="img"
                                            mode="widthFix"
                                            :src="getFileIcon(item.WJMC)"
                                        />
                                    </span>
                                    <span class="filename">{{
                                        item.WJMC
                                    }}</span>
                                </div>

                                <div class="right">
                                    <span @click="preFile(item)">
                                        <u-icon
                                            name="eye"
                                            color="#5486fc"
                                            size="18"
                                        ></u-icon>
                                    </span>
                                    <span @click="delFile(item)">
                                        <u-icon
                                            name="trash"
                                            color="#fc6473"
                                            size="18"
                                        ></u-icon>
                                    </span>
                                </div>
                            </li>
                        </ul>
                        <div v-if="fileList.length == 0" class="nodata">
                            暂无相关附件
                        </div>
                    </div>

                    <!-- <web-view src='/hybrid/html/index.html'></web-view> -->
                    <div class="gap"></div>

                    <div class="pd-modhd">
                        <strong
                            >生产线/工序
                            <i>{{ produceList.length || '' }}</i></strong
                        >
                        <image
                            src="../../static/app/images/addic2.png"
                            class="pd-add2"
                            @click="toAddProductionLine()"
                        />
                    </div>
                    <div class="zy-form">
                        <div
                            class="item"
                            @click="toScxDetail(item)"
                            v-for="(item, index) in produceList"
                            :key="index"
                        >
                            <p class="label wider">
                                {{ item.SCXMC }}/生产设备：{{ item.SBSL }}
                            </p>
                            <image
                                src="../../static/app/images/arwrt1.png"
                                class="pd-arw1"
                            />
                        </div>
                    </div>
                    <div class="gap"></div>
                    <div class="pd-modhd" @click="toAddPollutionLine()">
                        <strong
                            >治污线
                            <i>{{ pollutionList.length || '' }}</i></strong
                        >
                        <image
                            src="../../static/app/images/addic2.png"
                            class="pd-add2"
                        />
                    </div>
                    <div class="zy-form">
                        <div
                            class="item"
                            @click="toZwxDetail(item)"
                            v-for="(item, index) in pollutionList"
                            :key="index"
                        >
                            <p class="label wider">
                                {{ item.ZWXMC }}/治污设备：{{ item.SBSL }}
                            </p>
                            <image
                                src="../../static/app/images/arwrt1.png"
                                class="pd-arw1"
                            />
                        </div>
                    </div>
                    <div class="gap"></div>
                    <div class="pd-modhd">
                        <strong
                            >生产设备
                            <i>{{ produceEquList.length || '' }}</i></strong
                        >
                        <image
                            @click="toAddEquipment('sc')"
                            src="../../static/app/images/addic2.png"
                            class="pd-add2"
                        />
                    </div>
                    <div class="zy-form">
                        <div
                            class="item"
                            @click="toSbDetail({ SBLX: '生产' }, item)"
                            v-for="(item, index) in produceEquList"
                            :key="index"
                        >
                            <p class="label">
                                {{ item.SBMC }}
                                {{ item.IMEI && item.IMEI.substr(-6) }}
                                <span class="down" v-if="item.FCBJZT == 2"
                                    >脱落预警中</span
                                >
                            </p>
                            <image
                                src="../../static/app/images/arwrt1.png"
                                class="pd-arw1"
                            />
                        </div>
                    </div>
                    <div class="gap"></div>
                    <div class="pd-modhd">
                        <strong
                            >治污设备
                            <i>{{ pollutionEquList.length || '' }}</i></strong
                        >
                        <image
                            @click="toAddEquipment('zw')"
                            src="../../static/app/images/addic2.png"
                            class="pd-add2"
                        />
                    </div>
                    <div class="zy-form">
                        <div
                            class="item"
                            @click="toSbDetail({ SBLX: '治污' }, item)"
                            v-for="(item, index) in pollutionEquList"
                            :key="index"
                        >
                            <p class="label">
                                {{ item.SBMC }}
                                {{ item.IMEI && item.IMEI.substr(-6) }}
                                <span class="down" v-if="item.FCBJZT == 2"
                                    >脱落预警中</span
                                >
                            </p>
                            <image
                                src="../../static/app/images/arwrt1.png"
                                class="pd-arw1"
                            />
                        </div>
                    </div>
                    <div class="gap"></div>
                    <div class="pd-modhd">
                        <strong
                            >其他设备
                            <i>{{ otherEquipList.length || '' }}</i></strong
                        >
                        <image
                            @click="toAddPhAndRadiation('add')"
                            src="../../static/app/images/addic2.png"
                            class="pd-add2"
                        />
                    </div>
                    <div class="zy-form">
                        <div
                            class="item"
                            @click="toAddPhAndRadiation('detail', item)"
                            v-for="(item, index) in otherEquipList"
                            :key="index"
                        >
                            <p class="label">
                                {{ item.SBMC }}
                                {{ item.IMEI && item.IMEI.substr(-6) }}
                            </p>
                            <image
                                src="../../static/app/images/arwrt1.png"
                                class="pd-arw1"
                            />
                        </div>
                    </div>
                    <div class="gap"></div>
                </div>
            </div>
        </section>

        <div
            class="mask"
            style="display: block"
            v-if="showTip || showTipGy"
        ></div>

        <div class="pd-botdlg" v-if="showTip">
            <i class="dlgcls" @click="showTip = false"></i>
            <div class="gap"></div>
            <div class="gap"></div>
            <div class="pd-con1">
                <div class="pd-tit1">拍照示例</div>
                <div class="gap"></div>
                <image
                    mode="aspectFit"
                    src="../../static/images/sample/dm.jpg"
                    class="pd-pic"
                />
                <div class="gap"></div>
                <div class="gap"></div>
                <dl class="pd-dltxt1">
                    <dt>说明：</dt>
                    <dd>1、大门拍照尽量把企业名称拍下来；</dd>
                    <dd>2、可以多角度拍摄以便能识别企业所在位置或出入口；</dd>
                </dl>
                <div class="gap"></div>
                <div class="gap"></div>
            </div>
        </div>

        <div class="pd-botdlg" v-if="showTipGy">
            <i class="dlgcls" @click="showTipGy = false"></i>
            <div class="gap"></div>
            <div class="gap"></div>
            <div class="pd-con1">
                <div class="pd-tit1">拍照示例</div>
                <div class="gap"></div>
                <div class="image-box">
                    <image
                        mode="aspectFit"
                        src="../../static/app/images/gysl1.jpg"
                        class="pd-pic"
                    />
                    <image
                        mode="aspectFit"
                        src="../../static/app/images/gysl2.jpg"
                        class="pd-pic"
                    />
                </div>

                <div class="gap"></div>
                <div class="gap"></div>
                <dl class="pd-dltxt1">
                    <dt>说明：</dt>
                    <dd>
                        1、工艺流程图可以把企业已有的流程图拍照上传；（如厂区内张贴宣传提示图、监控室内的电脑画面等）；
                    </dd>
                    <dd>
                        2、可以把现场调研情况的产治污对应关系手绘出来拍照上传；
                    </dd>
                </dl>
                <div class="gap"></div>
                <div class="gap"></div>
            </div>
        </div>
        <AddDialog :info="info" :showAddDialog.sync="showAddDialog"></AddDialog>
    </div>
</template>

<script>
import AddPhotoDialog from '@/pages/enterprise/components/AddPhotoDialog';
import { getFilelist, deletefile, downloadFile } from '@/api/iot/appendix.js';
import {
    getQyxxInfo,
    getScxList,
    getZwxList,
    getScxsbList,
    getZwsbList,
    deleteRelation
} from '@/api/iot/enterprise.js';
import { getOtherList } from '@/api/iot/phAndRadiation.js';
import { queryPmtxx } from '@/api/iot/planeFigure.js';
import {
    API_LOGIN_SERVICE_URL,
    LOGIN_ULR_BASE,
    UPLOAD_URL,
    DOWNLOAD_URLZDY,
    PREVIEW_FILE_URL
} from '@/common/config.js';
// import PlaneFigureCanvas from '@/pages/component/PlaneFigure/PlaneFigureCanvas';
import WeakSignal from './components/WeakSignal';
import AddDialog from './components/AddDialog.vue';
export default {
    components: {
        WeakSignal,
        AddDialog,
        AddPhotoDialog
    },
    data() {
        return {
            showAddPhotoDialog: true,
            // 上传接口参数
            option: {
                // 上传服务器地址，此地址需要替换为你的接口地址
                url: UPLOAD_URL,
                // 上传附件的key
                name: 'file',
                // 根据你接口需求自定义请求头
                // header: {
                // 	'Authorization': 'bearer eyJhbGciOiJSUzI1NiIsI',
                // 	'uid': '27682',
                // 	'client': 'app',
                // 	'accountid': 'DP',
                // 'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8'
                // },
                // 根据你接口需求自定义body参数
                formData: {
                    LXDM: 'ZDFJ',
                    ZLXDM: 'CZWGXB',
                    YWSJID: '1660874688758020590592',
                    WJMC: 'file'
                }
            },
            // 选择文件后是否立即自动上传，true=选择后立即上传
            instantly: true,
            // 必传宽高且宽高应与slot宽高保持一致
            width: '50rpx',
            height: '50rpx',
            // 限制允许选择的格式，空串=不限制，默认为空
            //*.doc;*.docx,*.xls,*.xlsx,*.pdf,*.png,*.jpg,*.jpeg,*.bmp
            formats: 'pdf,doc,docx,xls,xlsx,jpeg,bmp,png,jpg,jpeg,bmp',
            // 文件上传大小限制
            size: 10,
            // 文件回显列表
            files: new Map(),

            // 是否打印日志
            debug: true,

            enterpriseInfo: {}, //
            info: {}, //上个页面带来的参数
            produceList: [],
            pollutionList: [],
            produceEquList: [],
            pollutionEquList: [],
            otherEquipList: [], //其他设备
            showAddItems: false,
            show: false,
            cancel: false,
            list: [
                {
                    text: '添加治污设备',
                    value: 'zw'
                },
                {
                    text: '添加产污设备',
                    value: 'cw'
                }
            ],
            tips: {
                text: ''
            },
            showTip: false,
            showTipGy: false,
            wryFileList: [],
            lctFileList: [],
            isWeakSignalDetail: false, //是否信号弱详情
            productWorkshopFileList: [], //生产车间全景
            pollutControlFileList: [], //治污线全景
            gateFileList: [], //大门图
            fileList: [],
            showAddDialog: false,
            enterpriseFileList: [],
            tiemr: null,
            arrPlanDispose: []
        };
    },
    onReady() {
        setTimeout(() => {
            this.$refs.lsjUpload.setData('formData.orderId', '动态设置的参数');
        }, 2000);
    },

    onLoad(option) {
        // 获取缓存中的企业信息
        this.enterpriseInfo = uni.getStorageSync('userInfo');
        this.info = JSON.parse(decodeURIComponent(option.info));
        this.isWeakSignalDetail = option.type == 'weakSignal';
        uni.setNavigationBarTitle({
            title: this.info.WRYMC
        });
    },
    onShow() {
        // this.getEnterpriseInfo();
    },

    mounted() {
        this.getEnterpriseInfo();
        this.getScx();
        this.getZwx();
        this.getScsbList();
        this.getZwsbList();
        this.getOtherEquipList();
        this.getFileList();
        this.queryPmtList(this.info.WRYBH);
    },
    destroyed() {},
    onBackPress(e) {
        // uni.addInterceptor('navigateBack', {
        //     invoke(args) {
        //         // request 触发前拼接 url
        //         args.mark = '污染源-列表-详情-离开';
        //         args.model = '污染源-列表';
        //         console.log(args, 'navigateBack');
        //     }
        // });
    },
    methods: {
        //前往实时监控
        toRealTime() {
            uni.navigateTo({
                url: `/pages/enterprise/realTimeMonitor/Index?wrybh=${this.info.WRYBH}`
            });
        },
        //平面图全屏
        toPmtFullScreen(item) {
            uni.navigateTo({
                url: `/pages/planeFigure/FullScreenPlaneFigureCanvas?pmtxh=${item.pmtxh}&pmtmc=${item.pmtmc}&wrybh=${this.info.WRYBH}`
            });
        },
        //查询平面图数组信息
        async queryPmtList(wrybh) {
            const data = await queryPmtxx(wrybh);
            data.forEach((e) => {
                e.dwsj = JSON.parse(e.dwsj);
            });
            this.arrPlanDispose = data;
            if (this.arrPlanDispose.length) {
                this.pmtxh = this.arrPlanDispose[0].pmtxh;
            }
        },
        //获取图片路径
        getPmtFileUrl(item) {
            return DOWNLOAD_URLZDY + item.pmtxh;
        },
        //获取优化情况记录
        getOptimization() {
            let obj = {
                WRYBH: this.info.WRYBH,
                FXID: this.info.FXID
            };
            yhjllist(obj).then((res) => {
                if (res.status === '000') {
                    this.optimizationList = res.data;
                    this.firstSituation = this.optimizationList[0];
                }
            });
        },
        getFileIcon(fileName) {
            // 找到文件名最后一个.的位置
            let fileIndex = fileName.lastIndexOf('.'); // 14
            // 截取.之后的文件格式
            let substrName = fileName.substr(fileIndex); // .jpg
            switch (substrName) {
                case '.jpg':
                    return require('../../static/app/images/filePic.png');
                    break;
                case '.png':
                    return require('../../static/app/images/filePic.png');
                    break;
                case '.pdf':
                    return require('../../static/app/images/filePdf.png');
                    break;
                case '.doc':
                    return require('../../static/app/images/fileWord.png');
                    break;
                case '.docx':
                    return require('../../static/app/images/fileWord.png');
                    break;
                case '.xls':
                    return require('../../static/app/images/fileExc.png');
                    break;
                case '.xlsx':
                    return require('../../static/app/images/fileExc.png');
                    break;
                default:
            }
        },
        // 某文件上传结束回调(成功失败都回调)
        onuploadEnd(item) {
            console.log(`${item.name}已上传结束，上传状态=${item.type}`);
            // 更新当前状态变化的文件
            this.files.set(item.name, item);
            // 演示上传完成后取服务端数据
            if (item['responseText']) {
                console.log('演示服务器返回的字符串JSON转对象');
                this.files.get(item.name).responseText = JSON.parse(
                    item.responseText
                );
            }

            this.getFileList();
            this.$forceUpdate();
        },
        // 上传进度回调
        onprogress(item) {
            // 更新当前状态变化的文件
            this.files.set(item.name, item);
            // 强制更新视图
            this.$forceUpdate();
        },
        // 文件选择回调
        onChange(files) {
            // 更新选择的文件
            this.files = files;
            // 强制更新视图
            this.$forceUpdate();
        },
        // 手动上传
        upload() {
            // name=指定文件名，不指定则上传所有type等于waiting和fail的文件
            this.$refs['lsjUpload'].upload();
        },
        // 移除某个文件
        // clear(name) {
        // 	// name=指定文件名，不传name默认移除所有文件
        // 	this.$refs['lsjUpload'].clear(name);
        // },
        getEnterpriseInfo() {
            let { sjqx } = this.enterpriseInfo;
            let { WRYBH } = this.info;
            getQyxxInfo({
                ORGID: sjqx,
                WRYBH: WRYBH
            }).then((res) => {
                const info = Object.assign(res.data[0], this.info);
                for (let key in info) {
                    if (info[key] == 'null') {
                        info[key] = '';
                    }
                }
                this.info = {...info,...this.info,};
                this.$nextTick(() => {
                    this.option.formData.YWSJID = this.info.WRYBH;
                    this.$refs.lsjUpload.setData('动态设置的参数');
                });
            });
        },
        getScx() {
            let { sjqx } = this.enterpriseInfo;
            let { WRYBH } = this.info;
            getScxList({
                ORGID: sjqx,
                WRYBH: WRYBH
            }).then((res) => {
                this.produceList = res.data;
            });
        },
        getZwx() {
            let { sjqx } = this.enterpriseInfo;
            let { WRYBH } = this.info;
            getZwxList({
                ORGID: sjqx,
                WRYBH: WRYBH
            }).then((res) => {
                this.pollutionList = res.data;
            });
        },
        getScsbList() {
            let { sjqx } = this.enterpriseInfo;
            let { WRYBH } = this.info;
            getScxsbList({
                ORGID: sjqx,
                WRYBH: WRYBH
            }).then((res) => {
                this.produceEquList = res.data;
            });
        },
        getZwsbList() {
            let { sjqx } = this.enterpriseInfo;
            let { WRYBH } = this.info;
            getZwsbList({
                ORGID: sjqx,
                WRYBH: WRYBH
            }).then((res) => {
                this.pollutionEquList = res.data;
            });
        },
        //查询其他设备
        getOtherEquipList() {
            let { sjqx } = this.enterpriseInfo;
            let { WRYBH } = this.info;
            getOtherList({
                ORGID: sjqx,
                WRYBH: WRYBH
            }).then((res) => {
                this.otherEquipList = res.data || [];
            });
        },
        editJbxx(option) {
            uni.navigateTo({
                url:
                    '/pages/enterprise/editEnterpriseInfo?type=edit&info=' +
                    encodeURIComponent(
                        JSON.stringify(this.info).replace(/%/g, '%25')
                    )
            });
        },
        //传参前替换百分号
        rePlaceSpecialCharacters(obj) {
            let replaceObj = {
                ...obj
            };
            for (let key in replaceObj) {
				if(!['',null].includes(replaceObj[key])){
					replaceObj[key] = replaceObj[key].toString().replace(/\%/g, '%25');
				}
            }
            return replaceObj;
        },
        toAddProductionLine() {
            if (!this.info.AZLXR || this.info.AZLXR == 'null') {
                uni.showModal({
                    title: '提示',
                    content: '请先填写安装人员',
                    success: function (res) {
                        if (res.confirm) {
                            console.log('用户点击确定');
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    }
                });
            } else {
                let params = this.rePlaceSpecialCharacters(this.info);
                uni.navigateTo({
                    url:
                        '/pages/enterprise/scx/addProductionLine?type=add&info=' +
                        encodeURIComponent(JSON.stringify(params))
                });
            }
        },
        toAddPollutionLine() {
            if (!this.info.AZLXR || this.info.AZLXR == 'null') {
                uni.showModal({
                    title: '提示',
                    content: '请先填写安装人员',
                    success: function (res) {
                        if (res.confirm) {
                            console.log('用户点击确定');
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    }
                });
            } else {
                let params = this.rePlaceSpecialCharacters(this.info);
                uni.navigateTo({
                    url:
                        '/pages/enterprise/zwx/addPollutionLine?type=add&info=' +
                        encodeURIComponent(JSON.stringify(params))
                });
            }
        },
        toScxDetail(params) {
            let infoParams = this.rePlaceSpecialCharacters(this.info);
            uni.navigateTo({
                url:
                    '/pages/enterprise/scx/productionLineDetail?info=' +
                    encodeURIComponent(JSON.stringify(infoParams)) +
                    '&scxId=' +
                    params.SCXID
            });
        },

        toZwxDetail(params) {
            let infoParams = this.rePlaceSpecialCharacters(this.info);
            uni.navigateTo({
                url:
                    '/pages/enterprise/zwx/pollutionLineDetail?info=' +
                    encodeURIComponent(JSON.stringify(infoParams)) +
                    '&zwxId=' +
                    params.ZWXID
            });
        },
        deleteRelation(item) {
            let self = this;
            uni.showModal({
                title: '提示',
                content: '确定解除此生产线与治污线的关系绑定?',
                success: function (res) {
                    if (res.confirm) {
                        console.log('用户点击确定');
                        deleteRelation({
                            CZWID: item.CZWID
                        }).then((res) => {
                            uni.showToast({
                                title: '删除成功',
                                duration: 500
                            }).then(() => {
                                setTimeout(() => {
                                    self.getRelationList();
                                }, 500);
                            });
                        });
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                }
            });
        },
        getXzqh() {
            let xzqh = '';
            if (this.info.SSSF && this.info.SSSF != 'null') {
                xzqh += this.info.SSSF;
            }
            if (this.info.SSDS && this.info.SSDS != 'null') {
                xzqh += this.info.SSDS;
            }
            if (this.info.SSQX && this.info.SSQX != 'null') {
                xzqh += this.info.SSQX;
            }
            if (this.info.SSJD && this.info.SSJD != 'null') {
                xzqh += this.info.SSJD;
            }
            return xzqh;
        },
        addsb() {
            this.show = true;
        },
        select(item) {
            this.show = false;
            this.toAddEquipment(item.value);
        },
        // 到设备详情
        toSbDetail(v, item) {
            let url = '';
            item.WRYBH = this.info.WRYBH;
            if (v.SBLX == '治污') {
                url = `/pages/enterprise/zwsb/zwEquipmentDetail?info=${encodeURIComponent(
                    JSON.stringify(item)
                )}&sbInfo=${encodeURIComponent(
                    JSON.stringify(v)
                )}&enterpInfo=${encodeURIComponent(JSON.stringify(this.info))}`;
            } else if (v.SBLX == '生产') {
                url = `/pages/enterprise/cwsb/cwEquipmentDetail?info=${encodeURIComponent(
                    JSON.stringify(item)
                )}&sbInfo=${encodeURIComponent(
                    JSON.stringify(v)
                )}&enterpInfo=${encodeURIComponent(JSON.stringify(this.info))}`;
            }
            uni.navigateTo({
                url: url
            });
        },
        // 添加设备
        toAddEquipment(type) {
            let infoParams = this.rePlaceSpecialCharacters(this.info);
            let url = '';
            if (type == 'zw') {
                if (!this.pollutionList.length) {
                    uni.showToast({
                        title: '该企业暂无治污线，请先添加企业治污线',
                        icon: 'none',
                        duration: 3000
                    });
                    return;
                }
                url =
                    '/pages/enterprise/zwsb/addZwEquipment?type=add&info=' +
                    encodeURIComponent(JSON.stringify(infoParams));
            } else {
                if (!this.produceList.length) {
                    uni.showToast({
                        title: '该企业暂无生产线，请先添加企业生产线',
                        icon: 'none',
                        duration: 3000
                    });
                    return;
                }
                url =
                    '/pages/enterprise/cwsb/addCwEquipment?type=add&info=' +
                    encodeURIComponent(JSON.stringify(infoParams));
            }
            uni.navigateTo({
                url: url
            });
        },
        // 跳转其他设备详情
        toAddPhAndRadiation(type, item) {
            if (!this.produceList.length) {
                uni.showToast({
                    title: '该企业暂无生产线，请先添加企业生产线',
                    icon: 'none',
                    duration: 3000
                });
                return;
            }
            let infoParams = this.rePlaceSpecialCharacters(this.info);
            let url = '';
            if (type == 'add') {
                url =
                    '/pages/enterprise/phAndRadiation/PhAndRadiation?type=add&info=' +
                    encodeURIComponent(JSON.stringify(infoParams));
            } else if (type === 'detail') {
                url =
                    '/pages/enterprise/phAndRadiation/PhAndRadiation?type=detail&info=' +
                    encodeURIComponent(JSON.stringify(infoParams)) +
                    '&sbInfo=' +
                    encodeURIComponent(JSON.stringify(item));
            }
            uni.navigateTo({
                url: url
            });
        },
        back() {
            uni.navigateBack({
                delta: 1
            });
        },
        delFile(file) {
            if (this.timer) {
                clearTimeout(this.timer);
            }
            this.timer = setTimeout(() => {
                let self = this;
                uni.showModal({
                    title: '提示',
                    content: '确认删除?',
                    success: function (res) {
                        if (res.confirm) {
                            console.log('用户点击确定');
                            deletefile(file.WJID).then((res) => {
                                uni.showToast({
                                    title: '删除成功',
                                    duration: 500
                                }).then(() => {
                                    setTimeout(() => {
                                        self.getFileList();
                                    }, 500);
                                });
                            });
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    }
                });
            }, 500);
        },
        //添加文件
        addFile(zlx) {
            //WRYZP 污染源照片
            //GYLCT 工艺流程图照片
            //SCQJT 生产车间全景
            //ZWQJT 治污线全景
            if (
                (zlx == 'WRYZP' && this.wryFileList.length >= 20) ||
                (zlx == 'SCQJT' && this.productWorkshopFileList.length >= 20) ||
                (zlx == 'ZWQJT' && this.pollutControlFileList.length >= 20) ||
                (zlx == 'QYPMT' && this.enterpriseFileList.length >= 20) ||
                (zlx == 'QYDMT' && this.gateFileList.length >= 20)
            ) {
                uni.showToast({
                    title: '最多只能上传20张照片',
                    icon: 'none'
                });
                return;
            }
            let lenCount = 20;

            if (zlx == 'SCQJT') {
                lenCount = 20 - this.productWorkshopFileList.length;
            } else if (zlx == 'ZWQJT') {
                lenCount = 20 - this.pollutControlFileList.length;
            } else if (zlx == 'QYPMT') {
                lenCount = 20 - this.enterpriseFileList.length;
            } else if (zlx == 'WRYZP') {
                lenCount = 20 - this.wryFileList.length;
            }
            let self = this;
            uni.chooseImage({
                count: lenCount, //默认9
                sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
                success: function (res) {
                    let len = res.tempFilePaths.length;
                    for (var i = 0; i < len; i++) {
                        self.uploadFile(res, zlx, i);
                    }
                }
            });
        },
        paintWatermark(imageUrl, multiLineTexts, fileName) {
            return this.$refs.watermarkPainter.paintWatermark(
                imageUrl,
                multiLineTexts,
                fileName
            );
        },
        uploadFile(res, zlx, i) {
            let f = res.tempFilePaths[i];
            let { size, type } = res.tempFiles[0];
            let self = this;
            uni.showLoading({
                title: '上传中'
            });
            uni.uploadFile({
                url: UPLOAD_URL,
                filePath: f,
                name: 'file',
                formData: {
                    WJDX: size / 1024,
                    WJLX: type,
                    pageSize: 100000,
                    pageNum: 1,
                    YWSJID: this.info.WRYBH,
                    LXDM: 'ZDFJ',
                    ZLXDM: zlx
                },
                timeout: 6000,
                success: function (res) {
                    uni.showToast({
                        title: '上传成功',
                        icon: 'none'
                    });
                    self.getFileList();
                    uni.hideLoading();
                },
                fail: function (err) {
                    console.log('err===>', err);
                    uni.showToast({
                        title: '上传失败',
                        icon: 'none'
                    });
                    uni.hideLoading();
                }
            });
        },

        getFileList() {
            let obj = {
                pageSize: 100000,
                pageNum: 1,
                YWSJID: this.info.WRYBH,
                LXDMS: 'ZDFJ',
                ZLXDMS: 'WRYZP,GYLCT,ZDSB,ZWQJT,SCQJT,CZWGXB,QYPMT,QYDMT'
            };
            getFilelist(obj)
                .then((res) => {
                    let fileData = res[0];
                    if (
                        fileData &&
                        fileData.zlxList &&
                        fileData.zlxList.length > 0
                    ) {
                        fileData.zlxList.forEach((list) => {
                            if (list.ZLXDM == 'WRYZP') {
                                this.wryFileList = list.fileList;
                            } else if (list.ZLXDM == 'SCQJT') {
                                this.productWorkshopFileList = list.fileList;
                            } else if (list.ZLXDM == 'ZWQJT') {
                                this.pollutControlFileList = list.fileList;
                            } else if (list.ZLXDM == 'CZWGXB') {
                                this.fileList = list.fileList;
                            } else if (list.ZLXDM == 'QYPMT') {
                                this.enterpriseFileList = list.fileList;
                            } else if (list.ZLXDM == 'QYDMT') {
                                this.gateFileList = list.fileList;
                            }
                        });
                    }
                })
                .catch((err) => {
                    console.log('err', err);
                });
        },
        getFileUrl(item) {
            return DOWNLOAD_URLZDY + item.WJID;
        },
        //文件预览
        preFile(item) {
            if (this.timer) {
                clearTimeout(this.timer);
            }
            this.timer = setTimeout(() => {
                let self = this;
                let str = item.MLSY.slice(35);
                let url = PREVIEW_FILE_URL + item.MLSY.slice(35);
                uni.downloadFile({
                    url: url,
                    success: function (res) {
                        let filepathss = plus.io.convertLocalFileSystemURL(
                            res.tempFilePath
                        );
                        setTimeout(
                            () =>
                                uni.openDocument({
                                    filePath: filepathss,
                                    showMenu: false,
                                    success: function () {
                                        console.log('打开文档成功');
                                    },
                                    fail: function () {
                                        uni.showToast({
                                            title: '暂不支持此类型',
                                            duration: 2000,
                                            icon: 'none'
                                        });
                                    }
                                }),
                            200
                        );
                    },
                    fail: function (res) {
                        console.log(res); //失败
                    }
                });
            }, 500);
        },
        previewImage(fileList, index) {
            let self = this;
            let fileUrls = fileList.map((file) => {
                return this.getFileUrl(file);
            });
            console.log('fileUrls', fileUrls);
            // 预览图片
            uni.previewImage({
                current: index,
                urls: fileUrls,
                //长按保存到本地
                longPressActions: {
                    itemList: ['保存图片到本地'],
                    success: (data) => {
                        // console.log('data', data)
                        if (data.tapIndex == 0) {
                            let imgurl = fileUrls[data.index];
                            self.saveImage(imgurl);
                        }
                    },
                    fail: function (err) {
                        console.log(err.errMsg);
                    }
                }
            });
        },

        //保存图片
        saveImage(imgurl) {
            // console.log(imgurl)
            uni.downloadFile({
                url: imgurl,
                success(res) {
                    let url = res.tempFilePath;
                    uni.saveImageToPhotosAlbum({
                        filePath: url,
                        success() {
                            uni.showToast({
                                title: '已存至系统相册',
                                icon: 'success'
                            });
                        },
                        fail(err) {
                            uni.showToast({
                                title: '保存失败',
                                icon: 'error'
                            });
                        }
                    });
                }
            });
        }
    }
};
</script>

<style scoped lang="scss">
// .inner{height:auto;}
.zy-nodata2 {
    image {
        width: 100rpx;
        height: 90rpx;
    }
}

.add {
    width: 100%;
    text-align: center;
    color: #57b3fe;
    font-weight: 500;
    font-size: 28rpx;
}

.addsb {
    position: relative;

    div {
        position: absolute;
        width: 100%;
        text-align: center;
        color: #57b3fe;
        font-weight: 500;
        bottom: 10rpx;
        font-size: 28rpx;
    }
}

.listWarp {
    width: 100%;
    height: 100%;
    padding: 20rpx 0;
    overflow-y: scroll;
}

.listWarp p {
    width: 100%;
    padding: 20rpx;
    text-align: center;
    border-bottom: 1px solid #efefef;
}

.listWarp p:last-child {
    border-bottom: none;
}

.opration {
    padding: 20rpx;
    display: flex;
    justify-content: space-between;
    position: absolute;
    width: 100%;

    background-color: #fff;
}

.confirm {
    color: rgb(60, 170, 255);
}

.on {
    color: rgb(60, 170, 255);
}

.zy-form .item .label {
    flex: 1;
}

.zy-form.info .item .label {
    width: 30%;
    flex: unset;
}

.inner {
    padding-top: 0;
}

.dlgcls {
    padding: 16rpx;
}

.image-box {
    display: flex;
    justify-content: space-around;
}

.image-box image {
    width: 45%;
    height: 360rpx;
}

.pd-dltxt1 dd {
    white-space: pre-wrap;
}

.pd-ulpic1 li .delImg {
    position: absolute;
    right: 0rpx;
    top: 0rpx;
    width: 34rpx;
    height: 34rpx;
    border-radius: 0 0 0 10rpx;
    opacity: 0.6;
}

.pd-pic {
    height: 360rpx;
}
.pd-ulpic1 {
    flex-wrap: wrap;
}
.pd-ulpic1 li {
    position: relative;
    width: 33.33%;
    height: auto;
    margin: 0;
    padding: 8rpx;
    background: #fff;
}
.pd-ulpic1 li .img-content {
    position: relative;
    width: 100%;
    height: 160rpx;
    overflow: hidden;
    border-radius: 10rpx;
    background: #ddd;
    .pic {
        display: block;
        width: 100%;
        height: 160rpx;
        margin: 0 auto;
        border-radius: 10rpx;
    }
}

.pd-ulpic1 li .picture {
    display: block;
    width: 100%;
    height: 100%;
    border-radius: 10rpx;
}

.pd-ulpic1 li .picbox {
    border-radius: 10rpx;
    width: 100%;
    height: 100%;
    position: relative;
}

.btn {
    height: 100rpx;
    border-radius: 10rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    background-color: transparent;
}

.otherFile {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 16rpx 10rpx 0rpx;
    border-bottom: 1rpx solid #eee;
    position: relative;
    z-index: 99;
}

.otherFile .file-icon {
    display: inline-block;
    width: 46rpx;
    height: 46rpx;
    position: relative;
    top: 10rpx;
    left: 0rpx;
}

.otherFile .file-icon .img {
    width: 30rpx;
}

.otherFile .title {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
}

.filelistbox {
    padding-bottom: 20rpx;
}

.filelist {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 8rpx;
    border-bottom: 1rpx solid #eee;
    font-size: 26rpx;
}

.filelist .left {
    flex: 1;
    display: flex;
    align-items: center;
}

.filelist .left .file-icon {
    width: 42rpx;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.filelist .left .file-icon .img {
    width: 38rpx;
}

.filelist .left .filename {
    flex: 1;
    word-wrap: break-word;
    word-break: break-all;
    white-space: pre-wrap;
}

.filelist .right {
    width: 150rpx;
    display: flex;
    justify-content: flex-end;
    flex-direction: row;
}

.filelist .right span {
    padding: 0 14rpx;
}

.file-icon {
    display: inline-block;
    width: 36rpx;
    height: 36rpx;
    margin-right: 12rpx;
}

.file-icon .img {
    width: 100%;
    height: 100%;
}

.nodata {
    color: #999;
    text-align: center;
    font-size: 26rpx;
    padding: 10rpx 0;
}
.zy-form .item .upload-title {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
}
.fjbtn {
    bottom: 0;
}
.zy-form .down {
    color: #f90b16;
    font-size: 13rpx;
    padding: 4rpx 20rpx;
    position: relative;
    left: 20rpx;
    top: -8rpx;
    background: rgba(249, 11, 22, 0.2);
    border-radius: 10px 10px 10px 0;
}
</style>
