<!DOCTYPE html>
<html>

    <head>
        <meta charset="UTF-8">
        <title></title>
        <style>
            #newCss {
                border: 1px solid #999;
                width: 100%;
                height: 600px;
                overflow: auto;
            }
        </style>
        <script src="https://cdn.bootcss.com/jquery/3.4.1/jquery.min.js"></script>
    </head>

    <body>
        <h4>rem样式</h4>
        <textarea id="css" style="width: 100%;" rows="10"></textarea>
        <br />
        <input type="button" class="btn" value="rem转换rpx" />
		<h4>转换后的样式 <button id="copy">复制</button></h4>
		<pre id="newCss"></pre>

        <script type="text/javascript">
            $(function(){
                $('.btn').click(function(){
                    var str =$('#css').val();
                    console.log(str);
                    //1rem,1.2rem,0.2rem,.2rem ,-1rem,-1.2rem,-0.2rem
                    var reg = /^(\d+rem)|(\d+\.\d+rem)|(\.\d+rem)|(-\d+rem)|(-\d+\.\d+rem)/g;
                    var str2 = str.replace(reg,function(aaa){
                        console.log(aaa);
                        return   Math.floor(parseFloat(aaa)*75 * 10000000) / 10000000   +'rpx';
                    })
                    console.log(str2);
                    $('#newCss').html(str2)
                })

				$('#copy').click(function(){
					var text = $('#newCss').text();
					var textarea = document.createElement("textarea");
					textarea.value = text;
					document.body.appendChild(textarea);
					textarea.select();
					document.execCommand('Copy');
					document.body.removeChild(textarea);
					//alert('复制成功')
				})
            })
        </script>
    </body>

</html>
