<!-- @format -->

<template>
    <view id="naviBar" class="navi-bar" :style="style">
        <view class="navi-bar-navi-button" @click="onNaviClick">
            <slot name="navi">
                <image
                    v-show="naviBack"
                    :src="naviIcon"
                    style="width: 39rpx; height: 39rpx; margin-left: 2rpx"
                ></image>
                <text
                    v-show="naviBack"
                    class="navi-bar-text"
                    :style="textStyle"
                >
                    {{ textLeft }}
                </text>
            </slot>
        </view>
        <text class="navi-bar-title navi-bar-text" style="color: #ffffff">{{
            title
        }}</text>
        <view class="navi-bar-option-button" @click="onOptionClick">
            <slot name="option">
                <text class="navi-bar-text" :style="textStyle">
                    {{ textRight }}
                </text>
            </slot>
        </view>

        <view
            class="navi-bar-search flex-row-layout"
            v-if="showSearch"
            :style="searchStyle"
        >
            <input
                class="uni-input navi-bar-search-keyword"
                type="text"
                confirm-type="search"
                placeholder="请输入关键字"
                :focus="focus"
                v-model="keyword"
                @keydown.enter="search()"
                @confirm="search()"
            />
            <text style="font-size: 32rpx" @click="disableSearch">取消 </text>
        </view>
    </view>
</template>

<script>
import defaultNaviIcon from '@/static/img/icon_back_white.png';

export default {
    props: {
        backgroundColor: {
            type: String,
            default: '#4874ff'
        },

        title: {
            type: String,
            default: '标题'
        },

        textLeft: {
            type: String,
            default: ''
        },

        textRight: {
            type: String,
            default: ''
        },

        naviIcon: {
            type: String,
            default: defaultNaviIcon
        },

        textColor: {
            type: String,
            default: '#fff'
        },

        naviBack: {
            type: Boolean,
            default: true
        }
    },

    computed: {
        style: function () {
            return {
                'background-color': this.backgroundColor,
                color: this.textColor
            };
        },

        searchStyle: function () {
            return {
                'background-color': this.backgroundColor
            };
        },

        textStyle: function () {
            return {
                'font-size': '32rpx',
                color: this.textColor
            };
        },

        isOptionClickable: function () {
            return this.textRight && this.textRight.length > 0;
        }
    },

    data() {
        return {
            handleBack: null,
            showSearch: false,
            focus: false,
            keyword: ''
        };
    },

    mounted() {
        let _self = this;
        this.$on('showSearch', () => {
            _self.showSearch = true;
        });

        // let plusReady = function (callback) {
        //     if (window.plus) {
        //         callback();
        //     } else {
        //         document.addEventListener('plusready', callback);
        //     }
        // };

        // let webView = plus.webview.currentWebview();
        // plusReady(function () {
        //     let firstBack = 0;
        //     window.handleBack = function () {
        //         let now =
        //             Date.now ||
        //             function () {
        //                 return new Date().getTime();
        //             };

        //         let url = location.pathname;
        //         if (url.indexOf('pages/book/book-detail') >= 0) {
        //             webView.close();
        //         } else {
        //             webView.back();
        //         }
        //     };
        //     plus.key.addEventListener('backbutton', window.handleBack);
        // });
    },

    destroyed() {
        // plus.key.removeEventListener('backbutton', window.handleBack);
    },

    methods: {
        onNaviClick() {
            let _self = this;

            // let url = location.pathname;
            // if (url.indexOf('pages/book/book-detail') >= 0) {
            //     let webView = plus.webview.currentWebview();
            //     webView.close();
            // } else {

            // }

            _self.$emit('naviClick');
            uni.navigateBack({
                delta: 1
            });
        },

        onOptionClick() {
            if (this.isOptionClickable) {
                //触发选项按钮监听
                this.$emit('optionClick');
            }
        },

        disableSearch() {
            this.focus = false;
            this.showSearch = false;
            this.keyword = '';
            this.search();
        },

        search() {
            this.$emit('search', this.keyword);
        }
    }
};
</script>

<style scoped>
.navi-bar {
    position: fixed;
    width: 100%;
    height: 96rpx;
    display: flex;
    flex-flow: row nowrap;
    justify-content: center;
    align-items: center;
    padding: 0 16rpx;
    color: '#FFFFFF';
    z-index: 999;
}

.navi-bar-title {
    width: 50%;
    line-height: 100%;
    text-align: center;
    display: inline-block;
    overflow: hidden;
    /* max-width: 400px; */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.navi-bar-text {
    font-size: 32rpx;
}

.navi-bar-navi-button {
    width: 25%;
    height: 100%;
    display: flex;
    padding-left: 16rpx;
    justify-content: flex-start;
    align-items: center;
}

.navi-bar-option-button {
    font-size: 28rpx;
    width: 25%;
    height: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-right: 16rpx;
}

.navi-bar-search {
    padding: 0px 10px;
    width: calc(100% - 40rpx);
    height: 100%;
    position: absolute;
    left: 0px;
    top: 0px;
}

.navi-bar-search-keyword {
    flex: 1;
    border-radius: 100rpx;
    line-height: 64rpx;
    padding: 10rpx 32rpx;
    margin-right: 16px;
    font-size: 32rpx;
    background-color: #fff;
    color: #333;
}
</style>
