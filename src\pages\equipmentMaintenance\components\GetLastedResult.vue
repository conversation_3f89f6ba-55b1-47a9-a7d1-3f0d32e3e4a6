<!-- @format -->

<template>
    <div>
        <div class="zy-form">
            <div class="item">
                <p
                    class="label"
                    style="color: #4874ff; flex: auto"
                    @click="changeShowThreeList"
                >
                    查看最近三次采集结果
                </p>
                <div class="rp">
                    <p class="pd-txt2" v-show="showThreeList">
                        <span class="show-newData">已展示采集结果</span>
                        <u-icon
                            name="reload"
                            color="#f4f7fa"
                            size="22"
                            @click="getnewStateList()"
                        ></u-icon>
                    </p>
                </div>
            </div>
        </div>
        <div class="gap"></div>
        <!-- 采集结果 -->
        <div v-show="showThreeList">
            <EquipStateList :list="threeList"></EquipStateList>
        </div>
    </div>
</template>

<script>
import { newStateList } from '@/api/iot/enterprise.js';
import EquipStateList from '@/pages/component/EquipStateList';
export default {
    name: 'DataCollectionAppGetLastedResult',
    props: {
        imei: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            showThreeList: false,
            threeList: []
        };
    },
    components: {
        EquipStateList
    },
    mounted() {},

    methods: {
        //展示列表最新3条数据数据
        changeShowThreeList() {
            if (this.imei.length == '') {
                uni.showToast({
                    title: '请先扫码获取IMEI号imei',
                    icon: 'none'
                });
                return;
            }
            if (!this.threeList || !this.threeList.length) {
                this.getnewStateList();
            }
            this.showThreeList = !this.showThreeList;
        },
        //获取最新的3条数据
        getnewStateList() {
            this.threeList = [];
            newStateList({
                IMEI: this.imei
            }).then((res) => {
                this.threeList = [...res.data];
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
