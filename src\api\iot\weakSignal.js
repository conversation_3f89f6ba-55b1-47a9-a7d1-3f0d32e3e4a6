

import axios from '@/common/ajaxRequest.js';
import { ULR_BASE } from '@/common/config.js';

// 信号弱企业列表
export const qyxxlist = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/sbyw/xhr/qyxx/list',
        params: data
    });
};

// 信号弱设备及最后上报状态数据
export const zdsblist = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/sbyw/xhr/zdsb/list',
        params: data
    });
};

// 信号弱优化情况记录列表
export const yhjllist = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/sbyw/xhr/yhjl/list',
        params: data
    });
};


// 信号弱优化情况记录新增
export const yhjladd = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/sbyw/xhr/yhjl/add',
        params: data
    });
};

// 信号弱优化情况记录编辑
export const yhjledit = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/sbyw/xhr/yhjl/edit',
        params: data
    });
};

// 设备历史状态列表
export const ztxxlist = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/baseinfo/zdsb/ztxx/list',
        params: data
    });
};

// 信号弱统计
export const xhrzttj = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/sbyw/xhr/zttj',
        params: data
    });
};
