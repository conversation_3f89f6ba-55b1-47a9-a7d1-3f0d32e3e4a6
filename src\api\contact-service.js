/*
 * @Author: your name
 * @Date: 2021-04-19 10:30:40
 * @LastEditTime: 2021-07-12 11:12:37
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /YDZF_APP/api/contact-service.js
 */
import http from '@/common/net/http.js';
import { deepCopyObject } from '@/common/merge.js';
import loginService from '@/api/login-service.js'

const USERS_CACHE_KEY = 'organization_users';

const getCacheKey = () => {
	return `${USERS_CACHE_KEY}@${loginService.getUserOrgid()}`
}

/**
 * 同步用户信息
 */
export const syncUsers = () => {
	return new Promise((resolve, reject) => {
		http.post(`${http.url}`, {
			service: "QUERY_ZFYH_INFO",
			version: "1",
			isDelDept:true
		})
			.then(resp => {
				if(resp.length > 0){
					saveUsersToLocal(resp);
				}
				resolve(resp)
			})
			.catch(error => {
			});
	});
}

const saveUsersToLocal = (users) => {
	uni.setStorageSync(getCacheKey(), users);
}

export const getDepartmentMenbers = (menbers, departmentId) => {
	if(menbers){
		return menbers.filter(m => {
			return m.BMBH = departmentId;
		})
	}
	return [];
}

/**
 * 加载所有用户
 */
export const loadUsers = (refresh = false) => {
	return new Promise((resolve, reject) => {
		let localSource = null;
		if(!refresh){
			localSource = uni.getStorageSync(getCacheKey());
		}
		
		if(localSource){
			resolve(deepCopyObject(localSource));
		} else {
			resolve(syncUsers());
		}
	});
}

/**
 * 根据用户ID数组过滤用户
 */
const filterUsersByIds = (users, userIds) => {
	return users.filter(user => {
		return userIds.indexOf(user.YHID) !== -1;
	});
}

export default {
	syncUsers,
	getDepartmentMenbers,
	loadUsers,
	filterUsersByIds
}