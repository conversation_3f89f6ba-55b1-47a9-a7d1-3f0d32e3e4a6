<!-- @format -->

<template>
    <div class="dialog-box zy-form">
        <header class="blue-header">
            <i class="ic-back" @click="handleValidate"></i>
            <h1 class="lf-title">报警阈值设置</h1>
        </header>
        <div class="item input-box">
            <p class="label star describe-box">PH值下限:</p>
            <input
                :disabled="isDetailPageShow()"
                class="input-msg"
                type="number"
                v-model="phFloorLimit"
                placeholder="请输入PH值下限"
            />
        </div>
		<div class="item input-box">
		    <p class="label star describe-box">PH值上限:</p>
		    <input
		        :disabled="isDetailPageShow()"
		        class="input-msg"
		        type="number"
		        v-model="phUpperLimit"
		        placeholder="请输入PH值上限"
		    />
		</div>
    </div>
</template>

<script>
export default {
    name: 'AlarmThreshold',
    props: {
        //页面类型
        pageType: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            phUpperLimit: 0,
            phFloorLimit: 0,
            QTYZ: ''
        };
    },
    methods: {
        isDetailPageShow() {
            return this.pageType === 'detail';
        },
        handleValidate() {
			console.log(typeof this.phFloorLimit)
			if(!this.phFloorLimit){
				uni.showToast({
				    title: 'PH下限值不能为空',
				    duration: 2000,
				    icon: 'none'
				});
				return;
			}else if(!this.phUpperLimit){
				uni.showToast({
				    title: 'PH上限值不能为空',
				    duration: 2000,
				    icon: 'none'
				});
				return;
				
			}
            if (this.phFloorLimit > this.phUpperLimit) {
                uni.showToast({
                    title: 'PH下限值需要小于等于PH上限值',
                    duration: 2000,
                    icon: 'none'
                });
                return;
            }
            this.QTYZ = `${this.phFloorLimit || 0}#${this.phUpperLimit || 0}`;
            this.$emit('setPHValue', this.QTYZ);
        },
        setPHvalue(value) {
            const phArr = value.split('#');
            this.QTYZ = value;
            this.phFloorLimit = phArr[0];
            this.phUpperLimit = phArr[1];
        }
    }
};
</script>

<style scoped lang="scss">
.blue-header {
    background-color: rgb(72, 116, 255);
}
.lf-title {
    font-size: 32rpx;
    font-weight: bold;
}
.dialog-box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    background: #f2f2f2;
    z-index: 1001;
    font-size: 30rpx;
    .uni-input-placeholder {
        text-align: left;
    }
}

.dialog-box .head {
    background: #4874ff;
    padding: 10rpx 20rpx;
    line-height: 60rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dialog-box .head .title {
    font-weight: bold;
    font-size: 36rpx;
}

.dialog-box .send {
    color: #fff;
    background: #4874ff;
    border-radius: 6rpx;
    padding: 4rpx 10rpx;
    font-size: 26rpx;
    line-height: 40rpx;
}

.dialog-box .input-box {
    padding: 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    &::after {
        content: '*';
        position: absolute;
        left: -6px;
        top: 0;
        color: red;
    }
}

.dialog-box .input-msg {
    border-radius: 6rpx;
    line-height: 80rpx;
    padding: 6rpx 6rpx 6rpx 10rpx;
    height: 66rpx;
    width: 80%;
}

.dialog-box .input-parent {
    position: relative;
}

.gray-icon {
    width: 38rpx;
}

.zy-form {
    padding-left: 0;
}
</style>
