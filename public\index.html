<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>
      <%= htmlWebpackPlugin.options.title %>
    </title>
    <!-- <script src="https://cdn.bootcss.com/vConsole/3.2.0/vconsole.min.js"></script>
    <script>
      //初始化
      let vConsole = new VConsole();
      
      //然后就可以直接使用了
      console.log('111')
    </script> -->
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        document.documentElement.style.fontSize =
          document.documentElement.clientWidth / 20 + 'px'
      })
      var coverSupport =
        'CSS' in window &&
        typeof CSS.supports === 'function' &&
        (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'))
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
          (coverSupport ? ', viewport-fit=cover' : '') +
          '" />'
      )
    </script>
     <!-- <link rel="stylesheet" href="<%= htmlWebpackPlugin.options.baseUrl %>static/index.css" /> -->
     <link rel="stylesheet" href="<%= htmlWebpackPlugin.options.baseUrl %>static/css/power.css" />
     <link rel="stylesheet" href="<%= htmlWebpackPlugin.options.baseUrl %>static/css/form.css" />
	
    <link
      rel="stylesheet"
      href="<%= BASE_URL %>static/index.<%= VUE_APP_INDEX_CSS_HASH %>.css"
    />
	
  </head>

  <body>
    <noscript>
      <strong>Please enable JavaScript to continue.</strong>
    </noscript>
    <div id="app"></div>

    <script>

window._AMapSecurityConfig = {
            securityJsCode:'e7d1cc9b5f55ee480345c1381ed4cee9',
        }
    </script>
    <!-- built files will be auto injected -->
    <!-- <script src="./static/js/echarts.min.js"></script> -->
<!-- <script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script> -->
<!--     <script>
    //VConsole will be exported to `window.VConsole` by default.
    var vConsole = new window.VConsole();
    </script> -->
  </body>
</html>
