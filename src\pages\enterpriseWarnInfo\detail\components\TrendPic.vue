<!-- @format -->

<template>
    <div class="tabs1-con">
        <div class="gap"></div>
        <div class="qiye-tu">
            <TendencyChart
                ref="tendencyChart"
                :curEquit="curEquit"
                :dateStr="dateStr"
            ></TendencyChart>
        </div>
        <div class="qiye-board">
            <div class="zy-line ac jb">
                <p class="qiye-til1">生产设备</p>
            </div>

            <div class="qiye-shebei">
                <radio-group
                    @change="radioChange($event, item, index)"
                    v-for="(item, index) in productList"
                    :key="item.SBXH"
                    :data-item="item"
                >
                    <label class="item radiogroup">
                        <div class="left">
                            <div class="ic" style="background-color: #3873ff">
                                <i style="background-color: #3873ff"></i>
                            </div>
                            <view>{{ item.SBMC }}</view>
                        </div>
                        <view>
                            <radio
                                style="transform: scale(0.7)"
                                :value="item.SBXH"
                                :checked="item.SBXH === curEquit.SBXH"
                            />
                        </view>
                    </label>
                </radio-group>
            </div>

            <div class="gap"></div>
            <div class="zy-line ac jb" v-if="polluteList.length != 0">
                <p class="qiye-til1">治污设备</p>
            </div>
            <div class="qiye-shebei" v-if="polluteList.length != 0">
                <radio-group
                    @change="radioChange($event, item, index)"
                    v-for="(item, index) in polluteList"
                    :key="item.SBXH"
                    :data-item="item"
                >
                    <label class="item radiogroup">
                        <div class="left">
                            <div class="ic" style="background-color: #3873ff">
                                <i style="background-color: #3873ff"></i>
                            </div>
                            <view>{{ item.SBMC }}</view>
                        </div>
                        <view>
                            <radio
                                style="transform: scale(0.7)"
                                :value="item.SBXH"
                                :checked="item.SBXH === curEquit.SBXH"
                            />
                        </view>
                    </label>
                </radio-group>
            </div>
            <div class="gap"></div>
            <div class="gap"></div>
            <div class="gap"></div>
        </div>

        <div class="gap"></div>
    </div>
</template>
<script>
import { sbsxzt } from '../../../../api/iot/runningData.js';
import TendencyChart from './TendencyChart.vue';
export default {
    components: {
        TendencyChart
    },
    props: {
        currentscx: {
            type: String,
            default: ''
        },
        dateStr: {
            type: String,
            default: ''
        }
    },
    watch: {
        currentscx: {
            handler: function (nv) {
                //更新设备
                this.gettjt();
            }
        }
    },
    data() {
        return {
            trendData: [],
            productList: [],
            polluteList: [],
            curEquit: {} //当前设备
        };
    },
    created() {},
    methods: {
        //获取设备列表
        gettjt() {
            sbsxzt({
                SCXID: this.currentscx,
                DATE: this.dateStr
            }).then((res) => {
                this.productList = res.data.scList.reverse();
                this.polluteList = res.data.zwList.reverse();
                this.curEquit = this.productList[0];
                this.$refs.tendencyChart.getTrendPic();
            });
        },
        radioChange($event) {
            this.curEquit = $event.currentTarget.dataset.item;
        }
    }
};
</script>

<style scoped>
.qiye-board {
    position: relative;
}
.radiogroup {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 28rpx;
}
.radiogroup .left {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}
.radiogroup .radio {
    transform: scale(0.5);
}
</style>
