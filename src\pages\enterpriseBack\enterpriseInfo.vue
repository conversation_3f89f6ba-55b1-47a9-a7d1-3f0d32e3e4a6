<template>
	<view style="background-color: #f1f2f6;" class="warp">
		<div class="zy-qiye">
			<header class="header">
				<i class="ic-back" @click="back"></i>
				<h1 class="title">污染源详情</h1>

				<div class="zy-add">
					<i class="add-icon" @click="showAddItems = !showAddItems"></i>
					<ul class="xiala" v-if="showAddItems">
						<li @click="toAddProductionLine">添加生产线</li>
						<li @click="toAddEquipment('zw')">添加治污设备</li>
						<li @click="toAddEquipment('cw')">添加产污设备</li>
					</ul>
				</div>
			</header>
			<section class="main" style="padding: 79.7101rpx 0 90.5796rpx;">
				<div class="inner">
					<ul class="zy-data3">
						<li class="li1">
							<div class="ic"></div>
							{{ info.WRYMC || '-' }}
						</li>
						<li class="li2">
							<div class="ic"></div>
							{{ info.DWDZ || '-' }}
						</li>
						<li class="li3">
							<div class="ic"></div>
							{{ info.HBLXR || '-' }}
						</li>
						<li class="li4">
							<div class="ic"></div>
							{{ info.HBLXRDH || '-' }}
						</li>
						<li class="li5">
							<div class="ic"></div>
							{{ info.YYCJ || '-' }}
						</li>
					</ul>

					<div class="zy-nodata1" v-if="infoList.length == 0">
						<img src="../../static/images/zy-nodata.png" alt="" />
						<div class="add" @click="toAddProductionLine">去添加 >></div>
					</div>
					<div class="zy-data4-wrap" v-if="infoList.length != 0">
						<ul class="zy-data4">
							<li v-for="(item, index) in infoList">
								<h3 @click="toScxDetail(item)">{{ item.SCXMC || '-' }}</h3>
								<ul class="info">
									<li>
										<p class="biaoti">工艺名称：</p>
										<p class="p1">{{ item.GYMC || '-' }}</p>
									</li>
									<li>
										<p class="biaoti">工艺特征：</p>
										<p class="p1">{{ item.GYTZ || '-' }}</p>
									</li>
									<li>
										<p class="biaoti">污染物：</p>
										<p v-if="!item.ZYWR" class="p2"><span>-</span></p>
										<p v-else class="p2" v-for="(zywr, i) in item.ZYWR.split(',')">
											<span v-if="zywr">{{ zywr }}</span>
										</p>
									</li>
									<li class="noflex" v-if="item.GLSB && item.GLSB.length == 0">
										<p class="biaoti">关联设备：</p>
										<div class="zy-nodata2 addsb">
											<image src="../../static/images/zy-nodata2.png"></image>
											<div @click="addsb">去添加 >></div>
										</div>
									</li>
									<li class="noflex" v-if="item.GLSB && item.GLSB.length > 0">
										<p class="biaoti">关联设备：</p>
										<p class="p3 jiantou" v-for="(ele, ind) in item.GLSB" :key="ind" @click="toSbDetail(ele)">
											<span class="s1">{{ ele.SBLX || '-' }}：{{ ele.SBMC || '-' }}</span>
											<span class="s2">{{ ele.SBSJ || '-' }}</span>
										</p>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</div>
			</section>
		</div>

		<u-popup v-model="show" mode="bottom" width="100%" height="16%" border-radius="20">
			<!-- <div class="opration">
				<span @click="cancel" class="cancel">取消</span>
				<span @click="confirm" class="confirm">确定</span>
			</div> -->
			<div class="listWarp">
				<p v-for="(item, index) in list" @click="select(item)">{{ item.text }}</p>
			</div>
		</u-popup>
	
	</view>
</template>

<script>

import { getScx, getScxsb } from '@/api/iot/enterprise.js';
export default {
	components:{
		AddPhotoDialog
	},
	data() {
		return {
			enterpriseInfo: {}, //
			info: {}, //上个页面带来的参数
			infoList: [],
			showAddItems: false,
			show: false,
			cancel: false,
			list: [
				{
					text: '添加治污设备',
					value: 'zw'
				},
				{
					text: '添加产污设备',
					value: 'cw'
				}
			],
			tips: {
				text: ''
			},
			showAddPhotoDialog:true
		};
	},
	onLoad(option) {
		// 获取缓存中的企业信息
		this.enterpriseInfo = uni.getStorageSync('userInfo');
		this.info = JSON.parse(decodeURIComponent(option.info));
		this.getEnterpriseInfo();
	},
	onShow() {
		if (uni.getStorageSync('isAddEquipment')) {
			this.getEnterpriseInfo();
			uni.setStorageSync('isAddEquipment',false)
		}
	},
	mounted() {},
	methods: {
		getEnterpriseInfo() {
			let { orgid } = this.enterpriseInfo;
			let { WRYBH } = this.info;
			getScx({ ORGID: orgid, pageSize: this.pageSize, orderBy: 'CJSJ', orderWay: 'DESC', WRYBH: WRYBH, SCXID: '' }).then(res => {
				if (res.data_array && res.data_array.length > 0) {
					let data = JSON.parse(JSON.stringify(res.data_array));
					let len = data.length;
					let arr = [];
					for (let i = 0; i < len; i++) {
						getScxsb({ SCXID: data[i].SCXID }).then(res => {
							data[i].GLSB = res.data_array || [];
							arr.push(data[i]);
						});
					}
					this.infoList = arr;
				}
			});
		},
		toAddProductionLine() {
			uni.navigateTo({
				url: '/pages/enterprise/scx/addProductionLine?info=' + encodeURIComponent(JSON.stringify(this.info))
			});
		},
		toScxDetail(params) {
			uni.navigateTo({
				url: '/pages/enterprise/scx/productionLineDetail?info=' + encodeURIComponent(JSON.stringify(this.info)) + '&scxId=' + params.SCXID
			});
		},

		addsb() {
			this.show = true;
		},
		select(item) {
			this.show = false;
			this.toAddEquipment(item.value);
		},
		// 到设备详情
		toSbDetail(v) {
			let url = '';
			if (v.SBLX == '治污') {
				url = `/pages/enterprise/zwsb/zwEquipmentDetail?info=${encodeURIComponent(JSON.stringify(this.info))}&sbInfo=${encodeURIComponent(JSON.stringify(v))}`;
			} else if (v.SBLX == '生产') {
				url = `/pages/enterprise/cwsb/cwEquipmentDetail?info=${encodeURIComponent(JSON.stringify(this.info))}&sbInfo=${encodeURIComponent(JSON.stringify(v))}`;
			}
			uni.navigateTo({
				url: url
			});
		},
		// 添加设备
		toAddEquipment(type) {
			let url = '';
			if (type == 'zw') {
				url = '/pages/enterprise/zwsb/addZwEquipment?info=' + encodeURIComponent(JSON.stringify(this.info));
			} else {
				url = '/pages/enterprise/cwsb/addCwEquipment?info=' + encodeURIComponent(JSON.stringify(this.info));
			}
			uni.navigateTo({
				url: url
			});
		},
		back() {
			uni.navigateBack({
				delta: 1
			});
		}
	}
};
</script>

<style scoped lang="scss">
.zy-nodata2 {
	image {
		width: 100rpx;
		height: 90rpx;
	}
}
.add {
	width: 100%;
	text-align: center;
	color: #57b3fe;
	font-weight: 500;
	font-size: 28rpx;
}
.addsb {
	position: relative;
	div {
		position: absolute;
		width: 100%;
		text-align: center;
		color: #57b3fe;
		font-weight: 500;
		bottom: 10rpx;
		font-size: 28rpx;
	}
}
.listWarp {
	width: 100%;
	height: 100%;
	padding: 20rpx 0;
	overflow-y: scroll;
}
.listWarp p {
	width: 100%;
	padding: 20rpx;
	text-align: center;
	border-bottom: 1px solid #efefef;
}
.listWarp p:last-child {
	border-bottom: none;
}
.opration {
	padding: 20rpx;
	display: flex;
	justify-content: space-between;
	position: absolute;
	width: 100%;

	background-color: #fff;
}
.confirm {
	color: rgb(60, 170, 255);
}
.on {
	color: rgb(60, 170, 255);
}
</style>
