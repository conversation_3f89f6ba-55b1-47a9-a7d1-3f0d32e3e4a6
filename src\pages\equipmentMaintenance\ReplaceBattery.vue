<!-- @format -->
<template>
    <!-- 更换电池 -->
    <section class="main" style="padding-bottom: 0; margin-bottom: 30.1932rpx">
        <div class="inner">
            <div class="gap"></div>
            <div class="zy-form">
                <!-- 设备基本信息 -->
                <GetIMEIMsg
                    @updateInfo="updateInfo"
                    ref="refGetIMEIMsg"
                    :isDisabled="isDisabled"
                    :options="options"
                ></GetIMEIMsg>
                <div class="item">
                    <p class="label" :class="{ star: options.type === 'add' }">
                        更换时间
                    </p>
                    <div class="rp" :class="{ 'detail-type': isDisabled }">
                        <p-mui-date-picker
                            v-show="!isDisabled"
                            :disabled="isDisabled"
                            @confirm="confirmTime"
                            dateType="SECOND"
                            format="YYYY-MM-DD HH:mm:ss"
                        >
                            <span class="date-ipt res">
                                {{ info.ywmx.ghsj }}
                            </span>
                        </p-mui-date-picker>
                        <span class="date-ipt" v-show="isDisabled">
                            {{ info.ywmx.ghsj }}
                        </span>
                    </div>
                </div>
            </div>
            <UploadImage
                ref="refUploadImage"
                :options="options"
                :arrUploadType="arrUploadType"
                :uploadId="info.mxid"
                :isDisabled="isDisabled"
                title="电池现场安装照片"
            >
                <ul class="pd-ulpic1 sample">
                    <li>
                        <image
                            mode="aspectFit"
                            src="@/static/equipmentMaintenance/images/replaceBattery01.jpg"
                            alt=""
                        />
                        <p>更换前</p>
                    </li>
                    <li>
                        <image
                            mode="aspectFit"
                            src="@/static/equipmentMaintenance/images/replaceBattery02.jpg"
                            alt=""
                        />
                        <p>更换后</p>
                    </li>
                    <li>
                        <image
                            mode="aspectFit"
                            src="@/static/equipmentMaintenance/images/replaceBattery03.jpg"
                            alt=""
                        />
                        <p>新旧电池拍照</p>
                    </li>
                </ul>
                <div class="gap"></div>
                <div class="gap"></div>
                <dl class="pd-dltxt1">
                    <dt>说明：</dt>
                    <dd>1、拍下电池更换前的监控仪照片；</dd>
                    <dd>2、拍下电池更换后的监控仪照片;</dd>
                    <dd>3、将旧电池放在监控仪旁边拍下照片。</dd>
                </dl>
            </UploadImage>
            <!-- 采集结果 -->
            <GetLastedResult
                :imei="info.imei"
                ref="refGetLastedResult"
            ></GetLastedResult>

            <div class="gap"></div>
            <div class="gap"></div>
            <div class="zy-bot-btn1" v-show="!isDisabled" @click="save">
                保存
            </div>
            <div class="gap"></div>
            <div class="gap"></div>
        </div>
    </section>
</template>

<script>
import { maintenanceDetail } from '@/api/iot/equipmentMaintenance';
import { guid } from '@/utils/uuid.js';
import GetIMEIMsg from './components/GetIMEIMsg';
import GetLastedResult from './components/GetLastedResult';
import UploadImage from './components/UploadImage';
import useSaveForm from './hook/useSaveForm';
const { handleSave } = useSaveForm();
export default {
    data() {
        return {
            options: {},
            info: {
                imei: '',
                mxid: '', //明细id
                ywid: '', //运维id
                ywlx: '', //运维类型
                ywmx: {
                    ghsj: ''
                }
            },
            rules: {
                ghsj: {
                    required: true,
                    message: '请输入更换时间'
                }
            },
            arrUploadType: [
                {
                    name: '更换前',
                    LXDM: 'YWMX', //类型代码
                    ZLXDM: 'GHDC1' //子类型代码
                },
                {
                    name: '更换后',
                    LXDM: 'YWMX',
                    ZLXDM: 'GHDC2'
                },
                {
                    name: '新旧电池',
                    LXDM: 'YWMX',
                    ZLXDM: 'GHDC3'
                }
            ],
            isDisabled: false //是否不可编辑
        };
    },
    components: {
        GetIMEIMsg,
        GetLastedResult,
        UploadImage
    },
    watch: {},
    onLoad(options) {
        this.options = options;
    },
    onHide() {},
    created() {},
    mounted() {
        if (this.options.type === 'add') {
            this.info.ywlx = this.options.ywlx;
            this.info.ywid = this.options.ywid;
            this.info.mxid = guid();
            this.info.ywmx.ghsj = this.$dayjs().format('YYYY-MM-DD HH:mm:ss');
        }
        if (this.options.type === 'detail') {
            uni.setNavigationBarTitle({
                title: '运维明细详情'
            });
            this.isDisabled = true;
            this.getMaintenanceDetail();
        }
    },
    onBackPress() {},
    methods: {
        //请求明细
        async getMaintenanceDetail(id) {
            let data = await maintenanceDetail(this.options.mxid);
            for (const key in data) {
                if (key == 'ywmx') {
                    let innerData = data['ywmx'];
                    for (const key1 in innerData) {
                        this.$set(
                            this.info.ywmx,
                            key1,
                            innerData[key1] === '' ? '-' : innerData[key1]
                        );
                    }
                } else {
                    this.$set(
                        this.info,
                        key,
                        data[key] === '' ? '-' : data[key]
                    );
                }
            }

            this.$refs.refGetIMEIMsg.updateInfo(this.info);
            this.$nextTick(() => {
                this.$refs.refUploadImage.getImageFileList();
            });
        },
        //更新表单数据
        updateInfo(payload) {
            // Object.assign(this.info, payload);
            this.info = {
                ...this.info,
                ...payload,
                ywmx: { ...payload.ywmx, ghsj: this.info.ywmx.ghsj }
            };
        },
        //确定时间
        confirmTime(item) {
            console.log('item', item);
            this.info.ywmx.ghsj = item.time;
        },

        //保存
        async save() {
            let objRules = {};
            let objValiData = {};
            Object.assign(objRules, this.$refs.refGetIMEIMsg.rules, this.rules);
            Object.assign(
                objValiData,
                this.info,
                this.info.ywmx,
                this.$refs.refGetIMEIMsg.info
            );
            let pages = getCurrentPages(); // 当前页面
            let arrUploadImage = this.$refs.refUploadImage.arrUploadImage;
            handleSave(
                objRules,
                objValiData,
                this.info,
                pages,
                true,
                arrUploadImage,
                true
            );
        }
    }
};
</script>

<style scoped lang="less">
section {
    background: #f1f1f1;
    .date-ipt {
        text-align: right;
    }
}
</style>
