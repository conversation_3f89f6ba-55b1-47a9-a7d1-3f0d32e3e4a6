@charset "utf-8";

@font-face {
  font-family: "DIN-Bold";
  src: url("~@/static/equipmentMaintenance/fonts/DIN-Bold.woff2") format("woff2"),
    url("~@/static/equipmentMaintenance/fonts/DIN-Bold.woff") format("woff");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'YouSheBiaoTiHei';
  src: url('~@/static/equipmentMaintenance/fonts/YouSheBiaoTiHei.woff2') format('woff2'),
    url('~@/static/equipmentMaintenance/fonts/YouSheBiaoTiHei.woff') format('woff');
  font-weight: normal;
  font-style: normal;
}


.flx1 {
  display: flex;
}

.ac {
  align-items: center;
}

.jc {
  justify-content: center;
}

.jb {
  justify-content: space-between;
}

.ja {
  justify-content: space-around;
}

.header.blue2 {
  background: #2b6cf9;
}

.ic-back {
  position: absolute;
  left: 30.1932rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 21.7391249rpx;
  height: 36.2319rpx;
  background: url(~@/static/equipmentMaintenance/images/backic.png) 0 0 no-repeat;
  background-size: 100%;
}

.bbgl-inner {
  padding-top: 337.5rpx;
}

.yy-line1 {
  padding: 34.722225rpx 33.3333rpx;
  display: flex;
  align-items: center;
  background: #2b6cf9;
}

.inpsear1 {
  flex: 1;
  height: 66.666675rpx;
  line-height: 66.666675rpx;
  background: #fff url(~@/static/equipmentMaintenance/images/sear1.png) no-repeat 4% center;
  background-size: 32.638875rpx;
  padding-left: 65.97225rpx;
  border-radius: 11.1111rpx;
  font-size: 29.166675rpx;
}

.addic {
  width: 42.3611249rpx;
  height: 42.3611249rpx;
  margin-left: 41.6667rpx;
  vertical-align: -35%;
}

.navtab1 {
  background: #fff;
  display: flex;
  justify-content: space-evenly;
  height: 84.7222499rpx;
}

.navtop {
  position: absolute;
  top: 116.6667rpx;
  left: 0;
  right: 0;
  z-index: 999;
}

.navtab1 li {
  font-size: 31.94445rpx;
  color: #666;
  line-height: 84.7222499rpx;
}

.navtab1 li.on {
  color: #4874ff;
  background: url(~@/static/equipmentMaintenance/images/botbar.png) no-repeat center bottom;
  background-size: 84.54105rpx;
}

.navtab1 li sub {
  font-size: 22.94685rpx;
}

.zd-mod {
  margin: 0 33.3333rpx;
  background-color: #fff;
  border-radius: 20.83335rpx;
}

.zd-mod .zd-hd {
  padding: 0 33.3333rpx;
  border-bottom: solid 1px #eee;
}

.zd-mod .zd-bd {
  padding: 0 33.3333rpx;
}

.zd-hd .t1 {
  padding-left: 55.555575rpx;
  font-size: 34.722225rpx;
  color: #333;
  font-weight: bold;
  line-height: 120.833325rpx;
  height: 120.833325rpx;
  background: url(~@/static/equipmentMaintenance/images/tit-ic1.png) 0 center no-repeat;
  background-size: 30.555525rpx auto;
}

.rtarr1 {
  width: 17.361075rpx;
  height: 29.8611rpx;
  display: inline-block;
}

.bbrow {
  line-height: 58.33335rpx;
}

.bbrow em {
  color: #666;
  font-size: 31.94445rpx;
  display: inline-block;
  width: 35%;
}

.bbrow span {
  color: #333;
  font-size: 31.94445rpx;
  display: inline-block;
}

.footer.footerfix {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  border-top: solid 1px #eee;
}

.foot-menu {
  display: flex;
  background: #fff;
  height: 116.6667rpx;
}

.foot-menu li {
  flex: 1;
}

.foot-menu li p {
  font-size: 22.2222rpx;
  color: #B2B4B9;
  text-align: center;
}

.foot-menu li.on p {
  color: #3580FF;
}

.foot-menu li i {
  height: 45.8333249rpx;
  display: block;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  margin: 13.888875rpx 0 13.888875rpx;
  position: relative;

}

.foot-menu li i sup {
  position: absolute;
  right: 27%;
  top: -17%;
  width: 34.722225rpx;
  height: 24.999975rpx;
  line-height: 24.999975rpx;
  text-align: center;
  font-size: 20.83335rpx;
  color: #fff;
  background-color: #ff502e;
  text-align: center;
  border-radius: 11.1111rpx 11.1111rpx 11.1111rpx 1.388925rpx;
}

.foot-menu li i.zd-ic1 {
  background-image: url(~@/static/equipmentMaintenance/images/work-ic1.png);
}

.foot-menu li i.zd-ic2 {
  background-image: url(~@/static/equipmentMaintenance/images/yj-ic2.png);
}

.foot-menu li i.zd-ic3 {
  background-image: url(~@/static/equipmentMaintenance/images/qy-ic3.png);
}

.foot-menu li i.zd-ic4 {
  background-image: url(~@/static/equipmentMaintenance/images/bb-ic4.png);
}

.foot-menu li.on i.zd-ic1 {
  background-image: url(~@/static/equipmentMaintenance/images/work-ic1s.png);
  width: 88.888875rpx;
  height: 88.888875rpx;
  display: block;
  margin: auto;
  margin-top: 15rpx;
}
.foot-menu li.on i.zd-ic1 p{
  display: none;
}
.foot-menu li.on i.zd-ic2 {
  background-image: url(~@/static/equipmentMaintenance/images/yj-ic2s.png);
}

.foot-menu li.on i.zd-ic3 {
  background-image: url(~@/static/equipmentMaintenance/images/qy-ic3s.png);
}

.foot-menu li.on i.zd-ic4 {
  background-image: url(~@/static/equipmentMaintenance/images/bb-ic4s.png);
}

.zd-hd .passbtn {
  width: 118.05555rpx;
  height: 55.555575rpx;
  line-height: 55.555575rpx;
  text-align: center;
  font-size: 31.94445rpx;
  color: #7dcd27;
  background-color: #f2fae9;
  display: inline-block;
}

.zd-hd .passbtn.unpass {
  color: #ff4848;
  background-color: #ffecec;
}

.zy-table1 {
  width: 100%;
}

.zy-table1 td {
  font-size: 31.94445rpx;
  color: #333;
  height: 59.027775rpx;
  vertical-align: top;
}

.zy-table1 td.color6 {
  color: #666;
}

.pd-ullst1 {
  background: #fff;
  padding-left: 33.3333rpx;
}

.pd-ullst1>li {
  padding: 37.5rpx 33.3333rpx 37.5rpx 0;
  display: flex;
  justify-content: space-between;
  position: relative;
}

.pd-ullst1>li+li {
  border-top: 1px solid #ededed;
}

.pd-ullst1>li em {
  font-size: 30.555525rpx;
  color: #666;
  width: 24%;
  display: inline-block;
}

.pd-ullst1>li em.zmzl {
  width: 100%;
  padding-left: 15rpx;
}

.pd-ullst1>li.nfx {
  display: list-item;
}

.pd-ulpic1 {
  display: flex;
}

.pd-ulpic1>li {
  width: 208.33335rpx;
  height: 138.8889rpx;
  background-color: #dddddd;
  border-radius: 11.1111rpx;
  margin: 0 2%;
}

.redx {
  color: #eb4444;
  width: 10px;
  display: inline-block;
}

.pd-ullst1>li i {
  font-size: 30.555525rpx;
  color: #333;
}

.yy-tit1 {
  padding: 34.722225rpx 33.3333rpx;
  background-color: #fff;
  border-bottom: 1px solid #eee;

}

.yy-tit1>h1 {
  font-size: 34.722225rpx;
  color: #333;
  font-weight: bold;
  padding-left: 27.77775rpx;
  position: relative;
}

.yy-tit1>h1::before {
  content: '';
  position: absolute;
  left: 0;
  top: 57%;
  width: 9.7222499rpx;
  height: 33.3333rpx;
  background: #4874ff;
  border-radius: 5.55555rpx;
  transform: translateY(-50%);
}

.rbtnbox .passbtn2 {
  width: 125.000025rpx;
  height: 55.555575rpx;
  line-height: 55.555575rpx;
  text-align: center;
  font-size: 24.999975rpx;
  background-color: #2b6cf9;
  color: #fff;
  border-radius: 27.77775rpx
}

.rbtnbox {
  display: flex;
}

.rbtnbox .passbtn2+.passbtn2 {
  margin-left: 22.5rpx;
}

.rbtnbox .passbtn2.nopass {
  background-color: #eee;
  color: #666;
}

.foot-btnbox {
  padding: 16.66665rpx 33.3333rpx;
  background-color: #fff;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  border-top: solid 1px #eee;
}

.foot-btnbox li {
  text-align: center;
  font-size: 31.94445rpx;
  height: 90.2778rpx;
  line-height: 90.2778rpx;
  background-color: #f5f5f5;
  font-size: 31.94445rpx;
  border-radius: 5.55555rpx;
  font-weight: bold;
  color: #666;
}

.foot-btnbox li {
  flex: 1;
}

.foot-btnbox li+li {
  margin-left: 27.77775rpx;
}

.foot-btnbox li.confirmbtn {
  background-color: #2b6cf9;
  color: #fff;
}

.pd-inptxt1 {
  font-size: 30.555525rpx;
  color: #333;
  text-align: right;
}

.pd-ullst1 .slec {
  background: url(~@/static/equipmentMaintenance/images/rt-arr2.png) no-repeat right center;
  padding-right: 33.3333rpx;
  background-size: 17.361075rpx auto;
  color: #bbb;
}

.pd-ulpic1.pd-ulpic2 li {
  background-color: #f6f7f9;
}

.addpic {
  width: 152.777775rpx;
  height: 83.333325rpx;
  margin: 27.77775rpx auto;
  display: block;
}

/* 首页 */
.footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: #fff;
  box-shadow: 0 -4.1667001rpx 11.805525rpx 0 rgba(0, 0, 0, 0.03);
}

.flxbtw2 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.wh-yjmodbox {
  margin-bottom: 180rpx;
}

.pd-menu {
  display: flex;
  justify-content: space-around;
  background: #fff;
  height: 116.6667rpx;
}

.pd-menu li p {
  font-size: 22.2222rpx;
  color: #999;
}

.pd-menu li i {
  display: block;
  width: 100%;
  height: 50.69445rpx;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  margin: 17.361075rpx 0 6.944475rpx;
}

.pd-menu li i.muic1 {
  background-image: url(~@/static/equipmentMaintenance/images/yj-ic2.png);
}

.pd-menu li i.muic2 {
  background-image: url(~@/static/equipmentMaintenance/images/qy-ic3.png);
}

.pd-menu li i.muic23 {
  background-image: url(~@/static/equipmentMaintenance/images/bb-ic4.png);
}


.pd-menu {
  flex: 1;
}

.pd-menu li.on p {
  color: #2b6cf9;
}

.pd-menu li.on i.muic1 {
  background-image: url(~@/static/equipmentMaintenance/images/yj-ic2s.png);
}

.pd-menu li.on i.muic2 {
  background-image: url(~@/static/equipmentMaintenance/images/qy-ic3s.png);
}

.pd-menu li.on i.muic23 {
  background-image: url(~@/static/equipmentMaintenance/images/bb-ic4s.png);
}

/* .lflogo {
  width: 88.888875rpx;
  height: 88.888875rpx;
  background: url(~@/static/equipmentMaintenance/images/foot-logo1.png) no-repeat 0 center;
  background-size: 88.888875rpx auto;
  margin-left: 72.222225rpx;
  margin-right: 69.44445rpx;
} */

.pbot {
  margin-bottom: 120rpx;
}

.nodatu {
  text-align: center;
}

.nodatu p {
  padding-bottom: 60rpx;
}


.whidx-topbg {
  width: 100%;
  color: #fff;
  height: 474.9999749rpx;
  background: url(~@/static/equipmentMaintenance/images/idx-topbg.png) no-repeat center center;
  background-size: 100% auto;

}

.whtop-titbox {
  position: relative;
  padding-top: 86.805525rpx;
}

.whtop-titbox>h1 {
  text-align: center;
  font-size: 34.722225rpx;
  color: #fff;
  font-weight: bold;
}

.rtuseric {
  width: 36.111075rpx;
  height: 38.8889249rpx;
  background: url(~@/static/equipmentMaintenance/images/user-ic1.png) no-repeat center center;
  background-size: 36.111075rpx auto;
  position: absolute;
  right: 37.5rpx;
  top: 86.805525rpx;
}

.topqybox {
  background-color: #fff;
  height: 364.583325rpx;
  background: url(~@/static/equipmentMaintenance/images/topqybox.png) no-repeat center center;
  background-size: 100% 100%;
  margin: 1rem 20.83335rpx 0;
  padding: 13.888875rpx 36.805575rpx;
  box-sizing: border-box;
}

.yqrow1 {
  color: #333;
  margin: 0 15rpx;
  padding: 27.77775rpx 0;
  border-bottom: solid 1px #E8E8E8;
}

.yqrow1 .f1 {
  background: url(~@/static/equipmentMaintenance/images/qyzs-ic1.png) no-repeat 0 center;
  background-size: 35.41665rpx auto;
  padding-left: 52.5rpx;
  font-size: 31.94445rpx;
  color: #333;
}

.yqrow1 .f2 {
  font-family: "DIN-Bold";
  font-size: 41.6667rpx;
  color: #333;
}

.yqrow1 .f2 sub {
  font-size: 24.999975rpx;
  margin-left: 7.5rpx;
}

.yqrow2 {
  color: #333;
  margin: 0 15rpx;
  padding: 27.77775rpx 0;
}

.yqrow2 .f1 {
  padding-left: 52.5rpx;
  font-size: 31.94445rpx;
  color: #333;
}

.yqrow2 .f2 {
  font-size: 41.6667rpx;
  color: #333;
  font-family: "DIN-Bold";
  margin-left: 26.3889rpx;
}

.yqrow2 .f2 sub {
  font-size: 24.999975rpx;
  color: #999;
  margin-left: 7.5rpx;
}

.yqrow2 .ic1 {
  background: url(~@/static/equipmentMaintenance/images/qyzs-ic2.png) no-repeat 0 center;
  background-size: 37.5rpx auto;
}

.yqrow2 .ic2 {
  background: url(~@/static/equipmentMaintenance/images/qyzs-ic3.png) no-repeat 0 center;
  background-size: 37.5rpx auto;
}

.yqrow2 .ic3 {
  background: url(~@/static/equipmentMaintenance/images/qyzs-ic4.png) no-repeat 0 center;
  background-size: 37.5rpx auto;
}

.yqrow2 .ic4 {
  background: url(~@/static/equipmentMaintenance/images/qyzs-ic5.png) no-repeat 0 center;
  background-size: 37.5rpx auto;
}

.idxtti {
  font-size: 34.722225rpx;
  color: #333;
  font-weight: bold;
  padding: 28.5rpx 30rpx;
}

.rbtjbox {
  margin: 0 20.83335rpx 0;
}

.rbtjbox .bg1 {
  width: 47%;
  margin: 0 1%;
  height: 154.8611249rpx;
  background: url(~@/static/equipmentMaintenance/images/ssjkbg1.png) no-repeat center center;
  background-size: 100% 100%;
}

.rbtjbox .bg2 {
  width: 47%;
  margin: 0 1%;
  height: 154.8611249rpx;
  background: url(~@/static/equipmentMaintenance/images/ssjkbg2.png) no-repeat center center;
  background-size: 100% 100%;
}

.tjfxbox .bg1 {
  width: 47%;
  margin: 0 1%;
  height: 126.388875rpx;
  background: linear-gradient(180deg, #E4FAF6 0%, #FFFFFF 100%);
  box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.09);
  border-radius: 11.1111rpx;
}

.tjfxbox .bg2 {
  width: 47%;
  margin: 0 1%;
  height: 126.388875rpx;
  background: linear-gradient(180deg, #FEF5ED 0%, #FFFFFF 100%);
  box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.09);
  border-radius: 11.1111rpx;
}

.rb-ic {
  width: 54.861075rpx;
  height: 54.861075rpx;
  margin-left: 41.6667rpx;
}

.rb-ic2 {
  width: 48.6110999rpx;
  height: 48.6110999rpx;
  margin-left: 41.6667rpx;
}

.tjfx-ic {
  width: 69.44445rpx;
  height: 69.44445rpx;
  margin-left: 41.6667rpx;
}

.tjfxbox b {
  font-size: 31.94445rpx;
  margin-left: 27.77775rpx;
  color: #333;
  display: inline-block;
}

.tjfxbox {
  margin: 0 20.83335rpx 27.77775rpx;
}

.rbtjbox b {
  font-size: 31.94445rpx;
  margin-left: 27.77775rpx;
  font-weight: bold;
  display: inline-block;
}

.rbtjbox p.num {
  font-family: "DIN-Bold";
  font-size: 48.6110999rpx;
  text-align: center;
}

.rbtjbox p.num sub {
  font-size: 24.999975rpx;
  color: #fff;
  margin-left: 7.5rpx;
}

.ssjktop {
  margin-top: 25.5rpx;
}

.new-ic {
  width: 104.166675rpx;
  height: 41.6667rpx;
  display: inline-block;
  margin-right: 24.3055499rpx;
}

.newsbox {
  height: 100.694475rpx;
  background: #FFEBEB;
  border-radius: 50.69445rpx;
  padding: 0 31.250025rpx;
  margin: 0 20.83335rpx 27.77775rpx;
}

.newsbox>p {
  font-size: 29.166675rpx;
  color: #333;
}

.ssyjmod {
  margin: 0 20.83335rpx;
  background-color: #FFFFFF;
  border-radius: 20.83335rpx;
  overflow: hidden;
  padding-bottom: 30rpx;
}

.ssyjmod .hd {
  height: 90.2778rpx;
  background: url(~@/static/equipmentMaintenance/images/hdbg.png) 0 0 no-repeat;
  background-size: 100% auto;
  padding: 0 33.3333rpx;
}

.ssyjmod .bd {
  background-color: #FFFFFF;
}

.ssyjmod .more {
  width: 39.5833499rpx;
  height: auto;
  display: inline-block;
}

.ssyjmod h1 {
  color: #148EFF;
  font-size: 29.166675rpx;
  font-weight: bold;
}

.ssyjmod h1 i {
  font-size: 41.6667rpx;
  font-family: 'YouSheBiaoTiHei';
  vertical-align: -10%;
  display: inline-block;
}

.wh-yjmod {
  padding: 0 29.166675rpx 0;
  margin-bottom: 48.6110999rpx;
  position: relative;
}

.wh-yjmod .tit {
  font-size: 29.166675rpx;
  color: #333;
  font-weight: bold;
  padding-left: 45rpx;
  margin-bottom: 17.361075rpx;
}

.wh-yjmod .yj-blue {
  background: url(~@/static/equipmentMaintenance/images/yj-blue.png) no-repeat 0 center;
  background-size: 31.250025rpx auto;
}

.wh-yjmod .yj-red {

  background: url(~@/static/equipmentMaintenance/images/yj-red.png) no-repeat 0 center;
  background-size: 31.250025rpx auto;
}

.wh-yjmod .stus {
  width: 118.05555rpx;
  height: 41.6667rpx;
  line-height: 41.6667rpx;
  text-align: center;
  background: #ffe4e4;
  color: #FF3535;
  border-radius: 20.83335rpx;
}

.wh-yjmod .stus1 {
  background: #ffe4e4;
  color: #FF3535;
}

.wh-yjmod .stus2 {
  background: #ddf8ea;
  color: #08D496;
}

.wh-yjmod:last-of-type {
  margin-bottom: 0;
  padding-bottom: 0;
}

.ssyjwrap .tit {
  position: relative;
}
.ssyjwrap .tit::before{
  content: '';
   position: absolute; 
   left: 27rpx; 
   top: 3.75rpx; 
   width: 10.416675rpx;
   height: 10.416675rpx;
   background: #FF3535;
   border-radius: 50%;
}
.wh-yjmod>p {
  font-size: 26.3889rpx;
  color: #666;
  line-height: 45rpx;
}

.wh-yjmod>p .v {
  font-size: 26.3889rpx;
  color: #171717;
}

.timelist {
  display: flex;
}
.timelist li {
  flex: 1;
  padding: 0 18.11595rpx;
  text-align: center;
  font-size: 29.166675rpx;
  color: #999;
  line-height: 32.608725rpx;
  white-space: nowrap;
}
.timelist li + li {
  border-left: 1px solid #DCDCDC;
}
.timelist li.on {
  font-weight: bold;
  color: #148EFF;
}
.yjqs-mod{
  margin: 0 20.83335rpx;
  background-color: #fff;
  border-radius: 20.83335rpx;
  padding: 23.611125rpx;
}
.dw{
  color: #999;
  font-size: 29.166675rpx;
}
.qschar{
  height: 347.2222499rpx;
  display: block;
  margin: auto;
}
.nodatabox{
  margin: 65.97225rpx auto;
}
.nodatabox p{
   font-size: 29.166675rpx;
   color: #BEC0C6;
   text-align: center;
   margin-top: 20.83335rpx;

}
.nodatabox .nodatatu{
  height: 180.555525rpx;
  display: block;
  margin: auto;
}