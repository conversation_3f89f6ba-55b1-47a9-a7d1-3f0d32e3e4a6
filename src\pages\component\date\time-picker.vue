<template>
	<picker-view 
		class="time-picker"
		:value="selectedIndexs" 
		@change="onTimeChange">
		<picker-view-column>
			<view 
				class="flex-row-layout time-column-item"
				v-for="(hour, index) in hours" 
				:key="index">
				<text>{{bitPaddingNumber(hour)}}时</text>
			</view>
		</picker-view-column>
		<picker-view-column>
			<view 
				class="flex-row-layout time-column-item"
				v-for="(minute, index) in minutes" 
				:key="index">
				<text>{{bitPaddingNumber(minute)}}分</text>
			</view>
		</picker-view-column>
	</picker-view>
</template>

<script>
	import dayjs from 'dayjs';
	
	export default {
		name: 'TimePicker',
		props: {
			time: {
				type: String
			}
		},
		
		data() {
			let hours = [];
			for(let h = 0; h < 24; h++){
				hours.push(h);
			}
			
			let minutes = [];
			for(let m = 0; m < 60; m++){
				minutes.push(m);
			}
			
			let time =  dayjs().format('HH:mm');
			return {
				hours,
				minutes,
				selectedTime: time,
				selectedIndexs: []
			}
		},
		
		mounted() {
			let time = this.time || this.selectedTime;
			if(time){
				this.selectedTime = time;
				this.selectedIndexs = this.parseTimeIndexs(time);
			}
		},
		
		methods: {
			
			/**
			 * 解析时间对应选择索引
			 * @param {Object} time
			 */
			parseTimeIndexs(time){
				let hour = parseInt(time.split(':')[0]);
				let minute = parseInt(time.split(':')[1]);
				let hourIndex = this.hours.indexOf(hour);
				let minuteIndex = this.minutes.indexOf(minute);
				return [hourIndex, minuteIndex];
			},
			
			/**
			 * 补齐两位数
			 * @param {Object} num
			 */
			bitPaddingNumber(num){
				return num < 10 ? `0${num}` : num;
			},
			
			/**
			 * 选择时间时的回调
			 * @param {Object} event
			 */
			onTimeChange(event){
				let selectedIndexs = event.detail.value;
				let hourIndex = selectedIndexs[0];
				let minuteIndex = selectedIndexs[1];
				
				let hour = this.hours[hourIndex];
				let minute = this.minutes[minuteIndex];
				this.selectedTime = `${this.bitPaddingNumber(hour)}:${this.bitPaddingNumber(minute)}`;
				this.selectedIndexs = selectedIndexs;
				this.$emit('change', this.selectedTime);
			}
		}
	}
</script>

<style scoped>
	.time-picker {
		width: 100%;
		height: 480rpx;
	}
	
	.time-column-item {
		justify-content: center;
		line-height: 96rpx;
		text-align: center;
	}
</style>
