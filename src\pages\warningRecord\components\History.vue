<template>
	<div style="height:100%;overflow: scroll;">
 		<p class="sw-ictxt1">发生时间：{{ accurTime || '--'}}</p>
			<div class="gap"></div>
			<ul class="sw-ulaxis1">
				<li v-for="item in warnList" >
					<h1>
						<span v-if="item.JCSJ == '' || item.JCSJ == null ">
							预警时间：
					</span>
					<span v-else>
							解除时间：
					</span>
					{{item.JCSJ == '' || item.JCSJ == null ? item.YJSJ:item.JCSJ}}</h1>
					<h2>
						<p>生产线：{{item.SCXMC == '' || item.SCXMC == null ? '--':item.SCXMC}}</p>
						<p>预警信息：{{item.YJNR == '' || item.YJNR == null ? '--':item.YJNR}}</p>
					<!-- 	 <p>当前状态：正在运行；治污设备【RTO1#助燃风机】</p>
						<p>当前状态：停止运行；疑似生产设备与治污设备未</p>
						<p>同时开启。该现象当天发生1次，累计30分。</p> -->
				 	</h2>
				</li>
			</ul>
		<div v-if="warnList.length == 0" style="text-align: center;color:#999;">暂无数据</div> 
	</div>
</template>


<script>
	import {yjxxInfo} from '@/api/warning.js'
	export default {
		props:{
			warning:{
				type:Object,
				default:()=>{}
			}
		},
		data() {
			return{
				info:{},
				warnList:[],
				accurTime:''
			}
		},
		computed:{
		
		},
		watch:{
			warning:{
				handler(newVal,oldVal){
					this.info = newVal;
					if(this.info){
						this.getWarnList();
					}
		     	},
				deep:true
				
			}
		},
		created() {
			
		},
		methods: {
			//预警内容
			getWarnList(){
				let obj = {
					YJBH:this.info.YJBH
				}		
				yjxxInfo(obj).then(res =>{
					if(res.status == '000' && res.data.length>0){
						this.warnList = res.data;
						this.accurTime = this.warnList[0].FSSJ;
					}
				})
			}
		}
	}
</script>


<style>
</style>
