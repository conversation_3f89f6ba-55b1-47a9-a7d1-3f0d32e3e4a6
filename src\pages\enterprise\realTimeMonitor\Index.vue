<template>
    <div class="main-page">
        <header class="header">
            <i class="ic-back" @click="back"></i>
            <h1 class="title">{{ enterpriseInfo.WRYMC}}</h1>
        </header>
        <!-- 实时监控 -->
        <div class="tabs1-con" v-show="currentscx">
            <div class="qiye-board">
                <div class="qiye-tabs2">
                    <div class="item" :class="currentscx==item.SCXID?'cur':''"  @click="changeProductLine(item)" v-for="(item, index) in scxlist" :key="index">
                        <p>{{ item.SCXMC }}</p>
                        <image v-if="item.ISYC == 'true'" src="@/static/app/enterpriseDetail/images/qiye-lamp.png" class="lamp">
                    </div>
                </div>
                <div class="gap"></div>
                <div class="zy-line ac jb">
                    <div class="qiye-tabs3">
                        <span :class="{cur: curPic == item.value,
                         unshow: item.value == 'PHData' && !isPHEquip
                                 }"
                        @click="changePic(item)" v-for="(item, index) in picTabArr" :key="index">{{item.name}}</span>
                    </div>
                    <div class="qiye-riqi" @click="open">
                        <u-icon name="calendar" color="#2979ff" size="16"></u-icon>
                        {{dateStr}}
                    </div>
                </div>
                <div class="gap"></div>
                <StatePic ref="statePic"  v-show="curPic == 'state'" :dateStr="dateStr" :currentscx="currentscx"></StatePic>
                <TrendPic ref="trendPic"  v-show="curPic == 'trend'" :dateStr="dateStr" :currentscx="currentscx"></TrendPic>
                <!-- PH实时数据 -->
                <PHRealtimeData
                     v-show="curPic == 'PHData'"
                    ref="refPHRealtimeData"
                    :dateStr="dateStr"
                    :currentscx="currentscx"
                ></PHRealtimeData>
            </div>
            <!-- 无每日时间状态显示 -->
            <u-calendar v-model="show" :mode="mode" @change="changeDate"></u-calendar>
        </div>
    </div>


</template>

<script>
/** @format */
import { getInfo } from '@/api/iot/realtime.js';
import {
    sbsxzt,
    qyztrl,
    getScrl,
    getDwzb
} from '../../../api/iot/runningData.js';
import StatePic from './components/StatePic.vue';
import TrendPic from './components/TrendPic.vue';
import PHRealtimeData from './components/PHRealtimeData';
export default {
    components: {
        StatePic,
        TrendPic,
        PHRealtimeData
    },
    // props: {
    //     enterpriseData: {
    //         type: Object,
    //         default: () => {}
    //     },
    //     enterpriseState: {
    //         type: String,
    //         default: ''
    //     }
    // },
    // watch: {
    //     enterpriseData: {
    //         handler: function (newVal) {
    //             this.scxlist = newVal.cxList;
    //             let scxLen = newVal.scxList.length;
    //             if (scxLen) {
    //                 this.currentscx = newVal.scxList[0].SCXID;
    //                 this.currentscxname = newVal.scxList[0].SCXMC;
    //             }
    //         },
    //         deep: true
    //     }
    // },
    data() {
        return {
            scxlist: [], //生产线list
            currentscx: '',
            picTabArr: [
                {
                    name: '状态图',
                    value: 'state'
                },
                {
                    name: '趋势图',
                    value: 'trend'
                },
                {
                    name: 'pH实时数据',
                    value: 'PHData'
                }
            ],
            curPic: 'state',
            dateStr: this.$dayjs().format('YYYY-MM-DD'),
            show: false,
            mode: 'date',
            wrybh: '',
            isPHEquip:false,
            enterpriseData:{},
            enterpriseInfo:{}
        };
    },
    provide(){
        return {
            getEnterpriseData:()=>this.enterpriseData
        }
    },
    onLoad(option) {
        let userinfo = uni.getStorageSync('user_info');
        this.xzqhdm = userinfo.orgid;
        this.wrybh = option.wrybh;
    },
     created() {

	},
    async mounted(){
        await this.initInfo();

    },

    methods: {
        //获取企业信息
		async initInfo() {
		 const {data} =  await getInfo({
		        WRYBH: this.wrybh,
		        XZQHDM: this.xzqhdm
		    });
			this.enterpriseData = data;
             this.enterpriseInfo = data.qyjbxx;
             console.log('this.enterpriseData',this.enterpriseData)
             this.scxlist = data.cxList;
                let scxLen = this.scxlist.length;
                if (scxLen) {
                    this.currentscx = this.scxlist[0].SCXID;
                }
                this.$nextTick(() => {
                    this.$refs.statePic.init();
                });
		},
           //初始化tab
        initTab() {
            //如果是PH类型，数据分析tab添加
            if (this.warning.YJLX === 'PHG' || this.warning.YJLX === 'PHD') {
                this.isPHEquip = true;
            }
        },
        //切换生产线
        changeProductLine(val) {
            this.currentscx = val.SCXID;
            this.currentscxname = val.SCXMC;
            this.curPic = 'state';
            this.$nextTick(() => {
                this.$refs.statePic.init();
            });
        },
        //切换图表
        changePic(item) {
            this.curPic = item.value;
            switch (item.value) {
                case 'state':
                    this.$nextTick(() => {
                        this.$refs.statePic.init();
                    });
                    break;
                case 'trend':
                    this.$nextTick(() => {
                        this.$refs.trendPic.initProductList();
                    });
                    break;
                case 'PHData':
                this.$nextTick(() => {
                        this.$refs.refPHRealtimeData.queryPHData();
                    });
                    break;
                default:
                    break;
            }
        },

        open() {
            this.show = true;
        },
        changeDate(e) {
            this.dateStr = `${e.year}-${
                e.month < 10 ? '0' + e.month : e.month
            }-${e.day < 10 ? '0' + e.day : e.day}`;
            if (this.curPic == 'state') {
                this.$nextTick(() => {
                    this.$refs.statePic.gettjt();
                });
            } else if(this.curPic == 'trend'){
                this.$nextTick(() => {
                    this.$refs.trendPic.initProductList();
                });
            }else if(this.curPic == 'PHData'){
                  this.$nextTick(() => {
                        this.$refs.refPHRealtimeData.queryPHData();
                    });
            }
        },
        back() {
            uni.navigateBack({
                delta: 1
            });
        }
    }
};
</script>



<style>
.main-page{
    padding-top: 70rpx;
}
.qiye-board {
    position: relative;
}
.qiye-tabs3 {
    display: flex;
}
.qiye-tabs3 span {
    font-size: 30rpx;
    color: #666;
    line-height: 10rpx;
    padding: 10rpx;
}
.qiye-tabs3 span:first-child {
    padding-left: 0;
}
.qiye-tabs3 span + span {
    border-left: 4rpx solid #ddd;
}
.qiye-tabs3 span.cur {
    color: #4874ff;
}
.unshow {
    display: none;
}

.qiye-riqi {
    font-size: 27.77775rpx;
    color: #4874ff;
    width: 201.388875rpx;
    text-align: center;
    line-height: 55.555575rpx;
    border-radius: 10.416675rpx;
    overflow: hidden;
    border: 0.694425rpx solid #4874ff;
}
::v-deep .u-iconfont::before {

    font-size: 28rpx;
}
</style>
