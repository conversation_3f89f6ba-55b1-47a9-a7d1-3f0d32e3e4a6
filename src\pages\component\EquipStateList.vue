<template>
      <div class="pd-con1" >
		<u-empty mode="data" v-if="!list || !list.length" text='暂无数据' font-size="28rpx"></u-empty>
		<ul class="pd-ullst1" v-for="(item,index) in list" :key="index">
			<li ><em >检测时间</em><i >{{item.JCSJ || '-'}}</i></li>
			<li v-if="item.ADS !== undefined"><em>开关状态</em>
				<i v-if="item.ADS == '1'" class="abnormal">弹起</i>
				<i v-if="item.ADS == '0'" class="normal">收缩</i>
			</li>
			<li ><em >设备状态</em><i :class="{'abnormal':item.ERR != '正常','normal':item.ERR == '正常',}" >{{item.ERR || '-'}}</i></li>
			<li ><em >设备电压</em><i :class="{'abnormal':item.VOL_ZT != 0,'normal':item.VOL_ZT == 0,}" >{{item.VOL_ZT == 0 ?'正常':'异常' }}</i></li>
			<li ><em >信号功率</em><i :class="{'abnormal':item.SP_ZT != 0,'normal':item.SP_ZT == 0,}" >{{item.SP_ZT  == 0 ?'正常':'异常' }}</i></li>
			<li ><em >信号质量</em><i :class="{'abnormal':item.RSRQ_ZT != 0,'normal':item.RSRQ_ZT == 0,}">{{item.RSRQ_ZT == 0 ?'正常':'异常'}}</i></li>
			<li v-if="item.ZNSBLX == 'ZD'"><em >振动能量</em><i >{{item.NL_LV || '-'}}</i></li>
			<li v-if="item.ZNSBLX == 'ZD'"><em >振动能量值</em><i >{{item.POWER || '-'}}</i></li>
			<li v-if="item.ZNSBLX == 'DL'"><em >电流量</em><i >{{item.NL_LV || '-'}}</i></li>
		</ul>
	</div>
</template>

<script>
	export default {
		props:{
			list:{
				type:Array,
			    default:()=>[]
			}
		},
	    components: {
	   
	    },
	    data() {
	        return {
	           
	        };
	    },
	
	    mounted() {
	     
	    },
	    methods: {
	       
	     
	    }
	};
</script>

<style lang="scss" scoped>
	.pd-ullst1 {
		margin-bottom: 20rpx;
		>li+li{
			border:none;
		}
		li{
			padding:20rpx 12rpx 20rpx 0rpx;
		}
		
    }
	.normal{
		color:#3ab918;
	}
	.abnormal{
		color:#ff0000;
	}
</style>
