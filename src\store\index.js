import Vue from 'vue';
import Vuex from 'vuex';

Vue.use(Vuex)

const store = new Vuex.Store({
	state: {
		token: null,
		/**
		 * 是否需要强制登录
		 */
		forcedLogin: false,
		hasLogin: false,
		userName: "",
		verifyList:[],
		historyList:[],
		mrzList:[],
		enterpriseInfo:{},//企业信息,
		warningInfo:{}//预警信息
	},
	
	mutations: {
		showLoading(state) {
			state.showLoading = true;
			uni.showLoading({
			    title: '加载中'
			});
		},
		hideLoading(state) {
			state.showLoading = false;
			uni.hideLoading({
			    title: '加载中'
			});
		},// todo
		errorMsg(state, error) {
			uni.showToast({
			    title: error.msg,
				icon: 'none',
			    duration: 2000
			});
		},
		login(state, userName) {
			state.userName = userName || '新用户';
			state.hasLogin = true;
		},
		
		logout(state) {
			state.userName = "";
			state.hasLogin = false;
		},
		// 设置预警信息
		SET_WARNING_INFO(state,payload){
			state.warningInfo=payload
		}

		
	}
})

export default store
