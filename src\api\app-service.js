/** @format */

import http from '@/common/net/http.js';
import { LOGIN_ULR_BASE } from '@/common/config.js';
import toastIcon from '@/common/toast-icon.js';

let progressUpdater = null;
let newVersion;
export default {
    checkMPAppUpgrade() {
        //测试地址
        http.post(`${http.loginUrl}/mobile/base/searchBbxxDetail`, {
            os: 'WECHAT',
            appId: 'device'
        })
            .then((latestVersion) => {
                if (latestVersion) {
                    console.log('latestVersion', latestVersion);
                    this.shouldUpgrade(latestVersion);
                }
            })
            .catch((e) => {
                console.log('e', e);
            });
    },

    shouldUpgrade(latestVersion) {
        plus.runtime.getProperty(plus.runtime.appid, (wgtInfo) => {
            let current = wgtInfo.versionCode || 0;
            current = parseFloat(current);
            let latest = latestVersion['BBH'] || 0;
            newVersion = latest;
            console.log('current', current); // 31
            console.log('latest', latest); // 32
            console.log('newVersion', newVersion); // 32

            if (current < latest) {
                let WJHZ = 'wgt';
                let WDLJ = latestVersion.WDLJ;
                let dotIndex = WDLJ.lastIndexOf('.');
                if (dotIndex > 0) {
                    WJHZ = WDLJ.substr(dotIndex + 1);
                }
                this.downloadUpgradeFile(latestVersion.WJID, WJHZ);
            } else if (current < 33) {
                let platform = uni.getSystemInfoSync().platform;
                if (platform === 'ios') {
                    // 苹果
                    uni.showModal({
                        title: '提示',
                        content:
                            '本次更新涉及APP模块配置更新,请前往App Store的已购项目中重新获取',
                        showCancel: false,
                        success: function () {
                            plus.runtime.openURL(
                                'itms-apps://itunes.apple.com/cn'
                            );
                        }
                    });
                } else if (platform === 'android') {
                    //let downloadPath = "https://iot-manage.iotdi.com.cn/iotManage/ShakeMoniterH5/__UNI__67E9FBB_0803112721.apk"
                    let downloadPath = 'https://www.pgyer.com/tba6';
                    // plus.runtime.openURL('https://www.pgyer.com/tba6');
                    // 安卓
                    uni.showModal({
                        title: '提示',
                        content: '本次更新涉及APP模块配置更新,需重新安装',
                        showCancel: false,
                        success: (res) => {
                            plus.runtime.openURL(downloadPath);
                        }
                    });
                }
            }

            // uni.showToast({
            // 	icon: 'none',
            // 	title: `current ==> ${current} + ==> latest ==> ${latest}`
            // })
        });
    },

    downloadUpgradeFile(fileId, suffix) {
        //测试地址
        let downloadUrl = `${http.loginUrl}/webapp/downloadFile?wdbh=${fileId}`;
        let options = {
            filename: `_downloads/mp_upgrade/${fileId}.${suffix}`
        };
        let task = plus.downloader.createDownload(
            downloadUrl,
            options,
            (download, status) => {
                if (status === 200) {
                    this.installUpgradePack(download.filename);
                } else {
                    uni.showToast({
                        title: '下载升级包出错',
                        duration: 3000,
                        icon: 'none'
                    });
                }
            }
        );
        let stateChange = (download, status) => {
            if (status === 200) {
                let percent = parseInt(
                    (download.downloadedSize / download.totalSize) * 100
                );
                if (progressUpdater) {
                    progressUpdater(percent);
                }
            }
        };
        task.addEventListener('statechanged', stateChange, false);
        task.start();
    },

    installUpgradePack(path) {
        // console.log(path);
        uni.showLoading({
            title: '安装升级包'
        });
        let successCallback = () => {
            uni.hideLoading();
            uni.showToast({
                title: '安装更新成功',
                duration: 2000,
                icon: 'success'
            });
            setTimeout(() => {
                uni.hideToast();

                plus.runtime.restart();
            }, 2000);
        };

        let failCallback = (e) => {
            // console.log(e);
            uni.hideLoading();
            uni.showToast({
                title: `安装更新失败`,
                duration: 2000,
                icon: 'error'
            });
            let platform = uni.getSystemInfoSync().platform;
            if (platform === 'ios') {
                // 苹果
                uni.showModal({
                    title: '提示',
                    content:
                        '本次更新涉及APP模块配置更新,请前往App Store的已购项目中重新获取',
                    showCancel: false,
                    success: function () {
                        plus.runtime.openURL('itms-apps://itunes.apple.com/cn');
                    }
                });
            }
        };
        plus.runtime.install(
            path,
            {
                force: true
            },
            successCallback,
            failCallback
        );
    },

    setProgressUpdater(updater) {
        progressUpdater = updater;
    },

    /**
     * 跳转应用市场
     * @param {Object} keyword 应用名称关键字
     */
    goToMarket(keyword) {
        let Uri = plus.android.importClass('android.net.Uri');
        let Intent = plus.android.importClass('android.content.Intent');
        let marketUri = Uri.parse(`market://search?q=${keyword}`);
        let marketIntent = new Intent(Intent.ACTION_VIEW, marketUri);
        plus.android.runtimeMainActivity().startActivity(marketIntent);
    },

    checkAppUpgrade() {
        // #ifdef APP-PLUS
        let upgradeManagerClassName = 'com.bovosz.webapp.app.AppUpgradeManager';
        let AppUpgradeManager = plus.android.importClass(
            upgradeManagerClassName
        );
        if (AppUpgradeManager) {
            let upgradeManager = new AppUpgradeManager(
                plus.android.runtimeMainActivity()
            );
            upgradeManager.checkUpgrade(`${LOGIN_ULR_BASE}/`);
        }
        // #endif
    }
};
