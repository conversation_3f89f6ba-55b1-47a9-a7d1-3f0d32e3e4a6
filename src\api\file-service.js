import http from '@/common/net/http.js';
import { DOWNLOAD_URL } from '@/common/config.js'

const getFileUrl = (fileId) => {
	return `${DOWNLOAD_URL}?wdbh=${fileId}`;
}

/**
 * 查询附件类型定义
 */
export const  queryAttachCategory = (majorCateCode) => {
	return new Promise((resolve, reject) => {
		http.post(`${http.loginUrl}/mobile/base/filebigclassify`, {
			LXDM: majorCateCode
		}).then(resp => {
			let major = resp
			if(major) {
				let majorCate = {
					code: majorCateCode,
					name: major.LXMC,
					valid: '1' === major.SFYX
				}
				
				let minors = major.ZLIST
				if(Array.isArray(minors) && minors.length > 0) {
					let minorCates = minors.map(cate => {
						return {
							code: cate.ZLXDM,
							name: cate.ZLXMC,
							parent: cate.LXDM,
							fileType: cate.FJLX || '',
							countLimit: cate.WJSL,
							sizeLimit: (cate.FJDX || 100) * 1024 * 1024 * 1024,
							valid: '1' === cate.SFYX
						}
					})
					majorCate.children = minorCates
				}
				resolve(majorCate)
			} else {
				resolve(null)
			}
		}).catch(error => {
			reject(error)
		})
	})
}

export default {
	getFileUrl,
	queryAttachCategory,
	downloadFile(fileId){
		return new Promise((resolve, reject) => {
			uni.downloadFile({
				url: getFileUrl(fileId),
				success(fileInfo, code) {
					resolve(fileInfo);
				},
				
				fail(error) {
					reject(error)
				}
			})
		})
	},
	
	openDocument(fileId, fileName){
		let fileUrl = getFileUrl(fileId);
		uni.showLoading({
				title: '下载中'
			});
		let downloadTask =  uni.downloadFile({
			url: fileUrl,//下载地址接口返回
			success: (data) => {
				if (data.statusCode === 200) {
					//文件保存到本地
					uni.saveFile({
						tempFilePath: data.tempFilePath, //临时路径
						success: function(res) {
							uni.setStorageSync(fileId, res.savedFilePath)
							uni.showToast({
								mask: true,
								title: '文件已保存', //保存路径
								duration: 1000,
								icon:'success'
							});
							setTimeout(() => {
								//打开文档查看
								uni.openDocument({
									filePath: res.savedFilePath,
									success: function(res) {
									}
								});
							}, 500)
						}
					});
				}
			},
			fail: (err) => {
				uni.showToast({
					icon: 'none',
					mask: true,
					title: '失败请重新下载',
				});
			},
		});

		downloadTask.onProgressUpdate((res) => {
			uni.$emit('downloadTaskProgress' + fileId , res.progress)
			uni.hideLoading();
		});
	},
	
	downloadFileProgress(fileId, suffix, listener) {
		let downloadUrl = `${http.loginUrl}/webapp/downloadFile?wdbh=${fileId}`
		
		let options = {
			filename: `_downloads/video/${fileId}.${suffix}`
		}
		let task = plus.downloader.createDownload(downloadUrl, options, (download, status) => {
			if(status === 200) {
				listener.onDownloadFinish(download.filename)
			} else {
				uni.showToast({
					title: '下载升级包出错',
					duration: 3000,
					icon: 'none',
				});
			}
		})
		let stateChange = (download, status) => {
			if(status === 200) {
				let percent = parseInt((download.downloadedSize / download.totalSize) * 100)
				if(listener) {
					listener.updateProgress(percent)
				}
			}
		}
		task.addEventListener('statechanged', stateChange, false)
		task.start()
	},
	

	uploadFileProgress(filePath, params, listener) {
		// #ifdef APP-PLUS
		let fileLocalPath = filePath
		if(fileLocalPath.startsWith('file://')) {
			fileLocalPath = fileLocalPath.substring(7)
		}
		fileLocalPath = plus.io.convertAbsoluteFileSystem(fileLocalPath)
		
		const uploadServiceUrl = `${http.loginUrl}/webapp/uploadFile`
		const uploadOptions = {
		        // 分块上传的大小单位kb，Android平台需设置分块上传才能准确触发statechanged返回上传进度，ios自动忽略
		        chunkSize: 100,
		        method: 'POST'
		}
		// 创建上传任务
		let uploadTask = plus.uploader.createUpload(uploadServiceUrl, uploadOptions)
		// 往上传任务里添加文件，第二个参数的字段有默认值，可以传空对象{}即可，但是不能不传
		uploadTask.addFile(fileLocalPath, {})
		// 这里可以将添加文件的返回值打印出来，true表示添加文件成功，false表示添加失败
		// 往接口里添加其他额外的请求参数,第一个参数是key，第二个参数是value
		if(params) {
			for(let p in params) {
				uploadTask.addData(p, params[p])
			}
		}
		// uploadTask.addData('string_key1', 'string_value1')
		// 这里可以将添加额外请求参数的返回值打印出来，true表示添加成功，false表示添加失败
		// uploadTask.addData('string_key2', 'string_value2')
		// 设置请求头信息,根据后端接口的要求设置
		// uploadTask.setRequestHeader('headerName1', 'headerValue1')
		// uploadTask.setRequestHeader('headerName2', 'headerValue2')
	 
		// 注意：上面addFile、addData中但凡有一个返回false，都不能上传成功。请检查是否设置错了
	 
		// 添加事件监听器用于监听实时进度和完成情况，第一个参数为“statechanged”，第二个参数是回调方法
		uploadTask.addEventListener( "statechanged", (upload, status) =>{
			switch (upload.state) {
					case 1: // 上传任务开始请求
						break
					case 2: // 上传任务请求已经建立
						break
					case 3: // 上传任务提交数据,监听 statechanged 事件时可多次触发此状态。（重点）
						// uploadedSize表示当前已经上传了的数据大小，totalSize表示文件总大小，单位是字节b
						let percent = parseInt(100 * upload.uploadedSize/upload.totalSize)
						if(listener) {
							listener.updateProgress(percent)
						}
						break
					case 4: // 上传任务已完成, 无论成功或失败都会执行到 4 这里
						if (status === 200) {
							listener.onUploadFinish(upload)
							// 上传成功
						} else {
							// 上传失败
						}
			}
		})
		// 开始执行上传任务
		uploadTask.start()
		// #endif
	}
	
};