<template>
	<body style="background: #f1f1f1;">
		<section class="main">
			<div class="inner">
				<div class="gap"></div>
				<div class="zy-form">

					<div class="item">
						<p class="label">生产线</p>
						<div class="rp">
							<div class="zy-selectBox" @click="showSelect">
								<p v-if="!model.SCXMC" class="res placeholder">请选择</p>
								<p else class="res">{{model.SCXMC}}</p>
							</div>

						</div>
					</div>
					<div class="item">
						<p class="label">治污线 </p>
						<div class="rp">
							<div class="zy-selectBox"  @click="showPollutionSelect = true">
								<p v-if="!model.ZWXMC" class="res placeholder" >请选择</p>
								<p else class="res">{{model.ZWXMC}}</p>
							</div>

						</div>
					</div>
					<div class="item remark">
						<p class="label">备注 </p>
						<div class="rp">
							<textarea v-model="model.BZ" auto-height="true" maxlength="-1" class="zy-textarea1" rows="3" placeholder="请填写备注信息, 如有特别需要说明的情况;"></textarea>
						</div>
					</div>
				</div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="zy-bot-btn1" @click="save()">保存</div>
				<div class="gap"></div>
				<div class="gap"></div>
			</div>
		</section>
		<m-select value-name="SCXID" label-name="SCXMC" mode="single-column" :list="produceList"
		v-model="showProduceSelect" @confirm="selectProduceLine"></m-select>

		<m-select value-name="ZWXID" label-name="ZWXMC" mode="single-column" :list="pollutionList"
		v-model="showPollutionSelect" @confirm="selectPolltionLine"></m-select>
	</body>
</template>

<script>
	import {
		getScxList,
		getZwxList,
		addRelation,
		getRelationList,
	} from '@/api/iot/enterprise.js';
	export default {
		data() {
			return {
				userInfo: {},
				info: {},
				produceList: [],
				pollutionList: [],
				showProduceSelect: false,
				showPollutionSelect: false,
				model: {
					SCXID: '',
					SCXMC: '',
					ZWXID: '',
					USER: '',
					BZ: ''
				},
				rules: {
					SCXMC: {
						required: true,
						message: '请选择生产线',
						trigger: 'change'
					},
					ZWXMC: {
						required: true,
						message: '请选择治污线'
					}
				},
				type: 'add',
				fixedLine: false,
				backPage: true,
				relationList: [],
				enterpriseInfo:{},
				relationList:[]
			}
		},
		onLoad(option) {
			this.userInfo = uni.getStorageSync('userInfo');
			this.info = JSON.parse(decodeURIComponent(option.info));
			//console.log(this.info);
			if (this.info.SCXID) {
				this.model.SCXID = this.info.SCXID;
				this.model.SCXMC = this.info.SCXMC;
				this.fixedLine = true;
				
			}
			if (this.type == 'edit') {
				uni.setNavigationBarTitle({
					title: '修改关系绑定'
				})
			}
			
			this.enterpriseInfo = uni.getStorageSync('userInfo');
			
			
		},
		mounted() {
			this.getProduceList();
			this.getPollutionList();
			this.getRelationList();
		},
		onBackPress() {
			let self = this;
			if (this.backPage) {
				uni.showModal({
					title: '提示',
					content: '当前内容未保存，是否离开?',
					success: function(res) {
						if (res.confirm) {
							console.log('用户点击确定');
							self.backPage = false;
							uni.navigateBack({
								delta: 1
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
							self.backPage = true;
						}
					}
				});
			}
			uni.hideKeyboard()
			return this.backPage;
		},
		methods: {
			getRelationList() {
			    let { sjqx } = this.enterpriseInfo;
			    getRelationList({
			        ORGID: sjqx,
			        SCXID: this.model.SCXID
			    }).then((res) => {
			        this.relationList = res.data;
					console.log('rela',this.relationList);
			    });
			},
			getProduceList() {
				getScxList({
					WRYBH: this.info.WRYBH
				}).then(res => {
					this.produceList = res.data;
				})
			},
			getPollutionList() {
				getZwxList({
					WRYBH: this.info.WRYBH
				}).then(res => {
					this.pollutionList = res.data;
				})
			},
			showSelect(){
				if(this.fixedLine === true){
					return;
				}
				this.showProduceSelect = true;
				
			},
			selectProduceLine(v) {
				this.model.SCXID = v[0].value;
				this.model.SCXMC = v[0].label;
				this.getRelationList()
			},
			selectPolltionLine(v) {
				this.model.ZWXID = v[0].value;
				this.model.ZWXMC = v[0].label;
				let isExistence = this.relationList.some(item => item.ZWXMC == this.model.ZWXMC)
				if(isExistence){
					uni.showModal({
						title: '提示',
						content: '该治污线已经关联，请勿重复关联！',
					});
					return;
				}
			},

			save() {

				this.model.ORGID = this.userInfo.sjqx
				this.model.WRYBH = this.info.WRYBH
				this.model.USER = this.userInfo.name
				let rules = Object.keys(this.rules);
				for (let i = 0; i < rules.length; i++) {
					let field = rules[i];
					let requires = this.rules[field];
					console.log(!this.model[field], requires.required)
					if ((!this.model[field]||!this.model[field].length) && requires.required) {
						uni.showToast({
							title: requires.message,
							icon: 'error'
						})

						return;
					}
					
				}
				
	            let isExistence = this.relationList.some(item => item.ZWXMC == this.model.ZWXMC)
	            if(isExistence){
	            	uni.showModal({
						title: '提示',
	            		content:'该治污线已经关联，请勿重复关联！'
	            	})
	            	return;
	            }

				let self = this;
				addRelation(this.model).then(res => {
					uni.showToast({
						title: '添加成功',
						duration: 1000
					}).then(() => {
						setTimeout(() => {
							// uni.navigateTo({
							// 	url: './saveScxSuccess?scxName='+this.model.SCXMC+'&info='+ encodeURIComponent(JSON.stringify(this.info))
							// });
							// uni.setStorageSync('isAddScx', true);
							let pages = getCurrentPages(); // 当前页面
							let beforePage = pages[pages.length - 2]; // 上一页
							if(beforePage.$vm){
								beforePage.$vm.getRelationList();
							}else{
								beforePage.getRelationList();
							}
							//从生产线详情进入时，需要刷新企业信息页面的治关系绑定列表
							if (self.fixedLine === true) {
								let parentPage = pages[pages.length - 3];
								if (parentPage.$vm) {
									parentPage.$vm.getRelationList();
								} else {
									parentPage.getRelationList();
								}
							}
							this.backPage = false;
							uni.navigateBack({
								delta: 1
							})
						}, 1000)
					})

				})

			},
		}
	}
</script>

<style scoped>
	html { -webkit-tap-highlight-color: transparent; height: 100%; }
	body { -webkit-backface-visibility: hidden; height: 100%;}
	textarea {

		min-height: 84rpx;
	}

	.uni-textarea-placeholder {
		white-space: pre-wrap;
		overflow: unset;
	}
</style>
