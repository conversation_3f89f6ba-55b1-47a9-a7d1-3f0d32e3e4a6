<template>
		<div>
			<web-view :src="webviewURL"></web-view>
		</div>
</template>
<script>
	export default {
		onLoad(options) {
			// #ifdef APP-PLUS
			plus.screen.lockOrientation('landscape-primary'); //横屏
			// #endif
			console.log('options',options)
				this.pmtxh = options.pmtxh;
				this.pmtmc = options.pmtmc;
				this.wrybh = options.wrybh;
				uni.setNavigationBarTitle({
					title: `${this.pmtmc}点位详情`
				});
				this.webviewURL = `http://iot-manage.iotdi.com.cn/pmt/index.html#/views/planeFigure/FullScreenPlaneFigureCanvas?pmtxh=${this.pmtxh}&wrybh=${this.wrybh}&pmtmc=${this.pmtmc}`
		},
		onBackPress(e) {
			// 退出页面时解除横屏
			// #ifdef APP-PLUS
			if (e.from == 'backbutton') {
				plus.screen.lockOrientation('portrait-primary'); 
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/enterpriseWarnInfo/whitePage'
					});
				}, 200);
				return true;
			}
			// #endif
		},
		data() {
			return {
		      pmtxh:'',
		      wrybh:'',
			  webviewURL:''

			};
		},
		watch: {

		},

		mounted() {

		},
		methods: {
			back() {
				// 退出页面时解除横屏
				// #ifdef APP-PLUS
				plus.screen.lockOrientation('portrait-primary'); //
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/enterpriseWarnInfo/whitePage'
					});
				}, 200);
				// #endif
			}
		}
	};
</script>

<style scoped>
	.echarts {
		width: 100%;
		height: 500rpx;
	}

	.ic-full {
		position: fixed;
		bottom: 50rpx;
		right: 30.1932rpx;
		width: 60.386475rpx;
		height: 60.386475rpx;
		background: url('~@/static/app/images/ic-full.png') 0 0 no-repeat;
		background-size: 100%;
		z-index: 1;
	}

	.zy-line {
		padding-left: 20rpx;
		padding-bottom: 20rpx;
		position: relative;
	}

	.date-input {
		float: right;
		width: 104px;
		height: 26px;
		color: #000000;
		line-height: 26px;
		font-size: 13px;
		cursor: pointer;
		padding-left: 5px;
		border-radius: 5px;
		border: 1px solid rgb(50, 131, 183);
		outline: none;
	}

	.datestr {
		position: relative;
		bottom: 6rpx;
	}
</style>
