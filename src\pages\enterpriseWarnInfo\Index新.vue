<template>
	<!-- 实时数据列表页 -->
	<body style="background: #f5f5f5;padding: 8rpx;">
		<header class="header pd-hd1">
			<div class="pd-row aic">
			<input type="text" class="pd-inpsrh1" @input="search" placeholder="输入关键字查询" v-model="serchtext" />
			<image src="../../static/app/images/sxic1.png" class="pd-sxic1" @click="showFilterBox = !showFilterBox" />
			</div>
		</header>
		<ul class="pd-ultbs1" style="top: 140rpx;">
			<li :class="currentTab == '1,2' ? 'on' : ''" @click="change('1,2', 'qd')">生产({{ count.D_COU || '0' }})</li>
			<li :class="currentTab == '3' ? 'on' : ''" @click="change('3','tz')">停止({{ count.S_COU || '0' }})</li>
			<li :class="currentTab == '0' ? 'on' : ''" @click="change('0','lx')">离线({{ count.L_COU || '0' }})</li>
			<li :class="!currentTab ? 'on' : ''" @click="change('','all')">全部({{ count.ALL_COU || '0' }})</li>
		</ul>
		<!-- 0: 离线， 1 生产正常 2 治污异常  3 生产停止 -->
		<section class="main" :style="{height:currentTab=='1,2'?'calc(100% - 80rpx)':'100%'}">
			<div class="inner pd-con1">
				<div class="gap"></div>
				<ul class="pd-ullst1">
					<!-- <scroll-view scroll-y style="height: calc(100vh - 80rpx)" @scrolltolower="getMore"> -->
					<li v-for="(item, index) in list" @click="toEnterprise(item)">
						<h1>
							<strong>{{ item.WRYMC }}</strong>

							<image :src="getFilePath(item.ZT)" class="mk"></image>
						</h1>
						<p>{{ item.DWDZ }}</p>
					</li>
					<!-- </scroll-view> -->
				</ul>
				<u-empty mode="data" v-if="nodata"></u-empty>
			</div>
		</section>
		<messageBox :time='updateTime' :zwycNum="zwycNum" v-if="currentTab == '1,2'" type='治污异常'></messageBox>
		<!-- <messageBox :zwycNum="zwycNum" v-if="currentTab == '3'" type='停止生产'></messageBox>
		<messageBox :zwycNum="zwycNum" v-if="!currentTab"></messageBox> -->
		<filterBox :showFilterBox="showFilterBox" :ZT="currentTab" @getFilterParams="getFilterParams"></filterBox>
	</body>
</template>

<script>
import { getList, getCount,getXzqh } from '../../api/iot/realtime.js';
import filterBox from './filter-box.vue';
import messageBox from './message.vue';
export default {
	components: { filterBox, messageBox },
	data() {
		return {
			serchtext: '',
			list: [],
			zwycNum: 0,
			currentTab: '1,2',
			timer: null,
			pageSize: 10,
			pagNum: 1,
			showFilterBox: false,
			xzqhdm: '',
			fdm: '',
			count: {
			},
			xzqhList: [],
			userinfo: {},
			nodata: false,
			request: null,
			currentUrl: 'qd',
			updateTime: ''
		};
	},
	mounted() {
		let userinfo = uni.getStorageSync('user_info');
		if(userinfo.orgid.length > 6){
			//区县级
			this.xzqhdm = userinfo.orgid;
		}else if(userinfo.orgid.length == 6){
			//区县级
			this.xzqhdm = userinfo.orgid;
			this.fdm = userinfo.orgid.substr(0,4)
		}else{
			//市级
			this.fdm = userinfo.orgid;
		}
		// this.initCount();
		// this.initList();
		// this.initRegion();
	},
	onShow(){
		this.initCount();
			this.initList();
		// this.getWarningCount();
	},
	methods: {
		// tab切换
		change(v, url) {
			this.currentUrl = url;
			//重置列表数据、数量
			this.reset();
			
			this.currentTab = v;
			this.initList();
			this.initCount();
		},
		// 搜索
		search() {
			if (this.timer) {
				clearTimeout(this.timer);
			}
			this.timer = setTimeout(() => {
				this.reset();
				this.initList();
				this.initCount();
			}, 500);
		},
		// tab 各种状态数量
		initCount(ZT) {
			let xzqhdm = this.xzqhdm;
			if(!xzqhdm){
				xzqhdm = this.fdm;
			}
			getCount({ WRYMC: this.serchtext,ZT:ZT||'', XZQHDM: xzqhdm }).then(res => {
				this.count = res.data.count;
				// this.xzqhList = res.data.xzqhList;
			});
		},
		getMore() {
			console.log(this.pagNum);
			// if (this.xxtxlist.length >= this.total) {
			//     uni.showToast({
			//         title: '没有更多了',
			//         icon: 'none'
			//     });
			//     return;
			// }
			// this.PageNum++;
			// this.xxtxlist();
		},
		// 初始化列表数据
		initList(v) {
			let ZT = this.currentTab;
			if (v) {
				ZT = v;
			}
			// 列表数据
			let xzqhdm = this.xzqhdm;
			if(!xzqhdm){
				xzqhdm = this.fdm;
			}
			if(this.request && this.request.cancel){
				this.request.cancel();
			}
			this.request = getList({ pageSize: this.pageSize, pageNum: this.pagNum, ZT: ZT, WRYMC: this.serchtext, XZQHDM: xzqhdm,url: this.currentUrl });
			this.request.then(res => {
				this.request = null;
				this.list = res.data;
				this.nodata = false;
				if (!res.data || !res.data.length) {
					this.nodata = true;
				}
			}).catch(err=>{
				console.error(err);
			})
			
			this.getWarningCount();
		},
		getWarningCount(){
			// 列表数据
			let xzqhdm = this.xzqhdm;
			if(!xzqhdm){
				xzqhdm = this.fdm;
			}
			// 治污异常数量
			getList({ pageSize: '5000', pageNum: '1', ZT: '', WRYMC: '', XZQHDM: xzqhdm, url: 'all' }).then(res => {
				let count = 0;
				let allData = res.data;
				for (let i in allData) {
					if (allData[i].ZT == '2') {
						count++;
					}
				}
				this.updateTime = this.$dayjs().format('YYYY-MM-DD HH:mm:ss');
				this.zwycNum = count;
			});
		},
		getFilterParams(params) {
			this.reset();
			this.xzqhdm = params.XZQHDM;
			this.fdm = params.FDM;
			this.initList(params.ZT);
			this.initCount(params.ZT);
		},
		getFilePath(v) {
			return v == '0'
				? require('../../static/app/images/mk3a.png')
				: v == '1'
				? require('../../static/app/images/mk1a.png')
				: v == '2'
				? require('../../static/app/images/mk2a.png')
				: require('../../static/app/images/tingzhi.png');
		},
		// 到企业详情页
		toEnterprise(options) {
			uni.navigateTo({
				url: './enterpriseInfo?WRYBH=' + options.WRYBH
			});
		},

		toMine() {
			uni.navigateTo({
				url: '../mine/Index'
			});
		},
		reset(){
			this.list = [];
			this.count = {
			};
		},
		//初始化行政区域列表
		initRegion(){
			this.xzqhList = []
			//省级用户查询地市
			if (this.xzqhdm.length == 2) {
				getXzqh({
					FDM: this.xzqhdm
				}).then(res=>{
					this.xzqhList = res.data;
				})
			}
			//市级用户查询区县
			//区县级用户查地市,查找父级市
			if (this.xzqhdm.length == 4 || this.xzqhdm.length == 6) {
				getXzqh({
					FDM: this.xzqhdm.substr(0,2) + '0000'
				}).then(res=>{
					let data = res.data;
					data.forEach(item => {
						if(item.XZQHDM.substr(0,4) == this.xzqhdm.substr(0,4)){
							this.xzqhList.push(item);
							return;
						}
					})
					
				})
			}
			
		}
	}
};
</script>

<style scoped lang="less">
.main {
	// height: calc(100% - 80rpx);
}
.pd-ultbs1 {
	z-index: 666;
}
.inner.pd-con1 {
	padding-top: 230rpx;
}
.tab {
	width: 100%;
	display: flex;
	justify-content: space-between;
}
.on {
	color: red;
}

.header {
	height: 141.90825rpx;
	padding: 45.289875rpx 28.985475rpx 0;
	box-sizing: border-box;
	background: #3873ff;
}
</style>
