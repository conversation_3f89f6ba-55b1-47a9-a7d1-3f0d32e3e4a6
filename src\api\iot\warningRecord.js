/** @format */

import axios from '@/common/ajaxRequest.js';
import { ULR_BASE} from '@/common/config.js';
//预警事件接口
export const getWarningEventList = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/warning/event/list',
        params: data
    });
};

//预警基本信息
export const getWarningBaseList = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/warning/base/list',
        params: data
    });
};

//预警反馈列表
export const getWarningFeedbackList = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/warning/feedback/list',
        params: data
    });
};

//预警反馈录入
export const getWarningFeedbackAdd = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/warning/feedback/add',
        params: data
    });
};
