<!-- @format -->

<template>
    <!-- <view style="background-color: #f1f2f6;" class="warp">
		<header class="header"><h1 class="title">个人中心</h1></header>
		<section class="main">
			<div class="inner">
				<ul class="zy-data1">
					<li>
						<span class="s1">用户账号</span>
						<span class="s2">{{ deptInfo.name }}</span>
					</li>
					<li>
						<span class="s1">所属部门</span>
						<span class="s2">{{ deptInfo.department }}</span>
					</li>
					<li>
						<span class="s1">当前版本号</span>
						<span class="s2">1.0</span>
					</li>
					<li @click="showUpData = !showUpData">
						<span class="s1">新版更新</span>
						<span class="s2 more-btn">有新版</span>
					</li>
					<li @click="clearStorage">
						<span class="s1">清除缓存</span>
						<span class="s2 more-btn">{{ storageSize }}M</span>
					</li>
				</ul>

				<button class="zy-btn1" @click="logOut">安全退出</button>
			</div>
		</section>

		<div class="zy-alert1" v-if="showUpData">
			<div class="zy-btn-groups1">
				<div class="box">
					<button class="zy-btn1">手动更新</button>
					<button class="zy-btn1">自动更新</button>
				</div>

				<button class="zy-btn1" @click="showUpData = false">取消</button>
			</div>
		</div>
	</view> -->
    <div style="background-color: #f5f5f5">
        <header class="header">
            <i class="pd-backbtn" @click="back"></i>
            <h1 class="title"></h1>
        </header>
        <section class="main">
            <div class="inner" style="padding-top: 0">
                <div class="userbg">
                    <div class="userlogo">
                        <image src="../../static/app/images/user1.png" alt="" />
                        <p class="name">
                            {{ (deptInfo && deptInfo.id) || '-' }}
                        </p>
                    </div>
                </div>
                <div class="zy-form">
                    <div class="item">
                        <p class="label">用户名称</p>
                        <div class="rp">
                            <i class="rfont">{{
                                (deptInfo && deptInfo.name) || '-'
                            }}</i>
                        </div>
                    </div>
                    <div class="item">
                        <p class="label">所属客户</p>
                        <div class="rp">
                            <i class="rfont arr" @click="toChangeCustom">{{
                                curCustomName ||
                                (deptInfo && deptInfo.department) ||
                                '-'
                            }}</i>
                        </div>
                    </div>

                    <div class="item">
                        <p class="label">版本介绍</p>
                        <div class="rp">
                            <i class="rfont arr" @click="toVersion"
                                >V{{ version }}</i
                            >
                        </div>
                    </div>
                    <div class="item" @click="updateVersion">
                        <p class="label">版本更新</p>
                        <div class="rp">
                            <i class="rfont" style="height: 19px"
                                >V{{ latestVersion }}
                            </i>
                            <image
                                v-if="!getCompareResult()"
                                class="arrow"
                                src="../../static/app/images/upArrow.png"
                                alt=""
                            />
                        </div>
                    </div>
                    <div class="item" @click="clearStorage">
                        <p class="label">清除缓存</p>
                        <div class="rp">
                            <i class="rfont arr">{{ storageSize }}M</i>
                        </div>
                    </div>

                    <div class="item" style="padding: 8rpx 0">
                        <p
                            class="label"
                            style="
                                display: flex;
                                justify-content: flex-start;
                                align-items: center;
                            "
                        >
                            自动登录
                        </p>
                        <div class="rp">
                            <switch
                                style="transform: scale(0.8)"
                                :checked="isAutoLogin === true"
                                @change="autoLoginChange"
                            />
                        </div>
                    </div>
                </div>

                <div class="quitbtn" @click="signOut">安全退出</div>
            </div>
        </section>
        <view v-if="showUpgrade" class="flex-column-layout upgrade-tip-mask">
            <view class="flex-column-layout upgrade-progress-tip">
                <text style="width: 100%; text-align: left"
                    >正在更新升级数据</text
                >
                <view style="width: 100%"
                    ><progress
                        :percent="upgradeProgress"
                        active
                        active-mode="forwards"
                        stroke-width="3"
                        show-info
                /></view>
            </view>
        </view>
    </div>
</template>

<script>
import { appVersion } from '@/api/iot/version.js';
import appService from '@/api/app-service.js';
export default {
    data() {
        return {
            showUpData: false,
            deptInfo: {},
            storageSize: '',
            isAutoLogin: false,
            version: '',
            versionCode: '',
            curCustomName: '',
            versionList: [],
            showUpgrade: false,
            upgradeProgress: 0,
            latestVersion: 0
        };
    },
    onLoad(option) {
        let _self = this;
        // 是否自动登录
        uni.getStorage({
            key: 'autoLogin',
            success: function (r) {
                _self.isAutoLogin = r.data;
            }
        });
    },
    onShow() {
        this.curCustomName = uni.getStorageSync('ORGID_LABEL') || '';
    },
    mounted() {
        this.deptInfo = uni.getStorageSync('userInfo');
        console.log('this.deptInfo', this.deptInfo);
        this.getStorageInfo();
        this.getVersionList();
        plus.runtime.getProperty(plus.runtime.appid, (wgtInfo) => {
            this.versionCode = wgtInfo.versionCode;
            this.version = wgtInfo.version;
        });
    },
    methods: {
        //选择所属客户
        toChangeCustom(e) {
            uni.navigateTo({
                url: '/pages/mine/customSelect'
            });
        },
        // 获取版本信息
        async getVersionList() {
            let params = {
                APPID: 'device'
            };
            const { data } = await appVersion(params);
            this.versionList = data;
            this.latestVersion =
                this.versionList &&
                this.versionList[0] &&
                this.versionList[0].BBMC;
        },
        // 更新版本
        async updateVersion() {
            let result = this.compare(this.latestVersion, this.version);
            if ([0, -1].includes(result)) {
                uni.showToast({
                    title: '已是最新版本',
                    icon: 'error',
                    duration: 1000
                });
            } else {
                this.checkAppUpgrade();
            }
        },

        getCompareResult() {
            let result = this.compare(this.latestVersion, this.version);
            return [0, -1].includes(result);
        },
        //获取版本信息
        checkAppUpgrade() {
            // #ifdef APP-PLUS
            let _self = this;
            appService.setProgressUpdater((percent) => {
                _self.showUpgrade = true;
                _self.upgradeProgress = percent;
                if (percent === 100) {
                    _self.showUpgrade = false;
                }
            });
            appService.checkMPAppUpgrade();
            // #endif
        },

        compare(v1, v2) {
            if (v1 === v2) {
                return 0;
            }
            const toNum = (version) => {
                version = version.toString();
                // const versionArr = version.split('.')
                const versionArr = version.split(/\D/);
                const NUM_FILL = ['0000', '000', '00', '0', ''];

                for (let i = 0; i < versionArr.length; i++) {
                    const len = versionArr[i].length;
                    versionArr[i] = NUM_FILL[len] + versionArr[i];
                }

                return parseInt(versionArr.join(''));
            };

            v1 = toNum(v1);
            v2 = toNum(v2);

            if (v1 > v2) {
                return 1;
            }
            if (v1 < v2) {
                return -1;
            }
        },

        clearStorage() {
            uni.showModal({
                title: '清除缓存后需要重新登录!',
                success: (res) => {
                    if (res.confirm) {
                        uni.clearStorage().then((res) => {
                            uni.showToast({
                                title: '清除缓存成功',
                                duration: 1500
                            }).then((res) => {
                                this.getStorageInfo();
                                setTimeout(() => {
                                    uni.redirectTo({
                                        url: '../login/login'
                                    });
                                }, 1500);
                            });
                        });
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                }
            });
        },
        autoLoginChange(e) {
            this.isAutoLogin = !this.isAutoLogin;
            uni.setStorage({
                key: 'autoLogin',
                data: this.isAutoLogin,
                success: function (r) {}
            });
        },
        back() {
            uni.navigateBack({
                delta: 1
            });
        },
        getStorageInfo() {
            let StorageInfo = uni.getStorageInfoSync();
            this.storageSize = (StorageInfo.currentSize / 1024).toFixed(2);
        },
        toVersion() {
            uni.navigateTo({
                url: '/pages/mine/version/Layout'
            });
        },
        signOut() {
            //退出登录清除草稿，清除上报阈值的缓存
            uni.removeStorageSync('duePolluteDraft');
            uni.removeStorageSync('equitDraft');
            uni.removeStorageSync('equitDraftFIleList');
            uni.removeStorageSync('arrHistoryThreshold');
            uni.removeStorageSync('arrHistoryCycle');
            // uni.removeStorageSync("ORGID");
            // uni.removeStorageSync("ORGID_LABEL");
            uni.showModal({
                title: '是否退出?',
                success: (res) => {
                    if (res.confirm) {
                        uni.redirectTo({
                            url: '../login/login'
                        });
                    }
                }
            });
        },
        call() {
            if (this.deptInfo.ywslxdh) {
                uni.makePhoneCall({
                    phoneNumber: this.deptInfo.ywslxdh //仅为示例
                });
            }
        }
    }
};
</script>

<style scoped>
html {
    -webkit-tap-highlight-color: transparent;
    height: 100%;
    position: relative;
}

body {
    -webkit-backface-visibility: hidden;
    height: 100%;
    position: relative;
}

.zy-data1 {
    padding: 16rpx;
}

.avatar-box {
    display: flex;
    align-items: center;
    /* justify-content: center; */
    padding: 12rpx 0;
    background-color: white;
}

.avatr {
    width: 96rpx;
    height: 96rpx;
    margin: 0 18rpx;
}

.inner {
    padding: 79.71015rpx 0 90.579675rpx;
}

.upgrade-tip-mask {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    justify-content: flex-start;
}

.upgrade-progress-tip {
    width: 72%;
    padding: 20rpx;
    border-radius: 6rpx;
    background-color: #fff;
    box-shadow: 0 0 20rpx 0 #ccc;
    position: absolute;
    top: 70rpx;
    left: 50%;
    margin-left: -36%;
}

.arrow {
    width: 30rpx;
    height: 30rpx;
    margin-left: 10rpx;
}
</style>
