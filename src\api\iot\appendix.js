import axios from '@/common/ajaxRequest.js';
import { ULR_BASE, BASE_URL,URL_BASE_ENTERPRISE_DETAIL,UPLOAD_URL,IOTMANAGE_URL} from '@/common/config.js';
// const IOTMANAGE_URL = 'http://iot-manage.iotdi.com.cn/iotManage'
export const getFilelist = (data) => {
    return axios.request({
        method: 'post',
        url: IOTMANAGE_URL + '/platform/file/filemanagecontroller/queryfileinfos',
        data: data,
		headers: { 'Content-Type': 'application/json; charset=utf-8' }
    });
};



export const deletefile = (data) => {
    return axios.request({
        method: 'get',
        url: IOTMANAGE_URL + '/platform/file/filemanagecontroller/deletefile/' + data,
		params:{}
    });
};


export const downloadFile = (data) => {
    return axios.request({
        method: 'get',
        url: IOTMANAGE_URL + '/platform/file/filemanagecontroller/downloadfilebyid/' + data,
		params:{}
    });
};


