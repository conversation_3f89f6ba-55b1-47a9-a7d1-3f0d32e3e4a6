<template>
	<ul class="yy-ullst1">
		<li><em>预警时间</em><i class="rfont">{{info.YJSJ || -''}}</i></li>
		<li><em>发生时间</em><i class="rfont">{{info.FSSJ  || -''}}</i></li>
		<li><em>预警类型</em><i class="rfont">{{info.YJLX_CH || -''}}</i></li>
		<li><em>预警设备</em><i class="rfont">{{equitments}}</i></li>
		<li><em>预警信息</em>
			<i class="rfont txtlef">
				{{info.YJNR  || -''}}
				</i>
		</li>
	</ul>
</template>


<script>
	export default {
		props:{
			warning:{
				type:Object,
				default:function(){}
			}
		},
		data() {
			return {
				info:{}
			}
		},
		computed:{
			equitments(){
				let SCSBMC = '';
				let ZWSBMC = '';
				if(this.info.ZWSBMC){
					SCSBMC = this.info.SCSBMC == null || this.info.SCSBMC == ''?'':this.info.SCSBMC + ',';
					ZWSBMC = this.info.ZWSBMC == null || this.info.ZWSBMC == ''?'':this.info.ZWSBMC;
				}else{
					SCSBMC = this.info.SCSBMC == null || this.info.SCSBMC == ''?'--':this.info.SCSBMC;
					ZWSBMC = ''
				}
	
				return SCSBMC+ZWSBMC
			}
		},
		watch:{
			warning:{
				handler(newVal,oldVal){
					this.info = newVal;
		     	},
				immediate: true,
				deep:true
				
			}
			
		},
		created() {
			//this.info = uni.getStorageSync('warningData')
		},
		methods: {
			//tab切换
			changeTab(item){
				this.curType = item.value;
			},
			
			
			
			
		}
	}
</script>

<style>
</style>
