/** @format */
import originalAxios from 'axios-miniprogram';
import axios from '@/common/ajaxRequest.js';
import { ULR_BASE, DELETFILE_URLZDY } from '@/common/config.js';

//最新上报的状态数据
export const getState = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/az/zdsb/ztxx',
        params: data
    });
};

//最新上报的震动数据
export const getVibration = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/az/zdsb/lssj',
        params: data
    });
};

// 获取预警类型
export const getCode = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/monitor/code',
        params: data
    });
};
// 预警记录
export const getYjjl = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/monitor/qyjbxx/yjjl',
        params: data
    });
};

// 列表
export const getList = (data) => {
    const CancelToken = originalAxios.CancelToken;
    const source = CancelToken.source();

    let promise = axios.request({
        method: 'get',
        url: ULR_BASE + `/monitor/list/${data.url}`,
        params: data,
        cancelToken: source.token
    });
    promise.cancel = () => {
        promise.cancel = null;
        source.cancel('Operation canceled by the user.');
    };
    return promise;
};

// 企业信息
export const getInfo = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/monitor/time_monitor/info',
        params: data
    });
};
