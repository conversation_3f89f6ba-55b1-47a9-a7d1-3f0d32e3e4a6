<!-- @format -->

<template>
    <body style="background-color: #f1f2f6" class="warp">
        <header class="header">
            <div class="title">编辑产污设备</div>
            <i class="ic-back" @click="back"></i>
            <i class="pd-edtbtn" v-if="disabled" @click="disabled = false"
                >编辑</i
            >
            <i class="pd-edtbtn" v-if="!disabled" @click="disabled = true"
                >关闭</i
            >
        </header>
        <ul class="zy-tabs1">
            <li
                :class="dataType == item ? 'cur' : ''"
                v-for="(item, index) in dataTypeArr"
                :key="index"
                @click="change(item)"
            >
                <p>{{ item }}</p>
            </li>
        </ul>
        <scroll-view
            scroll-y
            style="height: calc(100vh - 156rpx)"
            @scrolltolower="getMore"
        >
            <section class="main">
                <div class="inner" style="padding: 20rpx 18rpx 0px">
                    <div v-if="dataType == '设备信息'">
                        <div class="pd-tit1"><strong>基本信息</strong></div>
                        <u-form
                            :model="model"
                            :rules="rules"
                            ref="uForm"
                            :errorType="errorType"
                        >
                            <!-- 	<u-form-item :label-position="labelPosition" label="所属企业" required prop="SSQY" :label-width="labelWidth">
								<u-input :border="border" v-model="model.SSQY" placeholder="请填写所属企业"></u-input>
							</u-form-item> -->

                            <u-form-item
                                required
                                :label-position="labelPosition"
                                label="设备名称"
                                prop="SBMC"
                                :label-width="labelWidth"
                            >
                                <u-input
                                    :disabled="disabled"
                                    :border="border"
                                    placeholder="请填写设备名称"
                                    v-model="model.SBMC"
                                />
                            </u-form-item>

                            <u-form-item
                                required
                                :label-position="labelPosition"
                                label="所属生产线"
                                prop="SSSCX"
                                :label-width="labelWidth"
                            >
                                <u-input
                                    v-if="disabled"
                                    disabled
                                    :border="border"
                                    placeholder="请填写所属生产线"
                                    v-model="SSSCX"
                                />
                                <u-input
                                    v-if="!disabled"
                                    type="select"
                                    :select-open="scxShow"
                                    @click="scxShow = true"
                                    :border="border"
                                    placeholder="请填写所属生产线"
                                    v-model="SSSCX"
                                />
                            </u-form-item>
                            <u-form-item
                                :label-position="labelPosition"
                                label="行业类型"
                                required
                                prop="HYFL"
                                :label-width="labelWidth"
                            >
                                <u-input
                                    v-if="disabled"
                                    disabled
                                    v-model="model.HYFL"
                                    placeholder="请选择行业分类"
                                ></u-input>
                                <u-input
                                    v-if="!disabled"
                                    :border="border"
                                    type="select"
                                    :select-open="hylxShow"
                                    v-model="model.HYFL"
                                    placeholder="请选择行业分类"
                                    @click="hylxShow = true"
                                ></u-input>
                            </u-form-item>

                            <u-form-item
                                :label-position="labelPosition"
                                label="设备类型"
                                required
                                prop="HYFL_SBLX"
                                :label-width="labelWidth"
                            >
                                <u-input
                                    v-if="disabled"
                                    disabled
                                    :border="border"
                                    v-model="model.HYFL_SBLX"
                                    placeholder="请选择治污设备组织方式"
                                ></u-input>
                                <u-input
                                    v-if="!disabled"
                                    :border="border"
                                    type="select"
                                    :select-open="sblxShow"
                                    v-model="model.HYFL_SBLX"
                                    placeholder="请选择治污设备组织方式"
                                    @click="selectSblx()"
                                ></u-input>
                            </u-form-item>
                            <u-form-item
                                required
                                :label-position="labelPosition"
                                label="主要污染物"
                                prop="ZYWR"
                                :label-width="labelWidth"
                            >
                                <u-input
                                    v-if="disabled"
                                    disabled
                                    :border="border"
                                    placeholder="请填写主要污染物"
                                    v-model="model.ZYWR"
                                />
                                <u-input
                                    v-if="!disabled"
                                    type="select"
                                    @click="zywrShow = true"
                                    :select-open="zywrShow"
                                    :border="border"
                                    placeholder="请填写主要污染物"
                                    v-model="model.ZYWR"
                                />
                            </u-form-item>
                            <u-form-item
                                required
                                :label-position="labelPosition"
                                label="设备位置"
                                prop="SBWZ"
                                :label-width="labelWidth"
                            >
                                <u-input
                                    disabled
                                    :border="border"
                                    placeholder="请获取位置"
                                    v-model="SBWZ"
                                />
                                <u-icon
                                    v-if="!disabled"
                                    name="map"
                                    @click="getJwd"
                                ></u-icon>
                            </u-form-item>

                            <u-form-item
                                :label-position="labelPosition"
                                label="绑定智能设备"
                                prop="IMEI"
                                :label-width="labelWidth"
                            >
                                <u-input
                                    v-if="disabled"
                                    disabled
                                    :border="border"
                                    placeholder="可扫码获取设备id"
                                    v-model="model.IMEI"
                                />
                                <u-input
                                    v-if="!disabled"
                                    :border="border"
                                    placeholder="可扫码获取设备id"
                                    v-model="model.IMEI"
                                />
                                <u-icon
                                    v-if="!disabled"
                                    name="scan"
                                    @click="scanCode"
                                ></u-icon>
                            </u-form-item>

                            <u-form-item
                                label-position="top"
                                label="备注内容"
                                prop="BZNR"
                                :label-width="labelWidth"
                            >
                                <u-input
                                    type="textarea"
                                    :border="border"
                                    height="200"
                                    placeholder="填写备注内容"
                                    v-model="model.BZ"
                                />
                            </u-form-item>

                            <u-form-item
                                :label-position="labelPosition"
                                label-width="150"
                            >
                                <u-upload
                                    :before-remove="beforeRemove"
                                    width="160"
                                    height="160"
                                    :action="uploadUrl"
                                    ref="uUpload"
                                    :max-count="disabled ? 0 : maxCount"
                                    :auto-upload="true"
                                    :show-progress="true"
                                    :header="{
                                        jwtToken: token
                                    }"
                                    name="up"
                                    :before-upload="beforeUpload"
                                    :form-data="{
                                        LXDM: 'ZDFJ',
                                        ZLXDM: 'ZDFJLB',
                                        YWSJID: ''
                                    }"
                                    :deletable="!disabled"
                                ></u-upload>
                            </u-form-item>
                        </u-form>
                        <ul class="pd-ulbtn1">
                            <li class="on" @click="save">保存</li>
                        </ul>
                    </div>
                    <ReportData
                        :sbInfo="sbInfo"
                        :info="info"
                        v-if="dataType == '上报数据'"
                    ></ReportData>
                    <ReportStatus
                        :sbInfo="sbInfo"
                        :info="info"
                        v-if="dataType == '上报状态'"
                    ></ReportStatus>
                    <Trend
                        :sbInfo="sbInfo"
                        :info="info"
                        v-if="dataType == '振动趋势'"
                    ></Trend>
                </div>

                <u-popup
                    v-model="zywrShow"
                    mode="bottom"
                    width="100%"
                    height="50%"
                    border-radius="20"
                >
                    <div class="opration">
                        <span @click="cancel" class="cancel">取消</span>
                        <span @click="confirm" class="confirm">确定</span>
                    </div>
                    <div class="listWarp">
                        <p
                            v-for="(item, index) in zywrList"
                            @click="selectWrw(item)"
                            :class="selectWrwList.includes(item.DM) ? 'on' : ''"
                        >
                            {{ item.DMNR }}
                        </p>
                    </div>
                </u-popup>

                <u-select
                    value-name="SCXID"
                    label-name="SCXMC"
                    mode="single-column"
                    :list="scxList"
                    v-model="scxShow"
                    @confirm="getScx"
                ></u-select>
                <u-select
                    value-name="DM"
                    label-name="DMNR"
                    mode="single-column"
                    :list="hylxList"
                    v-model="hylxShow"
                    @confirm="getHylx"
                ></u-select>
                <u-select
                    value-name="DM"
                    label-name="DMNR"
                    mode="single-column"
                    :list="sblxList"
                    v-model="sblxShow"
                    @confirm="getSblx"
                ></u-select>
            </section>
        </scroll-view>
    </body>
</template>

<script>
import { getGgdmz } from '@/api/iot/ggdmz.js';
import {
    getScx,
    getCwsb,
    updateCwsb,
    getFilelist,
    deletefile
} from '@/api/iot/enterprise.js';
import ReportData from './reportData.vue';
import ReportStatus from './reportStatus.vue';
import Trend from './trend.vue';
import {
    DOWNLOAD_URLZDY,
    UPLOAD_URL,
    DELETFILE_URLZDY
} from '@/common/config.js';
import enterprise_store from '../enterprise.store.js';
export default {
    components: { ReportData, ReportStatus, Trend },
    watch: {
        JWD: {
            handler: function (v) {
                this.model.JD = v.JWD.longitude;
                this.model.WD = v.JWD.latitude;
                this.SBWZ = `${v.JWD.longitude} ~ ${v.JWD.latitude}`;
            },
            deep: true //深度监听
        }
    },
    data() {
        return {
            JWD: enterprise_store.state,
            autoUpload: false,
            uploadUrl: UPLOAD_URL,
            token: '',
            dataTypeArr: ['设备信息', '上报数据', '上报状态', '振动趋势'],
            dataType: '设备信息',
            disabled: true,
            enterpriseInfo: {},
            sbInfo: {},
            info: {},
            model: {
                SBMC: '',
                ORGID: '',
                CJR: '',
                // SSQY: '',
                SCXID: '',
                HYFL: '',
                HYFL_SBLX: '',
                IMEI: '',
                JD: '',
                WD: '',
                BZ: '', //备注内容
                XGR: '',
                SBID: ''
            },
            HYFL: '', //产污设备组织方式
            SBLX: '', //治污设备组织方式
            SSSCX: '',
            SBWZ: '',
            selectWrwList: [], //选中的污染物list
            hylxShow: false,
            hylxList: [],
            sblxShow: false,
            sblxList: [],
            zywrShow: false,
            zywrList: [],
            scxShow: false,
            scxList: [],
            rules: {
                SSQY: [
                    {
                        required: true,
                        message: '请填写生产线',
                        trigger: 'change'
                    }
                ],
                SCXID: [
                    {
                        required: true,
                        message: '请填写所属生产线'
                    }
                ],
                ZYWR: [
                    {
                        required: true,
                        message: '请选择主要污染物'
                    }
                ],
                HYFL: [
                    {
                        required: true,
                        message: '请选择行业分类'
                    }
                ],
                SBLX: [
                    {
                        required: true,
                        message: '请选择设备类型'
                    }
                ]
            },
            border: false, //input 是否显示边框, 默认false
            labelWidth: 200,
            selectShow: false,
            labelPosition: 'left', //表单域提示文字的位置，left-左侧，top-上方
            errorType: ['border-bottom'], //错误的提示方式，数组形式, 可选值为：
            fileArr: [],
            maxCount: 3
        };
    },
    onLoad(option) {
        this.enterpriseInfo = uni.getStorageSync('userInfo');
        this.info = JSON.parse(decodeURIComponent(option.info));
        this.sbInfo = JSON.parse(decodeURIComponent(option.sbInfo));
        this.token = uni.getStorageSync('token');
    },
    onHide() {},
    mounted() {
        this.$refs.uForm.setRules(this.rules);

        this.getGgdmz();
        // this.getJwd();
    },
    methods: {
        beforeUpload() {
            this.$refs.uUpload.formData.YWSJID = this.sbInfo.SBID;
            // this.$refs.uUpload.formData.file = this.$refs.uUpload.lists[0].file.path;
            // console.log(this.$refs.uUpload.lists);
        },
        //
        getGgdmz() {
            // 主要污染
            getGgdmz({ DMJBH: 'CWSB_ZYWR' }).then((res) => {
                if (res.data_array && res.data_array.length > 0) {
                    this.zywrList = res.data_array;
                }
            });
            // 行业分类
            getGgdmz({ DMJBH: 'CWSB_HYFL', FDM: 'CWSB_HYFL' }).then((res) => {
                if (res.data_array && res.data_array.length > 0) {
                    this.hylxList = res.data_array;
                }
            });
            // 组织方式
            let { orgid } = this.enterpriseInfo;
            let { WRYBH } = this.info;
            let { SBID } = this.sbInfo;
            let { SCXID } = this.sbInfo;
            getScx({
                ORGID: orgid,
                pageSize: 666,
                orderBy: 'CJSJ',
                orderWay: 'DESC',
                WRYBH: WRYBH
            }).then((res) => {
                if (res.data_array && res.data_array.length > 0) {
                    this.scxList = res.data_array;
                    for (let i = 0; i < res.data_array.length; i++) {
                        if (res.data_array[i].SCXID == SCXID) {
                            this.SSSCX = res.data_array[i].SCXMC;
                        }
                    }
                }
            });

            this.getCwsb();
        },
        // 手动移除列表的某一个图片
        beforeRemove(index, lists) {
            let WJID = this.fileArr[index].WJID;
            deletefile(WJID).then((res) => {
                this.getCwsb();
            });
        },
        getCwsb() {
            let { orgid } = this.enterpriseInfo;

            let { SBID } = this.sbInfo;
            let { SCXID } = this.sbInfo;
            // 查询一条治污设备
            getCwsb({ ORGID: orgid, SBID: SBID, SCXID: SCXID }).then((res) => {
                if (res.data_array && res.data_array.length > 0) {
                    let data = res.data_array[0];
                    this.SBWZ = `${data.JD} ~ ${data.WD}`;
                    this.model = res.data_array[0];
                    let HYFL = res.data_array[0].HYFL;
                    getFilelist({
                        YWSJID: res.data_array[0].SBID,
                        LXDMS: 'ZDFJ',
                        ZLXDMS: 'ZDFJLB'
                    }).then((res) => {
                        this.$refs.uUpload.lists = [];
                        this.fileArr = [];
                        if (res.length > 0) {
                            let file = res[0].zlxList[0].fileList;

                            for (let i = 0; i < file.length; i++) {
                                this.fileArr.push(file[i]);
                                this.$refs.uUpload.lists.push({
                                    file: {
                                        path: DOWNLOAD_URLZDY + file[i].WJID
                                    },
                                    url: DOWNLOAD_URLZDY + file[i].WJID
                                });
                            }
                        }
                    });
                    getGgdmz({ DMJBH: 'CWSB_HYFL', FDM: 'CWSB_HYFL' }).then(
                        (r) => {
                            if (r.data_array && r.data_array.length > 0) {
                                let HYFLDM = '';
                                for (let i = 0; i < r.data_array.length; i++) {
                                    if (r.data_array[i].DMNR == HYFL) {
                                        HYFLDM = r.data_array[i].DM;
                                    }
                                }
                                getGgdmz({
                                    DMJBH: 'CWSB_HYFL',
                                    FDM: HYFLDM
                                }).then((res) => {
                                    if (
                                        res.data_array &&
                                        res.data_array.length > 0
                                    ) {
                                        this.sblxList = res.data_array;
                                    }
                                });
                            }
                        }
                    );
                }
            });
        },
        // 产污设备组织方式 u-select @confirm 事件
        getHylx(v) {
            this.model.HYFL = v[0].label;
            this.model.HYFL_SBLX = '';
            getGgdmz({ DMJBH: 'CWSB_HYFL', FDM: v[0].value }).then((res) => {
                if (res.data_array && res.data_array.length >= 0) {
                    this.sblxList = res.data_array;
                }
            });
        },
        // 治污设备组织方式 u-select @confirm 事件
        getSblx(v) {
            this.model.HYFL_SBLX = v[0].label;
        },
        selectSblx() {
            if (!this.model.HYFL) {
                uni.showToast({
                    title: '请先选择工艺类型'
                });
                return;
            }
            this.sblxShow = true;
        },
        getScx(v) {
            this.model.SCXID = v[0].value;
            this.SSSCX = v[0].label;
        },
        confirm() {
            this.zywrShow = false;
            this.model.ZYWR = this.selectWrwList.join(',');
        },
        // 污染物  事件
        selectWrw(wrw) {
            let index = this.selectWrwList.findIndex((item) => {
                return item == wrw.DM;
            });
            if (index != -1) {
                this.selectWrwList.splice(index, 1);
            } else {
                this.selectWrwList.push(wrw.DM);
            }
        },
        cancel() {
            this.zywrShow = false;
            this.selectWrwList = [];
        },

        getJwd() {
            uni.navigateTo({
                url: '../map'
            });
        },

        scanCode() {
            uni.scanCode({
                scanType: ['qrCode'],
                success: (res) => {
                    this.model.IMEI = res.result.split(';')[0];
                },
                fail: (err) => {
                    uni.showToast({
                        title: '未识别到二维码！',
                        icon: 'none'
                    });
                }
            });
        },
        change(v) {
            this.dataType = v;
        },
        getMore() {},

        save() {
            this.model.ORGID = this.enterpriseInfo.orgid;
            this.model.XGR = this.enterpriseInfo.name;
            this.model.SBID = this.sbInfo.SBID;
            this.model.SCXID = this.sbInfo.SCXID;
            this.$refs.uForm.validate((valid) => {
                if (valid) {
                    updateCwsb(this.model).then((res) => {
                        uni.showToast({
                            title: '修改成功',
                            duration: 2000
                        });
                        // uni.setStorageSync('isAddEquipment',true)
                        setTimeout(() => {
                            uni.navigateTo({
                                url:
                                    './saveCwsbSuccess?cwsbName=' +
                                    this.model.SBMC +
                                    '&info=' +
                                    encodeURIComponent(
                                        JSON.stringify(this.info)
                                    ) +
                                    '&xg=' +
                                    1
                            });
                        }, 2000);
                    });
                }
            });
        },
        back() {
            uni.navigateBack({
                delta: 1
            });
        }
    }
};
</script>

<style scoped>
.pd-tablebx image {
    height: 30rpx;
}
.bznr {
    display: inline-block;
    height: 60rpx;
    line-height: 60rpx;
}
/deep/ .u-form-item--left__content__label {
    font-size: 30rpx;
    color: #2c323f;
}

/deep/ .u-input__textarea {
    border-radius: 8rpx;
    height: 100rpx;
    background-color: rgb(243, 245, 249);
    padding-left: 10rpx;
}
/deep/ .uni-textarea-wrapper {
    height: 100% !important;
}
/deep/ .u-list-item {
    margin: 0;
}
/deep/ .uni-input-placeholder {
    padding: 0 20rpx 0 0;
    /* text-align: right; */
    font-size: 26rpx;
}
.listWarp {
    width: 100%;
    height: 100%;
    overflow-y: scroll;
    padding-top: 80rpx;
}
.listWarp p {
    width: 100%;
    padding: 20rpx;
    text-align: center;
    border-bottom: 1px solid #efefef;
}
.opration {
    padding: 20rpx;
    display: flex;
    justify-content: space-between;
    position: absolute;
    width: 100%;

    background-color: #fff;
}
.confirm {
    color: rgb(60, 170, 255);
}
.on {
    color: rgb(60, 170, 255);
}
/deep/ .u-icon {
    padding: 0 0 0 10rpx;
}
.tip-title {
    text-align: center;
    border-bottom: 1px solid #efefef;
    padding: 20rpx;
    font-weight: 600;
}
.tip-content {
    padding: 20rpx;
}
.tip-content p {
    /* padding: 20rpx 0; */
    font-size: 26rpx;

    color: #666;
}
.tip-know {
    position: absolute;
    bottom: 0;
    padding: 20rpx;
    text-align: center;
    border-top: 1px solid #efefef;
    color: rgb(60, 170, 255);
    width: 100%;
}
.pd-tit1 {
    border-radius: 18rpx 18rpx 0 0;
}
/deep/ .u-form {
    border-radius: 0 0 18rpx 18rpx;
}
</style>
