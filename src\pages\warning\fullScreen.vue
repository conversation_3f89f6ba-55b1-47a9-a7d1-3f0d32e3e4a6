<template>

	<div class="pd-landscape">
		<section class="main">
			<div class="inner" style="padding-bottom: 0;">
				<div class="gap"></div>
				<div class="pd-row aic">
					<span class="pd-tit2" style="margin-left: 3%;">振动趋势</span>
					<span class="pd-tit2" style="margin-left: 5%;">预警时段：</span>
					<p class="zy-time">
						<c-datetime-picker @change="timeChange" @onShow="onPickerShow" v-model="datetimerange"
							:start="start" :end="end" type="datetimerange" rangeSeparator="~" :hide-second="true"
							:clear-icon="false" :border="false" />
					</p>
				</div>
				<div class="gap"></div>
				<div class="gap"></div>
				<view style="width: 100vw;height:60vh">
					<!-- #ifdef APP-PLUS || H5 -->
					<view :prop="option" :change:prop="echarts.updateEcharts" id="echarts" class="echarts"
						ref="echarts"></view>
					<!-- #endif -->
					<!-- #ifndef APP-PLUS || H5 -->
					<view>非 APP、H5 环境不支持</view>
					<!-- #endif -->
				</view>
				<div class="ic-full" @click="back"></div>
			</div>
		</section>
	</div>

</template>

<script>
	import {
		getInfo
	} from '../../api/iot/warning.js';
	export default {
		data() {
			return {
				IMEI: '',
				SBMC: '',
				SBID: '',
				markLine: '',
				datetimerange: [
					this.$dayjs()
					.subtract(12, 'hour')
					.format('YYYY-MM-DD HH:mm'),
					this.$dayjs().format('YYYY-MM-DD HH:mm')
				],
				// 可选时间范围
				start: this.$dayjs()
					.subtract(24, 'month')
					.format('YYYY-MM-DD HH:mm'),
				end: this.$dayjs().format('YYYY-MM-DD HH:mm'),
				fullScreen: false,
				option: {
					color: [
						// '#409EFF'
						'#E88511', '#1C8B14', '#14AACF', '#A825C9', '#781929', '#2C8B14'
					],
					legend: {
						show: true,
						data: [],

					},
					grid: {
						right: '100'
					},
					tooltip: {
						show: true,
						trigger: 'axis',
					},
					dataZoom: [{
						show: true,
						realtime: true,
						start: 0,
						end: 100,
					}],
					xAxis: [{
						name: '检查时间',
						scale: true,
						type: 'time',
						axisLabel: {}
					}],
					yAxis: [{
						type: 'value',
						name: '能　     \n量　　 ',
						nameLocation: 'middle',
						nameGap: 10,
						nameRotate: 0,
						nameTextStyle: {
							fontSize: 14
						},
						//默认以千分位显示，不想用的可以在这加一段
						axisLabel: {
							//调整左侧Y轴刻度， 直接按对应数据显示
							show: true,
							showMinLabel: true,
							showMaxLabel: true,
							formatter: function(value) {
								return value;
							}
						}
					}, ],
					series: []
				}
			};
		},
		mounted() {},
		onLoad(options) {
			// #ifdef APP-PLUS
			plus.screen.lockOrientation('landscape-primary'); //横屏
			// #endif
			this.YJID = options.YJID;
			// this.SBID = options.sbid;
			// this.SBMC = options.sbmc;
			// this.markLine = options.markLine;
			this.datetimerange = options.daterange.split(",")
			this.initChartData();
		},
		onBackPress(e) {
			// 退出页面时解除横屏
			// #ifdef APP-PLUS
			if (e.from == 'backbutton') {
				plus.screen.lockOrientation('portrait-primary'); //
				setTimeout(() => {
					uni.navigateTo({
						url: `../realtime/whitePage`
					});
				}, 200);
				return true;
			}
			// #endif
		},
		methods: {
			initChartData() {
				getInfo({
					YJID: this.YJID,
					startT: this.datetimerange[0],
					endT: this.datetimerange[1]
				}).then(res => {
					this.initChart(res.data.sbxxList);
				});
			},
			initChart(data, sbxx) {
				// 时间轴
				this.option.series = [];

				//计算X轴数据
				let xAxis = new Set();
				let legend = [];
				for (var i = 0; i < data.length; i++) {
					const sb = data[i];
					legend.push(sb.SBMC);
					// for (let j = 0; j < chartData.length; j++) {
					// 	const row = chartData[0];
					// 	xAxis.add(this.$dayjs(row.JCSJ).format("YYYY-MM-DD HH:mm"))
					// }
				}
				// 

				let colors = [];
				//计算y轴数据
				let series = [];
				let max = 0;
				let markLines = [];
				for (var i = 0; i < data.length; i++) {
					const sb = data[i];
					let markline = sb.YZ.split('#')[0]
					markLines.push(parseInt(markline))
					console.log("域值", markline);
					const chartData = sb.lssjList;
					let sery = {
						// itemStyle: {
						// 	normal: {
						// 		lineStyle: {
						// 			color: '#2C8B14'
						// 		}
						// 	}
						// },
						yAxisIndex: 0,
						xAxisIndex: 0,
						markLine: {
							symbol: ['circle', 'none'],
							data: [{
								silent: false,
								lineStyle: {
									type: 'dashed',
								},
								label: {
									position: 'end'
								},
								yAxis: markline
							}],
							label: {
								show: true,
								formatter: `启停阈值(${markline})`,
								color: 'inherit'
							}
						},
						data: [],
						name: sb.SBMC,
						type: 'line'
					}
					for (let j = 0; j < chartData.length; j++) {
						const row = chartData[j];
						if (parseInt(row.NL) > parseInt(max)) {
							max = row.NL;
						}
						sery.data.push([this.$dayjs(row.JCSJ).valueOf(), row.NL, this.$dayjs(row.JCSJ).format(
							"YYYY-MM-DD HH:mm")]);
					}

					series.push(sery);
				}

				let markData = Math.max(...markLines);
				markData = markData > max ? markData : max;
				this.option.yAxis[0].max = parseInt(markData) + 10;
				this.option.legend.data = legend;

				this.option.series = series;
				console.log(this.option);
			},
			timeChange(v) {
				console.log(v, 777);
				this.initChartData();
			},
			onPickerShow() {
				let self = this;
				setTimeout(function() {
					let queryDom = uni.createSelectorQuery().in(self) // 使用api并绑定当前组件
					queryDom
						.select('.uni-calendar__content')
						.boundingClientRect((res) => {
							console.log(res)



						})
						.exec()
				}, 10);
				this.$nextTick(() => {

				})
			},
			back() {
				// 退出页面时解除横屏
				// #ifdef APP-PLUS
				plus.screen.lockOrientation('portrait-primary'); //
				setTimeout(() => {
					uni.navigateTo({
						url: '../realtime/whitePage'
					});
				}, 200);
				// #endif
			}
		}
	};
</script>
<script module="echarts" lang="renderjs">
	let myChart;
	export default {
		data() {
			return {
				chartw: "",
				charth: '',
				flag: false
			}
		},
		onLoad() {


		},
		mounted() {

			if (typeof window.echarts === 'function') {
				this.initEcharts()
			} else {
				// 动态引入较大类库避免影响页面展示
				const script = document.createElement('script')
				// view 层的页面运行在 www 根目录，其相对路径相对于 www 计算
				script.src = 'static/echarts.js'
				script.onload = this.initEcharts.bind(this)
				document.head.appendChild(script)
			}
		},
		methods: {
			initEcharts() {
				myChart = echarts.init(document.getElementById('echarts'))
				// 观测更新的数据在 view 层可以直接访问到
				var series = this.option.series;
				var colors = this.option.color;
					let self = this;
				this.option.xAxis[0].axisLabel.formatter = function(value) {
					var t_date = new Date(value);
					let date = self.format(t_date, 'MM-dd');
					let time = self.format(t_date, 'hh:mm');
					return time + '\n' + date;
				}
			
				this.option.tooltip.formatter = function(params) {

					var res = "";

					for (var i = 0; i < series.length; i++) {
						let marker =
							`<span style=\"display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${colors[i]};\"></span>`;
						console.log(series[i].data, params);
						var timeStr = series[i].data[params[0].dataIndex][2];
						if (i != 0) {
							res += "<br>"
						}
						res += marker + timeStr + " " + series[i].name + "：" + series[i].data[params[0].dataIndex][1]
					}
					return res;
				}
				myChart && myChart.setOption(this.option)
			},
			updateEcharts(newValue, oldValue, ownerInstance, instance) {
				// 监听 service 层数据变更
				// #ifdef APP-PLUS
				document.getElementById('echarts').style.width = plus.screen.resolutionWidth + 'px'
				document.getElementById('echarts').style.height = (plus.screen.resolutionHeight - 120) + 'px'
				// #endif
				console.log(this.option)
				myChart && myChart.setOption(newValue)
				myChart.resize()
			},
			
			format(date, fmt) { 
			     var o = { 
			        "M+" : date.getMonth()+1,                 //月份 
			        "d+" : date.getDate(),                    //日 
			        "h+" : date.getHours(),                   //小时 
			        "m+" : date.getMinutes(),                 //分 
			        "s+" : date.getSeconds(),                 //秒 
			        "q+" : Math.floor((date.getMonth()+3)/3), //季度 
			        "S"  : date.getMilliseconds()             //毫秒 
			    }; 
			    if(/(y+)/.test(fmt)) {
			            fmt=fmt.replace(RegExp.$1, (date.getFullYear()+"").substr(4 - RegExp.$1.length)); 
			    }
			     for(var k in o) {
			        if(new RegExp("("+ k +")").test(fmt)){
			             fmt = fmt.replace(RegExp.$1, (RegExp.$1.length==1) ? (o[k]) : (("00"+ o[k]).substr((""+ o[k]).length)));
			         }
			     }
			    return fmt; 
				}
		}
	}
</script>
<style scoped>
	html { -webkit-tap-highlight-color: transparent; height: 100%; }
	body { -webkit-backface-visibility: hidden; height: 100%;}
	.inner {
		padding-top: 0;
	}

	/deep/ .uni-date-x {
		background-color: unset;
	}

	.ic-full {
		width: 84rpx;
		height: 84rpx;
		background: url(../../static/app/images/ic-back2.png) 0 0 no-repeat;
		background-size: 100%;
	}
	
	/deep/ .uni-input-input{
		color: #4874ff;
	}
</style>
