<!-- @format -->

<template>
    <Page
        :padding="false"
        :mainStyle="mainStyle"
        @layoutAware="onFixedContentHeight"
    >
        <template v-slot:bar>
            <NaviBar title="随手拍">
                <template v-slot:option>
                    <!-- <span @click="showList">列表</span> -->
                </template>
            </NaviBar>
        </template>
        <view
            class="flex-column-layout"
            style="height: 100%; justify-content: flex-start"
            id="formParent"
        >
            <template-form
                ref="templateForm"
                :parentHeight="height"
                :editable="editable"
                :template="template"
                :form-data="formData"
            />
            <!-- <p-button @click.native="onMenuClick" name="保存"></p-button> -->
            <view class="pd-btn">
                <div class="pd-botbtn1" @click="saveForm">保存</div>
            </view>
        </view>
    </Page>
</template>

<script>
import Page from '@/pages/component/Page.vue';
import NaviBar from '@/pages/component/NaviBar.vue';
import {
    dynamicformsave,
    queryTaskFormTemplate,
    queryRecordData
} from '@/api/record.js';
import { deepCopyObject } from '@/common/merge.js';
import { guid } from '@/common/uuid.js';
import nextIcon from '@/static/img/navi_next_icon.png';
import onform from '@/api/onsaveForm.js';
import templateForm from '@/pages/form/template-form.vue';
import styleUtil from '@/common/style.js';

const COMFIRM_TEMPLATEID = '2021120709330056e8f436071043f888d89383077eeaf4';

export default {
    components: {
        templateForm,
        Page,
        NaviBar
    },

    data() {
        let defaultRecordId = guid();
        // console.log(guid());
        defaultRecordId = '06278128-e15f-4dff-aed6-6ace310ba19f';
        return {
            nextIcon,
            editable: true,
            recordId: defaultRecordId,
            defaultInfo: '选择污染源',
            height: 600,
            constraints: [], //表单联动规则数组集合
            rwList: [],
            template: {}, //动态表单模板
            formData: {} //动态表单数据
        };
    },

    computed: {
        mainStyle() {
            return {
                'background-color': '#ffffff'
            };
        }
    },

    mounted() {
        let self = this;
        this.$store.state.mrzList = [];
        this.$store.state.verifyList = [];
        let templateHeight = 80;
        this.height = 800;
        styleUtil.getNodeLayout(this, '#formParent').then((layout) => {
            if (layout.height > 800) {
                templateHeight = templateHeight * 2;
            }
            self.height = layout.height - templateHeight;
            self.getList();
        });
    },

    methods: {
        showList() {
            console.log('showlist');
            uni.navigateTo({
                url: `/pages/book/dyna-list-page?listId=1633782110981022302720&title=随手拍&templateId=${COMFIRM_TEMPLATEID}`
            });
        },

        onFixedContentHeight(layout) {
            this.height = layout.height;
        },

        //获取表单数据及污染源的数据
        getList() {
            queryTaskFormTemplate('', COMFIRM_TEMPLATEID).then((res) => {
                this.template = res;
                queryRecordData(COMFIRM_TEMPLATEID, this.recordId).then((r) => {
                    if (r) {
                        this.formData = r;
                    }
                });
            });
        },

        //保存表单
        saveForm() {
            // 表单验证，首先要获取距离顶部的高度，在判断必填项是否填写
            let self = this;
            styleUtil.getNodeLayout(self, '#formParent').then((layout) => {
                let dataType = onform.saveTemplateForm(
                    self.formData,
                    self,
                    layout.top
                );
                if (dataType === true) {
                    self.postSaveForm();
                }
            });
        },

        /**
         * @description: 保存笔录，并发起任务。
         * @param { String } SFLA 是否立案，默认写死为0，不立案
         * @param { String } SFQS 是否签收，默认写死为0，不签收
         * @param { Array } name 将选择的检查人单独拆分出来传参
         * @return {*}
         */
        postSaveForm() {
            let self = this;
            let from = {
                client_type: 'mobile_web',
                record_id: self.recordId,
                service: 'DYNAMICFORM_SAVE',
                template_id: COMFIRM_TEMPLATEID,
                user_id: uni.getStorageSync('user_info').yhid || 'SYSTEM'
            };
            let tableName = self.template['templateTable'];
            let data = this.$store.state.formData || this.formData;
            data = deepCopyObject(data);
            from[tableName] = data;
            dynamicformsave(from)
                .then((res) => {
                    uni.showToast({
                        title: '保存成功',
                        duration: 2000,
                        icon: 'none'
                    });
                    setTimeout(() => {
                        console.log('success');
                    }, 1200);
                })

                .catch((res) => {
                    uni.showToast({
                        title: res.error_msg || '保存失败，请重新保存',
                        duration: 2000,
                        icon: 'none'
                    });
                });
        }
    }
};
</script>

<style scoped>
.template-form-layout {
    border-radius: 5px;
    padding: 10px;
    width: calc(100% - 20px);
    background-color: #fff;
}

.companyList {
    min-height: 60px;
    width: calc(100% - 20px);
    background-color: #fff;
    font-size: 34rpx;
    color: #999;
    padding: 10rpx 22rpx 10rpx 22rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.form-content-layout {
    width: 100%;
    height: 100%;
}

.nextImg {
    width: 34rpx;
    height: 34rpx;
}

.record-fragment {
    height: 100%vh;
}

.form-menu-layout {
    height: 56px;
    background-color: #fff;
    box-shadow: 0 -1px 1px 0 #ccc;
}
</style>
