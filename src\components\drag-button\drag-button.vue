<template>
    <view>
        <view
            id="_drag_button"
            class="drag"
            :style="'left: ' + left + 'px; top:' + top + 'px;'"
            @touchstart="touchstart"
            @touchmove.stop.prevent="touchmove"
            @touchend="touchend"
            @click.stop.prevent="click"
            :class="{ transition: isDock && !isMove }"
        >
            <!-- <text>尝试</text> -->
            <uni-fab
                :content="content"
                :febPosition="febPosition"
                :horizontal="febPosition"
                @trigger="trigger"
            ></uni-fab>
        </view>
    </view>
</template>

<script>
import caseIcon from '@/static/images/case.png';
import checkIcon from '@/static/images/check.png';
import bianqianIcon from '@/static/images/bianqian.png';
export default {
    name: 'drag-button',
    props: {
        isDock: {
            type: Boolean,
            default: false
        },
        existTabBar: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            content: [
                {
                    text: '检查要点',
                    active: false,
                    iconPath: checkIcon
                },
                {
                    text: '典型案例',
                    active: false,
                    iconPath: caseIcon
                },
                {
                    text: '便签',
                    active: false,
                    iconPath: bianqianIcon
                }
            ],
            febPosition: 'left',
            top: 0,
            left: 0,
            width: 0,
            height: 0,
            offsetWidth: 0,
            offsetHeight: 0,
            windowWidth: 0,
            windowHeight: 0,
            isMove: true,
            edge: 10,
            text: '按钮'
        };
    },
    mounted() {
        let styleData = uni.getStorageSync('recordHelper_Style');

        const sys = uni.getSystemInfoSync();
        this.windowWidth = sys.windowWidth;
        this.windowHeight = sys.windowHeight;
        // #ifdef APP-PLUS
        this.existTabBar && (this.windowHeight -= 50);
        // #endif
        if (sys.windowTop) {
            this.windowHeight += sys.windowTop;
        }
        const query = uni.createSelectorQuery().in(this);
        query
            .select('#_drag_button')
            .boundingClientRect(data => {
                this.width = data.width;
                this.height = data.height;
                this.offsetWidth = data.width / 2;
                this.offsetHeight = data.height / 2;
                if (styleData) {
                    this.top = styleData.dragTop;
                    this.left = styleData.dragLeft;
                } else {
                    this.left = this.windowWidth - this.width - this.edge;
                    this.top = this.windowHeight - this.height - this.edge;
                }
            })
            .exec();
    },
    methods: {
        click() {
            this.$emit('btnClick');
        },
        touchstart(e) {
            this.$emit('btnTouchstart');
        },
        touchmove(e) {
            // 单指触摸
            if (e.touches.length !== 1) {
                return false;
            }

            this.isMove = true;

            this.left = e.touches[0].clientX - this.offsetWidth;

            let clientY = e.touches[0].clientY - this.offsetHeight;
            // #ifdef H5
            clientY += this.height;
            // #endif
            let edgeBottom = this.windowHeight - this.height - this.edge;

            // 上下触及边界
            if (clientY < this.edge) {
                this.top = this.edge;
            } else if (clientY > edgeBottom) {
                this.top = edgeBottom;
            } else {
                this.top = clientY;
            }
        },
        touchend(e) {
            if (this.isDock) {
                let edgeRigth = this.windowWidth - this.width - this.edge;

                if (this.left < this.windowWidth / 2 - this.offsetWidth) {
                    this.left = this.edge;
                    this.febPosition = 'left';
                } else {
                    this.left = edgeRigth;
                    this.febPosition = 'right';
                }
                let dragStyle = {};
                dragStyle.dragLeft = this.left;
                dragStyle.dragTop = this.top;
                uni.setStorageSync('recordHelper_Style', dragStyle);
            }

            this.isMove = false;

            this.$emit('btnTouchend');
        },

        //
        trigger(data) {
            switch (data.index) {
                case 0:
                    uni.navigateTo({
                        url: `/pages/record/record-check-point`
                    });
                    break;
                case 1:
                    uni.navigateTo({
                        url: `/pages/record/record-Intelligent-judgment`
                    });
                    break;
                case 2:
                    uni.navigateTo({
                        url: `/pages/record/record-bookMark-add` //这里要直接去写便签，不展示列表
                    });
                    break;
                default:
                    break;
            }
        }
    }
};
</script>

<style lang="scss">
.drag {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.5);
    box-shadow: 0 0 6upx rgba(0, 0, 0, 0.4);
    color: $uni-text-color-inverse;
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    // font-size: $uni-font-size-sm;
    position: fixed;
    z-index: 999999;

    &.transition {
        transition: left 0.3s ease, top 0.3s ease;
    }
}

.drag-main {
    position: relative;
}
</style>
