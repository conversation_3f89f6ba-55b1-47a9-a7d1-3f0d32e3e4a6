@charset "utf-8";

/*common*/

.gap {
    height: 27.77775rpx;
}

.mask {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, .4);
    z-index: 1000;
    display: none;
}


/*page*/

.header {
    background: #4874ff;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    z-index: 999;
    height: 116.6667rpx;
}

.title {
    text-align: center;
    font-size: 34.722225rpx;
    color: #fff;
    line-height: 116.6667rpx;
}

.main {
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    height: 100%;
}

.inner {
    padding: 116.6667rpx 0;
}