<template>
	<!-- 实时监控 -->
	 <div class="tabs1-con">
		<div class="qiye-board">
			<div class="qiye-tabs2">
				<div class="item" :class="currentscx==item.SCXID?'cur':''"  @click="changeProductLine(item)" v-for="(item, index) in scxlist" :key="index">
					<p>{{ item.SCXMC }}</p>
					<image v-if="item.ISYC == 'true'" src="@/static/app/enterpriseDetail/images/qiye-lamp.png" class="lamp">
				</div>
			</div>
			<div class="gap"></div>
			<div class="zy-line ac jb">

				<div class="qiye-tabs3">
					<span :class="curPic==item.value?'cur':''"  @click="changePic(item)" v-for="(item, index) in picTabArr" :key="index">{{item.name}}</span>
				</div>

				<div class="qiye-riqi" @click="open">
					<u-icon name="calendar" color="#2979ff" size="16"></u-icon>
					{{dateStr}}
				</div>
			</div>
			<div class="gap"></div>
	        <StatePic ref="statePic"  v-show="curPic == 'state'" :dateStr="dateStr" :currentscx="currentscx"></StatePic>
			<TrendPic ref="trendPic"  v-show="curPic == 'trend'" :dateStr="dateStr" :currentscx="currentscx"></TrendPic>

		</div>
		<!-- 无每日时间状态显示 -->
		<u-calendar v-model="show" :mode="mode" @change="changeDate"></u-calendar>
	 </div>

</template>

<script>
	import {
		sbsxzt,
		qyztrl,
		getScrl,
		getDwzb
	} from '../../../api/iot/runningData.js';
	import StatePic from './components/StatePic.vue';
	import TrendPic from './components/TrendPic.vue';
	export default {
	    components: {
			StatePic,
			TrendPic
	    },
		props:{
			enterpriseData:{
				type:Object,
				default:()=>{}
			},
			enterpriseState:{
				type:String,
				default:''
			},

		},
		watch: {
			wrybh: {
				handler: function(nv) {
					this.WRYBH = nv;
					//this.initScxInfo();
				},
				immediate: true
			},
			enterpriseData:{
				handler: function(newVal) {
					console.log('newVal',newVal.scxList);
					this.scxlist = newVal.scxList;
					let scxLen = newVal.scxList.length;
					if (scxLen) {
						this.currentscx = newVal.scxList[0].SCXID;
						this.currentscxname = newVal.scxList[0].SCXMC;
					}

				},
				deep:true,
			}

		},
	    data() {
	        return {
			    scxlist:[],//生产线list
				currentscx:'',
				picTabArr:[
					{
						name:'状态图',
						value:'state'
					},
					{
						name:'趋势图',
						value:'trend'
					},
				],
				curPic:'state',
				dateStr: this.$dayjs().format('YYYY-MM-DD'),
				show:false,
				mode: 'date',
				wrybh:'',


	        };
	    },
	    onLoad(option) {
	       let userinfo = uni.getStorageSync('user_info');
			this.xzqhdm = userinfo.orgid;
			this.wrybh = option.WRYBH;
			this.initInfo();
	    },
	    created() {},
	    methods: {
			//切换生产线
			changeProductLine(val){
				this.currentscx = val.SCXID;
				this.currentscxname = val.SCXMC;
			},
			//切换图表
			changePic(item){
				this.curPic = item.value;
				switch (item.value){
					case 'state':
					this.$refs.statePic.gettjt();
					break;
					case 'trend':
					this.$refs.trendPic.gettjt();
					break;
					default:
					break;
				}
			},
			open() {
				this.show = true;
			},
			changeDate(e){
				this.dateStr = `${e.year}-${e.month<10?'0'+e.month:e.month}-${e.day<10?'0'+e.day:e.day}`;
			},
	    }
	};
</script>



<style>
	.qiye-board{
		position: relative;
	}
	.qiye-tabs3{
	    display: flex;
	}
	.qiye-tabs3 span{
	    font-size: 30rpx;
	    color: #666;
	    line-height: 10rpx;
	    padding: 10rpx;
	}
	.qiye-tabs3 span:first-child{
	    padding-left: 0;
	}
	.qiye-tabs3 span+span{
	    border-left: 4rpx solid #ddd;
	}
	.qiye-tabs3 span.cur{
	    color: #4874ff;
	}
</style>
