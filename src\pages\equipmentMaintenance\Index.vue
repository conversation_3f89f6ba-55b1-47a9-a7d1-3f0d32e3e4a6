<!-- @format -->

<!--  -->
<template>
    <div class="yy0706-topbg1">
        <div class="yy0706-line1" style="border-bottom: none">
            <input
                type="text"
                v-model="keyword"
                class="inpsear1"
                placeholder="输入关键字查询"
                @input="debounceInputKeyword"
            />
            <image
                @click="routeTo('add')"
                src="~@/static/equipmentMaintenance/images/yy0706-topic.png"
                class="yy0706-topic"
            />
        </div>
        <ul class="yy0706-navtab1">
            <li @click="changeState('')" :class="{ on: showState == '' }">
                全部
            </li>
            <li @click="changeState('0')" :class="{ on: showState == '0' }">
                处理中
            </li>
            <li @click="changeState('1')" :class="{ on: showState == '1' }">
                已完成
            </li>
        </ul>
        <div class="gap"></div>
        <div class="gap"></div>
        <div class="nodatabox" v-if="!tableData.length">
            <image
                style="width: 200px"
                mode="widthFix"
                src="@/static/app/workbench/images/nodata.png"
                alt=""
            />
            <p>暂无数据</p>
        </div>
        <scroll-view
            v-show="tableData.length"
            scroll-y
            class="yy0706cont-wrap"
            @scrolltolower="getMore"
        >
            <div
                v-for="(item, index) in tableData"
                :key="index"
                @click="routeTo('edit', item)"
            >
                <div class="zd-mod">
                    <div class="flx1 ac jb zd-hd yy0706-hd">
                        <h1 class="f1">
                            {{ item.jlbh || '-' }}
                            <!-- <img
                                v-if="item.zt == 0"
                                @click.stop="item.showTip = !item.showTip"
                                src="~@/static/equipmentMaintenance/images/yy0706-tip.png"
                                class="yy0706-tip"
                            /> -->
                            <p class="yy0706-tipbox" v-show="item.showTip">
                                {{ item.BZ || '-' }}
                            </p>
                        </h1>
                        <span
                            :class="[
                                'yydeal-btn',
                                item.zt == 0 ? 'deal' : 'done'
                            ]"
                            >{{ item.zt == 0 ? '处理中' : '已完成' }}</span
                        >
                    </div>
                    <div class="zd-bd">
                        <div class="gap"></div>
                        <table class="zy-table1 yy0706-table1">
                            <colgroup>
                                <col width="25%" />
                                <col width="" />
                                <col />
                            </colgroup>
                            <tbody>
                                <tr>
                                    <td class="color9">企业名称：</td>
                                    <td>{{ item.wrymc || '-' }}</td>
                                </tr>
                                <tr>
                                    <td class="color9">运维时间：</td>
                                    <td>{{ item.ywsj || '-' }}</td>
                                </tr>
                                <tr>
                                    <td class="color9">运维人员：</td>
                                    <td>{{ item.ywr || '-' }}</td>
                                </tr>
                                <tr>
                                    <td class="color9">运维明细：</td>
                                    <td>
                                        <i style="color: #2e7ff9">
                                            {{ item.mx_count + '' || '-' }}</i
                                        >
                                        条
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="gap"></div>
            </div>
        </scroll-view>
    </div>
</template>

<script>
import { debounce } from 'lodash';
import { getTableList } from '@/api/iot/equipmentMaintenance';
export default {
    //import引入的组件需要注入到对象中才能使用
    name: 'equipmentMaintenanceIndex',
    components: {},
    data() {
        return {
            tableData: [],
            showState: '',
            keyword: '',
            pages: {
                page: 1,
                size: 10,
                total: 0,
                isLastPage: false
            }
        };
    },
    computed: {},
    // watch: {
    //     showState(v) {}
    // },
    created() {
        this.debounceInputKeyword = debounce(this.inputKeyword, 1500);
    },
    onShow() {},
    onLoad() {},
    mounted() {
        this.freshData();
    },
    methods: {
        //搜索
        inputKeyword() {
            this.freshData(this.showState);
        },
        //tab切换
        changeState(v) {
            this.showState = v;
            this.freshData();
        },
        getData(state) {
            if (state) {
                this.showState = state;
            }
            getTableList({
                pageNum: this.pages.page,
                pageSize: this.pages.size,
                ZT: this.showState,
                ALLSEARCH: this.keyword
            }).then((r) => {
                this.tableData = this.tableData.concat(
                    r.data.list.map((a) => {
                        return { ...a, showTip: false };
                    })
                );
                this.pages.total = r.data.total;
                this.pages.isLastPage = r.data.isLastPage;
            });
        },
        freshData(state) {
            this.tableData = [];
            console.log('刷新');
            this.pages.page = 1;
            this.getData(state);
        },
        getMore() {
            this.pages.page += 1;
            if (!this.pages.isLastPage) {
                this.getData();
            }
        },
        routeTo(type, obj) {
            let url = '/pages/equipmentMaintenance/EditItem';
            switch (type) {
                case 'add':
                    url += '?type=add';
                    break;
                case 'edit':
                    url += '?type=edit&ywid=' + obj.ywid + '&zt=' + obj.zt;
                    break;

                default:
                    break;
            }
            uni.navigateTo({
                url
            });
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep .uni-input-placeholder,
.uni-textarea-placeholder {
    color: #fff;
}
// .yy0706cont-wrap {
//     padding-bottom: 100rpx;
// }
.yy0706-topbg1 {
    overflow-y: hidden;
}
.nodatabox {
    height: calc(100% - 160rpx);
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 0;
    flex-direction: column;
    margin: 0;
}
</style>
