<template>
	<div>
		<dl class="pd-dlbx1">
			<dt>上报状态</dt>
			<dd>展示最新上传的三组状态数据，其中信号功率、信号总功率、信噪比、信号质量的数据若小于其最小范围值会标红；</dd>
		</dl>
		<div v-for="(item,index) in list" :key='index'>
			<div class="gap"></div>
			<ul class="pd-ulbx1" >
					<div class="gap"></div>
				<sup>栏目{{index+1}}</sup>
				<li>
					<em>检测时间</em>
					<i>{{item.JCSJ||'-'}}</i>
				</li>
				<li>
					<em>上报时间</em>
					<i>{{item.SBSJ||'-'}}</i>
				</li>
				<li>
					<em>设备状态</em>
					<i :style="{color:item.SBZT=='正常'?'#22d866':item.SBZT=='电压低'?'#fffd5d':'#ef5758'}">{{item.SBZT||''}}</i>
				</li>
				<li>
					<em>电池电压</em>
					<i>{{item.VOL||'-'}}</i>
				</li>
				<li>
					<em>信号功率</em>
					<i>{{item.SP||'-'}}</i>
					<div class='icon'><u-icon name="question-circle" color="#909399" size="32" @click="showXhglTips"></u-icon></div>
				</li>
				<li>
					<em>信号总功率</em>
					<i>{{item.TP||'-'}}</i>
					<div class='icon'><u-icon name="question-circle" color="#909399" size="32" @click="showXhzglTips"></u-icon></div>
				</li>
				<li>
					<em>信噪比</em>
					<i>{{item.SINR||'-'}}</i>
					<div class='icon'><u-icon name="question-circle" color="#909399" size="32" @click="showXzbTips"></u-icon></div>
				</li>
				<li>
					<em>信号质量</em>
					<i>{{item.RSRQ||'-'}}</i>
					<div class='icon'><u-icon name="question-circle" color="#909399" size="32" @click="showXhzlTips"></u-icon></div>
				</li>
				<li>
					<em>小区基站</em>
					<i>{{item.PCI||'-'}}</i>
				</li>
				<li>
					<em>峭度</em>
					<i>{{item.QD||'-'}}</i>
				</li>
				<li>
					<em>峰值</em>
					<i>{{item.FZ||'-'}}</i>
				</li>
				<li>
					<em>裕度</em>
					<i>{{item.YD||'-'}}</i>
				</li>
				<li>
					<em>波形</em>
					<i>{{item.BX}}</i>
				</li>
			</ul>
		</div>
		
		<u-popup v-model="showXhglTip" mode="center" width="70%" height="30%" border-radius="10">
			<div class="tip-title">信号功率范围值说明</div>
			<div class="tip-content">
				<p>若小于最小范围值：-90，说明信号功率不足；</p>
			</div>
			<div class="tip-know" @click="showXhglTips()">我知道了</div>
		</u-popup>
		
		<u-popup v-model="showXhzglTip" mode="center" width="70%" height="30%" border-radius="10">
			<div class="tip-title">信号总功率范围值说明</div>
			<div class="tip-content">
				<p>若小于最小范围值：-85，说明信号总功率不足；</p>
			</div>
			<div class="tip-know" @click="showXhzglTips()">我知道了</div>
		</u-popup>
		<u-popup v-model="showXzbTip" mode="center" width="70%" height="30%" border-radius="10">
			<div class="tip-title">信噪比范围值说明</div>
			<div class="tip-content">
				<p>若小于最小范围值：5，说明信号干扰大；</p>
				
			</div>
			<div class="tip-know" @click="showXzbTips()">我知道了</div>
		</u-popup>
		<u-popup v-model="showXhzlTip" mode="center" width="70%" height="30%" border-radius="10">
			<div class="tip-title">信号质量范围值说明</div>
			<div class="tip-content">
				<p>若小于最小范围值：-10.8，说明信号质量不好；</p>
			</div>
			<div class="tip-know" @click="showXhzlTips()">我知道了</div>
		</u-popup>
	</div>
</template>

<script>
import { getZtxx } from '@/api/iot/enterprise.js';
export default {
	props:{
		info:{
			type:Object,
			default:()=>{
				return{}
			}
		},
		sbInfo:{
			type:Object,
			default:()=>{
				return{}
			}
		}
	},
	data() {
		return {
			showXhglTip: false,
			showXhzglTip: false,
			showXzbTip: false,
			showXhzlTip: false,
			enterpriseInfo:{},
			list:[],
			pageSize:'',
		};
	},
	mounted() {
		this.getZtxx()
	},
	methods:{
		getZtxx(){
			let {IMEI} = this.sbInfo
			getZtxx({IMEI:IMEI,pageSize:this.pageSize}).then(res=>{
				if (res.data_array && res.data_array.length > 0) {
					console.log(res.data_array);
					this.list = res.data_array;
				}
			})
		},
		showXhglTips(){
			this.showXhglTip=!this.showXhglTip
		},
		showXhzglTips(){
			this.showXhzglTip=!this.showXhzglTip
		},
		showXzbTips(){
			this.showXzbTip=!this.showXzbTip
		},
		showXhzlTips(){
			this.showXhzlTip=!this.showXhzlTip
		}
	}
};
</script>

<style scoped>
.pd-dlbx1 {
	background: #fff;
	border-radius: 9px;
	margin: 0;
	padding: 9px;
}
.pd-ulbx1 {
	margin: 0;
}
.pd-ulbx1 li{
	position: relative;
}
.pd-ulbx1 li .icon{
	position: absolute;
	right: 20rpx;
}
.tip-title {
	text-align: center;
	border-bottom: 1px solid #efefef;
	padding: 20rpx;
	font-weight: 600;
}
.tip-content {
	padding: 20rpx;
}
.tip-content p {
	/* padding: 20rpx 0; */
	font-size: 26rpx;

	color: #666;
}
.tip-know {
	position: absolute;
	bottom: 0;
	padding: 20rpx;
	text-align: center;
	border-top: 1px solid #efefef;
	color: rgb(60, 170, 255);
	width: 100%;
}
</style>
