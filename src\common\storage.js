//默认缓存过期时间
const CACHE_EXPIRED_DEFAULT = 60 * 60 * 1000


/**
 * 缓存数据并存储打过期时间标签
 * @param {Object} cacheKey
 * @param {Object} cacheData
 */
export const setStorageWithExpired = (cacheKey, cacheData) => {
	let saveTime = new Date().getTime()
	uni.setStorage({
		key: cacheKey,
		data: {
			time: saveTime,
			cache: cacheData
		}
	})
}

/**
 * 获取在有效期内的缓存数据
 * @param {Object} cacheKey
 */
export const getEffectiveStorage = (cacheKey) => {
	let data = uni.getStorageSync(cacheKey)
	if(data && !isCacheExpired(data)) {
		return data.cache
	}
	return null
}

//判断缓存数据是否已过期
const isCacheExpired = (data) => {
	let now = new Date().getTime()
	let diffTime = now - data.time
	return diffTime - CACHE_EXPIRED_DEFAULT >= 0
}

export default {
	setStorageWithExpired,
	getEffectiveStorage
}

