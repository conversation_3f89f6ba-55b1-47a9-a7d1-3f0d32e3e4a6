<template>
	<body style="background: #f1f1f1;">
		<section class="main">
			<div class="inner">
				<div class="gap"></div>
				<div class="zy-form">

					<div class="item">
						<p class="label">生产线</p>
						<div class="rp">
							<div class="zy-selectBox" @click="showSelect">
								<p v-if="!model.SCXMC" class="res">请选择</p>
								<p v-else class="res">{{model.SCXMC}}</p>
							</div>

						</div>
					</div>
					<div class="item">
						<p class="label">生产设备 </p>
						<div class="rp">
							<div class="zy-selectBox" @click="showPollutionSelect = true">
								<p v-if="!model.ZWXMC" class="res" >请选择</p>
								<p v-else class="res">{{model.ZWXMC}}</p>
							</div>

						</div>
					</div>
				</div>

				<div class="zy-bot-btn1" @click="save()">保存</div>
			</div>
		</section>
		<m-select value-name="SCXID" label-name="SCXMC" mode="single-column" :list="produceList"
		v-model="showProduceSelect" @confirm="selectProduceLine"></m-select>

		<m-select value-name="ZWXID" label-name="ZWXMC" mode="single-column" :list="equipmentList"
		v-model="showPollutionSelect" @confirm="selectPolltionLine"></m-select>
	</body>
</template>

<script>
	import {
		getScxList,
		getScxsbList,
		addRelation
	} from '@/api/iot/enterprise.js';
	export default {
		data() {
			return {
				userInfo: {},
				info: {},
				produceList: [],
				equipmentList: [],
				showProduceSelect: false,
				showPollutionSelect: false,
				model: {
					SCXID: '',
					SCXMC: '',
					SBID: '',
					SBMC: '',
					USER: '',
					BZ: ''
				},
				rules: {
					SCXMC: {
						required: true,
						message: '请选择生产线',
						trigger: 'change'
					},
					SBMC: {
						required: true,
						message: '请选择生产设备'
					}
				},
				type: 'add',

			}
		},
		onLoad(option) {
			this.userInfo = uni.getStorageSync('userInfo');

			
			this.info = JSON.parse(decodeURIComponent(option.info));
			if (this.info.SCXID) {
				this.model.SCXID = this.info.SCXID;
				this.model.SCXMC = this.info.SCXMC;
				this.type = 'FIXED';
				
			}
			if (this.type == 'edit') {
				uni.setNavigationBarTitle({
					title: '修改关系绑定'
				})
			}
		},
		mounted() {
			this.getProduceList();
			this.getScxsbList();
		},
		methods: {
			getProduceList() {
				getScxList({
					WRYBH: this.info.WRYBH
				}).then(res => {
					this.produceList = res.data;
				})
			},
			getScxsbList() {
				getScxsbList({
					WRYBH: this.info.WRYBH
				}).then(res => {
					this.equipmentList = res.data;
				})
			},
			showSelect(){
				if(this.type == 'FIXED'){
					return;
				}
				this.showProduceSelect = true;
				
			},
			selectProduceLine(v) {
				this.model.SCXID = v[0].value;
				this.model.SCXMC = v[0].label;
			},
			selectPolltionLine(v) {
				this.model.SBID = v[0].value;
				this.model.SBMC = v[0].label;
			},
			save() {
				this.model.ORGID = this.userInfo.sjqx
				this.model.WRYBH = this.info.WRYBH
				this.model.USER = this.userInfo.name
				let rules = Object.keys(this.rules);
				for (let i = 0; i < rules.length; i++) {
					let field = rules[i];
					let requires = this.rules[field];
					console.log(!this.model[field], requires.required)
					if ((!this.model[field].length) && requires.required) {
						uni.showToast({
							title: requires.message,
							icon: 'error'
						})

						return;
					}
				}

				let self = this;
				addRelation(this.model).then(res => {
					uni.showToast({
						title: '保存成功',
						duration: 1000
					}).then(() => {

						setTimeout(() => {
							// uni.navigateTo({
							// 	url: './saveScxSuccess?scxName='+this.model.SCXMC+'&info='+ encodeURIComponent(JSON.stringify(this.info))
							// });
							// uni.setStorageSync('isAddScx', true);
							let pages = getCurrentPages(); // 当前页面
							let beforePage = pages[pages.length - 2]; // 上一页
							if(beforePage.$vm){
								beforePage.$vm.getRelationList();
							}else{
								beforePage.getRelationList();
							}
							
							uni.navigateBack({
								delta: 1
							})
						}, 1000)
					})

				})

			},
		}
	}
</script>

<style scoped>
	html { -webkit-tap-highlight-color: transparent; height: 100%; } 
	body { -webkit-backface-visibility: hidden; height: 100%;}
</style>
