<template>
	<view>
		<div style="display: flex;justify-content: flex-end;">
			<image class="quanping" mode="widthFix" src="@/static/app/enterpriseDetail/images/qiye-quanping.png"
				@click="toFullScreen" alt="">
		</div>
		<view id="chart">
			<!-- #ifdef APP-PLUS || H5 -->
			<view :prop="option" :change:prop="echarts.updateEcharts" :name="curEquit.SBMC" id="echarts" class="echarts"
				ref="echarts"></view>
			<!-- #endif -->
			<!-- #ifndef APP-PLUS || H5 -->
			<view>非 APP、H5 环境不支持</view>
			<!-- #endif -->
		</view>

	</view>
</template>

<script>
/** @format */

import { getZdqs, sbsxzt, } from '@/api/iot/runningData.js';
import { getZdsj } from '@/api/iot/realtime.js';
export default {
    props: {
        dateStr: {
            type: String,
            default: ''
        },
        curEquit: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            trendData: [],
            fullScreen: false,
            option: {
                color: [
                    '#409EFF' //'#E88511', '#1C8B14', '#14AACF', '#A825C9', '#781929', '#2C8B14'
                ],
                legend: {
                    show: false,
                    y: 'bottom',
                    type: 'scroll',
                    data: [
                        '振动能量' //'X轴', 'Y轴', 'Z轴', 'X轴主频', 'Y轴主频', 'Z轴主频'
                    ],
                    selected: {
                        振动能量: true
                    }
                },
                grid: {
                    top: '50',
                    left: '20',
                    right: '36',
                    bottom: '10%',
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    extraCssText: 'z-index: 9',
                    formatter: function (params) {
                        let res = '振动时间：' + params[0].name;
                        for (let i = 0; i < params.length; i++) {
                            res +=
                                '<br>' +
                                params[i].seriesName +
                                '：' +
                                params[i].data;
                        }
                        return res;
                    }
                },
                // dataZoom: [{
                // 	show: true,
                // 	realtime: true,
                // 	start: 0,
                // 	end: 90,
                // 	xAxisIndex: [0, 1],
                // 	bottom: '5%'
                // }],
                xAxis: {
                    name: '时\n间',
                    scale: true,
                    type: 'category',
                    data: ['2020-09-01', '2020-10-01']
                    // axisLabel: {
                    //     interval: 2,
                    //     rotate: -20,
                    //     formatter: function (val) {
                    //         return val;
                    //     }
                    // }
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '能量 ',
                        // nameLocation: 'center',
                        nameTextStyle: {
                            padding: [0, 0, 10, 14], // 加上padding可以调整其位置
                            fontSize: 14
                        },
                        nameGap: 8,
                        nameRotate: 0,
                        //默认以千分位显示，不想用的可以在这加一段
                        axisLabel: {
                            //调整左侧Y轴刻度， 直接按对应数据显示
                            show: true,
                            showMinLabel: true,
                            showMaxLabel: true,
                            formatter: function (value) {
                                //return '启停阈值' + value;
                                return value;
                            }
                        }
                    },
                    {
                        type: 'value',
                        name: '　　　　主\n　　　　频',
                        nameLocation: 'center',
                        nameGap: 10,
                        nameRotate: 0,
                        nameTextStyle: {
                            fontSize: 16
                        },
                        //默认以千分位显示，不想用的可以在这加一段
                        axisLabel: {
                            //调整左侧Y轴刻度， 直接按对应数据显示
                            show: true,
                            showMinLabel: true,
                            showMaxLabel: true,
                            formatter: function (value) {
                                return value;
                            }
                        }
                    }
                ],
                // visualMap: {
                //     show: false,
                //     dimension: 0,
                //     pieces: [], //pieces的值由动态数据决定
                //     outOfRange: {
                //         color: '#409EFF'
                //     }
                // },
                series: [
                    {
                        yAxisIndex: 0,
                        itemStyle: {
                            normal: {
                                lineStyle: {
                                    color: '#409EFF'
                                }
                            }
                        },
                        markLine: {
                            symbol: ['circle', 'none'],
                            data: [
                                {
                                    silent: false,
                                    lineStyle: {
                                        type: 'dashed',
                                        color: '#9134ff'
                                    },
                                    label: {
                                        position: 'end',
                                        color: '#9134ff'
                                    },
                                    yAxis: 0
                                    // symbol:'none'
                                }
                                // {
                                // 	silent: false,
                                // 	lineStyle: {
                                // 		type: 'dashed',
                                // 		color: '#9134ff'
                                // 	},
                                // 	label: {
                                // 		position: 'end',
                                // 		color: '#9134ff'
                                // 	},
                                // 	yAxis: 0
                                // 	// symbol:'none'
                                // }
                            ]
                        },
                        data: [],
                        areaStyle: {
                            opacity: 0.1
                        },
                        name: '振动能量',
                        type: 'line'
                    }
                ]
            },
            // allData:new Map(),
            allData: [], // 存储设备id和list相对应的数组对象，如果卡顿，此处可用Map结构优化，但是需要调用$forceUpdate
            time: [
                {
                    endT: '2023-05-27 07:37:12',
                    startT: '2023-05-27 03:12:26'
                },
                {
                    endT: '2023-05-27 10:41:25',
                    startT: '2023-05-27 09:22:18'
                }
            ]
        };
    },
    watch: {
        curEquit: {
            handler(newVal) {
                // 这里的nextTick不能去掉，因为请求是异步的，不能保证allData的数据一定比watch的newVal的值先有，否则find出错，所以必须要等到下一轮事件循环结束拿到最新数据再去find才不会有问题
                this.$nextTick(() => {
                    if (this.allData && this.allData.length) {
                        //遍历所有数据寻找设备id和设备序号相同的
                        const item = this.allData.find(
                            (item) => item.SBID === newVal.SBXH
                        );
                        if (item) {
                            // 如果找到了，则说明之前存储过，直接拿到缓存数据去初始化图表
                            this.initChart(item.LIST, item.YZ, item.ZNSBLX);
                        } else {
                            // 未找到就发起请求重新添加
                            this.getTrendPic();
                        }
                    } else {
                        this.getTrendPic();
                    }
                });
            },
            deep: true
        }
    },
    mounted() {
   
    },
    methods: {
		/**
		 * @method 根据某个字段对整个数组对象去重
		 * @parmas arr:要去重的数组
		 * @params field:字段名
		 */
        unique(arr, field) {
            const map = new Map();
            return arr.filter(
                (item) => !map.has(item[field]) && map.set(item[field], 1)
            );
        },
   
        //获取振动趋势图
        async getTrendPic() {
            try {
                let params = {
                    SBID: this.curEquit.SBXH,
                    DATE: this.dateStr
                };
                let res = await getZdqs(params);
                if (res.data) {
                    
                    this.trendData = res.data[0].LIST;
                    let trendType = res.data[0].ZNSBLX;
                    let yAxis = res.data[0].YZ;
                    this.allData.push(res.data[0]);
                    // 根据设备id对allData去重，防止重复push
                    this.allData = this.unique(this.allData, 'SBID');
                    this.$nextTick(() => {
                        this.initChart(this.trendData, yAxis, trendType);
                    });
                }
            } catch (e) {
                console.log(e);
            }
        },
        //获取振动趋势图
        // getTrendPic() {
        // 	let obj = {
        // 		SBID: this.curEquit.SBXH,
        // 		DATE: this.dateStr,
        // 	};
        // 	getZdqs(obj).then(res => {
        // 		if (res.data) {
        // 			this.trendData = res.data[0].LIST;
        // 			let yAxis = res.data[0].YZ;
        // 			this.$nextTick(() => {
        // 				this.initChart(this.trendData, yAxis);
        // 			});
        // 		}
        // 	});
        // },
        initChart(data, yAxis, trendType) {
            // 时间轴
            this.option.xAxis.data = [];
            this.option.series[0].data = [];
            this.option.yAxis[0].max = null;
            let max = 0;
            let x = [];
            for (let i = 0; i < data.length; i++) {
                const row = data[i];
              
                this.option.xAxis.data.push(row.JCSJ.slice(11, 16));
                if (row.NL - max > 0) {
                    max = row.NL;
                }
                this.option.series[0].data.push(row.NL);
            }

            this.option.series[0].markLine.data[0].yAxis = yAxis;
            this.option.series[0].name =
                trendType == 'DL'
                    ? this.curEquit.SBMC + '电流量'
                    : this.curEquit.SBMC + '振动能量';
            this.option.yAxis[0].name =
                trendType == 'DL' ? '电流量（A）' : '能量';
            this.option.yAxis[0].nameTextStyle.padding =
                trendType == 'DL' ? [0, 0, 10, 14] : [0, 36, 10, 0];
            if (yAxis - max > 0) {
                this.option.yAxis[0].max = yAxis;
            }
        },
        toFullScreen() {
            uni.navigateTo({
                url: `/pages/enterpriseWarnInfo/detail/FullScreenTendencyChart?dateStr=${
                    this.dateStr
                }&curEquit=${encodeURIComponent(JSON.stringify(this.curEquit))}`
            });
        },
        timeChange(v) {
            this.initChartData();
        }
    }
};
</script>
<script module="echarts" lang="renderjs">
	let myChart
	export default {
		data() {
			return {
				chartw: "",
				charth: '',
				flag: false,
				name: '',
			}
		},
		mounted() {
			if (typeof window.echarts === 'function') {
				this.initEcharts()
			} else {
				// 动态引入较大类库避免影响页面展示
				const script = document.createElement('script')
				// view 层的页面运行在 www 根目录，其相对路径相对于 www 计算
				script.src = 'static/echarts.js'
				script.onload = this.initEcharts.bind(this)
				document.head.appendChild(script)
			}
		},
		methods: {
			initEcharts() {
				let myDom = document.getElementById('echarts');
				myDom.setAttribute("style", "display:block;height:500px,width:100%;");
				myChart = echarts.init(myDom)
				// 观测更新的数据在 view 层可以直接访问到
				myChart && myChart.setOption(this.option)

			},
			updateEcharts(newValue, oldValue, ownerInstance, instance) {
				let myDom = document.getElementById('echarts');
				myDom.setAttribute("style", "display:block;height:500px,width:100%;");
				if (!newValue) {
					return;
				}
				let option = JSON.parse(JSON.stringify(newValue));

				if (option.yAxis[0].name == '能量') {
					option.tooltip.formatter = function(params) {
						var res = '<div style="font-size:12px">振动时间：' + params[0].name;
						for (var i = 0; i < params.length; i++) {
							res += '<br>' + params[i].seriesName + '：' + params[i].data;
						}
						return res + '<div>';
					}
				} else {
					option.tooltip.formatter = function(params) {
						var res = '<div style="font-size:12px">检测时间：' + params[0].name;
						for (var i = 0; i < params.length; i++) {
							res += '<br>' + params[i].seriesName + '：' + params[i].data + 'A';
						}
						return res + '<div>';
					}
				}

				option.tooltip.confine = true;
				// option.tooltip.formatter = function(obj) {
				// 	let data = obj.data;
				// 	return `<div style="background:${data.statusLabel== '停止'?'#ff7054':(data.statusLabel== '开启'?'#7dcd27':'#ccc')};color:#fff;padding:6px;opacity:0.9;border-radius:6px;border:1px solid #ddd;">          ${data.name}</br>
				//        开始时间：${data.start.slice(0,16)} </br>
				//        结束时间：${data.end} </br>
				//        状态：${data.statusLabel} </br>
				//      </div>`;
				// }
				// 监听 service 层数据变更
				myChart && myChart.clear()
				myChart && myChart.setOption(option)
				myChart && myChart.resize()
			},
		}
	}
</script>
<style scoped>
/**
 * #chart {
 * 	height: calc(100vh - 200rpx);
 * }
 *
 * @format
 */

.echarts {
    width: 100%;
    height: 500rpx;
}

.ic-full {
    z-index: 1;
}

.icon {
    display: flex;
    justify-content: flex-end;
}

.quanping {
    width: 30rpx;
}

.pic {
    width: 130rpx;
    border-radius: 20rpx;
    position: relative;
    bottom: -8rpx;
}

.qiye-tu .pingmian {
    width: 100%;
}
</style>
