/** @format */

import store from '@/store/index.js';
import { LOGIN_CACHE_KEYS } from '@/api/login-service.js';

import { ULR_BASE, LOGIN_ULR_BASE } from '@/common/config.js';
const SERVICE_URL = ULR_BASE;
const SERVICE_LOGIN_URL = LOGIN_ULR_BASE;

const KEY_STATUS_CODE = 'status_code';

//网络请求成功
const HTTP_STATUS_OK = 200;
//无效的请求
const HTTP_STATUS_INVALID_REQUEST = 400;
//身份未认证
const HTTP_STATUS_UNAUTHORIZED = 401;
//无权限访问
const HTTP_STATUS_FORBID = 403;
//请求路径未找到
const HTTP_STATUS_NOT_FOUND = 404;
//内部错误
const HTTP_STATUS_INNER_ERROR = 500;

const BUSINESS_STATUS_OK = 0;
const BUSINESS_STATUS_FAIL = 1;
const HTTP_STATUS_TOKEN_EXPIRED = '40001';

//是否正在刷新token
let isRefreshingToken = false;

//等待刷新的请求
let waittingRequests = [];

//拦截器
const INTERCEPTORS = [];

/**
 * 刷新token
 */
function refreshToken(resolve, reject) {
    isRefreshingToken = true;
    let username = uni.getStorageSync('user_id') || 'SYSTEM';
    let password = uni.getStorageSync('password') || 'power2018';
    uni.request({
        url: `${SERVICE_URL}/api/loginauthcontroller/login`,
        method: 'POST',
        data: {
            xtzh: username,
            yhmm: password
        },

        success: (resp) => {
            isRefreshingToken = false;
            let data = resp.data;
            if (isResponeOk(resp)) {
                saveToken2LocalStorage(username, password, data.token);
                waittingRequests.forEach((request) => {
                    request(data.token);
                });
                waittingRequests = [];
            } else {
                reject(data.code);
            }
        },

        fail: (error) => {
            isRefreshingToken = false;
            reject(error);
        }
    });
}

/**
 * 本地缓存token
 * @param {String} username
 * @param {String} password
 * @param {String} token
 */
function saveToken2LocalStorage(username, password, token) {
    uni.setStorageSync('token', token);
    let loginToken = Object.assign({
        // jwtToken: token,
        userid: username
        // pwd: password,
        // sim: PdStorage.get(PdGlobal.simKey,'',false)
    });
    store.commit('login', loginToken);
    store.state.hasLogin = true;
}

function get(url, params) {
    return request(url, 'get', null, params);
}

function post(url, params,config) {
    return request(url, 'post', null, params,config);
}

/**
 * 执行网络请求，基于uni.request封装成Promise返回
 */
function request(url, method, token, params = {},config={}) {
    // if(params){
    // 	params.sim = params.sim || '17777392862';
    // 	params.imei = params.imei || '111';
    // 	params.os = params.os || 'Android';
    // }

    let cacheToken = uni.getStorageSync('token');
    if (cacheToken) {
        params.jwtToken = cacheToken;
    }
    if (!params.hasOwnProperty('userid')) {
        let userId = uni.getStorageSync(LOGIN_CACHE_KEYS.userId);
        if (userId) {
            params.userid = userId;
        }
    }

    if (!params.hasOwnProperty('pwd')) {
        let password = uni.getStorageSync(LOGIN_CACHE_KEYS.password);
        if (password) {
            params.pwd = password;
        }
    }

    let resolveToken = token || uni.getStorageSync('token');

    return new Promise((resolve, reject) => {
        uni.request({
            url,
            method: method.toUpperCase(),
				// #ifdef H5
            header: {
                token: `${resolveToken}`,
                Authorization: `Bearer ${resolveToken}`,
				 'Content-Type': config.contentType ||'application/x-www-form-urlencoded; charset=utf-8'
            },
				// #endif
				
				// #ifdef APP-PLUS
				header: {
				    token: `${resolveToken}`,
				    Authorization: `Bearer ${resolveToken}`,
				},
					// #endif
            data: params || {},
            success: (resp) => {
                if (!isHttpStatusOk(resp)) {
                    let msg = parseHttpStatus(resp);
                    showRequestErrorTip(msg);
                    reject(msg);
                    return;
                }

                let actualData = resp.data;
                if (isTokenExpired(actualData)) {
                    cacheWaittingRequest(
                        url,
                        method,
                        params || {},
                        resolve,
                        reject
                    );
                    if (!isRefreshingToken) {
                        refreshToken();
                    }
                    return;
                }

                if (isResponeOk(actualData)) {
                    let businessData =
                        actualData.data_json ||
                        actualData.data_array ||
                        actualData.datas ||
                        actualData.result;
                    resolve(businessData || actualData);
                    return;
                }
                reject(actualData);
            },
            fail: (error) => {
                reject(error);
            }
        });
    });
}

/**
 * 缓存请求
 * @param {String} url
 * @param {String} method
 * @param {Object} params
 */
function cacheWaittingRequest(url, method, params, reqResolve, reqReject) {
    waittingRequests.push((token) => {
        return new Promise((resolve, reject) => {
            resolve(request(url, method, token, params));
        })
            .then(reqResolve)
            .catch(reqReject);
    });
}

/**
 * 判断网络状态码成功
 * @param {Object} response
 */
function isHttpStatusOk(response) {
    return HTTP_STATUS_OK === response.statusCode;
}

/**
 * 自定义业务响应是否成功判断
 * @param {Object} resp
 */
function isResponeOk(resp) {
    if (resp.hasOwnProperty(KEY_STATUS_CODE) || resp.hasOwnProperty('code')) {
        //有的服务状态码是status_code，有的是code
        let statusCode = parseInt(resp[KEY_STATUS_CODE] || resp.code);
        return statusCode === BUSINESS_STATUS_OK;
    } else {
        return true;
    }
}

/**
 * 判断token是否过期
 * @param {Object} resp
 */
function isTokenExpired(resp) {
    return resp[KEY_STATUS_CODE] === HTTP_STATUS_TOKEN_EXPIRED;
}

/**
 * 解析Http状态码含义
 * @param {Object} response
 */
function parseHttpStatus(response) {
    let statusCode = response.statusCode;
    if (HTTP_STATUS_INVALID_REQUEST === statusCode) {
        return '无效的请求，请检查您的请求参数';
    }
    if (HTTP_STATUS_UNAUTHORIZED === statusCode) {
        return '您的身份未认证，请求不通过';
    }
    if (HTTP_STATUS_FORBID === statusCode) {
        return '您无权限请求';
    }
    if (HTTP_STATUS_NOT_FOUND === statusCode) {
        return '您请求的服务不存在';
    }
    if (HTTP_STATUS_INNER_ERROR === statusCode) {
        return '服务内部错误';
    }
    return '网络请求出错，请稍后再试';
}

function showRequestErrorTip(msg) {
    uni.showToast({
        title: msg,
        duration: 3000,
        icon: 'none'
    });
}

export default {
    url: SERVICE_URL,
    loginUrl: SERVICE_LOGIN_URL,
    get,
    post,
    interceptors: INTERCEPTORS
};
