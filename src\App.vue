<!-- @format -->

<script>
/** @format */
import loginService from '@/api/login-service.js';
import appWatch from './utils/app-watch.js';

import { mapState, mapMutations } from 'vuex';
// import { initDatabase } from '@/common/sqlite.js'

export default {
    data() {
        return {
            errorTimer: null,
            timer: null, //进入登录页的定时器
            statusLineHeight: 0
        };
    },

    onLaunch: function () {
        let uniIdToken = uni.getStorageSync('uniIdToken');
        if (uniIdToken) {
            this.login(uni.getStorageSync('username'));
        }
        appWatch.getWatchAPP(); //每次打开APP都开启监测
    },
    onShow: function () {
        console.log('从后台进入前台显示');
        //清空定时器
        clearTimeout(this.timer);

        //#ifdef APP-PLUS
        if (plus.os.name == 'Android') {
            // console.log('');
        } else {
            this.iOSCheckUpdate();
        }
        //#endif
        let self = this;
        // uni.getLocation({
        //     type: 'wgs84',
        //     geocode: true,
        //     success: function (res) {
        //         uni.setStorageSync('APP_location', res);
        //     },
        //     fail(res) {
        //         if (!self.errorTimer) {
        //             uni.showToast({
        //                 title: '定位失败，请检查位置信息是否开启',
        //                 duration: 3000,
        //                 icon: 'none'
        //             });
        //             clearTimeout(self.errorTimer);
        //             self.errorTimer = setTimeout(() => {
        //                 self.errorTimer = null;
        //             }, 1800000);
        //         }
        //     }
        // });
    },
    onHide: function () {
        // console.log('hide', '从前台进入后台');
        // if (this.timer) {
        //     clearTimeout(this.timer);
        //     this.timer = null;
        // }
        // //开启定时器，超过半小时自动跳转去登录页
        // this.timer = setTimeout(function () {
        //     uni.redirectTo({
        //         url: '/pages/login/login'
        //     });
        // }, 1800000);
    },
    onReady() {},
    mounted() {
        // setTimeout(() => {
        //     setInterval(async () => {
        //         let userId = uni.getStorageSync('user_id') || '';
        //         let password = uni.getStorageSync('password') || '';
        //         await loginService.loginByPassword(userId, password);
        //     }, 900000);
        // }, 900000);
    },
    created() {},
    methods: {
        ...mapMutations(['login']),
        iOSCheckUpdate() {
            if (uni.getSystemInfoSync().platform != 'ios') {
                return;
            }
            let that = this;
            uni.request({
                url: 'http://itunes.apple.com/lookup?id=1671922136',
                success: (res) => {
                    console.log(res);
                    if (res.statusCode == 200) {
                        let lastVersionCode =
                            parseFloat(res?.data?.results[0]?.version) || 0;
                        plus.runtime.getProperty(
                            plus.runtime.appid,
                            (wgtInfo) => {
                                let current = wgtInfo.version || 0;
                                let flag = that.compare(
                                    lastVersionCode,
                                    current
                                );
                                if (flag == 1) {
                                    uni.showModal({
                                        title: '更新提示',
                                        //content: res.data.results[0]description,
                                        success: function (modalRes) {
                                            if (modalRes.confirm) {
                                                plus.runtime.openURL(
                                                    res.data.results[0]
                                                        .trackViewUrl
                                                );
                                            } else if (modalRes.cancel) {
                                                console.log('用户点击取消');
                                            }
                                        }
                                    });
                                }
                            }
                        );
                    }
                }
            });
        },
        compare(v1, v2) {
            if (v1 === v2) {
                return 0;
            }
            const toNum = (version) => {
                version = version.toString();
                // const versionArr = version.split('.')
                const versionArr = version.split(/\D/);
                const NUM_FILL = ['0000', '000', '00', '0', ''];

                for (let i = 0; i < versionArr.length; i++) {
                    const len = versionArr[i].length;
                    versionArr[i] = NUM_FILL[len] + versionArr[i];
                }

                return parseInt(versionArr.join(''));
            };

            v1 = toNum(v1);
            v2 = toNum(v2);

            if (v1 > v2) {
                return 1;
            }
            if (v1 < v2) {
                return -1;
            }
        }
    }
};
</script>

<style lang="scss">
/**
 * 头条小程序需要把 iconfont 样式放到组件外
 *
 * @format
 */

@import 'components/m-icon/m-icon.css';
// @import 'static/css/power.css';
// @import 'static/css/form.css';
// @import 'static/css/list.css';

@import 'static/app/css/style_zdy.css';
@import 'static/app/css/uni.css';
@import 'p-mui/index.scss';
@import 'static/app/css/my.css';
@import 'static/app/css/reset.css';
@import 'static/app/css/build.css';
@import 'static/app/enterpriseDetail/css/zy08-20.css';
@import 'static/warningRecord/css/zy07-06.css';

@import '@/static/app/css/warn0929.css';
@import '@/static/customSelect/customSelect.css';
@import '@/static/equipmentMaintenance/css/my1024.css';
@import '@/static/equipmentMaintenance/css/my0508.css';
@import '@/static/equipmentMaintenance/css/zm0614.css';
@import '@/static/equipmentMaintenance/css/my0706.css';

/*每个页面公共css */
page {
    height: 100%;
    min-height: 100%;
    // display: flex;
    font-size: 16px;
}

/* #ifdef MP-WEIXIN */
page {
    width: 100%;
}

/* #endif */

/* #ifdef MP-BAIDU */
page {
    width: 100%;
    height: 100%;
    display: block;
}

swan-template {
    width: 100%;
    min-height: 100%;
    display: flex;
}

/* 原生组件模式下需要注意组件外部样式 */
custom-component {
    width: 100%;
    min-height: 100%;
    display: flex;
}

/* #endif */

/* #ifdef MP-ALIPAY */
page {
    min-height: 100vh;
}

/* #endif */

/* 原生组件模式下需要注意组件外部样式 */
m-input {
    width: 100%;
    /* min-height: 100%; */
    display: flex;
    flex: 1;
}

.inner {
    height: 100%;
}

.warp {
    height: 100%;
}

.content {
    display: flex;
    flex: 1;
    flex-direction: column;
    background-color: #f4f4f4;
    padding: 10px;
}

.search-view {
    background: #fff;
}

.power-page {
    background-color: #f4f4f4;
}

.input-group {
    background-color: #ffffff;
    margin-top: 20px;
    position: relative;
}

.input-group::before {
    position: absolute;
    right: 0;
    top: 0;
    left: 0;
    height: 1px;
    content: '';
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    background-color: #c8c7cc;
}

.input-group::after {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    height: 1px;
    content: '';
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    background-color: #c8c7cc;
}

.input-row {
    display: flex;
    flex-direction: row;
    position: relative;
    font-size: 18px;
    line-height: 40px;
}

.input-row .title {
    width: 100px;
    padding-left: 15px;
}

.input-row.border::after {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 8px;
    height: 1px;
    content: '';
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    background-color: #c8c7cc;
}

.btn-row {
    margin-top: 25px;
    padding: 10px;
}

button.primary {
    background-color: #0faeff;
}

.flex-row-layout {
    position: relative;
}

.form-verify {
    position: absolute;
    top: 30rpx;
}

.form-checkbox-element {
    position: relative;
}

.form-radio-element {
    position: relative;
}

.form-textarea-element {
    position: relative;
}

.uni-toast {
    z-index: 9999999 !important;
}

.pd-btn {
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.pd-botbtn1 {
    width: 80%;
    margin-top: 28rpx;
}

.form-print-btn {
    border: 1rpx solid #4874ff;
    border-radius: 80rpx;
    color: #4874ff;
    display: flex;
    align-items: center;
    padding: 6rpx 24rpx;
    font-size: 26rpx;
    justify-content: space-around;
}

.printimage {
    width: 32rpx;
    height: 32rpx;
    margin-right: 6rpx;
}

.record-list {
    width: 120rpx;
    display: flex;
    align-items: center;
    justify-content: space-around;
}

.record-ilist-image {
    height: 40rpx;
    width: 40rpx;
    margin-right: 6rpx;
}

.sign-info-layout {
    position: relative;
    padding: 20rpx 30rpx;
    height: auto;
    width: calc(100% - 20px);
    background-color: #fff;
    align-items: flex-start;
}

.sign-prop {
    font-size: 30rpx;
    font-weight: 400;
    color: #666666;
}

.color-black {
    font-size: 30rpx;
    font-weight: 500;
    color: #333333;
    line-height: 62rpx;
}

.sign-prop-value {
    padding: 24rpx 18rpx;
    height: auto;
    line-height: auto;
    border-radius: 3px;
    background-color: #f4f4f4;
    color: #999;
    font-size: 28rpx;
    width: 92%;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap;
    /*设置不换行*/
}

.position-data {
    position: absolute;
    right: 65rpx;
    top: bottom;
    top: 75px;
    color: #4874ff;
}

.container {
    position: relative;
    margin-right: 36rpx;
    margin-bottom: 20rpx;
}

.content {
    padding: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #eeeeee;
    color: #333333;
    font-size: 28rpx;
    border-radius: 4rpx;
}

.conton {
    display: flex;
    flex-wrap: wrap;
    padding: 14rpx 10rpx 0 20rpx;
    width: 85%;
}

.imgic {
    position: absolute;
    top: -13rpx;
    right: -13rpx;
    width: 32rpx;
    height: 32rpx;
    z-index: 10;
}

// uni-page-body {
//     display: inline !important;
// }

.test {
    position: fixed;
    top: 20;
    bottom: 20;
    left: 20;
    right: 20;
}

.body {
    display: block;
}

/* .uni-textarea-wrapper{
		min-height: 120px!important;
	} */

textarea.two {
    min-height: 84rpx;
}

textarea.three {
    min-height: 126rpx;
}

uni-button:after {
    border: none;
}

.uni-input-placeholder,
.uni-textarea-placeholder {
    color: #c1c1c1;
}

.uni-textarea-textarea {
    text-align: right;
}

.zy-selectBox .res.placeholder {
    color: #c1c1c1;
}

.zy-selectBox .res.input {
    color: #666;
}

.height1 {
    height: calc(100% - 96.618375rpx - 30.1932rpx);
}

.height2 {
    height: 100%;
}

.no-icon {
    background: none;
}
</style>
