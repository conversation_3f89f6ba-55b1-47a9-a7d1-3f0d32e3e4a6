<template>
	<view>
		<show-modal></show-modal>
		<view class="flex-column-layout attach-icon-layout"
			  @click.stop="onPreviewAttach">
			<image class="attach-icon"
				   mode="aspectFit"
				   :src="src" />
			<image class="attach-delete"
				   mode="aspectFit"
				   :src="iconDel"
				   @tap.stop="deleteSrc" />

			<view v-if="showProgress"
				  class="flex-column-layout attach-progress-fitter">
				<circle-progress ref="progress"
								 class="attach-progress"
								 ringColor="#f00"
								 :width="42"
								 :progress="progress" />
			</view>

		</view>
		<!-- <view @click="uploadAttach" class="upDown">
		<text class="upDown-text">上传</text>
	</view> -->
	</view>
</template>

<script>
	import iconDoc from '@/static/img/file/word.png';
	import iconExcel from '@/static/img/file/excel.png';
	import iconPpt from '@/static/img/file/ppt.png';
	import iconPdf from '@/static/img/file/pdf.png';
	import iconImage from '@/static/img/file/image.png';
	import iconFile from '@/static/img/file/file.png';
	import iconDel from '@/static/img/icon_delete_corner.png'
	import circleProgress from '@/pages/component/circle-progress.vue'
	import fileUtil from '@/common/file.js'
	import mediaUtil from '@/common/media.js'
	import http from '@/common/net/http.js'

	export default {
		name: 'AttachIcon',
		components: {
			circleProgress
		},

		props: {
			attach: {
				type: String
			},

			type: {
				type: String
			}
		},

		data() {
			return {
				iconDel,
				progress: 0,
				showProgress: true
			}
		},

		computed: {
			suffix: function() {
				return fileUtil.parseFileSuffix(this.attach)
			},

			src: function() {
				switch (true) {
					case mediaUtil.isPicture(this.attach):
						(this.attach, '1')
						return this.attach
					case mediaUtil.isVideo(this.attach):
						(this.attach, '2')
						return `${this.attach}?x-oss-process=video/snapshot,t_0,f_jpg`
					case mediaUtil.isAudio(this.attach):
						(this.attach, '3')
						return this.attach
					case /doc|docx/g.test(this.suffix):
						(iconDoch, '4')
						return iconDoc;
					case /xls|xlsx/g.test(this.suffix):
						(iconExcel, '5')
						return iconExcel;
					case /ppt/g.test(this.suffix):
						(iconPpt, '6')
						return iconPpt;
					case /pdf/g.test(this.suffix):
						(iconPdf, '7')
						return iconPdf;
					default:
						(this.attach, '8')
						return iconFile
				}
			}
		},

		mounted() {
			this.uploadAttach()
		},

		methods: {
			uploadAttach() {
				uni.showLoading({
					title: '上传中'
				})
				let _self = this
				let uploadTask = uni.uploadFile({
					url: `${http.loginUrl}/webapp/uploadFile`,
					filePath: this.attach,
					name: 'file',
					formData: {
						LXDM: 'evidence',
						YWSJID: '000000',
						WJMC: 'name.png',
						WJLX: this.suffix,

					},
					success(e) {
						// (_self.src,'888');
						let data = JSON.parse(e.data)
						let dataFile = JSON.parse(data.wd_data)[0]
						dataFile.nowsrc = _self.src
						dataFile.fileType = _self.type
						_self.$emit('upload', dataFile)
						// (`上传附件成功`)
						_self.$refs.progress.updateProgress(100)
						_self.$nextTick(function() {
							_self.showProgress = false
						})
					},

					fail(e) {
						(`上传文件失败：${JSON.stringify(e, null, 4)}`)
					},
					complete() {
						uni.hideLoading()
					}
				})

				uploadTask.onProgressUpdate((resp) => {
					_self.$refs.progress.updateProgress(resp.progress)
				});
			},

			onPreviewAttach() {
				let attachUrl = this.attach
				if (mediaUtil.isPicture(attachUrl)) {
					uni.previewImage({
						urls: [attachUrl]
					})
				} else if (mediaUtil.isVideo(attachUrl)) {
					uni.$emit('playVideo', attachUrl)
				} else {

				}
			},

			//删除选择的图片
			deleteSrc() {
				let self = this
				this.$showModal({
					title: '提示',
					content: '确定是否删除吗？',
					success: function(res) {
						if (res.confirm) {
							self.$emit('deleteIcon', self.attach)
						} else if (res.cancel) {
							('用户点击取消');
						}
					}
				});
				// this.$emit('deleteIcon',this.attach)
				// this.attach = ''
				// (this.attach,'123');
			}
		}
	}
</script>

<style scoped>
	.attach-icon-layout {
		position: relative;
		width: 82px;
		height: 82px;
	}

	.attach-icon {
		width: 64px;
		height: 64px;
		background-color: #eee;
	}

	.attach-delete {
		position: absolute;
		top: 0;
		right: 0;
		width: 18px;
		height: 18px;
		z-index: 99;
	}

	.attach-progress-fitter {
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		background-color: rgba(0, 0, 0, .3);
	}

	.upDown {
		display: flex;
		justify-content: center;
		margin-top: 4rpx;
	}

	.upDown-text {
		border: 1rpx solid #ccc;
		border-radius: 8rpx;
		padding: 4rpx;
		font-size: 26rpx;
	}
</style>
