.blue-header {
	background: #2b6cf9;
	position: relative;
	padding: 20rpx;
	font-size: 36rpx;
}

.blue-header .lf-title {
	text-align: center;
	padding-left: 0rem;
	color:#fff;
}

.ic-back {
	position: absolute;
	left: 30.1932rpx;
	top: 50%;
	transform: translateY(-50%);
	width: 30rpx;
	height: 30rpx;
	background: url(~@/static/app/images/ic-back.png) center center no-repeat;
	background-size: 21.7391249rpx 36.2319rpx;
}

.zm-srhbx1 {
	padding: 30.722225rpx 42.6110999rpx 30.722225rpx 42.6110999rpx;
	background: #2b6cf9;
	position: relative;
	display: flex;
	margin-top: -15.944475rpx;
}

.zm-srhbx1 input {
	background: #F4F5F7;
	border-radius: 11.1111rpx;
	flex: 1;
	line-height: 72.222225rpx;
	height: 72.222225rpx;
	font-size: 29.166675rpx;
	color: #333;
	text-indent: 69.44445rpx;
}

.zm-srhbx1 input::-webkit-input-placeholder {
	color: #999;
}

.zm-srhbx1 button {
	background: url(~@/static/customSelect/images/zm17-searh.png) no-repeat center;
	background-size: 32.638875rpx;
	position: absolute;
	left: 62.499975rpx;
	top: 0;
	bottom: 0;
	width: 32.638875rpx;
}

.zm17-item1 {
	padding: 34.722225rpx 26.6110999rpx;
	overflow-y: auto;
	height: calc(100% - 214rpx);
}

.zm17-item1>.bg1 {
	background: url(~@/static/customSelect/images/zm17-bg1.png) no-repeat center;
	background-size: 100% 100%;
	height: 125.000025rpx;
	position: relative;
	margin-bottom: 41.6667rpx;
	margin-bottom: 10px;
}

.zm17-item1>.bg2 {
	background: url(~@/static/customSelect/images/zm17-bg2.png) no-repeat center;
	background-size: 100% 100%;
	height: 125.000025rpx;
	position: relative;
	margin-bottom: 10px;
}

.zm17-item1>.bg2>.p1 {
	width: 68.055525rpx;
	height: 56.944425rpx;
	line-height: 56.944425rpx;
	text-align: center;
	border-radius: 20.83335rpx 0 20.83335rpx 0;
	background-color: #c0c0c0;
	font-size: 31.94445rpx;
	color: #fff;
}

.zm17-item1>.bg2>.p2 {
	color: #333333;
	font-size: 34.722225rpx;
	font-weight: bold;
	position: absolute;
	left: 76.388925rpx;
	top: 50%;
	transform: translateY(-50%);
}

.zm17-item1>.bg1>.p1 {
	width: 68.055525rpx;
	height: 56.944425rpx;
	line-height: 56.944425rpx;
	text-align: center;
	border-radius: 20.83335rpx 0 20.83335rpx 0;
	background-color: #2b6cf9;
	font-size: 31.94445rpx;
	color: #fff;
}

.zm17-item1>.bg1>.p2 {
	position: absolute;
	left: 76.388925rpx;
	top: 50%;
	transform: translateY(-50%);
	color: #2b6cf9;
	font-size: 34.722225rpx;
	font-weight: bold;
}

.zm17-qh {
	padding: 0 34.722225rpx;
}

.btn {
	width: 100%;
	height: 93.055575rpx;
	background-color: #2b6cf9;
	border-radius: 11.1111rpx;
	font-size: 31.94445rpx;
	color: #fff;
	position: absolute;
	bottom: 34.722225rpx;
	left: 50%;
	transform: translatex(-50%);
}

.zm18-btn {
	position: fixed;
	border-radius: 11.1111rpx;
	bottom: 34.0278rpx;
	left: 20.83335rpx;
	right: 20.83335rpx;
	background: #2b6cf9;
	font-size: 31.94445rpx;
	color: #FFFFFF;
	height: 93.055575rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 290.444425rpx;
}
