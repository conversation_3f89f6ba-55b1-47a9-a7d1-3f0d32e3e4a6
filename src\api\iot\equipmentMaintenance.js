/** @format */
import originalAxios from 'axios-miniprogram';
import axios from '@/common/ajaxRequest.js';

import { ULR_BASE, IOTMANAGE_URL } from '@/common/config.js';

//根据IMEI查询设备信息
export const getIMEIData = (data) => {
    return axios.request({
        method: 'GET',
        url: IOTMANAGE_URL + '/sbyw/ywjlmx/init',
        params: data
    });
};

//1. 查询运维记录列表
export const getTableList = (data) => {
    return axios.request({
        method: 'POST',
        url: IOTMANAGE_URL + '/sbyw/ywjl/page',
        headers: { 'Content-Type': 'application/json; charset=utf-8' },
        data
    });
};

//2. 查询运维记录详情
export const getFormdataByYwid = (id) => {
    return axios.request({
        method: 'GET',
        url: IOTMANAGE_URL + '/sbyw/ywjl/info/' + id
    });
};

// 3. 保存运维记录
export const saveFormdata = (data) => {
    return axios.request({
        method: 'POST',
        url: IOTMANAGE_URL + '/sbyw/ywjl/save',
        headers: { 'Content-Type': 'application/json; charset=utf-8' },
        data
    });
};

//提交运维记录
export const commitFormdata = (data) => {
    return axios.request({
        method: 'POST',
        url: IOTMANAGE_URL + '/sbyw/ywjl/commit',
        headers: { 'Content-Type': 'application/json; charset=utf-8' },
        data
    });
};

// 保存运维明细
export const saveFormdataDetail = (data) => {
    return axios.request({
        method: 'POST',
        url: IOTMANAGE_URL + '/sbyw/ywjlmx/save',
        headers: { 'Content-Type': 'application/json; charset=utf-8' },
        data
    });
};

// 查询运维明细列表
export const maintenanceDetailList = (id) => {
    return axios.request({
        method: 'GET',
        url: IOTMANAGE_URL + '/sbyw/ywjlmx/list/' + id
    });
};

// 删除运维明细列表
export const deleteMaintenanceDetailList = (id) => {
    return axios.request({
        method: 'GET',
        url: IOTMANAGE_URL + '/sbyw/ywjlmx/del/' + id
    });
};

// 查询运维明细详情
export const maintenanceDetail = (mxid) => {
    return axios.request({
        method: 'GET',
        url: IOTMANAGE_URL + '/sbyw/ywjlmx/info/' + mxid
    });
};

// 查询运维明细图片详情
export const maintenanceDetailImage = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/commo/file',
        params: data
    });
};

// 查询imei是否已经存在别的运维明细
export const validIMEIMaintenDetail = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/baseinfo/ywmx/wtjimei',
        params: data
    });
};
