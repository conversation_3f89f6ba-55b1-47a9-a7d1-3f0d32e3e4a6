<template>
	<!-- 全屏平面图 -->
	<view>
		<image v-if="src"  mode="widthFix" style="width: 100vw;" :src="src"></image>
		<view v-if="!src" style="display: flex;justify-content: center;align-items: center;">
			<image  mode="heightFix" style="width:auto;height:90vh;margin: 0 auto;"  src="@/static/app/enterpriseDetail/images/empty.png" alt="">
		</view>
		<div class="ic-full" @click="back"></div>
    </view>

</template>

<script>
	import { DOWNLOAD_URLZDY } from '@/common/config.js';
	export default {
		data() {
			return {
				src:""
			};
		},
		mounted() {},
		onLoad(options) {
			this.src = options.SRC
			// #ifdef APP-PLUS
			plus.screen.lockOrientation('landscape-primary'); //横屏
			// #endif
		},
		created(){
			let port = uni.getSystemInfoSync().platform
			switch (port) {
				case 'android':
					console.log('运行Android上',port);//android
					break;
				case 'ios':
					console.log('运行iOS上',port);
					this.iosSystem = true;
					break;
				default:
					console.log('运行在开发者工具上');//devtools
					break;
			}
		},
		onBackPress(e) {
			// 退出页面时解除横屏
			// #ifdef APP-PLUS
			if (e.from == 'backbutton') {
				plus.screen.lockOrientation('portrait-primary'); //
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/realtime/whitePage'
					});
				}, 200);
				return true;
			}
			// #endif
		},
		methods: {
			//获取图片路径
			getFileUrl(item){
				return DOWNLOAD_URLZDY + item;
			},
			back() {
				// 退出页面时解除横屏
				// #ifdef APP-PLUS
				plus.screen.lockOrientation('portrait-primary'); //
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/realtime/whitePage'
					});
				}, 200);
				// #endif
			}
		}
	};
</script>

<style>		
	.ic-full{
		right: 20rpx;
	} 
</style>
