<template>
	<div class="pd-con1">
		<u-empty mode="data" v-if="!list || !list.length" text='暂无数据'  font-size="28rpx"></u-empty>
		<ul class="pd-ullst1" v-for="(item,index) in list" :key="index">
			<li><em>设备IMEI</em><i>{{item.IMEI || '-'}}</i></li>
			<li><em>检测时间</em><i>{{item.JCSJ || '-'}}</i></li>
			<li><em>上报时间</em><i style="color: rgb(248, 190, 69);">{{item.SBSJ || '-'}}</i></li>
			<li v-if="item.ZNSBLX == 'ZD'"><em>X轴振幅</em><i>{{item.X || '-'}}</i></li>
			<li v-if="item.ZNSBLX == 'ZD'"><em>Y轴振幅</em><i>{{item.Y || '-'}}</i></li>
			<li v-if="item.ZNSBLX == 'ZD'"><em>Z轴振幅</em><i>{{item.Z || '-'}}</i></li>
			<li v-if="item.ZNSBLX == 'ZD'"><em>X轴频率</em><i>{{item.X_FREQ || '-'}}</i></li>
			<li v-if="item.ZNSBLX == 'ZD'"><em>Y轴频率</em><i>{{item.Y_FREQ || '-'}}</i></li>
			<li v-if="item.ZNSBLX == 'ZD'"><em>Z轴频率</em><i>{{item.Z_FREQ || '-'}}</i></li>
			<li v-if="item.ZNSBLX == 'DL'"><em>电流量（A）</em><i>{{item.POWER || '0'}}</i></li>
		</ul>

	</div>
</template>

<script>
	export default {
		props: {
			list: {
				type: Array,
				default: () => []
			}
		},
		components: {

		},
		data() {
			return {

			};
		},

		mounted() {

		},
		
		methods: {


		}
	};
</script>

<style scoped>
	.pd-ullst1>li{
		padding:20rpx 12rpx 20rpx 0;
		border-top:none;
	}
	.pd-ullst1 li em {
	    font-size: 13px;
	    color: #999;
	    width: 23%;
		display: inline-block;
	}
	
	.pd-ullst1 {
		margin-bottom: 10px;
	}

	.normal {
		color: #3ab918;
	}

	.abnormal {
		color: #ff0000;
	}
</style>
