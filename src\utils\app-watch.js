/**
 * /*
 *
 * @format
 * @Author: 姚进玺 <EMAIL>
 * @Date: 2022-10-29 14:34:24
 * @LastEditors: 姚进玺 <EMAIL>
 * @LastEditTime: 2022-12-14 16:28:30
 * @FilePath: /YDZF_APP/api/app-watch.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import loginService from '@/api/login-service.js';
import {
	guid
} from '@/utils/uuid.js';
import {
	IOTMANAGE_URL
} from '@/common/config.js';
import arrPageUrl from '@/utils/pageUrl.js'
let seqCode = 0;
let trackUrl = '';
let list = [{
		"pagePath": "pages/enterprise/Index",
		"text": "污染源",
		"iconPath": "/static/app/images/muic1.png",
		"selectedIconPath": "static/app/images/muic1s.png"

	},
	{
		"pagePath": "pages/warning/Index",
		"text": "预警",
		"iconPath": "/static/app/images/lr27-4.png",
		"selectedIconPath": "/static/app/images/lr27-4-on.png"

	}, {
		"pagePath": "pages/realtime/index",
		"text": "实时查询",
		"iconPath": "/static/app/images/muic2.png",
		"selectedIconPath": "/static/app/images/muic2s.png"

	}, {
		"pagePath": "pages/mine/Index",
		"text": "我的",
		"iconPath": "/static/app/images/muic3.png",
		"selectedIconPath": "/static/app/images/muic3s.png"

	}
];
let matchUrlMsg = function(url) {
	let strUrl = url
	strUrl = strUrl.replace(/^\.?\//, "");
	let mark = ''
	let module = ''
	let markFlag = '-页面访问-访问'
	switch (strUrl) {
		case 'pages/login/login':
			mark = '登录';
			break;
		case 'pages/enterprise/Index':
			mark = '污染源';
			break;
		case 'pages/warning/Index':
			mark = '预警';
			break;
		case 'pages/realtime/index':
			mark = '实时查询';
			break;
		case 'pages/mine/Index':
			mark = '我的';
			break;
		case 'pages/enterprise/enterpriseInfo':
			mark = '污染源详情';
			break;
		case 'pages/enterprise/scx/addProductionLine':
			mark = '新增生产线';
			break;
		case 'pages/enterprise/scx/productionLineDetail':
			mark = '生产线详情';
			break;
		case 'pages/enterprise/cwsb/addCwEquipment':
			mark = '新增/编辑生产设备';
			break;
		case 'pages/enterprise/cwsb/cwEquipmentDetail':
			mark = '生产设备详情';
			break;
		case 'pages/enterprise/zwx/addPollutionLine':
			mark = '新增治污线';
			break;
		case 'pages/enterprise/zwx/pollutionLineDetail':
			mark = '治污线详情';
			break;
		case 'pages/enterprise/zwsb/addZwEquipment':
			mark = '新增/编辑治污设备';
			break;
		case 'pages/enterprise/zwsb/zwEquipmentDetail':
			mark = '治污设备详情';
			break;
		case 'pages/warningRecord/NumberingRecord':
			mark = '预警记录';
			break;
		case 'pages/enterpriseWarnInfo/detail/Layout':
			mark = '企业预警记录';
			break;
		case 'pages/enterprise/WeakSignalDetail':
			mark = '信号弱设备详情';
			break;
		case 'pages/realtime/SetImeiCircle':
			mark = '设置阈值周期';
			break;
		case 'pages/mine/customSelect':
			mark = '切换客户';
			break;
		case 'pages/mine/version/Layout':
			mark = '版本查看';
			break;
		default:
			mark = '其他';
	}
	return {
		mark,
		module: mark + markFlag
	}
}
export default {
	//设置路径ID
	setSessionId() {
		let sessionId = guid();
		uni.setStorageSync('SESSION_ID', sessionId);
		seqCode = 0;
	},
	getClickWatchAPP({
		url = '',
		clickModule = ''
	}) {
		this.setAppActionTrack(url, clickModule)
	},
	getWatchAPP() {
		let that = this;
		uni.addInterceptor('navigateTo', {
			invoke(args) {
				// request 触发前拼接 url
				that.invokeClick(args);
			}
		});
		uni.addInterceptor('redirectTo', {
			invoke(args) {
				// invokeClick 触发前拼接 url
				that.invokeClick(args);
			}
		});
		uni.addInterceptor('switchTab', {
			invoke(args) {
				// request 触发前拼接 url
				that.invokeClick(args);
			}
		});
		uni.addInterceptor('navigateBack', {
			invoke(args) {
				// request 触发前拼接 url
				that.invokeClick(args);
			}
		});
		uni.addInterceptor('reLaunch', {
			invoke(args) {
				// request 触发前拼接 url
				that.invokeClick(args);
			}
		});
	},

	//处理跳转时的URL参数
	invokeClick(e) {
		let routes = getCurrentPages();
		let setUrl = '';
		if (e.url) {
			setUrl = e.url;
		} else {
			//判断长度是为了处理登录页及首页无法左滑退出的场景
			if (routes.length < 2) {
				return;
			}
			setUrl = routes[routes.length - 2].route;
		}
		// if(e.url){
		let fillterUrl = this.setUrlFillter(setUrl);
		if (fillterUrl === trackUrl) {
			//判断，如果最近两次的行为一致，就不记录
			return;
		}
		//这里是举个事例，不需要的在下面这里加就可以过滤掉
		// if (fillterUrl === 'pages/record/record-resolver') {

		//     return;
		// }
		this.setAppActionTrack(fillterUrl);
	},

	//处理URL的格式，将格式不符合的全部剔除
	setUrlFillter(url) {
		var reg = /^\//g;
		if (reg.test(url)) {
			url = url.slice(1);
		}
		if (url.indexOf('?') !== -1) {
			url = url.slice(0, url.indexOf('?'));
		}
		return url;
	},


	//设置埋点的格式，确定要传的参数
	setAppActionTrack(url, clickModule) {
		// alert(seqCode, uni.getStorageSync('SESSION_ID'));
		let flag = arrPageUrl.some(e => e.url == url)
		let module = ''
		if (flag) {
			let filterData = arrPageUrl.find(e => e.url == url)
			module = filterData.module;
			if (clickModule) {
				module = clickModule
			}
		} else {
			module = '其他'
		}
		trackUrl = url; //记录行为
		let trackObj = {};
		trackObj.module = module
		trackObj.mark = `${module}-访问页面`
		trackObj.url = url; //mark就是我要定义的标识
		trackObj.ssxt = '智能运维App'; //mark就是我要定义的标识
		trackObj.time = new Date().getTime(); //获取当前时间戳
		trackObj.userId = uni.getStorageSync('userInfo').yhid; //获取当前登录人
		trackObj.orgid = uni.getStorageSync('userInfo').orgid; //上传用户的orgid
		trackObj.referrerUrl =
			'http://iot-manage.iotdi.com.cn/iotManage/platform/component/queryservice/analysis/analysiscontroller/showview/1647827306700021164032?v=2023022719308'
		seqCode++;
		let recordData = [];
		let data = uni.getStorageSync('APP_ACTION_TRACK_DATA');
		if (data) {
			data.push(trackObj);
			uni.setStorageSync('APP_ACTION_TRACK_DATA', data);
		} else {
			recordData.push(trackObj);
			uni.setStorageSync('APP_ACTION_TRACK_DATA', recordData);
		}
		this.checkTrackData();
	},

	//处理轨迹的处理方式，超过100条就提交一次
	checkTrackData() {
		let data = uni.getStorageSync('APP_ACTION_TRACK_DATA');
		if (data.length >= 50) {
			this.postRequestTrack();
			data = [];
			uni.setStorageSync('APP_ACTION_TRACK_DATA', data);
		}
		// console.log(
		// 	data,
		// 	'这里就是业务轨迹的整体流程啦，超过100条就会自动提交一次'
		// );
	},

	//提交用户的行为轨迹
	postRequestTrack() {
		let trackData = uni.getStorageSync('APP_ACTION_TRACK_DATA');
		uni.request({
			method: 'POST',
			url: IOTMANAGE_URL + '/platform/appwatch/appwatchcontroller/savetrack', //仅为示例，并非真实接口地址。
			data: trackData,
			success: (res) => {
				//console.log('res', res)
			}
		});
	},

	//提交用户的设备类型
	postEquipmentType() {
		uni.getSystemInfo({
			success: function(res) {
				let equipmentData = {
					screen: res.screenHeight + '*' + res.screenWidth,
					// system: res.system,
					os: res.system,
					// deviceType: res.deviceType,
					deviceBrand: res.deviceBrand,
					deviceModel: res.deviceModel,
					deviceId: res.deviceId,
					userId: loginService.getBaseUserId(),
					orgid: uni.getStorageSync('user_info').orgid,
					username: uni.getStorageSync('user_info').name || '',
					orgmc: uni.getStorageSync('user_info').department || '',
					ssxt: 'cp'
				};
				uni.request({
					method: 'POST',
					url: URL_TRACK + '/buriedPoint/saveDeviceJlb', //仅为示例，并非真实接口地址。
					data: equipmentData,
					success: (res) => {}
				});
			}
		});
	}
};