<template>
	<view>
		<div class="zy-line ac jb">
			<p class="qiye-til1">{{curEquit.SBMC}}趋势图</p>
			<div style="position: absolute;top: 50%;right: 24rpx;z-index:220;float:right;">
				<text style="float:right;line-height: 40rpx;" class="date-input" @click="open">
					<u-icon name="calendar" color="rgb(50, 131, 183)" size="24"></u-icon>
					<span class="datestr">{{dateStr}}</span>
				</text>
			</div>
		</div>
		<view id="chart">
			<!-- #ifdef APP-PLUS || H5 -->
			<view style="width: 96vw;height:400rpx" :prop="option" :change:prop="echarts.updateEcharts" id="echarts"
				class="echarts" ref="echarts"></view>
			<!-- #endif -->
			<!-- #ifndef APP-PLUS || H5 -->
			<view>非 APP、H5 环境不支持</view>
			<!-- #endif -->
			<div class="ic-full" @click="back"></div>
		</view>
		<u-calendar style="float:right" v-model="show" :mode="mode" @change="changeDate"></u-calendar>

	</view>

</template>

<script>
	import {
		getZdqs,
		sbsxzt
	} from '../../../api/iot/runningData.js';
	import {
		getZdsj
	} from '../../../api/iot/realtime.js';
	export default {
		onLoad(options) {
			// #ifdef APP-PLUS
			plus.screen.lockOrientation('landscape-primary'); //横屏
			// #endif
			this.dateStr = options.dateStr;
			this.curEquit = JSON.parse(decodeURIComponent(options.curEquit))
			this.getTrendPic();
		},
		onBackPress(e) {
			// 退出页面时解除横屏
			// #ifdef APP-PLUS
			if (e.from == 'backbutton') {
				plus.screen.lockOrientation('portrait-primary'); //
				setTimeout(() => {
					uni.navigateTo({
						url: `/pages/realtime/whitePage`
					});
				}, 200);
				return true;
			}
			// #endif
		},
		data() {
			return {
				show: false,
				mode: 'date',
				dateStr: '',
				curEquit: {},
				dateStr: '',
				trendData: [],
				fullScreen: false,
				option: {
					color: [
						'#409EFF' //'#E88511', '#1C8B14', '#14AACF', '#A825C9', '#781929', '#2C8B14'
					],
					legend: {
						show: false,
						y: 'bottom',
						type: 'scroll',
						data: [
							'振动能量' //'X轴', 'Y轴', 'Z轴', 'X轴主频', 'Y轴主频', 'Z轴主频'
						],
						selected: {
							振动能量: true
						}
					},
					grid: {
						top: '50',
						left: '20',
						right: '36',
						bottom: '10%',
						containLabel: true
					},
					tooltip: {
						trigger: 'axis',
						extraCssText: 'z-index: 9',
						formatter: function(params) {
							let res = '振动时间：' + params[0].name;
							for (let i = 0; i < params.length; i++) {
								res += '<br>' + params[i].seriesName + '：' + params[i].data;
							}
							return res;
						}
					},
					// dataZoom: [{
					// 	show: true,
					// 	realtime: true,
					// 	start: 0,
					// 	end: 90,
					// 	xAxisIndex: [0, 1],
					// 	bottom: '5%'
					// }],
					xAxis: {
						name: '时\n间',
						scale: true,
						type: 'category',
						data: ['2020-09-01', '2020-10-01']
						// axisLabel: {
						//     interval: 2,
						//     rotate: -20,
						//     formatter: function (val) {
						//         return val;
						//     }
						// }
					},
					yAxis: [{
							type: 'value',
							name: '能量 ',
							// nameLocation: 'center',
							nameTextStyle: {
								padding: [0, 0, 10, 14], // 加上padding可以调整其位置
								fontSize: 14
							},
							nameGap: 8,
							nameRotate: 0,
							//默认以千分位显示，不想用的可以在这加一段
							axisLabel: {
								//调整左侧Y轴刻度， 直接按对应数据显示
								show: true,
								showMinLabel: true,
								showMaxLabel: true,
								formatter: function(value) {
									//return '启停阈值' + value;
									return value;
								}
							},
						},
						{
							type: 'value',
							name: '　　　　主\n　　　　频',
							nameLocation: 'center',
							nameGap: 10,
							nameRotate: 0,
							nameTextStyle: {
								fontSize: 16
							},
							//默认以千分位显示，不想用的可以在这加一段
							axisLabel: {
								//调整左侧Y轴刻度， 直接按对应数据显示
								show: true,
								showMinLabel: true,
								showMaxLabel: true,
								formatter: function(value) {
									return value;
								}
							}
						}
					],
					series: [{
						yAxisIndex: 0,
						itemStyle: {
							normal: {
								lineStyle: {
									color: '#409EFF'
								}
							}
						},
						markLine: {
							symbol: ['circle', 'none'],
							data: [{
									silent: false,
									lineStyle: {
										type: 'dashed',
										color: '#9134ff'
									},
									label: {
										position: 'end',
										color: '#9134ff'
									},
									yAxis: 0
									// symbol:'none'
								},
								// {
								// 	silent: false,
								// 	lineStyle: {
								// 		type: 'dashed',
								// 		color: '#9134ff'
								// 	},
								// 	label: {
								// 		position: 'end',
								// 		color: '#9134ff'
								// 	},
								// 	yAxis: 0
								// 	// symbol:'none'
								// }
							]
						},
						data: [],
						areaStyle: {
							opacity: 0.1
						},
						name: '振动能量',
						type: 'line'
					}]
				},
				
				// option: {
				// 	color: [
				// 		'#409EFF' //'#E88511', '#1C8B14', '#14AACF', '#A825C9', '#781929', '#2C8B14'
				// 	],
				// 	legend: {
				// 		show: false,
				// 		y: 'bottom',
				// 		type: 'scroll',
				// 		data: [
				// 			'振动能量' //'X轴', 'Y轴', 'Z轴', 'X轴主频', 'Y轴主频', 'Z轴主频'
				// 		],
				// 		selected: {
				// 			振动能量: true
				// 		}
				// 	},
				// 	grid: {
				// 		top: '10',
				// 		left: '30',
				// 		right: '36',
				// 		bottom: '30%',
				// 		containLabel: true
				// 	},
				// 	tooltip: {
				// 		trigger: 'axis',
				// 		extraCssText: 'z-index: 9',
				// 		formatter: function(params) {
				// 			var res = '振动时间：' + params[0].name;
				// 			for (var i = 0; i < params.length; i++) {
				// 				res += '<br>' + params[i].seriesName + '：' + params[i].data;
				// 			}
				// 			return res;
				// 		}
				// 	},
				// 	dataZoom: [{
				// 		show: true,
				// 		realtime: true,
				// 		start: 0,
				// 		end: 90,
				// 		xAxisIndex: [0, 1],
				// 		bottom: '15%'
				// 	}],
				// 	xAxis: {
				// 		name: '时\n间',
				// 		scale: true,
				// 		type: 'category',
				// 		data: ['2020-09-01', '2020-10-01']
				// 		// axisLabel: {
				// 		//     interval: 2,
				// 		//     rotate: -20,
				// 		//     formatter: function (val) {
				// 		//         return val;
				// 		//     }
				// 		// }
				// 	},
				// 	yAxis: [{
				// 			type: 'value',
				// 			name: '能量 ',
				// 			// nameLocation: 'center',
				// 			nameTextStyle: {
				// 				padding: [0, 0, 10, 14], // 加上padding可以调整其位置
				// 				fontSize: 14
				// 			},
				// 			nameGap: 8,
				// 			nameRotate: 0,
				// 			nameTextStyle: {
				// 				fontSize: 14
				// 			},
				// 			//默认以千分位显示，不想用的可以在这加一段
				// 			axisLabel: {
				// 				//调整左侧Y轴刻度， 直接按对应数据显示
				// 				show: true,
				// 				showMinLabel: true,
				// 				showMaxLabel: true,
				// 				formatter: function(value) {
				// 					return '启停阈值' + value;
				// 				}
				// 			}
				// 		},
				// 		{
				// 			type: 'value',
				// 			name: '　　　　主\n　　　　频',
				// 			nameLocation: 'center',
				// 			nameGap: 10,
				// 			nameRotate: 0,
				// 			nameTextStyle: {
				// 				fontSize: 16
				// 			},
				// 			//默认以千分位显示，不想用的可以在这加一段
				// 			axisLabel: {
				// 				//调整左侧Y轴刻度， 直接按对应数据显示
				// 				show: true,
				// 				showMinLabel: true,
				// 				showMaxLabel: true,
				// 				formatter: function(value) {
				// 					return value;
				// 				}
				// 			}
				// 		}
				// 	],
				// 	series: [{
				// 		yAxisIndex: 0,
				// 		itemStyle: {
				// 			normal: {
				// 				lineStyle: {
				// 					color: '#409EFF'
				// 				}
				// 			}
				// 		},
				// 		markLine: {
				// 			symbol: ['circle', 'none'],
				// 			data: [{
				// 					silent: false,
				// 					lineStyle: {
				// 						type: 'dashed',
				// 						color: '#9134ff'
				// 					},
				// 					label: {
				// 						position: 'end',
				// 						color: '#9134ff'
				// 					},
				// 					yAxis: 0
				// 					// symbol:'none'
				// 				},
				// 				// {
				// 				// 	silent: false,
				// 				// 	lineStyle: {
				// 				// 		type: 'dashed',
				// 				// 		color: '#9134ff'
				// 				// 	},
				// 				// 	label: {
				// 				// 		position: 'end',
				// 				// 		color: '#9134ff'
				// 				// 	},
				// 				// 	yAxis: 0
				// 				// 	// symbol:'none'
				// 				// }
				// 			]
				// 		},
				// 		data: [],
				// 		areaStyle: {
				// 			opacity: 0.1
				// 		},
				// 		name: '振动能量',
				// 		type: 'line'
				// 	}]
				// }
			};
		},
		watch: {
			dateStr: {
				handler: function(newVal) {
					this.getTrendPic();
					console.log('sunzi', newVal);
				},
			},
		},
		created() {
			let port = uni.getSystemInfoSync().platform
			switch (port) {
				case 'android':
					console.log('运行Android上', port); //android
					break;
				case 'ios':
					console.log('运行iOS上', port);
					this.iosSystem = true;
					break;
				default:
					console.log('运行在开发者工具上'); //devtools
					break;
			}
		},
		mounted() {

		},
		methods: {
			open() {
				this.show = true;
			},
			changeDate(e) {
				this.dateStr = `${e.year}-${e.month<10?'0'+e.month:e.month}-${e.day<10?'0'+e.day:e.day}`
				this.getTrendPic();
			},
			//获取振动趋势图
			getTrendPic() {
				let obj = {
					SBID: this.curEquit.SBXH,
					DATE: this.dateStr,
				}
				getZdqs(obj).then(res => {
					if (res.data) {
						this.trendData = res.data[0].LIST;
						let trendType = res.data[0].ZNSBLX;
						let yAxis = res.data[0].YZ;
						this.$nextTick(() => {
							this.initChart(this.trendData, yAxis, trendType);
						})
					}
				})
			},
			// initChart(data, yAxis) {
			// 	// 时间轴
			// 	this.option.xAxis.data = [];
			// 	this.option.series[0].data = [];
			// 	let max = 0;
			// 	for (var i = 0; i < data.length; i++) {
			// 		const row = data[i];
			// 		//row.power = Math.sqrt(row.X * row.X + row.Y * row.Y + row.Z * row.Z);
			// 		this.option.xAxis.data.push(row.JCSJ.slice(11,16));
			// 		if(row.NL > max){
			// 			max = row.NL;
			// 		}
			// 		this.option.series[0].data.push(row.NL);
			// 	}
			// 	this.option.series[0].markLine.data[0].yAxis = yAxis;
			// 	this.option.series[0].name = this.curEquit.SBMC+'振动能量';
			// },
			initChart(data, yAxis, trendType) {
				// 时间轴
				this.option.xAxis.data = [];
				this.option.series[0].data = [];
				this.option.yAxis[0].max = null;
				let max = 0;
				for (let i = 0; i < data.length; i++) {
					const row = data[i];
					this.option.xAxis.data.push(row.JCSJ.slice(11, 16));
					if (row.NL - max > 0) {
						max = row.NL;
					}
					this.option.series[0].data.push(row.NL);
				}

				this.option.series[0].markLine.data[0].yAxis = yAxis;
				this.option.series[0].name = trendType == 'DL' ? this.curEquit.SBMC + '电流量' : this.curEquit.SBMC + '振动能量';
				this.option.yAxis[0].name = trendType == 'DL' ? '电流量（A）' : '能量';
				this.option.yAxis[0].nameTextStyle.padding = trendType == 'DL' ? [0, 0, 10, 14] : [0, 36, 10, 0];
				if (yAxis - max > 0) {
					this.option.yAxis[0].max = yAxis
				}
			},
			back() {
				// 退出页面时解除横屏
				// #ifdef APP-PLUS
				plus.screen.lockOrientation('portrait-primary'); //
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/enterpriseWarnInfo/whitePage'
					});
				}, 200);
				// #endif
			}
		}
	};
</script>
<script module="echarts" lang="renderjs">
	let myChart
	export default {
		data() {
			return {
				chartw: "",
				charth: '',
				flag: false,
				name: '',
			}
		},
		mounted() {
			if (typeof window.echarts === 'function') {
				this.initEcharts()
			} else {
				// 动态引入较大类库避免影响页面展示
				const script = document.createElement('script')
				// view 层的页面运行在 www 根目录，其相对路径相对于 www 计算
				script.src = 'static/echarts.js'
				script.onload = this.initEcharts.bind(this)
				document.head.appendChild(script)
			}
		},
		methods: {
			initEcharts() {
				let myDom = document.getElementById('echarts');
				myDom.setAttribute("style", "display:block;height:500px,width:100%;");
				myChart = echarts.init(myDom)
				// 观测更新的数据在 view 层可以直接访问到
				myChart && myChart.setOption(this.option)

			},
			updateEcharts(newValue, oldValue, ownerInstance, instance) {
				let myDom = document.getElementById('echarts');
				myDom.setAttribute("style", "display:block;height:500px,width:100%;");
				if (!newValue) {
					return;
				}
				let option = JSON.parse(JSON.stringify(newValue));
				if (option.yAxis[0].name == '能量') {
					option.tooltip.formatter = function(params) {
						var res = '<div style="font-size:12px">振动时间：' + params[0].name;
						for (var i = 0; i < params.length; i++) {
							res += '<br>' + params[i].seriesName + '：' + params[i].data;
						}
						return res + '<div>';
					}
				} else {
					option.tooltip.formatter = function(params) {
						var res = '<div style="font-size:12px">检测时间：' + params[0].name;
						for (var i = 0; i < params.length; i++) {
							res += '<br>' + params[i].seriesName + '：' + params[i].data + 'A';
						}
						return res + '<div>';
					}
				}
	        	option.tooltip.confine = true;
				// 监听 service 层数据变更
				myChart && myChart.clear()
				myChart && myChart.setOption(option)
				myChart && myChart.resize()
			},
		}
	}
</script>
<style scoped>
	/* #chart {
	height: calc(100vh - 200rpx);
} */
	::v-deep .u-calendar__bottom {
		padding: 0 20rpx 0 20rpx;
	}

	::v-deep .u-drawer-content-visible {
		width: 340px !important;
		height: 100% !important;
		transition-duration: 0s;
	}

	::v-deep .u-drawer-bottom {
		bottom: 0;
		left: auto;
		right: 0;
	}

	::v-deep .u-calendar__action {
		padding: 10rpx 0 10rpx 0;
	}

	::v-deep .u-calendar__content__item {
		margin-bottom: 0rpx;
	}

	::v-deep .u-calendar__content__item__inner {
		height: 40rpx;
	}

	.echarts {
		width: 100%;
		height: 500rpx;
	}

.ic-full {
    position: fixed;
    bottom: 50rpx;
    right: 30.1932rpx;
    width: 60.386475rpx;
    height: 60.386475rpx;
    background: url('~@/static/app/images/ic-full.png') 0 0 no-repeat;
    background-size: 100%;
	z-index: 1;
}
	.zy-line {
		padding-left: 20rpx;
		padding-bottom: 20rpx;
		position: relative;
	}

	.date-input {
		float: right;
		width: 104px;
		height: 26px;
		color: #000000;
		line-height: 26px;
		font-size: 13px;
		cursor: pointer;
		padding-left: 5px;
		border-radius: 5px;
		border: 1px solid rgb(50, 131, 183);
		outline: none;
	}
	.datestr{
		position: relative;
		bottom:6rpx;
	}
</style>
