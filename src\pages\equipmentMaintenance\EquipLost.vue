<!-- @format -->
<template>
    <!-- 拆除设备 -->
    <section class="main" style="padding-bottom: 0; margin-bottom: 30.1932rpx">
        <div class="inner">
            <div class="gap"></div>
            <div class="zy-form">
                <!-- 设备基本信息 -->
                <GetIMEIMsg
                    @updateInfo="updateInfo"
                    ref="refGetIMEIMsg"
                    :isDisabled="isDisabled"
                    :options="options"
                ></GetIMEIMsg>
                <div class="item">
                    <p
                        class="label"
                        :class="{ star: options.type === 'add' }"
                        style="flex: 5"
                    >
                        产治污设备绑定关系
                    </p>
                    <div class="rp pd-btn1">
                        <button type="button" class="on">解除</button>
                    </div>
                </div>
            </div>
            <UploadImage
                ref="refUploadImage"
                :options="options"
                :arrUploadType="arrUploadType"
                :uploadId="info.mxid"
                :isDisabled="isDisabled"
                title="现场照片"
            >
                <ul class="pd-ulpic1 sample">
                    <li>
                        <image
                            mode="aspectFit"
                            src="@/static/equipmentMaintenance/images/remove.png"
                            alt=""
                        />
                        <p>现场照片</p>
                    </li>
                </ul>
                <div class="gap"></div>
                <div class="gap"></div>
                <dl class="pd-dltxt1">
                    <dt>说明：</dt>
                    <dd>
                        <p>
                            1、请拍下设备拆下来的相关照片，其中有一张能看清设备的IMEI编号。
                        </p>
                    </dd>
                </dl>
            </UploadImage>
            <!-- 采集结果 -->
            <!-- <GetLastedResult
                :imei="info.imei"
                ref="refGetLastedResult"
            ></GetLastedResult> -->

            <div class="gap"></div>
            <div class="gap"></div>
            <div class="zy-bot-btn1" v-show="!isDisabled" @click="save">
                保存
            </div>
            <div class="gap"></div>
            <div class="gap"></div>
        </div>
    </section>
</template>

<script>
import { maintenanceDetail } from '@/api/iot/equipmentMaintenance';
import { guid } from '@/utils/uuid.js';
import GetIMEIMsg from './components/GetIMEIMsg';
import GetLastedResult from './components/GetLastedResult';
import UploadImage from './components/UploadImage';
import useSaveForm from './hook/useSaveForm';
const { handleSave } = useSaveForm();
export default {
    data() {
        return {
            options: {},
            info: {
                imei: '',
                mxid: '', //明细id
                ywid: '', //运维id
                ywlx: '', //运维类型
                ywmx: {
                    gxbd: '解除'
                }
            },
            arrUploadType: [
                {
                    name: '现场照片',
                    LXDM: 'YWMX', //类型代码
                    ZLXDM: 'SBDS' //子类型代码
                }
            ],
            isDisabled: false, //是否不可编辑
            isShowReminderPop: false
        };
    },
    components: {
        GetIMEIMsg,
        GetLastedResult,
        UploadImage
    },
    watch: {},
    onLoad(options) {
        this.options = options;
    },
    onHide() {},
    created() {},
    mounted() {
        if (this.options.type === 'add') {
            this.info.ywlx = this.options.ywlx;
            this.info.ywid = this.options.ywid;
            this.info.mxid = guid();
        }
        if (this.options.type === 'detail') {
            uni.setNavigationBarTitle({
                title: '运维明细详情'
            });
            this.isDisabled = true;
            this.getMaintenanceDetail();
        }
    },
    onBackPress() {},
    methods: {
        //请求明细
        async getMaintenanceDetail(id) {
            let data = await maintenanceDetail(this.options.mxid);
            for (const key in data) {
                if (key == 'ywmx') {
                    for (const key in data['ywmx']) {
                        this.$set(
                            this.info.ywmx,
                            key,
                            data[key] === '' ? '-' : data[key]
                        );
                    }
                } else {
                    this.$set(
                        this.info,
                        key,
                        data[key] === '' ? '-' : data[key]
                    );
                }
            }
            let index = this.info.ywmx?.qtyz?.indexOf('#') || 0;
            this.info.qtyz = this.info.ywmx?.qtyz?.slice(0, index) || '-';
            this.$refs.refGetIMEIMsg.updateInfo(this.info);
            this.$nextTick(() => {
                this.$refs.refUploadImage.getImageFileList();
            });
        },
        //更新表单数据
        updateInfo(payload) {
            Object.assign(this.info, payload);
        },

        //保存
        async save() {
            let objRules = {};
            let objValiData = {};
            Object.assign(objRules, this.$refs.refGetIMEIMsg.rules);
            Object.assign(
                objValiData,
                this.info,
                this.info.ywmx,
                this.$refs.refGetIMEIMsg.info
            );
            let pages = getCurrentPages(); // 当前页面
            let arrUploadImage = this.$refs.refUploadImage.arrUploadImage;
            handleSave(
                objRules,
                objValiData,
                this.info,
                pages,
                true,
                arrUploadImage,
                true
            );
        }
    }
};
</script>

<style scoped lang="less">
section {
    background: #f1f1f1;
}
</style>
