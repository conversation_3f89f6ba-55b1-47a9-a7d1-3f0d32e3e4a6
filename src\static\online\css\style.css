@charset "utf-8";

/** * mobile reset **/
html,
body,
div,
span,
ol,
ul,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
	margin: 0;
	padding: 0;
	border: 0;
	font: inherit;
	font-size: 100%;
	vertical-align: baseline;
}

ol,
ul {
	list-style: none;
}

input,
button,
textarea,
select {
	-webkit-appearance: none;
	appearance: none;
	box-sizing: border-box;
	border-radius: 0;
	padding: 0;
	margin: 0;
	border: none;
	outline: none;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
}

caption,
th,
td {
	font-weight: normal;
	vertical-align: middle;
}

q,
blockquote {
	quotes: none;
}

q:before,
q:after,
blockquote:before,
blockquote:after {
	content: "";
	content: none;
}

a img {
	border: none;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section,
summary {
	display: block;
}

a {
	text-decoration: none;
}

a:hover,
a:active {
	outline: none;
}

html {
	font-family: Helvetica, "STHeiti STXihei", "Microsoft JhengHei", "Microsoft YaHei", "Noto Sans CJK SC", "Source Han Sans CN"Tohoma, Arial;
	-webkit-text-size-adjust: 100%;
	-webkit-tap-highlight-color: transparent;
	height: 100%;
	overflow: hidden;
}

body {
	color: #333;
	background-color: #fff;
	-webkit-backface-visibility: hidden;
	overflow: hidden;
	height: 100%;
}

/*common*/
.gap {
	height: 18.1159rpx;
}

.mask {
	display: none;
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background: rgb(0, 0, 0, 0.5);
	z-index: 900;
}

/*page*/
.header {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	background: #009bff;
	height: 79.7101rpx;
	z-index: 899;
}

.title {
	text-align: center;
	line-height: 79.7101rpx;
	color: white;
	font-size: 33.8164rpx;
}

.main {
	height: 100%;
	overflow-y: auto;
	overflow-x: hidden;
	-webkit-overflow-scrolling: touch;
}

.inner {
	padding: 79.7101rpx 0 90.5796rpx;
}

/* 底部导航栏 */
.footer {
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
	height: 90.5796rpx;
	border-top: 1px solid #dce0e6;
	box-sizing: border-box;
	background: #fff;
	z-index: 899;
}

.menu {
	display: flex;
	display: -webkit-box;
	height: 100%;
	overflow: hidden;
	padding: 0 24.1545rpx;
}

.menu li {
	flex: 1;
	-webkit-box-flex: 1;
	height: 100%;
}

.menu li a {
	position: relative;
	display: block;
	height: 100%;
	overflow: hidden;
}

.menu li a p {
	font-size: 20.5313rpx;
	color: #a5bed6;
	text-align: center;
	padding-top: 3.75rpx;
}

.menu li a i {
	display: block;
	background-repeat: no-repeat;
	background-position: center;
	height: 39.855rpx;
	background-size: contain;
	margin-top: 12.0773rpx;
}

.menu li.on p {
	color: #5296ff;
}

.menu-icon1 {
	background-image: url(~@/static/online/images/menu_icon1.png);
}

.menu-icon2 {
	background-image: url(~@/static/online/images/menu_icon2.png);
}

.menu-icon3 {
	background-image: url(~@/static/online/images/menu_icon3.png);
}

.menu li.on .menu-icon1 {
	background-image: url(~@/static/online/images/menu_icon1s.png);
}

.menu li.on .menu-icon2 {
	background-image: url(~@/static/online/images/menu_icon2s.png);
}

.menu li.on .menu-icon3 {
	background-image: url(~@/static/online/images/menu_icon3s.png);
}

/* 地图 */
.MAP {
	background: url('~@/static/online/images/MAP.png') center no-repeat;
	background-size: 685.3864rpx 955.9179rpx;
}

.MAP-2 {
	background: url('~@/static/online/images/MAP-2.png') center no-repeat;
	background-size: 100% 100%;
}

/* tab 切换 */
.tab-wrap {
	border-top: 1px solid #eee;
	height: 72.4638rpx;
	position: absolute;
	top: 79.7101rpx;
	left: 0;
	width: 100%;
	box-shadow: 0 4.5rpx 15rpx rgba(0, 97, 159, .1);
	z-index: 800;
}

.menu-tab {
	display: flex;
	display: -webkit-box;
	height: 100%;
	background: rgba(255, 255, 255, 0.95);
}

.menu-tab li {
	flex: 1;
	min-width: 20%;
	text-align: center;
	-webkit-box-flex: 1;
	height: 100%;
	line-height: 72.4638rpx;
	font-size: 26.57rpx;
	color: #a5bed6;
}

.menu-tab li.on,
.menu-tab li:active {
	color: #3fa2ff;
	background: url('~@/static/online/images/bot_bg.png') center bottom no-repeat;
	background-size: 99.6376rpx 6.0386rpx;
}

.menu-tab li sub {
	vertical-align: sub;
}

.menu-tab-2 li {
	min-width: 50%;
}

.menu-tab-2 li.on,
.menu-tab-2 li:active {
	background-size: 160.628rpx 6.0386rpx;
}

.menu-tab-3 li {
	min-width: 33.3%;
}

.menu-tab-3 li.on,
.menu-tab-3 li:active {
	background-size: 160.628rpx 6.0386rpx;
}

.menu-tab-4 li {
	height: 84.541rpx;
	min-width: 25%;
}

.menu-tab-4 li p {
	line-height: 1;
	padding-top: 11.25rpx;
}

.menu-tab-4 li p+p {
	padding-top: 7.5rpx;
	display: none;
}

.menu-tab-4 li.on p+p {
	display: block;
}

.menu-tab-4 li.on,
.menu-tab-4 li:active {
	background-size: 85% 6.0386rpx;
}

/* 污染源监测实时状况 */
.inner.mp {
	height: 100%;
	position: relative;
	box-sizing: border-box;
}

.pd-tuliz {
	position: absolute;
	left: 30.1932rpx;
	bottom: 120.7729rpx;
	width: 95.4106rpx;
	height: 88.1642rpx;
}

.ul-slt-typ {
	position: absolute;
	top: 2rem;
	right: 18.1159rpx;
	padding: 18.1159rpx;
}

.ul-slt-typ li {
	text-align: center;
	font-size: 26.57rpx;
	color: #949494;
	height: 60.3864rpx;
	line-height: 60.3864rpx;
	border-radius: 30.1932rpx;
	min-width: 120.7729rpx;
	box-shadow: 0 4.5rpx 15rpx rgba(0, 0, 0, .15);
	margin-top: 24.1545rpx;
}

.ul-slt-typ li.on,
.ul-slt-typ li:active {
	color: #fff;
	background: #3fa2ff;
}

.more-menu-wrap {
	position: absolute;
	width: 100%;
	left: -100%;
	bottom: 0;
	top: 79.7101rpx;
	background: rgba(0, 0, 0, 0.3);
	z-index: 1000;
}

.more-menu {
	width: 386.4734rpx;
	background: #fff;
	height: 100%;
}

.more-menu li {
	text-align: center;
	height: 87.5604rpx;
	line-height: 87.5604rpx;
	font-size: 30.1932rpx;
	color: #515151;
}

.more-menu li.on,
.more-menu li:active {
	background: #cadfff;
	color: #4c91ff;
}

.ul-1 {
	position: absolute;
	right: 18.1159rpx;
	top: 3rem;
	width: 111.7149rpx;
	padding: 9.0579rpx;
	background: #fff;
	border-radius: 15rpx;
	box-shadow: 0 4.5rpx 15rpx rgba(0, 0, 0, .15);
}

.ul-1-2 {
	top: 405rpx;
}

.ul-1 li {
	height: 38.6473rpx;
	line-height: 38.6473rpx;
	border-radius: 9.0579rpx;
	text-align: center;
	font-size: 26.57rpx;
	color: #949494;
}

.ul-1 li+li {
	margin-top: 11.25rpx;
}

.ul-1 li.on,
.ul-1 li:hover {
	color: #fff;
	background: #3fa2ff;
}

.ul-2 {
	overflow: hidden;
	display: flex;
	justify-content: space-between;
	padding: 9.0579rpx 18.1159rpx;
}

.ul-2 li {
	width: 163.0434rpx;
	height: 60.3864rpx;
	line-height: 60.3864rpx;
	border-radius: 30.1932rpx;
	text-align: center;
	font-size: 26.57rpx;
	color: #838f9a;
	background: #e5ecf3;
}

.ul-2 li.on,
.ul-2 li:hover {
	background: #3fa2ff;
	color: #fff;
}

.ppanel {
	display: flex;
	justify-content: space-between;
	height: 60.3864rpx;
	line-height: 60.3864rpx;
	padding: 0 18.1159rpx;
}

.tit {
	display: inline-block;
	font-size: 30.1932rpx;
	color: #333;
	padding-left: 39.2511rpx;
}

.tit em {
	color: #a5bed6;
}

.tit-1 {
	background: url('~@/static/online/images/tit_bf_1.png') left center no-repeat;
	background-size: 26.57rpx 26.57rpx;
}

.tit-2 {
	background: url('~@/static/online/images/tit_bf_2.png') left center no-repeat;
	background-size: 26.57rpx 26.57rpx;
}

.tit-3 {
	background: url('~@/static/online/images/tit_bf_3.png') left center no-repeat;
	background-size: 21.1353rpx 24.7584rpx;
}

.ppanel .rili-slt {
	font-size: 26.57rpx;
	color: #a5bed6;
	padding-right: 39.2511rpx;
	background: url('~@/static/online/images/icon_rili.png') right center no-repeat;
	background-size: 26.57rpx 26.57rpx;
}

.qstj-wrap {
	background: #fff;
	text-align: center;
	padding: 30.1932rpx 45rpx;
}

.ul-3 {
	display: flex;
	justify-content: space-between;
	margin-top: 30.1932rpx;
}

.ul-3 li {
	position: relative;
	min-width: 99.6376rpx;
	padding-left: 60.3864rpx;
}

.ul-3 li img {
	display: block;
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
}

.ul-3 li:nth-child(1) img {
	width: 53.7439rpx;
	height: 53.7439rpx;
}

.ul-3 li:nth-child(2) img {
	width: 53.7439rpx;
	height: 45.2898rpx;
}

.ul-3 li:nth-child(3) img {
	width: 41.0627rpx;
	height: 53.7439rpx;
}

.ul-3 li h2 {
	font-size: 24.1545rpx;
	color: #666666;
}

.ul-3 li p {
	font-size: 28.9854rpx;
	color: #3fa2ff;
}

.ul-4 {
	padding: 0 18.1159rpx;
}

.ul-4 li {
	display: flex;
	display: -webkit-box;
	background: #fff;
	border-radius: 7.2463rpx;
	height: 126.2077rpx;
	margin-top: 18.1159rpx;
}

.ul-4 li>div {
	flex: 1;
	-webkit-box-flex: 1;
	height: 100%;
	text-align: left;
	width: 465rpx;
}

.ul-4 li>div:first-child {
	width: 90.5796rpx;
	text-align: center;
	font-size: 30.1932rpx;
	color: #4da9ff;
	line-height: 126.2077rpx;
}

.ul-4 li>div:last-child {
	width: 150rpx;
	text-align: center;
}

.ul-4 li:nth-child(1)>div:first-child,
.ul-4 li:nth-child(2)>div:first-child,
.ul-4 li:nth-child(3)>div:first-child {
	color: #ff9352;
}

.ul-4 li:nth-child(1)>div:last-child .cir,
.ul-4 li:nth-child(2)>div:last-child .cir,
.ul-4 li:nth-child(3)>div:last-child .cir {
	border-color: #ff9352;
	color: #ff9352;
}

.cir {
	display: inline-block;
	width: 95.4106rpx;
	height: 95.4106rpx;
	border-radius: 50%;
	border: 2px solid #54b4ff;
	margin-top: 12.0773rpx;
	box-sizing: border-box;
	padding-top: 15rpx;
	color: #54b4ff;
	font-size: 24.1545rpx;
}

.ul-4 li>div h4 {
	font-size: 31.401rpx;
	color: #252525;
	padding-top: 24.1545rpx;
	padding-bottom: 7.5rpx;
}

.ul-4 li>div:nth-child(2) p span {
	display: inline-block;
	width: 50%;
	font-size: 24.1545rpx;
	color: #666666;
}

.date-slt-cont {
	z-index: 1000;
	position: absolute;
	right: -100%;
	top: 79.7101rpx;
	width: 495.169rpx;
	bottom: 0;
	background: #fff;
	text-align: center;
}

.date-confirm {
	position: absolute;
	bottom: 66.4251rpx;
	left: 50%;
	margin-left: -181.1595rpx;
	width: 362.3188rpx;
	height: 60.3864rpx;
	font-size: 26.57rpx;
	color: #fff;
	background: #3fa2ff;
	border-radius: 30.1932rpx;
	border: none;
}

.date-confirm:active {
	opacity: 0.85;
}

.dlg-panel {
	font-size: 30.1932rpx;
	color: #333;
	padding: 15.0966rpx 30.1932rpx;
	text-align: left;
	clear: both;
}

.ul-5 {
	overflow: hidden;
}

.ul-5 li {
	overflow: hidden;
	width: 211.3526rpx;
	height: 60.3864rpx;
	line-height: 60.3864rpx;
	border-radius: 30.1932rpx;
	text-align: center;
	font-size: 26.57rpx;
	color: #838f9a;
	float: left;
	background: #e5ecf3;
	margin-left: 30.1932rpx;
	margin-bottom: 18.1159rpx;
}

.ul-5 li.on,
.ul-5 li:active {
	color: #fff;
	background: #3fa2ff;
}

.ul-6 {
	overflow: hidden;
	padding-left: 30.1932rpx;
}

.ul-6 li {
	float: left;
	height: 60.3864rpx;
	line-height: 60.3864rpx;
}

.ul-6 li:nth-child(2) {
	width: 24.1545rpx;
}

.ul-6 li:first-child,
.ul-6 li:last-child {
	width: 211.3526rpx;
	border-radius: 30.1932rpx;
	text-align: center;
	font-size: 26.57rpx;
	color: #838f9a;
	float: left;
	background: #eeeeee;
}

.ul-6 li.on,
.ul-6 li:active {
	color: #fff;
	background: #3fa2ff;
}

.cbqy-wrap {
	background: #fff;
	text-align: center;
	padding-bottom: 15rpx;
}

.cbqy-wrap .pinfo {
	color: #333333;
	font-size: 25.3623rpx;
	line-height: 42.2705rpx;
}

.cbqy-wrap .pinfo span {
	margin: 0 15rpx;
}

.cbqy-wrap .pinfo span em {
	color: #d74a4a;
}

.table-1 {
	border-spacing: 0;
	border-collapse: collapse;
	width: 100%;
}

.table-1 thead tr td {
	background: #cfe8ff;
}

.table-1 tr td {
	font-size: 25.3623rpx;
	color: #333333;
	height: 74.8792rpx;
	text-align: center;
}

.table-1 tbody tr td {
	border-bottom: 1px dashed #dddddd;
}

.table-1 thead tr td:first-child {
	border-top-left-radius: 9.0579rpx;
	border-bottom-left-radius: 9.0579rpx;
}

.table-1 thead tr td:last-child {
	border-top-right-radius: 9.0579rpx;
	border-bottom-right-radius: 9.0579rpx;
}

.zxjk-wrap {
	height: 250.6038rpx;
	margin: 0 18.1159rpx;
	border-radius: 12.6811rpx;
	background: #fff;
	overflow: hidden;
}


.table-2 {
	border-spacing: 0;
	border-collapse: collapse;
	width: 100%;
	margin-top: 18.1159rpx;
}

.table-2 thead tr td {
	font-size: 28.9854rpx;
	color: #424242;
}

.table-2 tr td {
	width: 33.3%;
	font-size: 25.3623rpx;
	color: #333333;
	height: 54.3478rpx;
	text-align: center;
	font-size: 36.2319rpx;
	color: #ff9c43;
}

.friendly-tips {
	text-align: right;
	padding: 0 18.1159rpx;
	font-size: 24.1545rpx;
	color: #a4a0a0;
	padding-top: 11.25rpx;
}

.ul-7 {
	display: flex;
	justify-content: space-between;
	margin-top: 30.1932rpx;
}

.ul-7 li {
	position: relative;
	min-width: 33.3%;
	color: #fff;
	font-size: 30.1932rpx;
}

.ul-7 li h4 {
	line-height: 2;
}

.ul-8 li {
	display: flex;
	height: 54.3478rpx;
}

.ul-8 li p {
	font-size: 27.7777rpx;
	color: #999999;
	line-height: 54.3478rpx;
	vertical-align: middle;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.ul-8 li p:first-child {
	display: flex;
	justify-content: space-between;
	width: 176.25rpx;
	color: #252525;
	margin-right: 72.4638rpx;
}

.ul-8 li:nth-child(1) img {
	margin-top: 18rpx;
	width: 23.5507rpx;
	height: 23.5507rpx;
	font-size: 0;
}

.ul-8 li:nth-child(2) img {
	margin-top: 18rpx;
	width: 22.343rpx;
	height: 23.5507rpx;
	font-size: 0;
}

.ul-8 li:nth-child(3) img {
	margin-top: 18rpx;
	width: 18.1159rpx;
	height: 23.5507rpx;
	font-size: 0;
}

.btn-spjk {
	width: 100%;
	height: 63.4058rpx;
	border: 1px solid #3fa2ff;
	border-radius: 9.0579rpx;
	font-size: 25.3623rpx;
	color: #3fa2ff;
	background: #fff;
	margin-top: 22.5rpx;
}

.btn-spjk:active {
	opacity: 0.85;
}

.tab-wrap-2 {
	height: 72.4638rpx;
	background: none;
}

.tab-wrap-2 .menu-tab {
	background: none;
}

.tab-wrap-2 .menu-tab li em {
	display: inline-block;
	color: #fff;
	min-width: 54.3478rpx;
	height: 28.9854rpx;
	line-height: 28.9854rpx;
	font-size: 20.5313rpx;
	border-radius: 14.4927rpx;
	margin-left: 7.5rpx;
	vertical-align: middle;
}

em.normal {
	background-color: #89d148;
}

em.pass {
	background-color: #e84040;
}

.dwxx-wrap {
	padding: 18.1159rpx;
	background: #fff;
}

.dwxx-wrap .htit {
	font-size: 28.9854rpx;
	color: #252525;
	padding-bottom: 24.1545rpx;
}

.dwpanel {
	position: relative;
	padding: 0 18.1159rpx;
	line-height: 60.3864rpx;
	display: flex;
	justify-content: space-between;
}

.dwpanel .dwtit {
	padding-left: 27.1739rpx;
	font-size: 26.57rpx;
	color: #1e1e1e;
	background: url('~@/static/online/images/icon_bf_hwt.png') left center no-repeat;
	background-size: 12.6811rpx 12.6811rpx;
}

.dwpanel em {
	font-size: 26.57rpx;
	color: #a5bed6;
	padding-left: 27.1739rpx;
	background: url('~@/static/online/images/icon_bf_hwts.png') left center no-repeat;
	background-size: 18.7197rpx 19.9274rpx;
}

.chart-wrap {
	text-align: center;
	padding: 30rpx 18.1159rpx;
}

.chart-wrap img {
	width: 668.4782rpx;
	height: 265.0966rpx;
}

