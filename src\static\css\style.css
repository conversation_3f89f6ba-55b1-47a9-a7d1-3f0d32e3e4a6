@charset "utf-8";

/** * mobile reset **/
html,
body,
div,
span,
ol,
ul,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
	margin: 0;
	padding: 0;
	border: 0;
	font: inherit;
	font-size: 100%;
	vertical-align: baseline;
}

ol,
ul {
	list-style: none;
}

input,
button,
textarea,
select {
	-webkit-appearance: none;
	appearance: none;
	box-sizing: border-box;
	border-radius: 0;
	padding: 0;
	margin: 0;
	border: none;
	outline: none;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
}

caption,
th,
td {
	font-weight: normal;
	vertical-align: middle;
}

q,
blockquote {
	quotes: none;
}

q:before,
q:after,
blockquote:before,
blockquote:after {
	content: "";
	content: none;
}

a img {
	border: none;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section,
summary {
	display: block;
}

a {
	text-decoration: none;
}

a:hover,
a:active {
	outline: none;
}

html {
	font-family: Helvetica, "STHeiti STXihei", "Microsoft JhengHei", "Microsoft YaHei", "Noto Sans CJK SC", "Source Han Sans CN"Tohoma, Arial;
	-webkit-text-size-adjust: 100%;
	-webkit-tap-highlight-color: transparent;
	height: 100%;
	overflow-x: hidden;
}

body {
	color: #333;
	background-color: #fff;
	-webkit-backface-visibility: hidden;
	/*line-height: 1;*/
	height: 100%;
	overflow: hidden;
}

/*common*/
.gap {
	height: 18.1159rpx;
}

.gap.h40 {
	height: 24.1545rpx;
}

.mask {
	position: fixed;
	left: 0;
	top: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, .4);
	z-index: 1000;
	display: none;
}

/*page*/
.header {
	background: #4874ff;
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	z-index: 999;
	height: 79.7101rpx;
}

.title {
	text-align: center;
	font-size: 32.6087rpx;
	color: #fff;
	line-height: 79.7101rpx;
}

.main {
	overflow-y: auto;
	overflow-x: hidden;
	-webkit-overflow-scrolling: touch;
	height: 100%;
	width: 100%;
}

.inner {
	padding: 79.7101rpx 0 90.5796rpx;
	width: 100%;
}

.pd-bg1a {
	background: url(~@/static/images/bg1.png) no-repeat;
	background-size: 100% 100%;
}

.pd-bg1a .header {
	background: transparent;
	padding: 90.5796rpx 36.2319rpx 0;
	height: auto;
	position: static;
}

.pd-bg1a .main {
	height: auto;
	overflow-y: hidden;
	padding: 84.541rpx 36.2319rpx 0;
}

.pd-aqibx1 {
	float: left;
	background: url(~@/static/images/aqibg2.png) no-repeat;
	width: 400rpx;
	height: 48.3091rpx;
	background-size: contain;
	font-size: 25.3623rpx;
	color: #fff;
	text-indent: 63.4058rpx;
	line-height: 48.3091rpx;
	position: relative;
	top: -52rpx;
}

.pd-usrname {
	position: relative;
	top: -48rpx;
	float: right;
	font-size: 30.1932rpx;
	color: #fff;
	background: url(~@/static/images/usric1.png) no-repeat left center;
	background-size: 35.0241rpx 35.0241rpx;
	padding-left: 48.3091rpx;
}

.pd-logo {
	display: block;
	width: 254.227rpx;
	height: 148.5507rpx;
	margin: 90.5796rpx auto 0;
}

.pd-srhbx1a {
	height: 72.4638rpx;
	border-radius: 9.0579rpx;
	background: rgba(255, 255, 255, .8);
	position: relative;
}

.pd-srhbx1a input {
	width: 100%;
	height: 72.4638rpx;
	line-height: 72.4638rpx;
	font-size: 27.1739rpx;
	color: #333;
	text-align: center;
	background: transparent;
}

.pd-srhbx1a input:focus+i {
	display: none;
}

.pd-srhbx1a i {
	position: absolute;
	left: 50%;
	top: 50%;
	background: url(~@/static/images/srhic1.png) no-repeat left center;
	font-size: 27.1739rpx;
	color: #999;
	background-size: 25.3623rpx 25.9662rpx;
	padding-left: 39.2511rpx;
	transform: translate(-50%, -50%);
	pointer-events: none;
}

.pd-ulbx1a {
	background: #fff;
	height: 158.2125rpx;
	border-radius: 9.0579rpx;
	display: flex;
	box-shadow: 0 0 27.7777rpx rgba(76, 112, 236, .04);
}

.pd-ulbx1a li {
	flex: 1;
	text-align: center;
}

.pd-ulbx1a li h1 {
	font-size: 42.2705rpx;
	padding-top: 24.1545rpx;
	color: #4874ff;
}

.pd-ulbx1a li p {
	font-size: 27.1739rpx;
	color: #666;
	padding-top: 12.0773rpx;
}

.pd-ulbx2a {
	display: flex;
	flex-wrap: wrap;
	background: #fff;
	box-shadow: 0 0 27.7777rpx rgba(76, 112, 236, .04);
	padding: 36.2319rpx 0;
}

.pd-ulbx2a li {
	width: 33.33%;
	padding: 36.2319rpx 0;
	position: relative;
}

.pd-ulbx2a li img {
	display: block;
	width: 57.3671rpx;
	height: 62.1981rpx;
	margin: 0 auto;
}

.pd-ulbx2a li p {
	font-size: 27.1739rpx;
	color: #333;
	text-align: center;
	padding-top: 18.1159rpx;
}

.pd-ulbx2a li sup {
	position: absolute;
	left: 66%;
	bottom: 72%;
	background: url(~@/static/images/supbg1.png) no-repeat;
	width: 55.5555rpx;
	height: 60.9903rpx;
	background-size: contain;
	text-align: center;
	line-height: 2.1;
	font-size: 26.57rpx;
	color: #fc5256;
}

.pd-backbtn {
	position: absolute;
	left: 0;
	top: 0;
	width: 92.9952rpx;
	height: 79.7101rpx;
	background: url(~@/static/images/backic.png) no-repeat center;
	background-size: 18.1159rpx 30.7971rpx;
}

.pd-dtl1 {
	position: absolute;
	right: 0;
	top: 0;
	line-height: 79.7101rpx;
	font-size: 32.6087rpx;
	color: #fff;
	padding-right: 36.2319rpx;
}

.pd-ullst1a {
	padding: 0 0 0 24rpx;
	background: #fff;
}

.pd-ullst1a li {
	padding: 30.1932rpx 36.2319rpx 30.1932rpx 0;
	border-bottom: 1px solid #f1f1f1;
	position: relative;
}

.pd-ullst1a li h1 {
	font-size: 30.7971rpx;
	color: #666;
}

.pd-ullst1a li p {
	font-size: 34.4202rpx;
	color: #333;
	padding-top: 18.1159rpx;
	line-height: 1.5;
	width: 75%;
}

.pd-dllst1a {
	padding: 0 0 0 36.2319rpx;
}

.pd-dllst1a dt span {
	font-size: 38.0434rpx;
	color: #333;
	position: relative;
}

.pd-dllst1a dt span:after {
	content: '';
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
	height: 8.454rpx;
	border-radius: 300px;
	background: linear-gradient(#d0deff, #4874ff);
}

.pd-dllst1a dd {
	padding: 30.1932rpx 36.2319rpx 30.1932rpx 0;
	overflow: hidden;
	border-bottom: 1px solid #f1f1f1;
}

.pd-dllst1a dd image {
	float: left;
	width: 64.0096rpx;
	height: 74.2753rpx;
	margin-right: 33.2125rpx;
	margin-top: 12.0773rpx;
}

.pd-dllst1a dd h1 {
	font-size: 30.7971rpx;
	color: #333;
}

.pd-dllst1a dd p {
	display: flex;
	justify-content: space-between;
	padding-top: 12.0773rpx;
}

.pd-dllst1a dd p em {
	font-size: 25.3623rpx;
	color: #aaa;
}

.pd-ulbtn1a {
	display: flex;
	position: relative;
}

.pd-ulbtn1a::after {
	content: '';
	position: absolute;
	left: 0;
	top: 100%;
	right: 0;
	height: 21.1353rpx;
	background: url(~@/static/images/botsd.png) repeat-x;
	background-size: 100% 21.1353rpx;
}

.pd-ulbtn1a li {
	flex: 1;
	text-align: center;
	height: 114rpx;
	line-height: 114rpx;
}

.pd-ulbtn1a li i {
	font-size: 32rpx;
	color: #4874ff;
	background-repeat: no-repeat;
	background-position: left center;
	padding: 20rpx 0 20rpx 64rpx;
	background-size: 45.8937rpx 40.4589rpx;
}

.pd-ulbtn1a li+li {
	border-left: 1px solid #f1f1f1;
}

.pd-ulbtn1a li i.iic1 {
	background-image: url(~@/static/images/iic1.png);
}

.pd-ulbtn1a li i.iic2 {
	background-image: url(~@/static/images/iic2.png);
}

.pd-ulbtn1a li i.iic3 {
	background-image: url(~@/static/images/iic3.png);
}

.pd-ultit {
	font-size: 41.6667rpx;
	color: #333;
	position: relative;
	padding-left: 35.0241rpx;
}

.pd-ultit::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	width: 12.0773rpx;
	height: 37.4396rpx;
	background: #4874ff;
	border-radius: 9.0579rpx;
	transform: translateY(-50%);
}

.pd-ullst1a li .btn {
	position: absolute;
	right: 36.2319rpx;
	top: 50%;
	width: 144.9275rpx;
	height: 54.9516rpx;
	background-repeat: no-repeat;
	background-position: center;
	background-size: contain;
	transform: translateY(-50%);
	border: none;
	background-color: transparent;
}

.pd-ullst1a li .btn.hj {
	background-image: url(~@/static/images/hjbtn.png);
}

.pd-ullst1a li .btn.dh {
	background-image: url(~@/static/images/dhbtn.png);
}

.pd-ulbx3a {
	display: flex;
	background: #fff;
}

.pd-ulbx3a li {
	width: 25%;
	padding: 30.1932rpx 0;
}

.pd-ulbx3a li img {
	display: block;
	width: 54.3478rpx;
	height: 53.7439rpx;
	margin: 0 auto;
}

.pd-ulbx3a li p {
	font-size: 27.1739rpx;
	text-align: center;
	padding-top: 18.1159rpx;
}

.pd-optbx1 {
	height: 111.7149rpx;
	border-bottom: 1px solid #ddd;
	display: flex;
	justify-content: space-between;
	padding: 0 36.2319rpx;
	align-items: center;
}

.pd-optbx1 em {
	font-size: 32.6087rpx;
	color: #333;
}

.pd-optbx1 i {
	font-size: 32.6087rpx;
	color: #666;
	background: url(~@/static/images/arwrt1.png) no-repeat right center;
	padding-right: 40.4589rpx;
	background-size: 18.1159rpx 30.1932rpx;
}

.pd-dlbx1a {
	border: 1px solid #ccc;
	background: #f8f8f8;
	padding: 0 30.1932rpx;
	margin: 0 36.2319rpx;
}

.pd-dlbx1a dt {
	font-size: 32.6087rpx;
	color: #333;
	padding-top: 24.1545rpx;
}

.pd-dlbx1a dd {
	padding-top: 24.1545rpx;
}

.pd-txtarea1 {
	width: 100%;
	height: 483.0918rpx;
	background: transparent;
	border: none;
	font-size: 30.7971rpx;
	color: #333;
}

.pd-txtarea1::-webkit-input-placehoder {
	color: #666;
}

.pd-botbtn1 {
	border-radius: 100rpx;
	background: #4874ff;
	font-size: 30.7971rpx;
	color: #fff;
	line-height: 99.6376rpx;
	text-align: center;
}

.header {
	background-color: #4874ff;
}

.title {
	color: #fff;
}

.zy-login {
	height: 100%;
	background: url(~@/static/images/login-top.png) center 24.1545rpx no-repeat, url(~@/static/images/login-bot.png) center bottom no-repeat;
	background-size: 100% auto, 100% auto;
	padding: 0 60.3864rpx;
	padding-top: 260rpx;
	box-sizing: border-box;
}

.login-logo {
	width: 92.9952rpx;
	margin: 0 auto;
}

.login-logo img {
	width: 100%;
}

.zy-login h1 {
	font-size: 48rpx;
	color: #333;
	line-height: 141.9082rpx;
	font-weight: 600;
	text-align: center;
}

.zy-form .item {
	position: relative;
	margin-bottom: 36.2319rpx;
}

.zy-form .item input {
	width: 100%;
	border: none;
	outline: none;
	background: none;
	height: 84.541rpx;
	border-bottom: 1px solid #dddddd;
	font-size: 30.7971rpx;
	color: #333;
	line-height: 84.541rpx;
	text-indent: 3.0193rpx;
}

.zy-form .item input::placeholder {
	color: #ccc;
}

.zy-form .item input:focus {
	border-color: #4874ff;
}

.zy-form .item .icons {
	position: absolute;
	right: 0;
	top: 0;
	z-index: 2;
	height: 100%;
	display: flex;
	align-items: center;
}

.zy-form .item .icons .clear {
	width: 33.8164rpx;
	height: 33.8164rpx;
	background: url(~@/static/images/ic-clear.png) no-repeat;
	background-size: 100%;
	margin-left: 24.1545rpx;
}

.zy-form .item .icons .open-eyes {
	width: 36.2319rpx;
	height: 24.1545rpx;
	background: url(~@/static/images/ic-open-eye.png) no-repeat;
	background-size: 100%;
	margin-left: 24.1545rpx;
}

.zy-form .item .icons .cls-eyes {
	width: 36.2319rpx;
	height: 16.9082rpx;
	background: url(~@/static/images/ic-cls-eye.png) no-repeat;
	background-size: 100%;
	margin-left: 24.1545rpx;
}

.zy-label {
	display: flex;
}

.zy-label input[type="checkbox"] {
	display: none;
}

.zy-label input[type="checkbox"]~i {
	width: 27.7777rpx;
	height: 27.7777rpx;
	background: url(~@/static/images/ic-nocheck.png) no-repeat;
	background-size: 100%;
}

.zy-label input[type="checkbox"]:checked~i {
	background-image: url(~@/static/images/ic-checked.png);
}

.zy-label span {
	font-size: 22.9468rpx;
	color: #666;
	margin-left: 18.1159rpx;
	line-height: 27.7777rpx;
}

.btn-login:disabled {
	background-color: #cccccc;
}

.btn-login {
	border: none;
	outline: none;
	background: none;
	display: block;
	margin-top: 66.4251rpx;
	width: 100%;
	border-radius: 99rpx;
	font-size: 30.7971rpx;
	color: #fff;
	background-color: #4874ff;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 99rpx;
}

.zy-quxiao {
	position: absolute;
	font-size: 32.6087rpx;
	color: #fefefe;
	left: 36.2319rpx;
	top: 0;
	height: 100%;
	line-height: 79.7101rpx;
}

.zy-queding {
	position: absolute;
	font-size: 32.6087rpx;
	color: #fefefe;
	right: 36.2319rpx;
	top: 0;
	height: 100%;
	line-height: 79.7101rpx;
}

.zy-bumen .wrap {
	padding: 0 24.1545rpx;
	background-color: #fff;
}

.zy-bumen .selected {
	display: flex;
	padding-top: 54.3478rpx;
	padding-bottom: 36.2319rpx;
}

.zy-bumen .selected p {
	position: relative;
	padding: 0 19.3236rpx;
	background-color: #eee;
	border-radius: 3.6231rpx;
	margin-right: 42.2705rpx;
}

.zy-bumen .selected p span {
	font-size: 28.9854rpx;
	color: #333;
	line-height: 51.3285rpx;
}

.zy-bumen .selected p .del {
	position: absolute;
	top: -16.3044rpx;
	right: -16.3044rpx;
	width: 32.6087rpx;
	height: 32.6087rpx;
	background: url(~@/static/images/ic-del.png) no-repeat;
	background-size: 100%;
}

.zy-search2 {
	position: relative;
}

.zy-search2 input {
	width: 100%;
	height: 90.5796rpx;
	box-sizing: border-box;
	border: 1px solid #eee;
	border-radius: 6.0386rpx;
	background-color: #fff;
	box-shadow: 0 0 14.4927rpx rgba(140, 140, 140, 0.1);
	font-size: 30.7971rpx;
	color: #333;
	line-height: 89.3719rpx;
	padding-left: 75.483rpx;
	background: url(~@/static/images/zy-search2.png) 31.401rpx center no-repeat;
	background-size: 28.3816rpx;
}

.zy-search2 .clear {
	position: absolute;
	top: 50%;
	right: 27.1739rpx;
	transform: translateY(-50%);
	width: 33.8164rpx;
	height: 33.8164rpx;
	background: url(~@/static/images/ic-clear.png) no-repeat;
	background-size: 100%;
	margin-left: 24.1545rpx;
}

.zy-bread {
	height: 111.7149rpx;
	width: 100%;
	overflow-x: auto;
	white-space: nowrap;
}

.zy-bread span {
	display: inline-block;
	font-size: 30.7971rpx;
	color: #333;
	padding-right: 33.2125rpx;
	background: url(~@/static/images/arrow-right.png) right center no-repeat;
	background-size: 12.0773rpx;
	margin-right: 18.1159rpx;
	line-height: 111.7149rpx;
}

.zy-bread span:last-child {
	margin-right: 0;
	padding-right: 0;
	color: #4874ff;
}

.zy-bumen .list {
	background-color: #fff;
	padding-left: 24.1545rpx;
	padding-bottom: 12.0773rpx;
}

.zy-bumen .list li {
	height: 96.6183rpx;
	display: flex;
	align-items: center;
	border-bottom: 1px solid #ddd;
}

.zy-bumen .list li label span {
	font-size: 30.7971rpx;
	color: #333;
}

.border-top {
	border-top: 1px solid #ddd;
	margin-top: 24.1545rpx;
}

.zy-tabs1 {
	height: 99.0338rpx;
	display: flex;
	border-bottom: 1px solid #f4f4f4;
}

.zy-tabs1 span {
	flex: 1;
	font-size: 32.6087rpx;
	color: #666;
	line-height: 99.0338rpx;
	text-align: center;
	position: relative;
}

.zy-tabs1 span.cur {
	color: #4874ff;
}

.zy-tabs1 span.cur::after {
	content: "";
	position: absolute;
	bottom: -4%;
	left: 50%;
	transform: translateX(-50%);
	width: 60.3864rpx;
	height: 6.0386rpx;
	border-radius: 3.6231rpx;
	background-color: #4874ff;
}

.zy-list2 {}

.zy-list2 li {
	padding-left: 147.3429rpx;
	position: relative;
	background: url(~@/static/images/list2-li.png) 36.2319rpx center no-repeat;
	background-size: 90.5796rpx auto;
}

.zy-list2 li .wrapper {
	padding-top: 30.1932rpx;
	border-bottom: 1px solid #f1f1f1;
	padding-bottom: 24.1545rpx;
	background: url(~@/static/images/li-right-arrow.png) 96% center no-repeat;
	background-size: 18.1159rpx auto;
}

.zy-list2 li .line1 {
	display: flex;
	height: 51.9324rpx;
	align-items: center;
}

.zy-list2 li .line1 .til {
	font-size: 30.7971rpx;
	color: #333;
	line-height: 51.9324rpx;
	max-width: 332.1255rpx;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
	font-weight: 900;
}

.zy-list2 li .line1 .type {
	font-size: 23.5507rpx;
	color: #4874ff;
	height: 41.0627rpx;
	line-height: 41.0627rpx;
	padding: 0 19.9274rpx;
	border-radius: 20.5313rpx;
	background-color: #ebf1ff;
	margin-left: 18.1159rpx;
}

.zy-list2 li .line2 {
	display: flex;
}

.zy-list2 li .line2 p {
	font-size: 26rpx;
	color: #aaa;
	line-height: 51.9324rpx;
}

.zy-list2 li .line2 .local {
	padding-left: 30.1932rpx;
	background: url(~@/static/images/ic-local.png) 0 center no-repeat;
	background-size: 18.1159rpx;
	margin-left: 90.5796rpx;
}

.zy-list2 li .line3 {
	font-size: 26rpx;
	color: #ff8d47;
	line-height: 51.9324rpx;
}

.zy-list3 .li1 {
	background-image: url(~@/static/images/list2-li2.png);
}

.zy-list3 .li2 {
	background-image: url(~@/static/images/list2-li3.png);
}

.search2-wrap {
	padding: 24.1545rpx;
}

.ic-dunpai {
	position: absolute;
	right: 36.2319rpx;
	top: 50%;
	transform: translateY(-50%);
	width: 33.8164rpx;
	height: 36.2319rpx;
	background: url(~@/static/images/ic-dunpai.png) no-repeat;
	background-size: 100%;
}

.pd-botbtn2 {
	height: 99.6376rpx;
	position: absolute;
	left: 36.2319rpx;
	right: 36.2319rpx;
	bottom: 63.4058rpx;
	border-radius: 89.0579rpx;
	background: #4874ff;
	font-size: 30.7971rpx;
	color: #fff;
	line-height: 99.6376rpx;
	text-align: center;
}

.pd-dlbx2a {
	padding: 0 24.1545rpx;
}

.pd-dlbx2a dt {
	overflow: hidden;
	padding-top: 24.1545rpx;
}

.pd-dlbx2a dt image {
	float: left;
	width: 185.9903rpx;
	height: 139.4927rpx;

}

.pd-dlbx2a dt h1 {
	font-size: 30.1932rpx;
	color: #333;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}

.pd-dlbx2a dt p {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-top: 36rpx;
}

.pd-dlbx2a dt p span {
	font-size: 24.1545rpx;
	color: #aaa;
	line-height: 1.5;
}

.pd-dlbx2a dt p em {
	font-size: 0;
}

.pd-dlbx2a dt p em i {
	background-repeat: no-repeat;
	background-position: center;
	background-size: 32.6087rpx 31.401rpx;
	width: 32.6087rpx;
	height: 31.401rpx;
	display: inline-block;
	position: relative;
}

.pd-dlbx2a dt p em i+i:before {
	content: '';
	position: absolute;
	left: -30.1932rpx;
	top: 0;
	width: 1px;
	background: #eee;
	height: 31.401rpx;
}

.pd-dlbx2a dt p em i+i {
	margin-left: 60.3864rpx;
}

.pd-dlbx2a dt p em i.ext {
	background-image: url(@/static/images/extic1.png);
}

.pd-dlbx2a dt p em i.del {
	background-image: url(@/static/images/deleic1.png);
}

.pd-dlbx2a dd {
	padding: 12.0773rpx 0;
}

.pd-dlbx2a dd p {
	font-size: 27.1739rpx;
	color: #666;
	line-height: 1.8;
}

.pd-dlbx2a dd p em {
	color: #4874ff;
}

.pd-dlbx2a dd+dd {
	border-top: 1px solid #f1f1f1;
	padding: 0;
}

.pd-dlbx3a dt image {
	display: block;
	width: 202.8985rpx;
	height: 185.9903rpx;
	margin: 0 auto;
}

.pd-dlbx3a dd {
	display: flex;
	background: #fafafa;
	width: 513.285rpx;
	height: 157.0048rpx;
	margin: 30.1932rpx auto 60.3864rpx;
	align-items: center;
}

.pd-dlbx3a dd p {
	flex: 1;
	position: relative;
	text-align: center;
}

.pd-dlbx3a dd p em {
	font-size: 34.4202rpx;
	font-weight: 600;
	color: #333;
	display: block;
}

.pd-dlbx3a dd p i {
	display: block;
	font-size: 27.1739rpx;
	color: #aaa;
	padding-top: 18.1159rpx;
}

.pd-dlbx3a dd p+p:before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	width: 1px;
	height: 36.2319rpx;
	background: #f1f1f1;
	transform: translateY(-50%);
}

.pd-dlbx3a .zy-btn1 {
	width: 471.0144rpx;
	display: block;
	margin: 0 auto;
}

.pd-dlbx4a {
	background: #fff;
	padding-top: 18.1159rpx;
	border-top: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
}

.pd-dlbx4a dt {
	font-size: 30.7971rpx;
	color: #333;
	background-repeat: no-repeat;
	background-position: left center;
	background-size: 30.1932rpx 32.6087rpx;
	padding-left: 48.3091rpx;
	/* margin: 0 36.2319rpx; */
	margin-left: 36rpx;
}

.pd-dlbx4a dt+dt {
	margin-top: 18.1159rpx;
}

.pd-dlbx4a dt i {
	color: #666;
}

.pd-dlbx4a dt em {
	color: #4874ff;
	font-size: 23.5507rpx;
	background: #ebf1ff;
	padding: 6.0386rpx 24.1545rpx;
	border-radius: 300px;
}

.pd-dlbx4a dt.dtic1 {
	background-image: url(@/static/images/dtic1.png);
}

.pd-dlbx4a dt.dtic2 {
	background-image: url(@/static/images/dtic2.png);
}

.pd-dlbx4a dt.dtic3 {
	background-image: url(@/static/images/dtic3.png);
}

.pd-dlbx4a dd {
	padding: 0 24.1545rpx 0 84.541rpx;
}

.pd-dlbx4a dd p {
	font-size: 30.7971rpx;
	color: #666;
	line-height: 1.5;
	padding-top: 9.0579rpx;
}

.pd-dlbx4a dd.dtl {
	line-height: 90.5796rpx;
	text-align: center;
	border-top: 1px solid #ddd;
}

.pd-dlbx4a dd.dtl a {
	font-size: 30.7971rpx;
	color: #4874ff;
	background: url(@/static/images/arwrt2.png) no-repeat right center;
	background-size: 13.285rpx 21.7391rpx;
	padding-right: 25.3623rpx;
}

.pd-dlbx5a {
	margin-left: 24.1545rpx;
	position: relative;
	padding: 2rpx 0 28rpx 0;
}

.pd-dlbx5a:before {

	content: '';
	position: absolute;
	left: 12.0773rpx;
	top: 36rpx;
	bottom: 1%;
	width: 1px;
	background: #ccc;
}

.pd-dlbx5a dt {
	position: relative;
	padding-left: 42.2705rpx;
}

.pd-dlbx5a dt~dt {
	margin-top: 48.3091rpx;
}

.pd-dlbx5a dt:before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	width: 24.1545rpx;
	height: 24.1545rpx;
	border-radius: 50%;
	background: #ccc;
	margin-top: -12.0774rpx;
}

.pd-dlbx5a dt em {
	font-size: 30.7971rpx;
	color: #333;
	position: relative;
}

.pd-dlbx5a dt i {
	font-size: 28.9854rpx;
	color: #aaa;
	padding-left: 24.1545rpx;
}

.pd-dlbx5a dd {
	font-size: 28.9854rpx;
	color: #333;
	padding-top: 12.0773rpx;
	padding-left: 42.2705rpx;
}

.pd-dlbx5a dd+dd {
	font-size: 25.3623rpx;
	color: #999;
}

.list-show-info {
	width: 100%;
	color: #aaa;
	font-size: 26rpx;
	display: flex;
	justify-content: center;
	padding-top: 8rpx;
}

.form-print-btn {
	width: 100rpx;
	position: absolute;
	right: 0;
	bottom: 55rpx;
}

.printimage {
	width: 32rpx;
	height: 32rpx;
	margin-right: 6rpx;
}

/* 任务地图页面样式 */
.marker-content {
	background: #fff;
	display: flex;
	flex-direction: row;
	position: relative;
	border-radius: 12rpx;
	box-shadow: 0 0 12rpx rgb(0 0 0 / 10%);
	height: 108.75rpx;
	align-items: center;
}

.marker-content.mine {
	height: 55rpx;
}

.marker-content::after {
	content: "";
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	bottom: -30rpx;
	border: 18rpx solid transparent;
	border-color: #fff transparent transparent transparent;
}

.marker-left {
	max-width: 350.25rpx;
	padding: 0 24rpx;
}

.marker-left div {
	font-size: 27rpx;
	color: #333;
	line-height: 45rpx;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
}

.marker-right {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
	color: #4874ff;
	box-shadow: 0 0 12rpx rgb(0 0 0 / 10%);
	height: 100%;
}

.marker-right span {
	font-size: 29.25rpx;
	color: #4874ff;
	line-height: 45rpx;
	text-align: center;
}

.marker-main {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

.marker-content .mine {
	width: 160rpx;
	text-align: center;
}

.marker-main img {
	width: 80rpx;
	margin-top: 18rpx;
}

/* 任务详情执法记录样式修改 */
.task-detail-info .pd-dlbx2a.bg2 {
	background-image: url(@/static/images/bxg2a.png);
}

.task-detail-info .pd-dlbx2a.bg1 {
	background-image: url(@/static/images/bxg1a.png);
}

.task-detail-info .pd-dlbx2a {
	background-repeat: no-repeat;
	background-position: center;
	background-size: 100% 100%;
	width: 650.25rpx;
	height: 380.25rpx;
	filter: drop-shadow(8.25rpx 11.25rpx 30.7499rpx rgba(185, 185, 185, .29));
	margin: 0 auto;
}

.task-detail-info .pd-dlbx2a dt {
	font-size: 38.25rpx;
	color: #4874ff;
	padding: 36rpx 36rpx 21rpx;
	font-weight: bold;
}

.pd-ulbx4a {
	display: flex;
	flex-wrap: wrap;
	padding: 0 36rpx;
}

.pd-ulbx4a li {
	width: 50%;
}

.pd-ulbx4a li h1 {
	padding: 15rpx 0;
}

.pd-ulbx4a li h1 em {
	font-size: 29.25rpx;
	color: #333;
	background-repeat: no-repeat;
	background-position: left center;
	background-size: 36rpx;
	padding-left: 53.25rpx;
	vertical-align: middle;
}

.pd-ulbx4a li h1 i {
	font-size: 36rpx;
	color: #333;
	font-family: "DIN-Medium";
	vertical-align: middle;
	margin-left: 15rpx;
}

.pd-ulbx4a li h1 em.aic1 {
	background-image: url(@/static/images/aic1-1.png);
}

.pd-ulbx4a li h1 em.aic2 {
	background-image: url(@/static/images/aic2-1.png);
}

.pd-ulbx4a li h1 em.aic3 {
	background-image: url(@/static/images/aic3.png);
}

.pd-ulbx4a li h1 em.aic4 {
	background-image: url(@/static/images/aic4.png);
}

.pd-ulbx4a li h1 em.aic5 {
	background-image: url(@/static/images/aic5.png);
}

.pd-ulbx4a li p {
	font-size: 27rpx;
	color: #666;
	padding-left: 53.25rpx;
}

.pd-ulbx4a li p+p {
	padding-top: 18rpx;
}
