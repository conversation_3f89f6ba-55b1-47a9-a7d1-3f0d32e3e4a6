.zd13-alert {
    position: absolute;
    left: 0;
    top: 0px;
    right: 0;
    bottom: 0px;
    margin: auto;
    width: 100%;
    height: auto;
    /* transform: translateX(-50%) translateY(-50%); */
    background: #fff;
    z-index: 2001;
}

.zd13-alert .hd {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 50px;
    background: #5391f7;
    padding: 0 20px;
}

.zd13-alert .hd .til {
    font-size: 18px;
    color: #fff;
    font-weight: bold;
}

.zd13-alert .hd .close {
    width: 14px;
    height: 14px;
    background: url(../images/zd13_close.png) 0 0 no-repeat;
    cursor: pointer;
}

.zd13-alert .bd {
    position: absolute;
    top: 0px;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 20px;
}

.zd13-tabs1 {
    display: flex;
    align-items: center;
}

.zd13-tabs1 span {
    background-color: #fff;
    font-weight: normal;
    color: #666;
    min-width: 96px;
    padding: 0 5px;
    height: 34px;
    box-sizing: border-box;
    border-radius: 3px;
    font-size: 16px;
    border: 1px solid #ddd;
    font-weight: bold;
    text-align: center;
    line-height: 32px;
    margin-right: 10px;
    cursor: pointer;
}

.zd13-tabs1 span.on {
    border: 1px solid #5391f7;
    background-color: #5391f7;
    color: #fff;

}

.zd13-add1 {
    width: 96px;
    height: 34px;
    box-sizing: border-box;
    border-radius: 3px;
    background: #5391f7 url(../images/zd13_add.png) 20px center no-repeat;
    font-size: 16px;
    color: #fff;
    padding-left: 45px;
    line-height: 34px;
    margin-right: 10px;
    cursor: pointer;
}

.zd13-board1 {
    display: flex;
    height: calc(100% - 54px);
}

.zd13-board1 .lp {
    flex: 1;
    height: 100%;
    background-color: #f6f7f8;
    box-sizing: border-box;
    padding: 15px;
    position: relative;
    border: 1px solid #eeeeee;
    border-radius: 3px;
}

.zd13-board1 .rp {
    margin-left: 20px;
    width: 320px;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.zd13-pmtu {}

.zd13-pmtu .tu {}

.zd13-shebei {
    position: absolute;
    top: 32%;
    left: 20%;
}

.zd13-shebei .dot {
    display: block;
}

.zd13-shebei .info {
    position: absolute;
    top: -2px;
    left: 38px;
}

.zd13-shebei .info .zi {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding-left: 25px;
    padding-top: 13px;
}

.zd13-shebei .info .zi p {
    font-size: 16px;
    color: #fff;
    line-height: 24px;
    font-weight: bold;
}

.zd13-caozuo {
    display: flex;
    justify-content: center;
    margin: 20px 0;
}

.zd13-caozuo button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 96px;
    height: 34px;
    box-sizing: border-box;
    border-radius: 3px;
    background: #5391f7;
    font-size: 16px;
    color: #fff;
    line-height: 34px;
    margin: 0 5px;
    cursor: pointer;
}

.zd13-caozuo button img {
    margin-right: 10px;
}

.zd13-data1 {
    position: absolute;
    top: 25px;
    right: 25px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    background-color: #fff;
    box-sizing: border-box;
    display: flex;
    z-index: 9;
}

.zd13-data1.off {}

.zd13-data1 .arrow {
    cursor: pointer;
    width: 28px;
    height: 100px;
    background: url(../images/zd13_toggle_arrow.png) center no-repeat;
}

.zd13-data1.off .arrow {
    transform: rotate(180deg);
}

.zd13-data1 .status {
    width: 120px;
    height: 100px;
    display: flex;
    padding-right: 10px;
    flex-direction: column;
    justify-content: center;
}

.zd13-data1.off .status {
    display: none;
}

.zd13-data1 .status p {
    font-size: 16px;
    color: #333;
    line-height: 40px;
    background: url(../images/zd13_toggle_off.png) right center no-repeat;
    cursor: pointer;
}

.zd13-data1 .status p.on {
    background-image: url(../images/zd13_toggle_on.png);
}

.zd13-til1 {
    font-size: 18px;
    color: #333;
    font-weight: bold;
    padding-left: 10px;
    position: relative;
    line-height: 50px;
    margin-left: 20px;
}

.zd13-til1::after {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #5391f7;
}

.zd13-data2 {
    height: calc(100% - 200px);
    overflow: auto;
}

.zd13-data2 li {
    border-top: 1px solid #ddd;
    padding: 0 20px;
    padding-bottom: 20px;
}

.zd13-data2 li .p1 {
    font-size: 16px;
    color: #e98b1c;
    line-height: 40px;
    font-weight: bold;
    text-align: center;
}

.zd13-data2 li .item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    padding: 0 10px;
    border-radius: 3px;
}

.zd13-data2 li .item.on {
    background-color: #e9f3ff;
}

.zd13-data2 li .item p {
    font-size: 16px;
    color: #e98b1c;
    line-height: 40px;
}

.zd13-data2 li .item i {}

.zd13-data2 li.green {}

.zd13-data2 li.green .item p,
.zd13-data2 li.green .p1 {
    color: #20c520;
}

.zd13-data3 {
    height: 105px;
    margin: 30px 20px;
    box-shadow: 0 0 5px rgb(0 0 0 / 10%);
    border-radius: 3px;
    background-color: #fff;
    padding-top: 10px;
    box-sizing: border-box;
}

.zd13-data3 li {
    display: flex;
    padding-left: 16px;
}

.zd13-data3 li span {
    font-size: 16px;
    color: #333;
    line-height: 40px;
}

.zd13-data3 li .s1 {
    width: 180px;
}

.zd13-data3 li .s2 {}

.zd13-data3 li em {
    color: #5391f7;
}

.zd13-addfile {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
    width: 750px;
    height: 340px;
}

.zd13-addfile .anniu {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 150px;
    height: 42px;
    box-sizing: border-box;
    border-radius: 3px;
    background: #5391f7;
    font-size: 18px;
    color: #fff;
    line-height: 34px;
    margin: 0 auto;
    cursor: pointer;
}

.zd13-addfile .anniu img {
    margin-right: 10px;
}

.zd13-addfile .shuoming {
    margin-top: 50px;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 5px 20px;
}

.zd13-addfile .shuoming li {}

.zd13-addfile .shuoming li .zd13-til1 {
    margin-left: 0;
}

.zd13-addfile .shuoming li p.p1 {
    font-size: 18px;
    color: #333;
    line-height: 36px;
}

.zd13-data4 {
    display: flex;
    height: 374px;
}

.zd13-data4 .lp {
    background-color: #edf0f3;
    height: 100%;
    width: 700px;
    position: relative;
}

.zd13-data4 .r-t {
    position: absolute;
    top: 12px;
    right: 20px;
    display: flex;
    align-items: center;
}

.zd13-data4 .tuli {
    position: absolute;
    right: 20px;
    bottom: 15px;
}

.zd13-data4 .tuli p {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #333;
    line-height: 28px;
}

.zd13-data4 .tuli p img {
    margin-right: 10px;
}

.zd13-status {
    display: flex;
}

.zd13-status p {
    font-size: 16px;
    color: #333;
    line-height: 40px;
    background: url(../images/zd13_toggle_off.png) right center no-repeat;
    padding-right: 55px;
    margin-left: 20px;
    cursor: pointer;
}

.zd13-status p.on {
    background-image: url(../images/zd13_toggle_on.png);
}

.zd13-fangda {
    margin-left: 20px;
    cursor: pointer;
}

.zd13-options {
    /* display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-left: 20px; */
}

.zd13-options li {
    width: 190px;
    height: 118px;
    box-sizing: border-box;
    border: 2px solid #ddd;
    position: relative;
    cursor: pointer;
}

.zd13-options li img {
    width: 100%;
    height: 100%;
}

.zd13-options li p {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 32px;
    background-color: rgba(0, 0, 0, 0.5);
    font-size: 16px;
    color: #fff;
    font-weight: bold;
    text-align: center;
    line-height: 32px;
}

.zd13-options li.on {
    border-color: #3993ff;
    border-width: 2px;
}

.zd13-options li.on p {
    background-color: rgba(0, 135, 255, 0.7);
}

.zd13-options li.on::after {
    content: "";
    position: absolute;
    left: -8px;
    top: 17px;
    transform: rotate(-135deg);
    width: 10px;
    height: 10px;
    border: 2px solid transparent;
    border-color: #3993ff #3993ff transparent transparent;
    background-color: #fff;
}

.zd13-more {
    position: relative;
    display: inline-block;
    z-index: 2;
}

.zd13-more i {
    font-size: 14px;
    color: #3993ff;
    line-height: 28px;
}

.zd13-more .youla {
    position: absolute;
    top: -6px;
    left: calc(100% + 15px);
    padding: 0 5px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}

.zd13-more .youla::after {
    content: "";
    position: absolute;
    left: -4px;
    top: 14px;
    width: 10px;
    height: 10px;
    background-color: #fff;
    transform: rotate(45deg);
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
    z-index: -1;
}

.zd13-more .youla::before {
    content: "";
    position: absolute;
    left: -4px;
    top: 14px;
    width: 10px;
    height: 10px;
    background-color: #fff;
    transform: rotate(45deg);
    z-index: 1;
}

.zd13-more .youla p {
    font-size: 16px;
    color: #333;
    line-height: 48px;
    padding: 0 7px;
    text-align: center;
    white-space: nowrap;
    cursor: pointer;
}

.zd13-more .youla p+p {
    border-top: 1px solid #ddd;
}

.zd13-more .youla p.on {
    color: #3993ff;
}

.flowChart {
    width: 100%;
    height: 100%;
    z-index: 1;
}