@charset "utf-8";

/* -- flex布局-- */
.flx1 {
    display: flex;
}

.flx1.ac {
    align-items: center;
}

.flx1.jc {
    justify-content: center;
}

.flx1.jb {
    justify-content: space-between;
}

.flx1.ja {
    justify-content: space-around;
}

.flx1.start {
    justify-content: flex-start;
}

.flx1.end {
    justify-content: flex-end;
}

.flx1.ev {
    justify-content: space-evenly;
}

.edit-ic {
    width: 31.94445rpx;
    height: auto;
    display: inline-block;
}

.pd-ullst1.pd-ullst2>li em {
    width: 40%;
}

input::-webkit-input-placeholder {
    /* WebKit, Blink, Edge */
    color: #bbb;
}

:-moz-placeholder {
    /* Mozilla Firefox 4 to 18 */
    color: #bbb;
}

::-moz-placeholder {
    /* Mozilla Firefox 19+ */
    color: #bbb;
}

input:-ms-input-placeholder {
    /* Internet Explorer 10-11 */
    color: #bbb;
}

input::-ms-input-placeholder {
    /* Microsoft Edge */
    color: #bbb;
}

.upstaus {
    margin: 0 4%;
    height: 90.2778rpx;
    line-height: 90.2778rpx;
    text-align: center;
    color: #fff;
    font-size: 31.94445rpx;
    background-color: #2b6cf9;
    border-radius: 5.55555rpx;
    cursor: pointer;
    font-weight: bold;
}

.xqmod2 {
    background-color: #fff;
    padding-bottom: 34.722225rpx;
}

.updatabox {
    background-color: #fff;
}

.data-row1 {
    padding: 30rpx 33.3333rpx 0;
}

.data-row1 .f1 {
    font-size: 31.94445rpx;
    color: #333;
    width: 202.5rpx;
}

.data-row1 .f2 {
    font-size: 31.94445rpx;
    color: #333;
}

.zst1 {
    font-size: 29.166675rpx;
    color: #4874ff;
    line-height: 45.1389rpx;
    background-color: #dae3ff;
    padding: 24.3055499rpx 33.3333rpx;
}

.updatabox {
    padding-bottom: 41.25rpx;
}

.pd-ullst1.pd-ullst2>li em.zmzl {
    padding-left: 0;
}

.pd-ulpic2 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
}

.pd-ulpic2>li {
    width: 31%;
    height: 138.8889rpx;
    background-color: #dddddd;
    border-radius: 11.1111rpx;
}

.navtab1.navtab2 {
    justify-content: space-between;
    padding: 0 15%;
}

.rt-arr1 {
    width: 13.888875rpx;
    height: 25.6944749rpx;
    margin-left: 15rpx;
}

.sm-ic {
    width: 38.8889249rpx;
    height: 38.8889249rpx;
    display: inline-block;
}

.why-ic {
    width: 31.94445rpx;
    height: 31.94445rpx;
    display: inline-block;
    vertical-align: -10%;
}

.pd-rdo1 label input[type="radio"] {
    background: url(~@/static/equipmentMaintenance/images/qiye-radio-no.png) no-repeat center center;
    background-size: 100% 100%;
    width: 29.166675rpx;
    height: 29.166675rpx;
    margin-right: 13.888875rpx;
}

.pd-rdo1 label input[type="radio"]:checked {
    background: url(~@/static/equipmentMaintenance/images/qiye-radio-yes.png) no-repeat center center;
    background-size: 100% 100%;
}

.pd-rdo1 label {
    font-size: 29.166675rpx;
    color: #333;
    height: 41.6667rpx;
    display: flex;
    align-items: center;
}

.pd-rdo1 label+label {
    margin-left: 90.2778rpx;
}

.foot-btn {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 20.83335rpx;
}

.borb {
    border-bottom: 1px solid #ededed;
}

.a-alert1 {
    position: absolute;
    left: 34.722225rpx;
    right: 34.722225rpx;
    top: 0;
    bottom: 0;
    margin: auto;
    height: 541.66665rpx;
    background: url(~@/static/equipmentMaintenance/images/alerbg1.png) no-repeat center center;
    background-size: 100% 100%;
    border-radius: 11.1111rpx;
    z-index: 1000;

}

.a-alert1 .tit1 {
    font-size: 31.94445rpx;
    font-weight: bold;
    color: #333333;
    text-align: center;
    padding: 34.722225rpx 0 41.6667rpx;
}

.a-alert1 .bd {
    padding: 0 27.77775rpx;
}

.a-alert1 .bd .f1 {
    font-size: 29.166675rpx;
    color: #666666;
}

.pslist {
    display: grid;
    grid-template-columns: auto auto auto;
    gap: 13.888875rpx;
}

.pslist li {
    height: 138.8889rpx;
}

.pslist li img {
    width: 100%;
    height: 100%;
    display: block;
}

.pztit2 {
    font-size: 31.94445rpx;
    padding: 15rpx 31.250025rpx;
    padding-left: 37.5rpx;
    font-weight: bold;
    position: relative;
}

.pztit2::before {
    display: inline-block;
    width: 13.888875rpx;
    height: 13.888875rpx;
    background-color: #5c73ff;
    border-radius: 50%;
    content: "";
    position: absolute;
    top: 46%;
    left: 0;
}

.smul li {
    font-size: 29.166675rpx;
    color: #666;
    line-height: 58.33335rpx;
}

.a-close1 {
    width: 55.555575rpx;
    height: 55.555575rpx;
    display: inline-block;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    position: absolute;
    bottom: -15%;

}

.top-page {
    height: 100%;
    background: #f5f5f5 url(~@/static/equipmentMaintenance/images/bjtopbg1.png) 0 0 no-repeat;
    background-size: 100% auto;
    box-sizing: border-box;
}
.topbox2{
    position: relative;
}
.ic-back2 {
    position: absolute;
    left: 30.1932rpx;
    top: 53%;
    transform: translateY(-50%);
    width: 21.7391249rpx;
    height: 36.2319rpx;
    background: url(~@/static/equipmentMaintenance/images/backic.png) 0 0 no-repeat;
    background-size: 100%;
  }
.idx-inner {
    padding: 15rpx 33.3333rpx 0;
}

.znsbbg {
    height: 83.333325rpx;
    line-height: 83.333325rpx;
    background: url(~@/static/equipmentMaintenance/images/ggbg1.png) center center no-repeat;
    background-size: 100% 100%;
    padding-left: 222.222225rpx;
    box-sizing: border-box;
    color: #4d863d;
    font-size: 24.999975rpx;
    position: relative;
}

.online {
    position: absolute;
    right: 27.77775rpx;
    top: 0;
    font-size: 24.999975rpx;
    color: #4d863d;
    padding-left: 33.75rpx;
    background: url(~@/static/equipmentMaintenance/images/online.png) 0 center no-repeat;
    background-size: 16.66665rpx 16.66665rpx;
}

.picbox {
    width: 100%;
    height: 361.111125rpx;
    border-radius: 11.1111rpx;
}

.pic1 {
    width: 100%;
    height: 361.111125rpx;
    display: block;
}

.bannerbox {
    width: 100%;
    height: 435rpx;
    position: relative;
}

.swiper-container,
.swiper-wrapper,
.swiper-slide {
    width: 100%;
    height: 100%;
}

.swiper-pagination-bullet {
    width: 41.6667rpx;
    height: 13.888875rpx;
    border-radius: 6.944475rpx;
    opacity: 1;
    background: #ddd;
}

.swiper-pagination-bullet-active {
    width: 69.44445rpx;
    opacity: 1;
    background: #fff;
    border: solid 1px #ddd;
}

.jcul {
    width: 100%;
    display: grid;
    grid-template-columns: auto auto;
    gap: 27.77775rpx;
}

.jcul li {
    height: 180.555525rpx;
    border-radius: 11.1111rpx;
    background-color: #e1e9f6;
    text-align: center;
}

.jcul li .f1 {
    font-size: 31.94445rpx;
    color: #3580ff;
    padding-top: 41.6667rpx;
    font-family: "DIN-Bold";

}

.jcul li .f2 {
    padding-top: 27.77775rpx;
    font-size: 29.166675rpx;
    color: #333;
}

.gap40 {
    height: 30rpx;
}

.fresh-ic {
    width: 38.8889249rpx;
    height: 33.3333rpx;
    display: inline-block;
    margin-right: 27.77775rpx;
}

.add-ic {
    width: 34.722225rpx;
    height: 34.722225rpx;
    display: inline-block;
}

.fwb {
    font-weight: bold;
}

.rgtx-alert1 {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 69.44445rpx 69.44445rpx 0 0;
    background: #fff;
    z-index: 1000;
}

.rgtx-alert1 {
    font-size: 31.94445rpx;
    color: #333;
    line-height: 83.333325rpx;
}

.rgtx-tit {
    font-size: 31.94445rpx;
    color: #333;
    line-height: 83.333325rpx;
    font-weight: bold;
    padding-top: 30rpx;
    margin-left: 15rpx;
}

.a-line {
    padding: 0 33.3333rpx;
}

.a-close2 {
    width: 34.0278rpx;
    height: 34.0278rpx;
    display: inline-block;
    background: url(~@/static/equipmentMaintenance/images/qiye-close.png);
    background-size: 100%;
}

.pd-ullst1.pd-ullst3>li {
    padding: 15rpx 33.3333rpx 15rpx 0;
}

.pd-rdo1.pd-rdo2 label+label {
    margin-left: 18.75rpx;
}

.barwrap {
    flex: 1;
    height: 8.333325rpx;
    border-radius: 4.1667rpx;
    background-color: #eee;
    position: relative;
    margin-top: 6%;
    margin-left: 69.44445rpx;
}

.barwrap .bar {
    border-radius: 4.1667rpx;
    height: 100%;
    background-color: #2b6cf9;
    position: relative;
}

.barwrap .bar::after {
    content: "";
    position: absolute;
    right: -10%;
    top: -215%;
    width: 50.000025rpx;
    height: 50.000025rpx;
    background: url(~@/static/equipmentMaintenance/images/bar-dot.png) 0 0 no-repeat;
    background-size: 100% 100%;
}
.pd-ullst1.pd-ullst3{
   padding-bottom: 152.777775rpx;
}