<template>
	<div style="width:100%">
		<view>添加签名</view>
		<view class="signature-bg">
			<view class="signature-bg-list">
				<text v-for="(items,index) in showList"
				      :key="items.id"
				      class="signature-bg-all">
					<image :src="items.src"
					       class="signature-bg-imglist">
					</image>
					<image class="signature-bg-deleteimg"
					       @click="deleteSignature(index)"
					       :src="deleteimg"></image>
				</text>
				<image @click="signatureIndex()"
				       class="signature-bg-img"
				       :src="add"></image>
			</view>


		</view>
		<bottom-sheet ref="sheet">
			<view class="bottom-thumbnail">
				<view class="bottom-list">
					<view v-for="item in imageList"
					      :key="item.id"
					      class="bottom-li">
						<image class="board-img"
						       :src="item.src"></image>
					</view>
				</view>
				<view class="bottom-enter"
				      v-if="imageList.length>0"
				      @click="clearImage()">删除</view>
			</view>
			<view @touchend="touchEnd"
			      @touchmove="touchMove"
			      class="board"
			      :style="{backgroundImage:'url(' + bg + ')',
								backgroundRepeat:'no-repeat',
								backgroundSize:'100% 100%'}">
				<vue-esign ref="boardesgin"
				           :height="300"
				           :isCrop="isCrop"></vue-esign>

			</view>
			<view class="board-button">
				<!-- <view @click="handleReset">清空</view> -->
				<view @click="handleSave">保存</view>
			</view>
			<div id="image-container"></div>
		</bottom-sheet>
	</div>
</template>

<script>
	import bottomSheet from '@/pages/component/bottom-sheet.vue';
	export default {
		name: 'SignatureDialog',
		components: {
			bottomSheet
		},

		data() {
			return {
				bg: require('@/static/img/signature_bg.png'),
				add: require('@/static/img/add.png'),
				deleteimg: require('@/static/img/ic_guanbi.png'),
				visible: false,
				showContent: false,
				isCrop: true,
				timer: null,
				times: null,
				resultImg: '',
				imageList: [], //预览签名的列表
				showList: [] //上传签名的列表
			}
		},

		methods: {
			//清空签名
			handleReset() {
				this.$refs.boardesgin.reset()
			},

			//展示预览的签名
			handleGenerate() {
				this.$refs.boardesgin.generate().then(res => {
					this.resultImg = res

					let list = {}
					list.src = this.resultImg
					let name = 'siganture'
					let blob = this.dataURLtoBlob(this.resultImg)
					let file = this.blobToFile(blob, name)
					this.imageList.push(list)
					this.handleReset()

				}).catch(err => {
					uni.showToast({
						icon: 'none',
						title: '请签名后再保存哦'
					})
				})
			},

			dataURLtoBlob: function(dataurl) {
				var arr = dataurl.split(','),
					mime = arr[0].match(/:(.*?);/)[1],
					bstr = atob(arr[1]),
					n = bstr.length,
					u8arr = new Uint8Array(n);
				while (n--) {
					u8arr[n] = bstr.charCodeAt(n);
				}
				return new Blob([u8arr], {
					type: mime
				});
			},
			//将blob转换为file
			blobToFile: function(theBlob, fileName) {
				theBlob.lastModifiedDate = new Date();
				theBlob.name = fileName;
				return theBlob;
			},

			//保存当前的签名图，并关闭签名板
			handleSave() {
				if (this.imageList.length < 1) {
					uni.showToast({
						icon: 'none',
						title: '请至少有一张签名再保存'
					})
				} else {
					this.showList = this.showList.concat(this.imageList)
					this.$refs.sheet.dismiss()
				}
			},

			//触摸移出事件，超过1秒即触发预览图片方法；
			touchEnd(e) {
				this.times = setTimeout(() => {
					this.handleGenerate()
				}, 1000)
			},

			//鼠标移入事件，停止计时
			touchMove(e) {
				clearTimeout(this.times)
			},

			//删除预览图片列表中的最近一张图片
			clearImage() {
				this.imageList.pop()
			},

			//触发添加签名的方法
			signatureIndex() {
				this.imageList = []
				this.$refs.sheet.show()
			},

			deleteSignature(index) {
				this.showList.splice(index, 1)
			}
		}
	}
</script>

<style scoped>
	.board {
		height: 45vh
	}

	.board-img {
		height: 100upx;
		width: 100%;
	}

	.bottom-list {
		width: 80%;
		display: flex;
		flex-wrap: wrap;
	}

	.bottom-thumbnail {
		display: flex;
		width: 100%;
		padding: 8upx;
		border-bottom: 1upx dotted #ccc;
	}

	.bottom-enter {
		width: 20%;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #1E8EEF;
	}

	.bottom-li {
		width: 20%;
		margin: 2upx 10upx;
		border: 1upx dotted #1E8EEF;
	}

	.board-button {
		z-index: 100;
		color: #1E8EEF;
		padding: 30upx;
		display: flex;
		align-items: center;
		justify-content: space-around;
	}

	.signature-bg {
		width: 100%;
	}

	.signature-bg-img {
		height: 100upx;
		width: 100upx;
		display: inline-block;
	}

	.signature-bg-list {
		width: 100%;
	}

	.signature-bg-list image {
		padding: 6upx;
	}

	.signature-bg-deleteimg {
		width: 20upx;
		height: 20upx;
		position: absolute;
		right: 0;
	}

	.signature-bg-imglist {
		height: 100upx;
		width: 100upx;
	}

	.signature-bg-all {
		position: relative;
	}
</style>
