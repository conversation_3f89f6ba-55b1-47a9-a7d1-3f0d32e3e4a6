{"name": "my-project", "version": "0.1.0", "private": true, "scripts": {"fastInstall": "yarn install", "serve": "npm run dev:h5", "build": "npm run build:h5", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:custom": "cross-env NODE_ENV=production uniapp-cli custom", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:mp-360": "cross-env NODE_ENV=production UNI_PLATFORM=mp-360 vue-cli-service uni-build", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-kuaishou": "cross-env NODE_ENV=production UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "build:quickapp-native": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-native vue-cli-service uni-build", "build:quickapp-webview": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview vue-cli-service uni-build", "build:quickapp-webview-huawei": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build", "build:quickapp-webview-union": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-360": "cross-env NODE_ENV=development UNI_PLATFORM=mp-360 vue-cli-service uni-build --watch", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-kuaishou": "cross-env NODE_ENV=development UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "dev:quickapp-native": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-native vue-cli-service uni-build --watch", "dev:quickapp-webview": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview vue-cli-service uni-build --watch", "dev:quickapp-webview-huawei": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build --watch", "dev:quickapp-webview-union": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build --watch", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js", "serve:quickapp-native": "node node_modules/@dcloudio/uni-quickapp-native/bin/serve.js", "test:android": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=android jest -i", "test:h5": "cross-env UNI_PLATFORM=h5 jest -i", "test:ios": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=ios jest -i", "test:mp-baidu": "cross-env UNI_PLATFORM=mp-baidu jest -i", "test:mp-weixin": "cross-env UNI_PLATFORM=mp-weixin jest -i", "lint:fix": "eslint src/**/**/**/** --fix"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@dcloudio/uni-app-plus": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-h5": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-mp-360": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-mp-alipay": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-mp-baidu": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-mp-kuaishou": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-mp-qq": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-mp-toutiao": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-mp-vue": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-mp-weixin": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-quickapp-native": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-quickapp-webview": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-stat": "^2.0.0-31920210609001", "@vue/shared": "^3.2.31", "axios": "^0.19.2", "axios-miniprogram": "^2.0.0-rc-1", "babel-eslint": "^10.1.0", "bowo-sdk": "0.3.7", "core-js": "^3.21.1", "dayjs": "^1.11.0", "echarts": "^5.3.2", "flyio": "^0.6.2", "gcoord": "^0.3.2", "jquery": "^3.6.0", "lime-echart": "^1.0.1", "lodash": "^4.17.21", "p-charts": "^3.0.5", "p-mui": "^2.0.6", "regenerator-runtime": "^0.12.1", "uni-app-core-js": "^1.0.0", "uni-app-polyfills-presets": "^1.0.0", "uview-ui": "2.0.31", "vue": "^2.6.11", "vue-amap": "^0.5.10", "vue-i18n": "^8.27.1", "vuex": "^3.2.0", "weixin-js-sdk": "^1.6.0"}, "devDependencies": {"@babel/runtime": "~7.12.0", "@dcloudio/types": "^2.6.3", "@dcloudio/uni-automator": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-cli-i18n": "^2.0.1-33920220314003", "@dcloudio/uni-cli-shared": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-i18n": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-migration": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-template-compiler": "^2.0.0-alpha-33020211130001", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.0-alpha-33020211130001", "@dcloudio/vue-cli-plugin-uni": "^2.0.0-alpha-33020211130001", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.0-alpha-33020211130001", "@dcloudio/webpack-uni-mp-loader": "^2.0.0-alpha-33020211130001", "@dcloudio/webpack-uni-pages-loader": "^2.0.0-alpha-33020211130001", "@vue/cli-plugin-babel": "^4.5.17", "@vue/cli-service": "^4.5.17", "babel-plugin-import": "^1.11.0", "cross-env": "^7.0.2", "eslint": "5.16.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-prettier-vue": "^3.1.0", "eslint-plugin-vue": "^7.13.0", "jest": "^25.4.0", "less": "^4.1.2", "less-loader": "^7.3.0", "mini-types": "*", "miniprogram-api-typings": "^3.4.6", "postcss-comment": "^2.0.0", "prettier": "^2.6.2", "sass": "^1.26.5", "sass-loader": "^8.0.2", "stylus": "^0.54.8", "stylus-loader": "3.0.2", "vue-template-compiler": "^2.6.11"}, "browserslist": ["Android >= 4", "ios >= 8"], "uni-app": {"scripts": {}}, "globals": {"AMap": "true"}}