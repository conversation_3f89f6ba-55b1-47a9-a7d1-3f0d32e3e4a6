/** @format */

/**
 * @method 表单校验
 * @params objRules 校验规则
 * @params objValiData 需要检验的对象
 */
//校验
export const getValid = (objRules, objValiData) => {
    let keyRules = Object.keys(objRules);
    for (let i = 0; i < keyRules.length; i++) {
        let field = keyRules[i];
        let requires = objRules[field];
        if (objValiData[field] === '' && requires.required) {
            uni.showToast({
                icon: 'none',
                title: requires.message
            });
            return Promise.reject();
        }
    }
    return Promise.resolve();
};

/**
 * @method 校验指定类型的图片是否都已经上传
 * @params arrImage 上传的图片数组
 * @params isSpecifyTypeImage 是否需要检验上传类型，默认为false
 */
export const getValidUploadImage = (
    arrImage = [],
    isSpecifyTypeImage = false
) => {
    if (!isSpecifyTypeImage) {
        if (!arrImage.length) {
            uni.showToast({
                title: `请上传照片`,
                icon: 'none'
            });
            return Promise.reject();
        } else {
            return Promise.resolve();
        }
    }
    let objEmptyWJID = arrImage.find((item) => item.WJID === '');
    if (objEmptyWJID) {
        uni.showToast({
            title: `请上传${objEmptyWJID.name}照片`,
            icon: 'none'
        });
        return Promise.reject();
    }

    return Promise.resolve();
};
