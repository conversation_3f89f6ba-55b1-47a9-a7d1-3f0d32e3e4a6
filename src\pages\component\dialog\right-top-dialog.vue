<!-- 操作菜单对话框 -->
<template>

	<view>
		<div style="position: absolute;top: 0;left: 0;bottom: 0;right: 0; z-index: 999;"
			 @click="show = false"
			 v-show="show"></div>
		<div class="xiala"
			 v-show="show">
			<ul>
				<li class="xial_li"
					v-for="(menu, index) in menus"
					:key="index"
					@click.stop="onMenuClick(menu)">
					<image class="xial_li-image"
						   v-if="menu.icon"
						   :src="menu.icon"></image>
					<text>{{ menu[nameKey] || menu }}</text>
				</li>
			</ul>
		</div>
	</view>
</template>

<script>
	import bottomSheet from '@/pages/component/bottom-sheet.vue';

	export default {
		data() {
			return {
				show: false
			}
		},
		name: 'OperateMenuDialog',
		components: {
			bottomSheet
		},

		props: {
			menus: {
				type: [Array, Object],
				default: () => {
					return [];
				}
			},

			nameKey: {
				type: String,
				default: 'name'
			}
		},

		computed: {
			//计算页面可滚动距离的高度
			pageListStyle: function() {
				return {
					height: 'calc(100vh - 300upx)',
				}
			}
		},

		methods: {
			onMenuClick(menu) {
				this.show = false
				this.$emit('menuClick', menu);
			},

			onCancelClick() {
				this.show = false
			}
		}
	}
</script>

<style scoped>
	.operate-dialog-content {
		width: calc(100% - 64rpx);
		padding: 32rpx;
	}

	.operate-menu-list {
		border-radius: 10rpx;
	}

	.operate-menu-item {
		width: 100%;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		font-size: 32rpx;
		color: #333;
		background-color: #fff;
	}

	.operate-menu-item:first-child {
		border-top-left-radius: 10rpx;
		border-top-right-radius: 10rpx;
	}

	.operate-menu-item:last-child {
		border-bottom-left-radius: 10rpx;
		border-bottom-right-radius: 10rpx;
	}

	.operate-menu-item:active {
		background-color: #007AFF;
		color: #fff;
	}

	.operate-cancel {
		border-radius: 10rpx;
	}

	.xiala {
		z-index: 9999;
		position: absolute;
		top: 7.5%;
		right: 2%;
		border-radius: .180515rem;
		background-color: #333;
		opacity: 0.9;
	}

	.xiala::after {
		content: "";
		position: absolute;
		top: -0.805958rem;
		right: .322061rem;
		border: .461031rem solid #fff;
		border-color: transparent transparent #333 transparent;
	}

	.xiala ul {
		/* width: 5rem; */
		/* padding: .080515rem .161031rem; */
		padding: 12rpx 46rpx 12rpx 20rpx
	}

	.xiala ul li {
		font-size: 30rpx;
		color: #fff;
		line-height: 3.427214rem;
		display: flex;
		align-items: center;
		/* text-align: center; */
	}

	.xial_li:not(:last-child) {
		display: flex;
		align-items: center;
		/* border-bottom: 1px solid #727F86 */
	}

	.xial_li-image {
		width: 30rpx;
		height: 30rpx;
		padding: 0 20rpx;
	}
</style>
