<template>
	<body style="background: #f5f7fd;" class="warp">
		<mi-loading ref="Loading" title="定位中" :hasMask="true" id="loadingBtn"></mi-loading>
		<header class="header">
			<!-- pd-backbtn -->
			<i class="ic-back" @click="back()"></i>
			<h1 class="title">定位地址</h1>
		</header>
		<section class="main" style="height: unset;">
			<div class="inner" style="position: relative;">
				<div class="topdatas">
					<!-- <select class="yy-posselect1">
						<option value="">武汉</option>
						<option value="">北京</option>
						<option value="">上海</option>
					</select> -->
					<!-- <div style="padding:0 10rpx">当前城市：{{ dwdz }}</div> -->
					<input class="pd-inpsrh"  @input="locationMap.input" @confirm="locationMap.input" type="text"
					v-model="address" placeholder="请输入关键字进行查询" />
					</div>
				</div>
				<div>
					<!-- 重置位置 -->
					<view class="location-btn" @click="resetLocation">
						<i><em></em></i>
					</view>

					<!-- #ifdef APP-PLUS || H5 -->
					<view @click="locationMap.onClick" id="locationMap" :anginType="anginType" :prop="option"
						:change:prop="locationMap.updateEcharts" :change:anginType="locationMap.receiveMsg"></view>
					<!-- #endif -->
					<!-- #ifndef APP-PLUS || H5 -->
					<view>非 APP、H5 环境不支持</view>
					<!-- #endif -->
					<!-- <el-amap vid="amap" :plugin="plugin" class="amap-demo" :center="center">
	          	</el-amap> -->
				</div>
				<div class="yy-data4" v-if="false">
					<div class="zy-list1">
						<div class="botaddrbox">
							<div class="item" v-for="(item, index) in pointList" :key="item.id" @click="getPoint(item)">
								<p class="p1">{{ item.name }}</p>
								<p class="p2">
									<b class="sd">{{ item.distance }}m</b>
									{{ item.address }}
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>
	</body>
</template>

<script>
	import gcoord from 'gcoord'
	import enterprise_store from './enterprise.store.js';
	import miLoading from '@/components/mi-loading/mi-loading.vue';
	export default {
		components: {
			miLoading
		},
		data() {
			return {
				params: enterprise_store.state,
				option: {
					lng: 11,
					lat: 11
				},
				marker: null,
				anginType: false,
				map: null,
				lng: 116.412251,
				lat: 39.840609,
				placeSearch: null,
				pointList: [],
				dwdz: '',
				searchText: '',
				cpoint: [],
				address:''
			};
		},

		mounted() {
			this.resetLocation();

		},
		onLoad(options) {
			console.log(decodeURIComponent(options.address))
			this.address = options.address;
		},

		methods: {
			back() {
				uni.navigateBack({
					delta: 1
				});
			},
			resetLocation() {
				this.anginType = !this.anginType;
			},
			onViewClick(option) {
				this.option = JSON.parse(option.option);
				let JWD = JSON.parse(option.option);
				let address = JWD.address;
				console.log("原始", JWD)
				var result = gcoord.transform(
					[JWD.lng, JWD.lat], // 经纬度坐标
					gcoord.WGS84, // 当前坐标系
					gcoord.GCJ02 // 目标坐标系
				);
				console.log("转换后", result)
				let jd = result[0].toFixed(6);
				let wd = result[1].toFixed(6);
				uni.showModal({
					title: '提示',
					content: `是否定位到${address}附近，经度${jd}，纬度${wd}吗?`,
					success: res => {
						if (res.confirm) {
							this.params.JWD.longitude = jd.toString();
							this.params.JWD.latitude = wd.toString();
							this.params.JWD.DZ = address;
							uni.navigateBack({
								delta: 1
							});
						} else {
							console.log('取消选择');
						}
					}
				});
			},

			getPointList(e) {
				this.pointList = e;
			},
			getCityName(e) {
				this.dwdz = e;
			},

			getPoint(item) {
				console.log('itemobj',item)
				let address = `${item.cityname}${item.adname}${item.address}`;
				let jd = item.location.lng.toFixed(6);
				let wd = item.location.lat.toFixed(6);
				uni.showModal({
					title: '提示',
					content: `是否定位到${address}附近，经度${jd}，纬度${wd}吗?`,
					success: res => {
						if (res.confirm) {
							this.params.JWD.longitude = jd.toString();
							this.params.JWD.latitude = wd.toString();
							this.params.JWD.DZ = address;
							uni.navigateBack({
								delta: 1
							});
						} else {
							console.log('取消选择');
						}
					}
				});
			}
		}
	};
</script>
<script module="locationMap" lang="renderjs">
	const selectedStart = 'static/ITkoala-amap/selectedStart.png' //选中的图片
	import axios from 'axios';

	 window._AMapSecurityConfig = {
	           securityJsCode:'84167511c74715bc80662881dbe3cb21',
	        }
	window.onMapLoad = function() {
		console.log("地图加载了", vm)
		vm.initAmap();
	}
	export default {
		data() {
			return {
				cpoint: [],
				placeSearch: null,
				marker: null,
				map: null,
				geocoder: null,
				ownerInstanceObj: null //service层对象
			}
		},
		created() {
			window.vm = this;
		},
		mounted() {
			if (typeof window.AMap === 'function') {
				this.initAmap()
			} else {
				document.getElementById('loadingBtn').style.display = 'block';
				// 动态引入较大类库避免影响页面展示
				const script = document.createElement('script')
				script.charset = 'utf-8';
				// b698e8fc43715a16053e9fa156601706
				//dfdcba34d8024833098f0e24e22be1c2
				script.src =
					'https://webapi.amap.com/maps?v=1.4.15&key=a0d44f8c9428a50f6ff83f1be9c319ad&plugin=AMap.Geocoder'
				script.onload = this.initAmap.bind(this)
				document.head.appendChild(script)
			}
		},

		methods: {
			initAmap() {
				let self = this;
				// 如果沒有经纬度，那么就开启定位
				if (this.option.lng == '') {
					AMap.plugin('AMap.Geolocation', function() {
						var geolocation = new AMap.Geolocation({
							enableHighAccuracy: true, //是否使用高精度定位，默认:true
							timeout: 10000, //超过10秒后停止定位，默认：5s
							buttonPosition: 'RB', //定位按钮的停靠位置
							buttonOffset: new AMap.Pixel(10, 20), //定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
							zoomToAccuracy: true, //定位成功后是否自动调整地图视野到定位点

						});
						// map.addControl(geolocation);
						geolocation.getCurrentPosition(function(status, result) {
							if (status == 'complete') {
								self.option.lng = result.position.lng;
								self.option.lat = result.position.lat;
								self.dwdz = result.addressComponent.city
								self.$ownerInstance.callMethod('getCityName', result.addressComponent.city)
								self.updatePosition();
								AMap.service(["AMap.PlaceSearch"], function() {
									//构造地点查询类
									self.placeSearch = new AMap.PlaceSearch({
										type: '公司企业|政府机构及社会团体', // 兴趣点类别
										pageSize: 1, // 单页显示结果条数
										pageIndex: '', // 页码
										city: result.addressComponent.citycode, // 兴趣点城市
										citylimit: true, //是否强制限制在设置的城市内搜索
										map: self.map, // 展现结果的地图实例
										// panel: "panel", // 结果列表将在此容器中进行展示。
										autoFitView: true // 是否自动调整地图视野使绘制的 Marker点都处于视口的可见范围
									});
									var cpoint = [result.position.lng, result.position
										.lat
									]; //中心点坐标
									self.cpoint = [result.position.lng, result.position.lat]
									self.placeSearch.searchNearBy('', cpoint, 200, (status,
										result) => {
										if (status == 'complete') {
											self.$ownerInstance.callMethod('getPointList',
												result.poiList.pois)
										}
									});

								});
							}
						});
					});
				} else {
					this.updatePosition();
				}
			},
			input(event, ownerInstance) {
				this.search(event.detail.value)
			},
			search(searchText) {
				this.placeSearch.search(searchText, (status, result) => {
					let pointList = JSON.parse(JSON.stringify(result.poiList.pois));
					let arr = [];
					let reg = new RegExp(this.searchText);
					for (let i = 0; i < pointList.length; i++) {
						if (reg.test(pointList[i].name)) {
							let dis = AMap.GeometryUtil.distance(this.cpoint, [pointList[i].location.lng,
								pointList[i].location.lat
							]);
							pointList[i].distance = dis.toFixed(0);
							arr.push(pointList[i]);
						}
					}

					this.$ownerInstance.callMethod('getPointList', arr)
				});
			},
			againLocation() {
				document.getElementById('loadingBtn').style.display = 'block';
				this.option.lng = ''
				this.anginType = false
				this.initAmap()
			},

			receiveMsg(newValue, oldValue) {
				// if(oldValue){
				// this.getMapData()
				this.againLocation()
				// }
			},

			updatePosition() {
				let self = this;
				this.map = new AMap.Map('locationMap', {
					resizeEnable: true,
					center: [self.option.lng, self.option.lat],
					layers: [ //使用多个图层
						// new AMap.TileLayer.Satellite() //使用卫星图
					],
					zooms: [4, 18], //设置地图级别范围
					zoom: 16
				})

				this.geocoder = new AMap.Geocoder();

				this.getMarker();


				this.map.on('click', function(e) {
					self.option.lng = e.lnglat.lng
					self.option.lat = e.lnglat.lat

					self.map.remove(self.marker);
					self.getMarker()
				});
				document.getElementById('loadingBtn').style.display = 'none';
			},

			regeoCode(option, ownerInstance) {

				this.geocoder.getAddress([option.lng, option.lat], function(status, result) {
					if (status === 'complete' && result.regeocode) {
						option.address = result.regeocode.formattedAddress;
					} else {
						log.error('根据经纬度查询地址失败')
					}

					// 调用 service 层的方法
					ownerInstance.callMethod('onViewClick', {
						option: JSON.stringify(option)
					})
				});
			},


			// 点击传经纬度
			async onClick(event, ownerInstance) {
				let option = this.option;
				// 请求高德接口将经纬度转换成具体位置
				// let getAddress =  await	axios({
				// 						method: 'get',
				// 						url: `https://restapi.amap.com/v3/geocode/regeo?location=${option.lng},${option.lat}&coordsys=gps&key=f77fbf68937a2895014c7c3b717d21f3`,
				// 						data: {
				// 							type: 'geocode/regeo',
				// 							version: 'v3'
				// 						},
				// 						headers: {
				// 							'Content-Type': 'application/x-www-form-urlencoded'
				// 						}
				// 					})
				this.regeoCode(option, ownerInstance);
			},

			getMarker() {
				let that = this
				this.marker = new AMap.Marker({
					position: [that.option.lng, that.option.lat],
					map: that.map
				});
			}
		}
	}
</script>

<style scoped>
	html { -webkit-tap-highlight-color: transparent; height: 100%; }
	body { -webkit-backface-visibility: hidden; height: 100%;}
	.pd-inpsrh{

	}
	.topdatas{
		position: relative;
		top:80rpx;
		z-index:999;
		background: #fff;
	}
	.location {
		display: flex;
		width: 100%;
		align-items: center;
		justify-content: center;
		padding: 10upx;
	}

	.location view {
		width: 50%;
	}

	.location-btn {
		position: absolute;
		right: 20rpx;
		top: 80%;
		z-index: 500;
		background: #4874ff;
		border-radius: 20rpx;
		padding: 10rpx;
		color: #fff;
		width: 80rpx;
		height: 80rpx;
		background-color: #fff;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.location-btn i {
		width: 40rpx;
		height: 40rpx;
		border-radius: 50%;
		position: relative;
		border: 10rpx solid #4874ff;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #fff;
	}

	.location-btn i em {
		width: 20rpx;
		height: 20rpx;
		background: #4874ff;
		border-radius: 50%;
	}

	#locationMap {
		/* height: calc(100vh - 200rpx); */
		height: 100vh;
	}

	.yy-data4 {
		z-index: 666;
	}
</style>
