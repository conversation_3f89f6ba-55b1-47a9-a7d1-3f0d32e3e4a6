import {cloneDeep} from 'lodash';
export const setOption= function(data,x){
			let y = [];
			let lines = [];
			//x轴数据
			let xAxisData = x;
			//y轴数据
			let yAxiData = [];
			let zeroArr = [];
			
			let sereisData = [
			    {
			        type: 'bar',
			        data: [x.length],
			        z: -1,
			        silent: true,
			        itemStyle: {
			            opacity: 0
			        }
			    }
			];
			
			for (let i = 0; i < data.length; i++) {
			    let v = data[i];
			    yAxiData.push(v.name);
			    zeroArr.push(0);
			}
			for (let i = 0; i < data.length; i++) {
			    let v = data[i];
			
			    if (v.list) {
			        for (let j = 0; j < v.list.length; j++) {
			            let vList = v.list[j];
			            let innerZeroArr = cloneDeep(zeroArr);
			            vList.start = vList.start && vList.start.slice(11, 16);
			            vList.end = vList.end && vList.end.slice(11, 16);
			            let endIndex = xAxisData.findIndex(
			                (v) => v === vList.end
			            );
			            let startIndex = xAxisData.findIndex(
			                (v) => v === vList.start
			            );
			            innerZeroArr.splice(i, 1, {
			                value: endIndex - startIndex,
			                start: vList.start,
			                end: vList.end
			            });
			            let barItem = {};
			            barItem = {
			                name:
			                    vList.status == 2
			                        ? '开启'
			                        : vList.status == 1
			                        ? '停止'
			                        : '离线',
			                type: 'bar',
			                label: true,
			                barWidth: '24',
			                stack: '总时段',
			                itemStyle: {
			                    color:
			                        vList.status == 2
			                            ? 'rgba(117, 189, 35,.8)'
			                            : vList.status == 1
			                            ? 'rgba(255,100,94,.8)'
			                            : 'rgba(190,190, 190,.8)'
			                },
			                emphasis: {
			                    borderWidth: '3',
			                    itemStyle: {
			                        color:
			                            vList.status == 2
			                                ? 'rgba(117, 189, 35,1)'
			                                : vList.status == 1
			                                ? 'rgba(255,100,94,1)'
			                                : 'rgba(190,190, 190,1)',
			                        shadowBlur: 6,
			                        shadowOffsetX: 0,
			                        shadowColor: 'rgba(0, 0, 0, 0.4)'
			                    }
			                },
			                tooltip: {
			                    backgroundColor: 'transparent',
			                    borderColor: 'transparent',
			                    formatter: function (e) {
			                        return `${e.name}  ${e.data.start}~${e.data.end} ${e.seriesName}`;
			                    }
			                },
			                data: innerZeroArr
			            };
			            sereisData.push(barItem);
			        }
			    }
			}
			
			let option = {
			    backgroundColor: '#ffffff',
			    tooltip: {
			        trigger: 'item',
			        show: true,
			        padding: [0, 0],
			        formatter: function (obj) {
			            let data = obj && obj.data;
			            return `<div style="background:${
			                obj.seriesName == '停止'
			                    ? '#ff7054'
			                    : obj.seriesName == '开启'
			                    ? '#7dcd27'
			                    : '#888'
			            };color:#fff;padding:6px;opacity:0.9;border-radius:2px;font-size:12px;"> ${
			                obj.name
			            }</br>
			                开始时间：${data.start} </br>
			                结束时间：${data.end} </br>
			                状态：${obj.seriesName} </br>
			                </div>`;
			        }
			    },
			
			    grid: {
			        top: '10',
			        left: '16',
			        right: '16',
			        bottom: '0',
			        containLabel: true
			    },
			    xAxis: {
			        data: xAxisData,
			        max: xAxisData.length,
			        splitLine: {
			            show: false
			        },
			        type: 'value',
			        axisLabel: {
			            formatter: function (v) {
			                return xAxisData[v] || v;
			            },
			            show: true,
			            textStyle: {
			                color: '#666'
			            }
			        },
			        axisLine: {
			            show: true,
			            lineStyle: {
			                color: '#999'
			            }
			        }
			    },
			    yAxis: {
			        z: 3,
			        type: 'category',
			        axisLine: {
			            show: false
			        },
			        axisTick: {
			            show: false
			        },
			        axisLabel: {
			            show: true,
			            inside: true,
			            fontSize: '14',
			            margin: 4,
			            formatter: function (value) {
			                if (
			                    value === '治污设备' ||
			                    value === '生产设备' ||
			                    value === '其他设备'
			                ) {
			                    return '{black|' + value + '}';
			                } else {
			                    return '{white|' + value + '}';
			                }
			            },
			            rich: {
			                black: {
			                    color: '#333',
			                    fontSize: '14',
			                    fontWeight: 'bold'
			                },
			                white: {
			                    color: '#ffffff'
			                }
			            }
			        },
			        splitLine: {
			            show: false
			        },
			        data: yAxiData
			    },
			    series: sereisData
			};
			return option;
		}
		