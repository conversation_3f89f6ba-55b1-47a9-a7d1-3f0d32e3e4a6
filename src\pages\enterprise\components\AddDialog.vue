<!-- @format -->

<template>
	<div style="width: 100%; height: 100%;position:absolute;top:0;left:0;" v-show="showAddDialog">
		<div class="mask" style="display: block"></div>
		<div class="yj-alert" style="display: block">
			<h3>运维优化记录</h3>
			<p class="p1">优化内容:</p>
			<textarea v-model="FKNR" class="textarea1" placeholder="请输入优化信息" style="text-align: left;"></textarea>
			<div class="zy-line ac jc">
				<p class="btn1" @click="cancel">取消</p>
				<p class="btn2" @click="edit">编辑</p>
				<p class="btn2" @click="save">保存</p>
			</div>
			<div class="warn">
				<i class="star">*</i> 注意：可编辑数据为优化数据最近的一条数据
			</div>
		</div>
		<u-top-tips ref="uTips"></u-top-tips>
	</div>
</template>

<script>
	import {
		yhjladd,
		yhjledit
	} from '@/api/iot/weakSignal.js';

	import {
		guid
	} from '@/common/uuid.js';

	export default {
		props: {
			info: {
				type: Object,
				default: () => null
			},
			showAddDialog: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				FKR: '', //反馈人，运维端需手动填入
				FKNR: '', //反馈内容
				companyInfo: {},
				userinfo: '',
				saveType: 0, //0为保存1为编辑
			    firstSituation: {} //优化记录列表第一条
			};
		},
		mounted() {
			this.userinfo = uni.getStorageSync('userInfo')
			uni.$on('getOptimizationList', (payload) => {
				this.firstSituation = payload;
				console.log('this.firstSituation',this.firstSituation )
			})
		},
		methods: {
			cancel() {
				this.$emit('update:showAddDialog', false)
				this.FKNR = ''
			},
			save() {
				if(this.FKNR == ''){
					uni.showToast({
						title: '请填写反馈内容',
						icon:"none",
						duration: 1000
					})
					return
				}
				
				if (this.saveType) {
					//编辑
					this.saveEdit()
				} else {
					//保存
					this.add()
				}
			},
			//预警反馈录入
			add() {
				let obj = {
					FKNR: this.FKNR, //反馈内容
					FKR: this.userinfo.name, //反馈人 
					FXID: this.info.FXID, //分析ID
					WRYBH: this.info.WRYBH, //污染源编号
				};

				yhjladd(obj).then((res) => {
					if (res.data.status == '000') {
						uni.showToast({
							title: '添加成功',
							duration: 1000
						}).then(res => {
							setTimeout(() => {
								this.$emit('update:showAddDialog', false)
								//信号弱组件更新优化记录列表
								uni.$emit('getOptimization');
								this.FKNR =''
							}, 300);
							
						})
					}
				});
			},
			//编辑第一条数据
			edit() {
				if(!this.firstSituation){
					uni.showToast({
						title: '没有可编辑内容',
						icon:"none",
						duration: 1000
					})
					return
				}
				//点击获取第一条数据
				this.FKNR = this.firstSituation.FKNR;
				this.saveType = 1
			},
			saveEdit() {
				let obj = {
					FKNR: this.FKNR, //反馈内容
					FKR: this.userinfo.name, //反馈人             
					FKID: this.firstSituation.FKID
				};
				yhjledit(obj).then((res) => {
					if (res.data.status == '000') {
						uni.showToast({
							title: '修改成功',
							duration: 1000
						}).then(res => {
							setTimeout(() => {
								this.$emit('update:showAddDialog', false)
								//信号弱组件更新优化记录列表
								uni.$emit('getOptimization');
								this.FKNR =''
							}, 300);
						})
					}
				});
			},
			radioChange(v) {
				this.curValid = v.target.value;
			}
		}
	};
</script>

<style scoped>
	.yj-alert {
		height: 650rpx;
	}

	.star {
		color: #f00;
		padding-right: 10rpx;
	}

	.warn {
		padding: 30rpx 20rpx;
		color: #bbb;
		font-size:26rpx;
	}

	.yj-alert input {
		width: 100%;
		background: #FCFCFC;
		border-radius: 4rpx;
		border: 2rpx solid #E3E3E3;
		font-size: 30rpx;
		color: #333;
		line-height: 60rpx;
		height: 60rpx;
		box-sizing: border-box;
		padding: 14rpx 20rpx;
		resize: none;
		margin-bottom: 30rpx;
		font-family: "Microsoft YaHei";
	}

	.uni-label-pointer {
		cursor: pointer;
		display: flex;
		flex-direction: row;
		margin-right: 60rpx;
	}

	.radio-group {
		display: flex;
	}

	.radio-group .uni-list-cell {
		margin-right: 10rpx;
	}

	radio {
		transform: scale(0.7)
	}

	/deep/.uni-textarea-textarea {
		text-align: left;
	}
	.yj-alert .btn1 ,.yj-alert .btn2{height:60rpx;line-height: 60rpx;}
.yj-alert .textarea1{ margin-bottom: 60rpx;}
</style>
