{
    "name" : "智能运维",
    "appid" : "__UNI__67E9FBB",
    "description" : "智能运维APP",
    "versionName" : "1.10.2",
    "versionCode" : 48,
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "permissions" : {
            "location" : {
                "desc" : "用于获取定位信息",
                "scope" : "fine"
            }
        },
        "safearea" : {
            "background" : "#f1f1f1", //背景色
            "bottom" : {
                "offset" : "auto"
            }
        },
        "softinput" : {
            "mode" : "adjustPan"
        },
        "statusbar" : {
            "immersed" : false
        },
        "compatible" : {
            "ignoreVersion" : true
        },
        "usingComponents" : true,
        "compilerVersion" : 3,
        /* 模块配置 */
        "modules" : {
            "Maps" : {},
            "Camera" : {},
            "Barcode" : {},
            "Geolocation" : {},
            "VideoPlayer" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_LOCATION_EXTRA_COMMANDS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_MOCK_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CAPTURE_AUDIO_OUTPUT\"/>",
                    "<uses-permission android:name=\"android.permission.CAPTURE_VIDEO_OUTPUT\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.INSTALL_LOCATION_PROVIDER\"/>",
                    "<uses-permission android:name=\"android.permission.INSTALL_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.REQUEST_INSTALL_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a" ],
                "minSdkVersion" : 21,
                "targetSdkVersion" : 26
            },
            /* ios打包配置 */
            "ios" : {
                "dSYMs" : false,
                "privacyDescription" : {
                    "NSPhotoLibraryUsageDescription" : "是否允许智能运维App访问你的媒体资料库读取照片用于上传企业大门图，生产车间全景图，治污线全景图，安装照片？",
                    "NSPhotoLibraryAddUsageDescription" : "是否允许智能运维App访问你的媒体资料库保存企业大门图，生产车间全景图，治污线全景图，安装照片？",
                    "NSCameraUsageDescription" : "是否允许智能运维App使用你的摄像头拍摄照片用于上传企业大门图，生产车间全景图，治污线全景图，安装照片？",
                    "NSLocationWhenInUseUsageDescription" : "是否允许智能运维App在运行期间访问您的位置信息用于导航到企业位置？",
                    "NSLocationAlwaysUsageDescription" : "是否允许智能运维App在后台运行期间访问您的位置信息用于导航到企业位置？",
                    "NSLocationAlwaysAndWhenInUseUsageDescription" : "是否允许智能运维App总是访问您的位置信息用于导航到企业位置？",
                    "NSMicrophoneUsageDescription" : "是否允许智能运维App使用你的麦克风获取声音用于上传报备视频？"
                },
                "idfa" : false
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "share" : {},
                "maps" : {
                    "amap" : {
                        "appkey_ios" : "f412d4e588f07d3f15591c3c3ce04a8e",
                        "appkey_android" : "da7b465790e03b724efe2500bda342e5"
                    }
                },
                "geolocation" : {
                    "amap" : {
                        "__platform__" : [ "ios", "android" ],
                        "appkey_ios" : "f412d4e588f07d3f15591c3c3ce04a8e",
                        "appkey_android" : "da7b465790e03b724efe2500bda342e5"
                    }
                },
                "ad" : {},
                "statics" : {}
            },
            // "oauth" :
            "icons" : {
                "android" : {
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wx0e90ac017e1228a0",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true,
        "permission" : {}
    },
    "mp-alipay" : {
        "usingComponents" : true,
        "appid" : "920793623"
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "h5" : {
        "router" : {
            "mode" : "history",
            "base" : "./"
        },
        // "base" : "/pacp/h5/"
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : "MQHBZ-7ZS6S-GNPOX-64KGU-QJ743-EWBCZ"
                }
            }
        },
        "devServer" : {
            "port" : "8085",
            "proxy" : {
                "api_mobile" : {
                    "target" : "http://api-mobile.iotdi.com.cn:9011/",
                    //"target" : "http://************:9011/",
                    "ws" : true,
                    "changeOrigin" : true
                },
                "iotManage" : {
                    "target" : "http://iot-manage.iotdi.com.cn/",
                    // "target" : "http://192.168.4.38:8388/",
                    "changeOrigin" : true,
                    "ws" : true
                }
            }
        },
        "template" : "index.html"
    }
}
