/**
 * /*
 *
 * @format
 * @Author: Caijw
 * @LastEditors: Caijw
 * @Description: 存放一些共用的状态，这里考虑其他模块的状态要频繁使用，就放到和模块目录一致的位置，命名为 [模块].store.js
 * @Date: 2019-03-28 08:53:24
 * @LastEditTime: 2019-03-28 12:17:24
 */

import Vue from 'vue';
import Vuex from 'vuex';
Vue.use(Vuex);
const enterprise_store = new Vuex.Store({
    state: {
        JWD: {
            longitude: '',
            latitude: '',
            DZ: ''
        }
    },
    mutations: {
        setLongitude(state, longitude) {
            state.JWD.longitude = longitude;
        },
        setLatitude(state, latitude) {
            state.JWD.latitude = latitude;
        },
        setPointList(state, pointList) {
            state.pointList = pointList;
        }
    },
    // 存放着接口调用
    actions: {}
});
export default enterprise_store;
