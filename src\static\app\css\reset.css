@charset "utf-8";

/**
 * mobile reset
 **/
html, body, div, span, ol, ul, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video { margin: 0; padding: 0; border: 0; font-family: inherit; font-style: normal; font-weight: normal; font-size: 100%; vertical-align: baseline; }
ol, ul { list-style: none; }
*,*:before,*:after {-webkit-box-sizing:border-box; box-sizing:border-box;}
*{margin:0;padding:0;font-weight:normal;}
input, button, textarea, select {-webkit-appearance: none; appearance: none; box-sizing: border-box; border-radius: 0; padding: 0; margin: 0; border: none; outline: none;}
table { border-collapse: collapse; border-spacing: 0; }
input,button,select,textarea{outline:none;border-radius:0;}
input:focus,button:focus,select:focus,textarea:focus{outline:0;}
button.hidefocus::-moz-focus-inner {border:none;}
[hidefocus],summary{outline:0;}
input::-ms-clear { display: none; }
textarea {resize: none;}
/* input:-ms-input-placeholder, textarea:-ms-input-placeholder{  color: #ccc!important; }
input::-webkit-input-placeholder { color: #ccc !important;}
input:-moz-placeholder { color: #ccc;  }
input::-moz-placeholder { color: #ccc; }
:-ms-input-placeholder { color: #ccc;  } */
a.hidefocus,a:active,a:focus,.hidefocus:focus,input[type=button] ,input[type=submit] { outline:none; -moz-outline:none;}
caption, th, td { font-weight: normal; vertical-align: middle; }
q, blockquote { quotes: none; }
q:before, q:after, blockquote:before, blockquote:after { content: ""; content: none; }
a img { border: none; }
article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section, summary { display: block; }
a { text-decoration: none; }
a:hover, a:active { outline: none; }
html { font-family: Helvetica, "STHeiti STXihei", "Microsoft JhengHei", "Microsoft YaHei", "Noto Sans CJK SC",
 "Source Han Sans CN" Tohoma, Arial;} 
body { color: #333; background-color: #fff;}


/* -- Common style -- */
.dn{display:none;}
.db{display:block;}
.fl{float:left;}
.fr{float:right;}
.rel{position: relative;}
.abs{position: absolute;}
.gap{height: 10px;width: 100%;}
.auto{margin: 0 auto;}
.clear{clear: both;}
.clearfix:after{content: "\200B";display: block;height: 0;clear: both;}
.clearfix{*zoom: 1;}
