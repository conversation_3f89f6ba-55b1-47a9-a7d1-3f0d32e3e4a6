<!-- @format -->
<!-- 说明提示弹窗 -->

<template>
    <div>
        <div class="mask" style="display: block"></div>
        <div class="yy0706-alert1">
            <div class="hd">
                <h1 class="tit1">{{ title }}</h1>
                <image
                    src="~@/static/equipmentMaintenance/images/yy0706-cls.png"
                    alt=""
                    class="yy0706-cls"
                    @click="$emit('close')"
                />
            </div>
            <div class="bd">
                <div>{{ content }}</div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'DataCollectionAppReminderPop',
    props: {
        title: {
            type: String,
            default: '提示说明'
        },
        content: {
            type: String,
            default: '说明的内容。'
        }
    },
    data() {
        return {};
    },

    mounted() {},

    methods: {}
};
</script>

<style lang="scss" scoped>
.yy0706-alert1 {
    height: 340rpx;
}
</style>
