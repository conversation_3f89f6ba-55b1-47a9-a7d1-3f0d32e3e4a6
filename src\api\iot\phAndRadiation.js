/** @format */

import axios from '@/common/ajaxRequest.js';
import {
    ULR_BASE,
    LOGIN_ULR_BASE,
    QUERY_EQUIP_TYPE_URL,
    IOTMANAGE_URL
} from '@/common/config.js';

// 其他设备列表
export const getOtherList = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/az/qtsb/list',
        params: data
    });
};

// 辐射设备新增
export const addRADEquip = (data) => {
    return axios.request({
        method: 'POST',
        url: ULR_BASE + '/az/fssb/add',
        data
    });
};

// 辐射设备编辑
export const editRADEquip = (data) => {
    return axios.request({
        method: 'POST',
        url: ULR_BASE + '/az/fssb/modify',
        data
    });
};
// 辐射设备详情
export const detailRADEquip = (data) => {
    return axios.request({
        method: 'POST',
        url: ULR_BASE + '/az/fssb/info',
        data
    });
};

// pH设备新增
export const addPHEquip = (data) => {
    return axios.request({
        method: 'POST',
        url: ULR_BASE + '/az/phsb/add',
        data
    });
};

// pH设备编辑
export const editPHEquip = (data) => {
    return axios.request({
        method: 'POST',
        url: ULR_BASE + '/az/phsb/modify',
        data
    });
};

// pH设备详情
export const detailPHEquip = (data) => {
    return axios.request({
        method: 'POST',
        url: ULR_BASE + '/az/phsb/info',
        data
    });
};

// 放射源编号查询
export const queryRadiationCode = (data) => {
	return axios.request({
		method: 'POST',
		url: ULR_BASE + '/az/fsybh',
		data
	});
};