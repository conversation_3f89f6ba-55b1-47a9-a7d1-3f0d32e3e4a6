/*
 * @Author: your name
 * @Date: 2021-06-16 10:48:23
 * @LastEditTime: 2021-06-30 16:33:08
 * @LastEditors: Please set LastEditors
 * @Description: 获取天气的服务
 * @FilePath: /YDZF_APP/api/weather-service.js
 */

// nowAQI地址：https://www.nowapi.com/api/weather.today
// 和风天气地址：https://id.qweather.com/#/homepage


import http from '@/common/net/http.js'
import axios from '@/common/ajaxRequest.js'
const sign = '234b95a005edb991c79faf4b98bda9e5'
const appkey = '59707'//nowAQI的key
const HEFENG_KEY = "b08ed1b3d0524e40ad8a6038422984d4"//和风天气的KEY
const LEVEL_AQI =  [0, 50, 100, 150, 200, 300, 500]
const LEVEL_TEXTS = ["优", "良", "轻度", "中度", "重度", "严重"]

//查询指定地区的AQI数据
export const queryWeatherInfo = (id) => {
    let locationlist = uni.getStorageSync('APP_location')
    let aqiInfo = {
        aqi:'',
        category:''
    }
    if(locationlist.latitude){
        let jd = locationlist.longitude
        let wd = locationlist.latitude
        let url = `https://devapi.qweather.com/v7/air/now?location=${jd},${wd}&key=${HEFENG_KEY}`
        // return http.get(url)
        // return axios.request({
        //     method: 'get',
        //     url: url,
        // });
        return new Promise((resolve, reject) => {
            axios.request({
                method: 'get',
                url: url
            }).then(resp => {
                aqiInfo.aqi = resp.now.aqi
                aqiInfo.category = resp.now.category
                resolve(aqiInfo)
            }).catch(error => {
                reject(error)
                uni.showToast({
                    title: '污染因子数据异常，稍后再试',
                    duration: 2000,
                    icon: 'none',
                });
            })
        })
    }else{
        let url = `http://api.k780.com/?app=weather.today&cityNm=${id}&appkey=${appkey}&sign=${sign}&format=json`
        return new Promise((resolve, reject) => {
            axios.request({
                method: 'get',
                url: url
            }).then(resp => {
                aqiInfo.aqi = resp.result.aqi
                aqiInfo.category = AqiLevelInfo(resp.result.aqi)
                resolve(aqiInfo)
            }).catch(error => {
                reject(error)
                uni.showToast({
                    title: '污染因子数据异常，稍后再试',
                    duration: 2000,
                    icon: 'none',
                });
            })
        })
    }
};

//查询地区列表
export const queryCityInfo = () => {
    // let url = `http://api.k780.com/?app=weather.city&areaType=cn&appkey=${appkey}&sign=${sign}&format=json`
    return axios.request({
		method: 'get',
		url: url,
	});
};

//查询AQI对应的级别
export const AqiLevelInfo = (index) => {
    for(let i in LEVEL_AQI){
        if(index <  LEVEL_AQI[i]){
            return LEVEL_TEXTS[i - 1]
        }
    }
};


