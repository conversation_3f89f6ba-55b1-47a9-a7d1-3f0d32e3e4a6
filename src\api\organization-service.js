import http from '@/common/net/http.js';

//组织机构的根节点
const ORG_ROOT_ID = 'ROOT';

const ORG_CACHE_KEY = 'organization_department';

/**
 * 检测组织机构数据源为无效
 */
const sourceInvalid = (source) => {
	return typeof source === 'undefined' 
		|| Array.isArray(source) === false 
		|| source.length === 0;
}

/**
 * 获取组织机构根节点
 * @param {Object} allDepartment
 * @param {Object} rootId
 */
const getRootDepartment = (departments) => {
	if(sourceInvalid(departments)){
		console.log(`无效的源：${JSON.stringify(departments, null, 4)}`)
		return null
	}
	
	let matched = departments.filter(item => {
		return item.ZZBH === ORG_ROOT_ID;
	}) 
	if(matched.length > 0){
		return matched[0];
	}
	
	return null;
}

/**
 * 获取指定节点子节点
 */
const getSubDepartments = (departments, parentOrId) => {
	if(sourceInvalid(departments)){
		return [];
	}
	let parentId = parentOrId.ZZBH || parentOrId;
	return departments.filter(item => {
		return item.SJZZXH === parentId && item.ZZBH !== ORG_ROOT_ID;
	}) 
}

/**
 * 判断一个部门下是否有子部门
 */
const hasChildren = (departments, departmentOrId) => {
	let departmentId = departmentOrId.ZZXH || departmentOrId;
	let subDepartments = getSubDepartments(departments, departmentId);
	return subDepartments.length > 0;
}

/**
 * 根据机构获取下辖成员
 */
const getOrgMembers = (menbers, orgId) => {
	if(sourceInvalid(menbers)){
		return [];
	}
	
	return menbers.filter(item => {
		return item.BMBH == orgId;
	});
}

/**
 * 是否包含下辖机构
 */
const hasOrgChildren = (menbers, orgId) => {
	let children = getOrgMembers(menbers, orgId);
	return children.length > 0;
}

/**
 * 缓存部门信息到本地
 */
const saveDepartmentsToLocal = (departments) => {
	uni.setStorageSync(ORG_CACHE_KEY, departments);
}

/**
 * 从本地缓存读取部门信息
 */
const getDepartmentsFromLocal = () => {
	return uni.getStorageSync(ORG_CACHE_KEY);
}

/**
 * 同服务端同步全部缓存信息
 */
const syncDepartments = () => {
	return new Promise((resolve, reject) => {
		http.post(`${http.url}`, {
			service:"QUERY_ZFYH_BMXX",
			version: "1"
		})
			.then(resp => {
				if(Array.isArray(resp) && resp.length > 0){
					saveDepartmentsToLocal(resp);
				}
				resolve(resp);
			})
			.catch(error => {
				console.log(`同步部门信息出错：${error}`)
			})
	});
};

/**
 * 加载全部部门信息
 * @param {Boolean} refresh 是否刷新缓存  
 */
export const loadDepartments = (refresh = false) => {
	return new Promise((resolve, reject) => {
		let localSource = null;
		if(!refresh){
			localSource = getDepartmentsFromLocal();
		}
		if(localSource){
			resolve(localSource);
		} else {
			resolve(syncDepartments());
		}
	});
}

/**
 * 根据部门编号过滤部门信息
 */
const filterDepartmentByIds = (departments, ids) => {
	return departments.filter(d => {
		return ids.indexOf(d.ZZBH) !== -1;
	});
}


export default {
	syncDepartments,
	loadDepartments,
	getRootDepartment,
	getSubDepartments,
	hasChildren,
	getOrgMembers,
	hasOrgChildren,
	filterDepartmentByIds
	
}