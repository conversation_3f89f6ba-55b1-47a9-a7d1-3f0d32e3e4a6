<template>
	<!-- 企业信息 -->
	<div class='hour-box-wrapper' style="padding: 0rpx 30rpx;">
	
		<div class="time-data-wrap"
			style="position: relative;overflow-y: auto;overflow-x: hidden;background-color: white;"
			:style="{'height': hourBoxHeight}" id="table">

			<view class='equi-title'>产污设施</view>
			<view>
				<!-- #ifdef APP-PLUS || H5 -->
				<view style="width: 100%;height:400rpx" :prop2="option2" :change:prop2="echarts.updateEcharts2"
					id="echarts2" class="echarts" ref="echarts2"></view>
				<!-- #endif -->
				<!-- #ifndef APP-PLUS || H5 -->
				<view>非 APP、H5 环境不支持</view>
				<!-- #endif -->
			</view>

			<view class='equi-title'>治污设施</view>
			<view>
				<!-- #ifdef APP-PLUS || H5 -->
				<view style="width: 100%;height:400rpx" :prop1="option1" :change:prop1="echarts.updateEcharts1"
					id="echarts1" class="echarts" ref="echarts1"></view>
				<!-- #endif -->
				<!-- #ifndef APP-PLUS || H5 -->
				<view>非 APP、H5 环境不支持</view>
				<!-- #endif -->
			</view>

		</div>


	</div>
</template>
<script>
	//import * as echarts from 'echarts'
		import {monitorCxzt,monitorZdsj,monitorPicture} from '@/api/warning.js'
	// import {
	// 	getInfo,
	// 	getXssj
	// } from '../../api/iot/realtime.js';
	// import {
	// 	sbsxzt,
	// 	qyztrl
	// } from '../../api/iot/runningData.js';
	export default {
		data() {
			return {
				enterpriseInfo: {},
				xzqhdm: '',
				scxlist: [],
				currentscx: '',
				currentscxname: '',
				datetimerange: [
					this.$dayjs()
					.subtract(1, 'day')
					.format('YYYY-MM-DD HH:mm'),
					this.$dayjs().format('YYYY-MM-DD HH:mm')
				],
				header: [],
				hoursData: [],
				hourBoxHeight: 'auto',
				date: this.$dayjs().format('YYYY-MM-DD'),
				dateStr: this.$dayjs().format('YYYY-MM-DD'),
				calendarData: {},
				showCalender: false,
				pickerOptions: {
					disabledDate(time) {
						return time.getTime() > Date.now();
					},
					cellClassName(date) {}
				},
				CXID: '1eaeda4a-e900-11ea-b918-286ed888c994',
				option1: {},
				option2: {},
				stateColors: {
					1: {
						color: '#ff7054',
						label: '停止'
					},

					2: {
						color: '#7dcd27',
						label: '开启'
					},

					0: {
						color: '#888888',
						label: '离线'
					},

				},
				info:{}
			};
		},
		props: {
			// wrybh: {
			// 	type: String,
			// 	default: ''
			// },
			// headerHeight: {
			// 	type: Number,
			// 	default: 0
			// },
			// scrollHeight: {
			// 	type: Number,
			// 	default: 0
			// }
		},
		watch: {
			// wrybh: {
			// 	handler: function(nv) {
			// 		this.WRYBH = nv;
			// 		this.initScxInfo();
			// 	},
			// 	immediate: true
			// },

		},
		mounted() {
		        this.info = uni.getStorageSync('warningData')
				this.initChartData();
		},
		methods: {
	
			// 到生产线详情页
			toEquipmentInfo(v) {
				console.log(v);
				uni.navigateTo({
					url: `./equipmentInfo?SBID=${v.SBID}&IMEI=${v.IMEI}&SBMC=${v.SBMC}&SCXMC=${this.currentscxname}`
				});
			},
			// handleRect() {
			// 	const self = this;
			// 	this.$nextTick(() => {
			// 		setTimeout(function() {
			// 			let queryDom = uni.createSelectorQuery().in(self) // 使用api并绑定当前组件
			// 			let promiseTop = new Promise((resolve) => {
			// 				queryDom
			// 					.select('.hour-box-wrapper')
			// 					.boundingClientRect((res) => {
			// 						let top = res.top;
			// 						// console.log(res)

			// 						resolve(top);
			// 					})
			// 					.exec()

			// 			})

			// 			let promiseBottom = new Promise((resolve) => {
			// 				queryDom
			// 					.select('.time-data-wrap')
			// 					.boundingClientRect((res) => {
			// 						let bottom = res.top;
			// 						// console.log(res)

			// 						resolve(bottom);
			// 					})
			// 					.exec()

			// 			})

			// 			Promise.all([promiseTop, promiseBottom]).then(res => {
			// 				// let hourBoxHeight = (self.screenHeight - (res[1] - res[0]) - self.headerHeight);

			// 				// if(!plus.navigator.hasNotchInScreen()){
			// 				// 	hourBoxHeight = hourBoxHeight - plus.navigator.getStatusbarHeight();
			// 				// }

			// 				let hourBoxHeight = (self.scrollHeight - (res[1] - res[0]));
			// 				self.hourBoxHeight = hourBoxHeight + 'px';
			// 			})
			// 		}, 500);


			// 	})

			// },
			open() {
				this.$refs.calendar.open();
			},
			confirm(e) {
				console.log(e);
				this.dateStr = e.fulldate
				this.gettjt();
			},
			onMonthChange(e) {
				let month = e.year + "-" + (e.month <= 9 ? '0' + e.month : e.month)
				this.date = month;
				this.getDayData();
			},
			initChartData() {
				//this.getDayData();
				this.gettjt();
			},
			// getDayData() {
			// 	qyztrl({
			// 		wrybh: this.CXID,
			// 		scyf: this.$dayjs(this.date).format('YYYY-MM')
			// 	}).then(res => {
			// 		let data = res.data;
			// 		let map = {};
			// 		if (data && data.length > 0) {
			// 			data.forEach(item => {
			// 				map[item.date] = item.status;
			// 			});
			// 		}
			// 		this.calendarData = map;
			// 	});
			// },
			gettjt(scrq) {
				let that = this;
				monitorCxzt({
				SCXID:this.info.SCXID,
				startT:this.info.FSSJ,
				endT:this.info.YJSJ,
				}).then(res => {
					res.data.zwList.forEach(x => {
						x.name = x.SBMC;
						// x.ztList = JSON.parse(x.ztList);
						if(x.ztList && x.ztList.length > 0){
							x.list = x.ztList;
							x.list.forEach(y => {
								y.end = that
									.$dayjs(y.end)
									.format('YYYY-MM-DD HH:mm');
							});
						}
					});
					res.data.scList.forEach(x => {
						x.name = x.SBMC;
						// x.ztList = JSON.parse(x.ztList);
						if(x.ztList && x.ztList.length > 0){
							x.list = x.ztList;
							x.list.forEach(y => {
									// y.end = that
									// 	.$dayjs(y.end)
									// 	.add(1, 'minute')
									// 	.format('YYYY-MM-DD HH:mm');
									y.end = that
										.$dayjs(y.end)
										.format('YYYY-MM-DD HH:mm');
							});
						}
					});
					let data1 = JSON.stringify(res.data.zwList);
					let data2 = JSON.stringify(res.data.scList);
					console.log('data1,',data1);
					console.log('data2',data2);
					this.setChart(res.data.zwList, res.data.scList);
				});
			},
			
			setChart(data, data2) {
							let statusObj = {
								1: {
									color: '#ff7054',
									label: '停止'
								},
			
								2: {
									color: '#7dcd27',
									label: '开启'
								},
			
								0: {
									color: '#fff',
									label: '离线'
								},
							};
			
							let x = [];
			
							let minutes = 0;
			
							let jszj = '';
			
							if (this.dateStr == this.$dayjs().format('YYYY-MM-DD')) {
								minutes = this.getcurrentallminute();
			
			 					jszj = this.getcurrenthhmm();
								
								//minutes = 1440;
											
								//jszj = '23:59';
							} else {
								minutes = 1440;
			
								jszj = '23:59';
							}
			
							for (let i = 0; i < minutes; i++) {
								let num = i;
			
								let h = Math.floor(num / 60);
			
								let min = num % 60;
			
								h = (h <= 9 ? '0' : '') + h;
			
								min = (min <= 9 ? '0' : '') + min;
								x.push(h + ':' + min);
			
							}
			
							let y = [];
			
							let lines = [];
			
							data.forEach((v, i) => {
								y.push(v.name);
								//背景状态（如果数据中有可以去掉）
								lines.push({
									name: '断电',
			
									z: 1,
			
									type: 'lines',
			
									coordinateSystem: 'cartesian2d',
			
									silent: true,
			
									lineStyle: {
										width: 10,
			
										color: '#fff',
			
										opacity: 1
									},
			
									data: [{
										name: v.name,
			
										coords: [
											['00:00', v.name],
											[jszj, v.name]
										]
									}]
								});
			
								v.list && v.list.forEach((item, j) => {
									let xVal1 = item.start.slice(11, 16);
			
									let xVal2 = item.end.slice(11, 16);
			
									let obj = statusObj[v.list[j].status];
			
									lines.push({
										name: obj.label,
			
										z: 2,
			
										type: 'lines',
			
										coordinateSystem: 'cartesian2d',
			
										lineStyle: {
											width: 10,
			
											color: obj.color || '#888888',
			
											opacity: 1
										},
			
										data: [{
											name: v.name,
			
											start: item.start,
			
											end: item.end,
			
											statusLabel: obj.label,
			
											coords: [
												[xVal1, v.name],
												[xVal2, v.name]
											]
										}]
									});
								});
							});
			
							let y2 = [];
			
							let lines2 = [];
			
							data2.forEach((v, i) => {
								y2.push(v.name);
								//背景状态（如果数据中有可以去掉）
								lines2.push({
									z: 1,
									type: 'lines',
									coordinateSystem: 'cartesian2d',
									silent: true,
									lineStyle: {
										width: 10,
										color: '#fff',
										opacity: 1
									},
									data: [{
										coords: [
											['00:00', v.name],
											[jszj, v.name]
										]
									}]
								});
			
								v.list &&v.list.forEach((item, j) => {
									let xVal1 = item.start.slice(11, 16);
									let xVal2 = item.end.slice(11, 16);
									let obj = statusObj[v.list[j].status];
									lines2.push({
										name: obj.label,
										type: 'lines',
										coordinateSystem: 'cartesian2d',
										lineStyle: {
											width: 10,
											color: obj.color || '#fff',
											opacity: 1
										},
										data: [{
											name: v.name,
			
											start: item.start,
			
											end: item.end,
			
											statusLabel: obj.label,
			
											coords: [
												[xVal1, v.name],
												[xVal2, v.name]
											]
										}]
									});
								});
							});
			
							let option = {
								tooltip: {
									show: true,
									formatter: function(obj) {
										let data = obj.data;
										return `<p>${data.name}</p><p>开始时间：${
					                            data.start
					                        }</p><p>结束时间：${data.end}</p><p>状态：${
					                            data.statusLabel
					                        }</p>`;
									}
								},
								grid: {
									top: 10,
									left: 10
								},
								xAxis: {
									type: 'category',
									data: x,
									interval: 0,
									axisLabel: {
										// formatter(v) {
										//     let s = v.split(':')[1];
										//     if (s % 15 === 0) {
										//         return v;
										//     } else {
										//         return '';
										//     }
										// },
										show: true,
										textStyle: {
											color: 'black'
										}
									},
									axisLine: {
										show: true,
										lineStyle: {
											color: 'black'
										}
									},
									axisTick: {
										show: true
									}
								},
								yAxis: {
									type: 'category',
									data: y,
									axisLabel: {
										interval: 0,
										show: true,
										inside: true,
										textStyle: {
											color: 'black'
										},
										padding: [-20, 0, 0, 0]
									},
									axisLine: {
										show: true,
										lineStyle: {
											color: 'black'
										}
									},
									axisTick: {
										show: false
									}
								},
								series: lines
							};
			
							if (data.length > 3) {
								option.dataZoom = [{
										start: 0, //默认为0
										end: 30, //默认为100
										type: 'slider',
										show: true,
										yAxisIndex: [0],
										handleSize: 0, //滑动条的 左右2个滑动条的大小
										width: 10,
										height: '70%', //组件高度
										right: '0', //左边的距离
										bottom: 40, //右边的距离
										borderColor: '#000',
										fillerColor: '#269cdb',
										borderRadius: 5,
										backgroundColor: '#33384b', //两边未选中的滑动条区域的颜色
										showDataShadow: false, //是否显示数据阴影 默认auto
										showDetail: false, //即拖拽时候是否显示详细数值信息 默认true
										realtime: true, //是否实时更新
										filterMode: 'filter'
									},
			
									//下面这个属性是里面拖到
									{
										type: 'inside',
										show: true,
										yAxisIndex: [0],
										zoomOnMouseWheel: false,
										moveOnMouseMove: true,
										moveOnMouseWheel: true
									},
								];
							}
							let option2 = {
								tooltip: {
									show: true,
									formatter: function(obj) {
										let data = obj.data;
										return `<p>${data.name}</p>
					            	              <p>开始时间：${data.start}</p>
					            	              <p>结束时间：${data.end}</p>
					            	              <p>状态：${data.statusLabel}</p>
					            	            `;
									}
								},
								grid: {
									top: 10,
									left: 10
								},
			
								xAxis: {
									type: 'category',
									data: x,
									interval: 0,
									axisLabel: {
										show: true,
										textStyle: {
											color: 'black'
										},
										// formatter(v) {
										//     let s = v.split(':')[1];
										//     if (s % 15 === 0) {
										//         return v;
										//     }else{
										//         return '';
										//     }
										// }
									},
									axisLine: {
										show: true,
										lineStyle: {
											color: 'black'
										}
									}
								},
								yAxis: {
									type: 'category',
									data: y2,
									axisLabel: {
										interval: 0,
										show: true,
										textStyle: {
											color: 'black'
										},
										inside: true,
										padding: [-20, 0, 0, 0]
									},
									axisTick: {
										show: false
									},
									axisLine: {
										show: true,
										lineStyle: {
											color: 'black'
										}
									}
								},
								series: lines2
							};
			
							if (data2.length > 3) {
								option2.dataZoom = [{
										start: 70, //默认为0
										end: 100, //默认为100
										type: 'slider',
										show: true,
										yAxisIndex: [0],
										handleSize: 0, //滑动条的 左右2个滑动条的大小
										width: '10',
										height: '70%', //组件高度
										right: '0', //左边的距离
										bottom: 40, //右边的距离
										borderColor: '#c3c3c3',
										fillerColor: '#269cdb',
										borderRadius: 5,
										backgroundColor: '#c3c3c3', //两边未选中的滑动条区域的颜色
										showDataShadow: false, //是否显示数据阴影 默认auto
										showDetail: false, //即拖拽时候是否显示详细数值信息 默认true
										filterMode: 'filter'
									},
			
									//下面这个属性是里面拖到
									{
										type: 'inside',
										show: true,
										yAxisIndex: [0],
										zoomOnMouseWheel: false,
										moveOnMouseMove: true,
										moveOnMouseWheel: true
									},
			
								];
							}
							this.option2 = option2;
							this.option1 = option;
							let str = JSON.stringify(this.option2)
							console.log('str',str);
						},
	
			getcurrentallminute() {
				let hour = this.$dayjs().format('HH');
				let minutes = this.$dayjs().minute();
				//console.log('m',hour,hour * 60 + minutes + 1);
				return hour * 60 + minutes +1;
			},
			getcurrenthhmm() {
				//console.log('Hm',this.$dayjs().format("HH:mm"));
				return this.$dayjs().add(1, 'minute').format("HH:mm");
			},
			getClass(date) {
				console.log(date);
			},
		}
	};
</script>
<script module="table" lang="renderjs">
	export default {
		data() {
			return {}
		},
		methods: {
			scrollEvent() {
				// 头部随滚动条滚动
				this.$refs.th.style.transform = `translate(-${this.$refs.table.scrollLeft}px)`
			}
		},
		mounted() {}
	}
</script>

<script module="echarts" lang="renderjs">
	let myChart1;
	let myChart2;
	export default {
		data() {
			return {
				chartw: "",
				charth: '',
				flag: false
			}
		},
		onLoad() {


		},
		mounted() {

			if (typeof window.echarts === 'function') {
				this.initEcharts()
			} else {
				// 动态引入较大类库避免影响页面展示
				const script = document.createElement('script')
				// view 层的页面运行在 www 根目录，其相对路径相对于 www 计算
				script.src = 'static/echarts4.9.0.js'
				script.onload = this.initEcharts.bind(this)
				document.head.appendChild(script)
			}
		},
		methods: {
			initEcharts() {
				myChart1 = echarts.init(document.getElementById('echarts1'))
				myChart2 = echarts.init(document.getElementById('echarts2'))
				// let charts = echarts.init(this.$refs.gkqkfst1);
				// charts.clear();
				// charts.setOption(option);
				// let charts2 = echarts.init(this.$refs.gkqkfst2);
				// charts2.clear();
				// charts2.setOption(option2);
				let option1 = {};
				if (this.option1 && this.option1.tooltip && this.option1.tooltip.yAxis) {
					option1 = {
						...this.option1
					};
					option1.tooltip.formatter = function(obj) {
						let data = obj.data;
						return `<p>${data.name}</p>
				              <p>开始时间：${data.start}</p>
				              <p>结束时间：${data.end}</p>
				              <p>状态：${data.statusLabel}</p>
				            `;
					}
					option1.tooltip.confine = true;
				}

				myChart1 && myChart1.setOption(option1)
				let option2 = {};
				if (this.option2 && this.option2.tooltip){
					option2 = {
						...this.option2
					};
					option2.tooltip.formatter = function(obj) {
						let data = obj.data;
						return `<p>${data.name}</p>
				              <p>开始时间：${data.start}</p>
				              <p>结束时间：${data.end}</p>
				              <p>状态：${data.statusLabel}</p>
				            `;
					}
				}


				myChart2 && myChart2.setOption(option2)
			},
			updateEcharts1(newValue, oldValue, ownerInstance, instance) {
				if (!newValue) {
					return;
				}
				//自定义状态的文字和颜色   
				let option = JSON.parse(JSON.stringify(newValue));
				// 渲染后触发的option1
				option.tooltip.formatter = function(obj) {
					let data = obj.data;
					return `             ${data.name}
				          开始时间：${data.start}
				          结束时间：${data.end}
				          状态：${data.statusLabel}
				        `;
				}
				option.tooltip.confine = true;
				// 监听 service 层数据变更
				// #ifdef APP-PLUS
				// document.getElementById('echarts1').style.width = plus.screen.resolutionWidth + 'px'
				// document.getElementById('echarts1').style.height = (plus.screen.resolutionHeight - 120) + 'px'
				// #endif
				myChart1 && myChart1.clear()
				myChart1 && myChart1.setOption(option)
				myChart1 && myChart1.resize()
			},

			updateEcharts2(newValue, oldValue, ownerInstance, instance) {
				//自定义状态的文字和颜色
				if (!newValue) {
					return;
				}
				let option = JSON.parse(JSON.stringify(newValue));
				// 渲染后触发的option2
				option.tooltip.formatter = function(obj) {
					let data = obj.data;
					// return `<p>${data.name}</p>
				 //          <p>开始时间：${data.start}</p>
				 //          <p>结束时间：${data.end}</p>
				 //          <p>状态：${data.statusLabel}</p>
				 //        `;
				 return `          ${data.name}
				       开始时间：${data.start}
				       结束时间：${data.end}
				       状态：${data.statusLabel}
				     `;
				}
				option.tooltip.confine = true;
				// 监听 service 层数据变更
				// #ifdef APP-PLUS
				// document.getElementById('echarts2').style.width = plus.screen.resolutionWidth + 'px'
				// document.getElementById('echarts2').style.height = (plus.screen.resolutionHeight - 120) + 'px'
				// #endif
				myChart2 && myChart2.clear()
				myChart2 && myChart2.setOption(option)
				myChart2 && myChart2.resize()
			},

			format(date, fmt) {
				var o = {
					"M+": date.getMonth() + 1, //月份 
					"d+": date.getDate(), //日 
					"h+": date.getHours(), //小时 
					"m+": date.getMinutes(), //分 
					"s+": date.getSeconds(), //秒 
					"q+": Math.floor((date.getMonth() + 3) / 3), //季度 
					"S": date.getMilliseconds() //毫秒 
				};
				if (/(y+)/.test(fmt)) {
					fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
				}
				for (var k in o) {
					if (new RegExp("(" + k + ")").test(fmt)) {
						fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k])
							.length)));
					}
				}
				return fmt;
			}
		}
	}
</script>
<style scoped lang="scss">
	.title {
		display: flex;
		position: relative;
		padding: 18rpx 0;
		align-items: center;
	}

	.title .legend {
		display: flex;
		border-radius: 18rpx;
	}

	.title .legend .cell {
		display: flex;
		align-items: center;
		color: #000000;
	}


	.title .legend .cell .icon {
		height: 48rpx;
		line-height: 48rpx;
		width: 72rpx;
		background-color: #7dcd27;
		color: #fff;
		font-size: 26rpx;
	}

	.title .legend .cell:first-child .icon,
		{
		border-radius: 10rpx 0 0 10rpx;
	}

	.title .legend .cell:last-child .icon {
		border-radius: 0 10rpx 10rpx 0;
	}

	.title .legend .cell .text {
		margin: 0 16rpx;
		font-size: 28rpx;
	}

	.c-red {
		background-color: #ff7048;
	}

	.c-gray {
		background-color: #888888;
	}

	.c-green {
		background-color: #7dcd27;
	}

	.on {
		color: #007aff;
	}

	.time-data-wrap {
		display: flex;
		flex-direction: column;
		width: 100%;
		position: relative;
	}

	.time-data-wrap .left {
		width: 120rpx;
		position: relative;
		z-index: 1;
	}


	/* #ifdef APP-PLUS */
	// .time-data-wrap .left div {
	// 	width: 120rpx;
	// 	height: 48rpx;
	// 	box-sizing: border-box;
	// 	background-color: #fff;
	// 	margin: 10rpx 0;
	// 	border: 1px solid #0000FF;
	// }
	/* #endif */
	/* #ifdef H5 */
	// .time-data-wrap .left div {
	// 	width: 120rpx;
	// 	height: 48rpx;
	// 	box-sizing: border-box;
	// 	background-color: #fff;
	// 	margin: 8rpx 0;
	// }
	/* #endif */


	.time-data-wrap .right .tb {
		width: calc(100vw - 146rpx);
		overflow: auto;
	}

	.time-data-wrap .left .tb div {
		float: left;
		height: 48rpx;
		box-sizing: border-box;
		margin: 10rpx 5rpx;
	}

	.time-data-wrap .right {}

	.time-data-wrap .right .tb div.tr {
		float: left;
		height: 48rpx;
		box-sizing: border-box;
		margin: 10rpx 5rpx;
	}

	.time-data-wrap .right .tb div.td {
		margin: 0 5rpx;
	}

	.th {
		position: sticky;
		top: 120rpx;
	}

	.th div {
		width: 200rpx;
		white-space: nowrap;
		margin: 10rpx 5rpx;
		overflow: hidden;
		height: 48rpx;
		display: flex;
		align-items: center;
	}

	.hours-data {
		width: 200rpx;
		border-radius: 12.077325rpx;
		flex-shrink: 0;
	}

	.mark {
		background-color: #fff;
		color: #fff;
		width: 28rpx;
		height: 40vh;
		right: 0;
		z-index: 666;
		position: absolute;
	}

	.th-item {
		box-sizing: border-box;
	}

	.th-item image {
		height: 35rpx;
		width: 35rpx;
	}

	.zy-tabs1 {
		flex-wrap: wrap;

		li {
			margin-top: 10rpx;
		}
	}

	.date-input {
		float: right;
		width: 200rpx;
		height: 50rpx;
		color: #000000;
		line-height: 50rpx;
		font-size: 26rpx;
		cursor: pointer;
		padding-left: 10rpx;
		border-radius: 10rpx;
		border: 2rpx solid rgb(50, 131, 183);
		outline: none;
	}

	.equi-title {
		font-size: 27rpx;
		font-weight: bold;
	}
</style>
