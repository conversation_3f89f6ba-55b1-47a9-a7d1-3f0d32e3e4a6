<template>
	<div>
		<div v-for="(item, index) in list" :key="index">
			<ul class="pd-ullst1">
					<li class="to-detail">
						<em>设备IMEI</em>
						<div>
							<i @click="setImeiCircle(item)">{{item.IMEI || '-'}} </i>
							<image v-show="item.ZNSBLX === 'ZD' || item.ZNSBLX === 'DL'" @click="setImeiCircle(item)" class="gray-icon-reverse"
							src="@/static/app/images/gray-arrow.png" mode="widthFix">
							 </image>
						</div>
					</li>
				<li><em>检测时间</em><i>{{item.JCSJ || '-'}}</i>
				</li>
				<li><em>上报时间</em><i style="color: rgb(255, 0, 0);">{{item.SBSJ || '-'}}</i></li>
				<li v-if="item.ADS !== undefined"><em>开关状态</em>
					<i style="color: rgb(255, 0, 0);" v-if="item.ADS == '1'">弹起</i>
					<i style="color: rgb(58, 185, 24);" v-if="item.ADS == '0'">收缩</i>
				</li>
				<li><em>设备状态</em><i :class="{'abnormal':item.ERR != '正常','normal':item.ERR == '正常',}">{{item.ERR || '-'}}</i></li>
				<li v-if="item.ZNSBLX != 'RAD'"><em>设备电压</em><i :class="{'abnormal':item.VOL_ZT == 1,'normal':item.VOL_ZT == 0,}">{{item.VOL || '-'}}</i></li>
				<li v-if="item.ZNSBLX == 'RAD'"><em>设备电压</em><i>{{getBatteryNumVol(item)}}</i></li>
				
				<!-- 设备类型是振动，电量 -->
				<template v-if="item.ZNSBLX === 'ZD' || item.ZNSBLX === 'DL'">
					<li><em>信号功率</em><i :class="{'abnormal':item.SP_ZT == 1,'normal':item.SP_ZT == 0,}">{{item.SP || '-'}}</i></li>
					<li><em>总功率</em><i :class="{'abnormal':item.TP_ZT == 1,'normal':item.TP_ZT == 0,}">{{item.TP || '-'}}</i></li>
					<li><em>信噪比</em><i :class="{'abnormal':item.SINR_ZT == 1,'normal':item.SINR_ZT == 0,}">{{item.SINR || '-'}}</i></li>
					<li><em>信号质量</em><i :class="{'abnormal':item.RSRQ_ZT == 1,'normal':item.RSRQ_ZT == 0,}">{{item.RSRQ || '-'}}</i></li>
				</template>
					
				<!-- 设备类型是PH -->
				<template v-if="item.ZNSBLX === 'PH'">
					<li><em>基站经度</em><i>{{Number(item.LON).toFixed(4) || '-'}}</i></li>
					<li><em>基站纬度</em><i>{{Number(item.LAT).toFixed(4) || '-'}}</i></li>
					<li><em>网络模式</em><i>{{item.SYS_MODE || '-'}}</i></li>
					<li><em>接收信号码功率</em><i>{{item.RSCP || '-'}}</i></li>
	           </template>
			   
				<!-- 设备类型是辐射 -->
				<template v-if="item.ZNSBLX === 'RAD'">
					<li ><em>当前电池编号</em><i>{{item.BATTERYNUM || '-'}}</i></li>
					<li ><em>信号功率</em><i>{{item.SP || '-'}}</i></li>
					<li ><em>信号总功率</em><i>{{item.TP.toString() || '-'}}</i></li>	   
					<li ><em>信噪比</em><i>{{item.SINR || '-'}}</i></li>
					<li><em>信号质量</em><i>{{item.RSRQ || '-'}}</i></li>	
					<li ><em>小区基站</em><i>{{item.PCI || '-'}}</i></li>	
				</template>
			</ul>
			<div class="gap"></div>
		</div>

	</div>

</template>

<script>
	export default {
		props: {
			list: {
				type: Array,
				default: () => [],
				require: true
			}

		},
		methods: {
			//去设置阈值
			setImeiCircle(item){
				if(item.ZNSBLX === 'PH'){
					return;
				}
				this.$emit('setImeiCircle',item.IMEI)
			}, 
			//获取辐射设备电压值
			getBatteryNumVol(item) {
				if (item.BATTERYNUM === '2') {
					return item.VOL2;
				} else if(item.BATTERYNUM === '1') {
					return item.VOL1;
				}else{
					return '-'
				}
			},
		}
	}
</script>
<style scope>
.pd-ullst1{
    padding-left: 22rpx;
}
.pd-ullst1 > li {
    padding: 0rpx;
    line-height: 70rpx;
}
.pd-ullst1 > li + li {
    border: none;
}
	.pd-ullst1 li em {
		font-size: 13px;
		color: #999;
		width: 140rpx;
		display: inline-block;
	}
	.to-detail{
		display: flex;
		justify-content: space-between;
		align-items: center;

	}
	.to-detail .gray-icon-reverse {
		width: 26rpx;
		transform: rotate(180deg);
	}
	.normal{
		color:#3ab918;
	}
	.abnormal{
		color:#ff0000;
	}
	.pd-ullst1 li em{
		width:200rpx;
	}
</style>
