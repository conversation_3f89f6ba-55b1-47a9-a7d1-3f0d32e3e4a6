/** @format */

import http from '@/common/net/http.js';
import Vue from 'vue';
import dayjs from '@/components/dayjs/dayjs.min.js';

const CACHE_KEY_USER_INFO = 'user_info';

const CACHE_KEY_TOKEN = 'token';
const CACHE_KEY_TOKEN_TIME = 'token_time'
const CACHE_KEY_ISLOGIN = 'is_login';
const CACHE_KEY_USER_ID = 'user_id';

const CACHE_KEY_PASSWORD = 'password';

export const LOGIN_CACHE_KEYS = {
	userInfo: CACHE_KEY_USER_INFO,
	token: CACHE_KEY_TOKEN,
	token_time: CACHE_KEY_TOKEN_TIME,
	userId: CACHE_KEY_USER_ID,
	password: CACHE_KEY_PASSWORD,
	is_login: CACHE_KEY_ISLOGIN
};

export const loginByPassword = (userId, password) => {
	//console.log('obj',userId, password)
	clearAuthInfoBeforeLogin(userId);
	http.post(`${http.loginUrl}/mobileLogin`, {
			userid: userId,
			password
		}).then((resp) => {
			console.log('我请求了登录')
			uni.setStorageSync('userInfo', resp.userInfo);
			uni.setStorageSync('password', password);
			// 重置登录状态
			uni.setStorageSync('IS_LOGIN', true);
			onLoginSuccess(userId, resp);
			uni.$emit('onLoginSuccess', resp.userInfo);
		})
		.catch((error) => {

			uni.$emit('onLoginFail', error || error.errMsg);
		});
	



	// // #ifdef H5
	// http.post(`${http.loginUrl}/dataservice/sjcl/api/api_mobile/system/iotManage/login`, {
	// 		userid: userId,
	// 		password
	// 	})
	// 	.then((resp) => {
	// 		// 重置登录状态
	// 		uni.setStorageSync('IS_LOGIN', true);
	// 		console.log('resp', JSON.parse(resp.data).userInfo);
	// 		uni.setStorageSync('userInfo', JSON.parse(resp.data).userInfo);
	// 		uni.setStorageSync('user_info', JSON.parse(resp.data).userInfo);
	// 		onLoginSuccess(userId, JSON.parse(resp.data));
	// 		uni.$emit('onLoginSuccess', JSON.parse(resp.data).userInfo);
	// 	})
	// 	.catch((error) => {

	// 		uni.$emit('onLoginFail', error || error.errMsg);
	// 	});
	// // #endif

};

const onLoginSuccess = (userId, loginResp) => {
	//缓存用户ID
	uni.setStorage({
		key: CACHE_KEY_USER_ID,
		data: userId,
		fail(error) {}
	});

	//缓存token
	uni.setStorage({
		key: CACHE_KEY_TOKEN,
		data: loginResp.jwtToken,
		fail(error) {}
	});

	//缓存用户基本信息
	uni.setStorage({
		key: CACHE_KEY_USER_INFO,
		data: loginResp.userInfo,
		fail(error) {}
	});

	// #ifdef APP-PLUS
	setAuthorityUserOnAndroid(userId, loginResp);
	// #endif
};

// #ifdef APP-PLUS
const setAuthorityUserOnAndroid = (userId, loginResp) => {
	if (plus.android) {
		let AuthorityUser = plus.android.importClass(
			'com.bovosz.webapp.auth.AuthorityUser'
		);
		if (AuthorityUser) {
			let authorityUser = new AuthorityUser();
			authorityUser.setUserId(userId);
			authorityUser.setToken(loginResp.jwtToken);
			let AuthAgent = plus.android.importClass(
				'com.bovosz.webapp.auth.AuthAgent'
			);
			let authAgent = AuthAgent.getInstance();
			authAgent.setAuthorityUser(authorityUser);
		}
	}
};
// #endif

/**
 * 登录前清除用户缓存
 */
const clearAuthInfoBeforeLogin = (userId) => {
	let cachedUserId = uni.getStorageSync(CACHE_KEY_USER_ID);
	if (cachedUserId && cachedUserId !== userId) {
		for (let p in LOGIN_CACHE_KEYS) {
			try {
				uni.removeStorageSync(LOGIN_CACHE_KEYS[p]);
			} catch (error) {}
		}
		uni.removeStorageSync('userInfo');
	}
};

export default {
	loginByPassword,
	getUserOrgid() {
		let userInfo = uni.getStorageSync('userInfo');
		return userInfo.orgid;
	},

	getAuthUser() {
		return uni.getStorageSync(CACHE_KEY_USER_INFO) || {};
	},

	getAuthUserId() {
		return this.getAuthUser().id || '';
	},

	//获取用户部门ID
	getUserDepartmentId() {
		return this.getAuthUser().bmbh || '';
	}
};
