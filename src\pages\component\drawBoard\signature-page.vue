<template>
	<Page title="勘察画板" :mainStyle="mainStyle">
		<signature 
			ref="signature"
			style="margin-top: 10px;"
			:width="width"
			:height="height"
		/>
		<view class="flex-row-layout" style="margin-top: 20rpx;">
			<view class="power-button power-button-primary clear-button" @click="onClickClear">清除</view>
			<view class="power-button power-button-primary" style="margin-left: auto;" @click="onClickConfirm">确定</view>
		</view>
	</Page>
</template>

<script>
	import Page from '@/pages/component/Page.vue';
	import signature from './signature.vue';
	import styleUtil from '@/common/style.js';
	
	export default {
		components: {
			Page, signature
		},
		
		data() {
			return {
				width: 300,
				height: 220,
				pageHeight: 700
			}
		},
		
		computed: {
			mainStyle: function() {
				let style = {
					width: 'calc(100% - 20px)',
					height: `${this.pageHeight}px`,
					padding: '0',
					'background-color': '#fff'
				}
				return style;
			}
		},
		
		mounted() {
			let _self = this;
			styleUtil.getScreenLayout()
				.then(layout => {
					_self.width = layout.width - 20;
					_self.pageHeight = layout.height;
				})
		},
		
		methods: {
			onClickConfirm() {
				this.$refs.signature.generate()
					.then(image => {
						
						// todo 这里就是图片

						// // #ifdef APP-PLUS
						// let base64 = image.tempFilePath;
						// const bitmap = new plus.nativeObj.Bitmap("test");
						// bitmap.loadBase64Data(base64, function() {
						// 	const url = "_doc/" + new Date().getTime() + ".png";  // url为时间戳命名方式
						// 	console.log('saveHeadImgFile', url)
						// 	bitmap.save(url, {
						// 		overwrite: true,  // 是否覆盖
						// 		// quality: 'quality'  // 图片清晰度
						// 	}, (i) => {
						// 		uni.saveImageToPhotosAlbum({
						// 			filePath: url,
						// 			success: function() {
						// 				uni.showToast({
						// 					title: '图片保存成功',
						// 					icon: 'none'
						// 				})
						// 				bitmap.clear()
						// 			}
						// 		});
						// 	}, (e) => {
						// 		uni.showToast({
						// 			title: '图片保存失败',
						// 			icon: 'none'
						// 		})
						// 		bitmap.clear()
						// 	});
						// }, (e) => {
						// 	uni.showToast({
						// 		title: '图片保存失败',
						// 		icon: 'none'
						// 	})
						// 	bitmap.clear()
						// });
						// // #endif

						uni.$emit('kcsyt_print', image);
						uni.navigateBack({
							delta: 1
						})
					})
			},
			
			onClickClear() {
				this.$refs.signature.reset();
			}
		}
	}
</script>

<style scoped>
	.clear-button {
		background-color: transparent;
		color: #1E8EEF;
		border: 1px solid #1E8EEF;
	}
</style>
