/** @format */

import http from '@/common/net/http.js';
import { deepCopyObject } from '@/common/merge.js';

//定义从表单数据中读取附件信息的键
export const ATTACH_DATA_KEY = 'T_ATTACH';

//从表单配置中读取对应物理表名的键
const TEMPLATE_TABLE_KEY = 'templateTable';

//从表单配置中读取对应模板ID的键
const TEMPLATE_ID_KEY = 'templateId';

/**
 * 从链接中解析动态表单模板ID
 */
const parseTemplateIdFromLink = (link) => {
    let urlWithNoParams = link.split('?')[0];
    let lastSlashIndex = urlWithNoParams.lastIndexOf('/');
    if (lastSlashIndex !== -1) {
        return urlWithNoParams.substring(lastSlashIndex + 1);
    }
    return '';
};

/**
 * 查询服务端配置的所有表单
 */
const getTemplateList = () => {
    return new Promise((resolve, reject) => {
        http.post(`${http.loginUrl}/mobile/base/getAllDynamicFormModel`, {})
            .then((templateListStr) => {
                let templateList = JSON.parse(templateListStr);
                resolve(templateList);
            })
            .catch((error) => {
                reject(error);
            });
    });
};

const getTemplateById = (templateId) => {
    let path = `dynamicform/viewresolvercontroller/getmodel/${templateId}`;
    return new Promise((resolve, reject) => {
        http.post(`${http.loginUrl}/${path}?isView=false`, {})
            .then((templateConfig) => {
                let templateJsonStr = templateConfig.MODELJSON;
                try {
                    let templateJson = JSON.parse(templateJsonStr);
                    resolve(templateJson);
                } catch (e) {
                    reject(
                        `解析表单出错：${JSON.stringify(
                            templateJsonStr,
                            null,
                            4
                        )}`
                    );
                }
            })
            .catch((error) => {
                reject(error);
            });
    });
};

const getRecordData = (templateId, businessId) => {
    // return http.post(`${http.loginUrl}/dynamicform/viewresolvercontroller/getrecorddata/${businessId}/${templateId}`, {})
    return new Promise((resolve, reject) => {
        http.post(`${http.loginUrl}/mobile/dynamicform/getrecodedata`, {
            recordId: businessId,
            mbbh: templateId
        })
            .then((resp) => {
                let data = resp.data || {};
                if (resp.data_fjxx && resp.data_fjxx.length > 0) {
                    data[ATTACH_DATA_KEY] = resp.data_fjxx;
                }
                resolve(data);
            })
            .catch((error) => {
                reject(error);
            });
    });
};

const getRecordDataComplex = () => {
    let params = {
        table: 'T_WL_CSBD1',
        primaryKey: 'XH',
        XH: '5cafe1c2-8f19-4678-9e40-43043009e599',
        bbxh: '20190225090858313050a4cb2d4d60b5701337ef2ea500'
    };
    return http.post(
        `${http.loginUrl}/mobile/dynamicform/dynamicformquery`,
        params
    );
};

/**
 * 提交表单数据至服务端
 * @param {String} recordId 表单数据唯一ID
 * @param {Object} template 表单模板配置
 * @param {Object} data 表单数据
 */
const submitFormData = (template, data, recordId, recordInfo) => {
    let params = {
        clientType: 'mobile_web',
        userid: 'SYSTEM',
        templateId: template[TEMPLATE_ID_KEY]
    };

    if (recordId) {
        params.recordId = recordId;
    }
    params.recordInfo = recordInfo;
    let copyData = deepCopyObject(data);
    copyData.MBBH = template[TEMPLATE_ID_KEY];
    let tableName = template[TEMPLATE_TABLE_KEY];
    params[tableName] = copyData;
    return http.post(`${http.loginUrl}/mobile/base/newdynamicformsave`, params);
};

const uploadFormAttach = () => {};

export default {
    parseTemplateIdFromLink,
    getTemplateList,
    getTemplateById,
    getRecordData,
    submitFormData,
    getRecordDataComplex
};
