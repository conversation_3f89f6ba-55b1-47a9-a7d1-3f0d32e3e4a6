<!-- @format -->

<template>
    <div>
        <div class="item">
            <p class="label" :class="{ star: options.type === 'add' }">
                IMEI号
            </p>
            <div class="rp">
                <input
                    type="text"
                    :disabled="isDisabled"
                    v-model="info.imei"
                    class="zy-input1"
                    placeholder="请扫描或手动输入后6位数"
                    style="font-size: 31rpx"
                    @input="search(info.imei)"
                />
                <image
                    v-show="!isDisabled"
                    src="@/static/app/images/sysic.png"
                    class="pd-sysbtn"
                    @click="scanCode()"
                />
            </div>
        </div>
        <div class="item">
            <p class="label">设备名称</p>
            <div class="rp">
                <input
                    type="text"
                    disabled
                    v-model="info.sbmc"
                    class="zy-input1"
                    placeholder="扫码后获取设备名称"
                />
            </div>
        </div>
        <div class="item">
            <p class="label">设备用途</p>
            <div class="rp">
                <input
                    type="text"
                    disabled
                    v-model="info.sblx"
                    class="zy-input1"
                    placeholder="扫码后获取设备用途"
                />
            </div>
        </div>
        <div class="item">
            <p class="label">产治污线</p>
            <div class="rp">
                <input
                    disabled
                    type="text"
                    v-model="info.cxmc"
                    class="zy-input1"
                    placeholder="扫码后获取产治污线"
                />
            </div>
        </div>

        <u-select
            value-name="IMEI"
            label-name="IMEI"
            mode="single-column"
            :list="imeiList"
            v-model="imeiShow"
            @confirm="selectimei"
        ></u-select>
    </div>
</template>

<script>
import {
    getIMEIData,
    validIMEIMaintenDetail
} from '@/api/iot/equipmentMaintenance.js';
import { znsb, getValidate } from '@/api/iot/enterprise.js';
export default {
    props: {
        options: {
            type: Object,
            default: () => {}
        },
        isDisabled: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            imeiShow: false,
            imeiList: [],
            info: {
                imei: '',
                sbmc: '',
                sblx: '',
                cxmc: ''
            },
            rules: {
                imei: {
                    required: true,
                    message: '请扫描获取IMEI'
                }
            }
        };
    },
    watch: {},
    onHide() {},
    created() {},
    mounted() {},
    onBackPress() {},
    methods: {
        //更新表单
        updateInfo(newInfo) {
            for (const key in this.info) {
                this.$set(this.info, key, newInfo[key]);
            }
        },

        //选择IMEI设备
        async selectimei(v) {
            this.info.imei = v[0].value;
            this.validImei(v);
        },
        //取消选择imei
        cancel() {
            console.log('取消选择');
        },
        // 校验imei码是否已经被绑定，如果已绑定，则判断是否该企业的设备
        //如果未绑定，提示该imei号未绑定
        validImei(v) {
            // 先校验选择的码是不是已经被绑定
            getValidate({
                IMEI: v[0].value
            }).then(async (res) => {
                // IMEI未被绑定
                if (res.data.length === 0) {
                    uni.showModal({
                        title: '提示',
                        content: `该IMEI没有绑定产治污设备`,
                        showCancel: false,
                        success: function (res) {
                            if (res.confirm) {
                                console.log('用户点击确定');
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        }
                    });
                    this.info.imei = '';
                }
                // IMEI已经被绑定
                else if (res.data && res.data.length > 0) {
                    //如果是更换设备类型或者拆除设备，需要判断是否已被别的运维工单绑定，如果已经绑定需提示已被运维
                    let maintenanceType = this.options.ywlx; //运维类型
                    if (maintenanceType === '1' || maintenanceType === '4') {
                        const { data } = await validIMEIMaintenDetail({
                            IMEI: v[0].value
                        });
                        if (data.length) {
                            let objTips = data.find(
                                (item) => item.CODE === maintenanceType
                            );
                            uni.showModal({
                                title: '提示',
                                content: `${objTips.TEXT}`,
                                showCancel: false,
                                success: (res) => {
                                    console.log('用户点击确定');
                                    this.info.imei = '';
                                }
                            });
                        } else {
                            this.getIMEIMsg(v);
                        }
                    } else {
                        this.getIMEIMsg(v);
                    }
                }
            });
        },
        //获取imei相关信息
        async getIMEIMsg(v) {
            let params = {
                ywlx: this.options.ywlx,
                imei: v[0].value
            };
            try {
                const data = await getIMEIData(params);
                if (data.wrybh != this.options.wrybh) {
                    uni.showModal({
                        title: '提示',
                        content: `该设备不属于该企业：${this.options.wrymc}`,
                        showCancel: false,
                        success: function (res) {
                            console.log('用户点击确定');
                        }
                    });
                    this.info.imei = '';
                } else {
                    for (const key in data) {
                        if (key == 'ywmx') {
                            let innerData = data['ywmx'];
                            for (const key1 in innerData) {
                                innerData[key1] =
                                    innerData[key1] === '' ||
                                    innerData[key1] == null
                                        ? '-'
                                        : innerData[key1];
                            }
                        } else {
                            this.$set(
                                this.info,
                                key,
                                data[key] === '' || data[key] == null
                                    ? '-'
                                    : data[key]
                            );
                        }
                    }
                    this.$emit('updateInfo', data);
                }
            } catch (error) {
                console.log(error);
            }
        },
        //搜索
        search(imei) {
            if (imei.length < 5) {
                if (this.timer) {
                    clearTimeout(this.timer);
                }
                this.timer = setTimeout(() => {
                    uni.showToast({
                        title: '请先输入5位以上的IMEI号',
                        icon: 'none'
                    });
                }, 1000);
                return;
            }
            if (imei.length >= 5) {
                if (this.timer) {
                    clearTimeout(this.timer);
                }
                this.timer = setTimeout(() => {
                    this.getImei(imei);
                }, 1000);
            }
        },

        // 查询完整imei码
        getImei(imei) {
            znsb({
                IMEI: imei
                // IMEI: '513710'
                // IMEI: '34040'
            }).then((res) => {
                if (res?.data?.length) {
                    uni.hideKeyboard(); //隐藏软键盘
                    this.imeiList = res.data;
                    this.imeiShow = true;
                    this.isGetIMEI = true;
                } else {
                    uni.showModal({
                        title: '提示',
                        content: '未匹配到IMEI号，请检查输入！',
                        showCancel: false,
                        success: function (res) {
                            console.log('用户点击确定');
                        }
                    });

                    this.isGetIMEI = false;
                }
            });
        },
        //扫码
        scanCode() {
            uni.scanCode({
                scanType: ['qrCode'],
                success: (res) => {
                    this.fromScan = true;
                    let imei = res.result.split(';')[0];
                    if (imei != '') {
                        this.search(imei);
                    }
                },
                fail: (err) => {
                    uni.showToast({
                        title: '未识别到二维码！',
                        icon: 'none'
                    });
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.pd-sysbtn {
    margin-left: 18rpx;
}
</style>
