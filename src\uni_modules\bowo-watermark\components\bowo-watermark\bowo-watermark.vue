<template>
    <view class="bowo-watermark">
        <canvas 
            v-if="prepared"
            :id="canvasId"
            :canvas-id="canvasId"
            :style="canvasDimens"
        />
    </view>
</template>

<script>
    import platform from 'bowo-sdk/util/platform.js'
    import watermark from './watermark.js'
    
    const DIMEN_BASE_WIDTH = 1365
    const DIMEN_BASE_HEIGHT = 2208
    const DIMEN_BASE_WIDTH_IOS = 1008
    const DIMEN_BASE_HEIGHT_IOS = 1344
    const FONT_SIZE_BASE_WIDTH = '455'
    
    //IOS对画布尺寸有限制
    const LIMIT_CANVAS_AREA_IOS = 16777216
    
    export default {
        name: 'BowoWatermark',
        props: {
            canvasId: {
                type: String,
                default: 'watermark'
            },
            
            color: {
                type: String,
                default: '#ff0000'
            },
            
            fontSize: {
                type: Number,
                default: 10
            }
        },
        
        data() {
            return {
                originalDimens: [600, 1200],
                dimens: [600, 1200],
                prepared: false,
                canvasContext: null,
            }
        },
        
        computed: {
            canvasDimens: function() {
                return `width: ${this.dimens[0]}px; height: ${this.dimens[1]}px`
            }
        },
        
        methods: {
            /**
             * 在图片上绘制水印
             * @param {String} imageUrl
             */
            paintWatermark(imageUrl, multiLineTexts, fileName) {
                if((imageUrl || null) === null) {
                    throw '加水印原图地址不能为空'
                }
                
                let taskParams = {
                    imageUrl,
                    multiLineTexts,
                    fileName
                }
                
                return this.prepareCanvas(taskParams)
                    .then(this.resolveImageDimens)
                    .then(this.drawImageAndWatermark)
                    .then(this.exportImage)
            },
            
            prepareCanvas(taskParams) {
                if(this.prepared) {
                    return Promise.resolve(taskParams)
                }
                return new Promise((resolve, reject) => {
                    this.$nextTick(() => {
                        try {
                            this.canvasContext = uni.createCanvasContext(this.canvasId, this)
                            this.prepared = true
                            resolve(taskParams)
                        } catch(error) {
                            reject(`canvasContext未正确初始化`)
                        }
                    })
                })
            },
            
            /**
             * 确定原图的尺寸
             * @param {Object} taskParams
             */
            resolveImageDimens(taskParams) {
                return new Promise((resolve, reject) => {
                    watermark.resolveImageDimens(taskParams.imageUrl)
                        .then(dimens => {
                            this.originalDimens = dimens
                            this.dimens = watermark.shouldScaleImage() ? this.scaleDimens(dimens) : dimens
                            //canvas尺寸变更，会进行重绘，需要延迟一段时间canvas尺寸重绘完成
                            setTimeout(() => {
                                resolve(taskParams)
                            }, 300)
                        })
                        .catch(error => {
                            console.log(`解析图片尺寸出错：${error}`)
                            reject(error)
                        })
                })
            },
            
            /**
             * 绘制原图并在原图上绘制水印文字
             * @param {Object} taskParams
             */
            drawImageAndWatermark(taskParams) {
                return new Promise((resolve, reject) => {
                    let canvas = this.canvasContext
                    if(watermark.shouldScaleImage()) {
                        canvas.drawImage(taskParams.imageUrl, 0, 0, this.originalDimens[0], this.originalDimens[1], 0, 0, this.dimens[0], this.dimens[1])
                    } else {
                        canvas.drawImage(taskParams.imageUrl, 0, 0)
                    }
                    this.drawWatermark(canvas, taskParams.multiLineTexts)
                    canvas.draw(false, () => {
                        resolve(taskParams)
                    })
                })
            },
            
            drawWatermark(canvas, multiLineTexts) {
                if(multiLineTexts.length === 0) {
                    return
                }
                
                canvas.fillStyle = this.color
                let fontSize = this.calculateAdapterFontSize()
                canvas.setFontSize(fontSize)
                let lineMargin = this.calculateLineMargin()
                
                let startX = this.calculateStartX()
                let startY = this.dimens[1] - this.calculateStartY()
                let index = multiLineTexts.length - 1
                for(index; index >=0; index--) {
                    let lineText = multiLineTexts[index]
                    canvas.fillText(lineText, startX, startY)
                    startY -= fontSize + lineMargin
                }
            },
            
            /**
             * 导出带水印的图片
             */
            exportImage(taskParams) {
                let { imageUrl, fileName } = taskParams
                let imageFileName = this.parseFileName(imageUrl, fileName)
                let suffix = this.resolveImageSuffix(imageFileName)
                let width = this.dimens[0]
                let height = this.dimens[1]
                let self = this
                return new Promise((resolve, reject) => {
                    uni.canvasToTempFilePath({
                        canvasId: this.canvasId,
                        x: 0,
                        y: 0,
                        width,
                        height,
                        destWidth: width,
                        destHeight: height,
                        fileType: suffix,
                        success(resp) { 
                            let pathOrBase64 = resp.tempFilePath
                            //h5为base64
                            // #ifdef H5
                            let blobFile = watermark.base64ToBlobFile(pathOrBase64, imageFileName)
                            resolve({
                                path: window.URL.createObjectURL(blobFile),
                                file: blobFile
                            })
                            // #endif
                            
                            // #ifndef H5
                            resolve({
                                path: pathOrBase64
                            })
                            // #endif
                            self.canvasContext.clearRect(0, 0, self.dimens[0], self.dimens[1])
                        },
                        fail(error) {
                            reject(error)
                        }
                    }, this)
                })
            },
            
            calculateStartX() {
                return this.adaptePixleToImageDimen(10)
            },
            
            calculateStartY() {
                return this.adaptePixleToImageDimen(10)
            },
            
            calculateAdapterFontSize() {
                return this.adaptePixleToImageDimen(this.fontSize)
            },
            
            /**
             * 导出行间距
             */
            calculateLineMargin() {
                return this.adaptePixleToImageDimen(5)
            },
            
            adaptePixleToImageDimen(pixle) {
                let adaptedPixle = pixle * this.dimens[0] / FONT_SIZE_BASE_WIDTH
                let greaterAdaptedPixle = Math.floor(adaptedPixle)
                return greaterAdaptedPixle % 2 === 0 ? greaterAdaptedPixle : Math.ceil(adaptedPixle)
            },
            
            parseFileName(imageUrl, fileName) {
                if(fileName) {
                    return fileName
                }
                let lastSlashIndex = imageUrl.lastIndexOf('/')
                if(lastSlashIndex !== -1) {
                    return imageUrl.substring(lastSlashIndex + 1)
                }
                return null
            },
            
            resolveImageSuffix(fileName) {
                let notNullFileName = fileName || ''
                let lastDotIndex = notNullFileName.lastIndexOf('.')
                if(lastDotIndex !== -1) {
                    return notNullFileName.substring(lastDotIndex + 1)
                }
                return 'png'
            },
            
            scaleDimens(dimens) {
                let width = dimens[0]
                let height = dimens[1]
                
                //无论是Android还是IOS平台，对图片的处理都有尺寸限制，设置基准的以竖屏为准，
                //当读取的图片尺寸判断照片为横向时，需要反转基准值
                let inverse = width > height
                let basePortraitWidth = platform.isIOSBrowser() ? DIMEN_BASE_WIDTH_IOS : DIMEN_BASE_WIDTH
                let basePortraitHeight = platform.isIOSBrowser() ? DIMEN_BASE_HEIGHT_IOS : DIMEN_BASE_HEIGHT
                //反转基准宽、高
                let baseWidth = inverse ? basePortraitHeight : basePortraitWidth
                let baseHeight = inverse ? basePortraitWidth : basePortraitHeight
                
                let widthRatio = width / baseWidth
                let heightRatio = height / baseHeight
                if(widthRatio <=1 && heightRatio <= 1) {
                    return dimens
                }
                
                let effectiveRatio = Math.min(widthRatio, heightRatio)
                let scaleWidth = Math.floor(width / effectiveRatio)
                let scaleHeight = Math.floor(height / effectiveRatio)
                if(this.useMaxRatio(scaleWidth, scaleHeight)) {
                    effectiveRatio = Math.max(widthRatio, heightRatio)
                    scaleWidth = Math.floor(width / effectiveRatio)
                    scaleHeight = Math.floor(height / effectiveRatio)
                }
                return [scaleWidth, scaleHeight]
            },
            
            useMaxRatio(scaleWidth, scaleHeight) {
                if(!platform.isIOSBrowser()) {
                    return false
                }
                //IOS的图片分辨率需要乘3，宽高总共需要乘9
                return scaleWidth * scaleHeight * 9 > LIMIT_CANVAS_AREA_IOS
            }
        }
    }
</script>

<style scoped>
    .bowo-watermark {
        width: 0;
        height: 0;
        overflow: hidden;
    }
</style>
