<!-- @format -->

<template>
    <div>
        <div class="zy-line ac jb">
            <p class="qiye-til1">生产设备</p>
            <ul class="shebei-tuli">
                <li>
                    <i style="background-color: #7dcd27"></i>
                    <span>启动</span>
                </li>
                <li>
                    <i style="background-color: #ff7054"></i>
                    <span>停止</span>
                </li>
                <li>
                    <i style="background-color: #888888"></i>
                    <span>离线</span>
                </li>
            </ul>
        </div>
        <div class="qiye-tu">
            <view>
                <!-- #ifdef APP-PLUS || H5 -->
                <view
                    style="width: 100%; height: 500rpx"
                    :prop2="option2"
                    :change:prop2="echarts.updateEcharts2"
                    id="echarts2"
                    class="echarts"
                    ref="echarts2"
                ></view>
                <!-- #endif -->
                <!-- #ifndef APP-PLUS || H5 -->
                <view>非 APP、H5 环境不支持</view>
                <!-- #endif -->
            </view>
        </div>
        <div class="zy-line ac jb">
            <p class="qiye-til1">治污设备</p>
        </div>
        <div class="qiye-tu">
            <view>
                <!-- #ifdef APP-PLUS || H5 -->
                <view
                    style="width: 100%; height: 400rpx"
                    :prop1="option1"
                    :change:prop1="echarts.updateEcharts1"
                    id="echarts1"
                    class="echarts"
                    ref="echarts1"
                ></view>
                <!-- #endif -->
                <!-- #ifndef APP-PLUS || H5 -->
                <view>非 APP、H5 环境不支持</view>
                <!-- #endif -->
            </view>
        </div>
        <div class="ic-full" @click="toRunningData"></div>
    </div>
</template>

<script>
import { ucs2 } from 'punycode';
import {
    sbsxzt,
    qyztrl,
    getScrl,
    getDwzb
} from '../../../../api/iot/runningData.js';
export default {
    props: {
        currentscx: {
            type: String,
            default: ''
        },
        dateStr: {
            type: String,
            default: ''
        }
    },
    watch: {
        currentscx: {
            handler: function (nv) {
                //渲染图表
                this.$nextTick(() => {
                    this.option2 = {};
                    this.option1 = {};
                    this.gettjt();
                });
            }
        },
        dateStr: {
            handler: function (newVal) {
                //渲染图表
                this.$nextTick(() => {
                    this.option2 = {};
                    this.option1 = {};
                    this.gettjt();
                });
            }
        }
    },
    data() {
        return {
            option1: {},
            option2: {}
        };
    },
    mounted() {
        uni.$on('changeTab', () => {
            this.$nextTick(() => {
                this.option2 = {};
                this.option1 = {};
                this.gettjt();
            });
        });
    },
    methods: {
        gettjt(scrq) {
            let that = this;
            sbsxzt({
                SCXID: this.currentscx,
                DATE: this.dateStr
            }).then((res) => {
                res.data.zwList.forEach((x) => {
                    x.name = x.SBMC;
                    // x.ztList = JSON.parse(x.ztList);
                    if (x.ztList && x.ztList.length > 0) {
                        x.list = x.ztList;
                        x.list.forEach((y) => {
                            y.end = that
                                .$dayjs(y.end)
                                .format('YYYY-MM-DD HH:mm');
                        });
                    }
                });
                res.data.scList.forEach((x) => {
                    x.name = x.SBMC;
                    // x.ztList = JSON.parse(x.ztList);
                    if (x.ztList && x.ztList.length > 0) {
                        x.list = x.ztList;
                        x.list.forEach((y) => {
                            // y.end = that
                            // 	.$dayjs(y.end)
                            // 	.add(1, 'minute')
                            // 	.format('YYYY-MM-DD HH:mm');
                            y.end = that
                                .$dayjs(y.end)
                                .format('YYYY-MM-DD HH:mm');
                        });
                    }
                });
				 let option2 =  res.data.scList;
				 let option1 =  res.data.zwList;
				let newArr1 = res.data.scList
				let newArr2 = res.data.zwList
				this.option2 = this.setChart(option2);
				this.option1 = this.setChart(option1);
				let str = JSON.stringify(this.option2)
       
            });
        },
        setChart(data) {
            let statusObj = {
                1: {
                    color: '#ff7054',
                    label: '停止'
                },

                2: {
                    color: '#7dcd27',
                    label: '开启'
                },

                0: {
                    color: '#999',
                    label: '离线'
                }
            };

            let x = [];

            let minutes = 0;

            let jszj = '';

            if (this.dateStr == this.$dayjs().format('YYYY-MM-DD')) {
                minutes = this.getcurrentallminute();

                jszj = this.getcurrenthhmm();

                //minutes = 1440;

                //jszj = '23:59';
            } else {
                minutes = 1440;

                jszj = '23:59';
            }

            for (let i = 0; i < minutes; i++) {
                let num = i;

                let h = Math.floor(num / 60);

                let min = num % 60;

                h = (h <= 9 ? '0' : '') + h;

                min = (min <= 9 ? '0' : '') + min;
                x.push(h + ':' + min);
            }

            let y = [];

            let lines = [];

        
            data.forEach((v, i) => {
                y.push(v.name);
                //背景状态（如果数据中有可以去掉）
                lines.push({
					
                    name: '断电',

                    z: 1,
					     
                    type: 'lines',

                    coordinateSystem: 'cartesian2d',

                    silent: true,

                    lineStyle: {
                        width: 24,

                        color: '#999',

                        opacity: 1
                    },

                    data: [
                        {
                            name: v.name,

                            coords: [
                                ['00:00', v.name],
                                [jszj, v.name]
                            ]
                        }
                    ],

                });

                v.list &&
                    v.list.forEach((item, j) => {
                        let xVal1 = item.start.slice(11, 16);

                        let xVal2 = item.end.slice(11, 16);

                        let obj = statusObj[v.list[j].status];

                        lines.push({
                            name: obj.label,

                            z: 2,
	                             
                            type: 'lines',

                            coordinateSystem: 'cartesian2d',

                            lineStyle: {
                                width: 24,

                                color: obj.color || '#999',

                                opacity: 1
                            },

                            data: [
                                {
                                    name: v.name,

                                    start: item.start,

                                    end: item.end,

                                    statusLabel: obj.label,

                                    coords: [
                                        [xVal1, v.name],
                                        [xVal2, v.name]
                                    ]
                                }
                            ]
                        });
                    });
            });


         

            let option = {
                tooltip: {
                    show: true,
                    axisPointer: {
                        type: 'cross',
                        crossStyle: {
                            color: '#4874ff'
                        },
                        label: {
                            color: '#fff',
                            backgroundColor: '#4874ff'
                        }
                    },
                    backgroundColor: 'rgba(255,255,255,0)',
                    padding: [0, 0]
                },
                grid: 
					    {
					      left: 20,
					      right: 20,
					      top: 10,
					    },
					 
				
                xAxis: 
		
					{
					    type: 'category',
					    data: x,
					    interval: 0,
					    axisLabel: {
					        // formatter(v) {
					        //     let s = v.split(':')[1];
					        //     if (s % 15 === 0) {
					        //         return v;
					        //     } else {
					        //         return '';
					        //     }
					        // },
					        show: true,
					        textStyle: {
					            color: '#999'
					        }
					    },
					    axisLine: {
					        show: true,
					        lineStyle: {
					            color: '#999'
					        }
					    },
					    axisTick: {
					        show: true
					    }
					},
				
                yAxis: 
				
					{
					    type: 'category',
					    data: y,
					    axisLabel: {
					        interval: 0,
					        show: true,
					        inside: true,
					        textStyle: {
					            color: '#fff'
					        },
					        padding: [2, 0, 0, 0]
					    },
					    axisLine: {
					        show: false,
					        lineStyle: {
					            color: 'black'
					        }
					    },
					    axisTick: {
					        show: false
					    },
					    z: 3
					},
				
                series: 
					lines
				
				
            };

			let strOption = JSON.stringify(option)
            return option;
           
        },
       
		getcurrentallminute() {
            let hour = this.$dayjs().format('HH');
            let minutes = this.$dayjs().minute();
            return hour * 60 + minutes + 1;
        },
        getcurrenthhmm() {
            return this.$dayjs().add(1, 'minute').format('HH:mm');
        },
        toRunningData() {
            uni.navigateTo({
                url: `/pages/runningData/Index?SCXID=${this.currentscx}&DATE=${this.dateStr}`
            });
        }
    }
};
</script>

<script module="echarts" lang="renderjs">
let myChart1;
let myChart2;
export default {
	data() {
		return {
			chartw: "",
			charth: '',
			flag: false,
		}
	},
	onLoad() {

	},
	mounted() {
		if (typeof window.echarts === 'function') {
			this.initEcharts()
		} else {
			// 动态引入较大类库避免影响页面展示
			const script = document.createElement('script')
			// view 层的页面运行在 www 根目录，其相对路径相对于 www 计算
			script.src = 'static/echarts4.9.0.js'
			script.onload = this.initEcharts.bind(this)
			document.head.appendChild(script)
		}

	},
	methods: {
		initEcharts() {
			console.log('initEcharts');
			let echarts1 = document.getElementById('echarts1');
			echarts1.setAttribute("style", "display:block;height:500px,width:100%;");
			let echarts2 = document.getElementById('echarts2');
			echarts2.setAttribute("style", "display:block;height:500px,width:100%;");
			myChart1 = echarts.init(echarts1)
			myChart2 = echarts.init(echarts2)

			let option1 = {};
			if (this.option1 && this.option1.tooltip && this.option1.tooltip.yAxis) {
				option1 = {
					...this.option1
				};

				option1.tooltip.formatter = function(obj) {
					let data = obj.data;
					return `<p>${data.name}</p>
				              <p>开始时间：${data.start}</p>
				              <p>结束时间：${data.end}</p>
				              <p>状态：${data.statusLabel}</p>
				            `;
				}
				option1.tooltip.confine = true;
			}
			myChart1 && myChart1.setOption(option1)

			let option2 = {};
			if (this.option2 && this.option2.tooltip) {
				option2 = {
					...this.option2
				};
				option2.tooltip.formatter = function(obj) {
					let data = obj.data;
					return `<p >${data.name}</p>
				              <p>开始时间：${data.start}</p>
				              <p>结束时间：${data.end}</p>
				              <p>状态：${data.statusLabel}</p>
				            `;
				}
			}
			myChart2 && myChart2.setOption(option2)

		},
		updateEcharts1(newValue, oldValue, ownerInstance, instance) {
			let echarts1 = document.getElementById('echarts1');
			echarts1.style.height = newValue && newValue.yAxis && newValue.yAxis.data.length * 32 + 70 + 'px';
			// 解决在切换tab时把option1和option2改成了{}，导致没有formatter，tooltip等参数而报错
			if (!newValue || Object.keys(newValue).length ===0) {
				return;
			}
			//自定义状态的文字和颜色
			let option = JSON.parse(JSON.stringify(newValue));
			// 渲染后触发的option1
			option.tooltip.formatter = function(obj) {
				let data = obj.data;
				return `<div style="background:${data.statusLabel== '停止'?'#ff7054':(data.statusLabel== '开启'?'#7dcd27':'#ccc')};color:#fff;padding:6px;opacity:0.9;border-radius:6px;border:1px solid #ddd;font-size:12px;">          ${data.name}</br>
			       开始时间：${data.start.slice(0,16)} </br>
			       结束时间：${data.end} </br>
			       状态：${data.statusLabel} </br>
			     </div>`;
			}
			option.tooltip.confine = true;
			myChart1 && myChart1.clear()
			myChart1 && myChart1.setOption(option)
			myChart1 && myChart1.resize()
		},

		updateEcharts2(newValue, oldValue, ownerInstance, instance) {
			let echarts2 = document.getElementById('echarts2');
			echarts2.style.height = newValue && newValue.yAxis && newValue.yAxis.data.length * 32 + 70 + 'px';
			//自定义状态的文字和颜色
			// 解决在切换tab时把option1和option2改成了{}，导致没有formatter，tooltip等参数而报错
			if (!newValue || Object.keys(newValue).length ===0) {
				return;
			}
			let option = JSON.parse(JSON.stringify(newValue));
			// 渲染后触发的option2
			option.tooltip.formatter = function(obj) {
				let data = obj.data;
				return `<div style="background:${data.statusLabel== '停止'?'#ff7054':(data.statusLabel== '开启'?'#7dcd27':'#888')};color:#fff;padding:6px;opacity:0.9;border-radius:6px;border:1px solid #ddd;font-size:12px;">          ${data.name}</br>
			       开始时间：${data.start.slice(0,16)} </br>
			       结束时间：${data.end} </br>
			       状态：${data.statusLabel} </br>
			     </div>`;
			}
			option.tooltip.confine = true;
			myChart2 && myChart2.clear()
			myChart2 && myChart2.setOption(option)
			myChart2 && myChart2.resize()
		},

		format(date, fmt) {
			var o = {
				"M+": date.getMonth() + 1, //月份
				"d+": date.getDate(), //日
				"h+": date.getHours(), //小时
				"m+": date.getMinutes(), //分
				"s+": date.getSeconds(), //秒
				"q+": Math.floor((date.getMonth() + 3) / 3), //季度
				"S": date.getMilliseconds() //毫秒
			};
			if (/(y+)/.test(fmt)) {
				fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
			}
			for (var k in o) {
				if (new RegExp("(" + k + ")").test(fmt)) {
					fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k])
						.length)));
				}
			}
			return fmt;
		},
	}
}
</script>

<style>
.ic-full {
    position: absolute;
    right: 26rpx;
    bottom: 26rpx;
}
</style>

