@charset "utf-8";

.flx1 {
    display: flex;
}

.ac {
    align-items: center;
}

.jb {
    justify-content: space-between;
}

.ja {
    justify-content: space-around;
}

.yjic-btn {
    width: 97.2221999rpx;
    height: 97.2221999rpx;
    border-radius: 11.1111rpx;
    background-color: #ff4848;
    margin-right: 22rpx;
}

.yjic {
    width: 32.638875rpx;
    height: 38.8889249rpx;
    display: block;
    margin: 15% auto 0;
}

.yjic-btn>p {
    font-size: 20rpx;
    color: #fff;
    text-align: center;
    margin-top: 7.5rpx;
}

.rtarr {
    width: 17.361075rpx;
    height: 29.8611rpx;
    background: url(~@/static/app/images/rt-arr1.png) no-repeat center center;
    background-size: 17.361075rpx auto;
}

.yj-mod {
    background-color: #fff;
    border-radius: 17.361075rpx;
}

.yj-mod .hd {
    border-bottom: 0.694425rpx solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 23.611125rpx 30rpx;
	width: 100%;
	}

.yj-mod .til {
    font-size: 34.722225rpx;
    color: #333;
    line-height: 60rpx;
    font-weight: 600;
	width:calc(100% - 120rpx)
}

.yj-mod .bd {
    padding: 0 33.3333rpx;
}

.yj-info li {
    display: flex;
    padding-bottom: 10rpx;
}

.yj-info li .p1 {
    width: 166.66665rpx;
    font-size: 31.94445rpx;
    color: #999;
    line-height: 46rpx;
}

.yj-info li .p2 {
    flex: 1;
    font-size: 31.94445rpx;
    color: #333;
    line-height: 46rpx;
}

.yjcxbox{
    background-color: #f6f6f6;
    border-radius: 11.1111rpx;
    padding: 20.83335rpx;
    box-sizing: border-box;
}
.yjcxbox li{
    font-size: 29.166675rpx;
    line-height: 55.555575rpx;
    display: flex;
    color: #333;
    position: relative;
}

.yjcxbox li em{
    color: #999;
    width: 26%;
}
.yxic{
    width: 125.000025rpx;
    height: 38.194425rpx;
    position: absolute;
    right: 0;
    top: 12%;
}