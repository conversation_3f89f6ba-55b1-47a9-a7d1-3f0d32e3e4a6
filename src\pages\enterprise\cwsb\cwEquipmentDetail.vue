<!-- @format -->

<template>
	<!-- 生产设备详情 -->
	<body style="background: #f1f1f1">
		<section class="main">
			<div class="inner">
				<div class="pd-modhd">
					<strong>基本信息</strong>
					<image src="../../../static/app/images/edtic.png" class="pd-edt1" @click="toEdit()" />
				</div>
				<div class="zy-form">
					<div class="item">
						<p class="label">设备名称</p>
						<div class="rp">
							<i class="pd-txt2">{{ detailInfo.SBMC || '-'}}</i>
						</div>
					</div>
					<div class="item">
						<p class="label">IMEI号</p>
						<div class="rp">
							<i class="pd-txt2">{{ detailInfo.IMEI || '-'}}</i>
						</div>
					</div>
					<div class="item" v-if="detailInfo['GYLX_SBLX_CH']">
						<p class="label">设施种类</p>
						<div class="rp">
							<i class="pd-txt2">{{ detailInfo.GYLX_SBLX_CH || '-'}}</i>
						</div>
					</div>
					<div class="item" v-if="detailInfo['SST_WJID']"> 
						<p class="label">设施图片预览</p>
						<div class="rp">
							<i class="pd-txt2">
								<image class="equip-pic" mode="aspectFit" :src="getFileUrlPic(detailInfo)" alt="" />
							</i>
						</div>
					</div>
					<div class="item">
						<p class="label">设备安装时间</p>
						<div class="rp">

							<i class="pd-txt2">{{ detailInfo.AZSJ || '-'}}</i>
						</div>
					</div>
					<div class="item">
						<p class="label">设备安装状态</p>
						<div class="rp">
							<i class="pd-txt2">
								<!-- 旧版的数据传的中文 -->
								<span v-if="detailInfo.YXZT == '工作'">工作</span>
								<span v-if="detailInfo.YXZT == '待机'">待机</span>
								<span v-if="detailInfo.YXZT == '关闭'">关闭</span>
								<span v-if="detailInfo.YXZT == 1">工作</span>
								<span v-if="detailInfo.YXZT == 2">待机</span>
								<span v-if="detailInfo.YXZT == 3">关闭</span>
								<span v-if="detailInfo.YXZT == '' || detailInfo.YXZT == null">-</span>
							</i>
						</div>
					</div>
					<div class="item">
						<p class="label">设备类型</p>
						<div class="rp">
							<i class="pd-txt2">
								<!-- 旧版的数据传的中文 -->
								<span v-if="detailInfo.ZNSBLX && detailInfo.ZNSBLX == 'ZD'">振动</span>
								<span v-else-if="detailInfo.ZNSBLX && detailInfo.ZNSBLX == 'DL'">电流</span>
								<span v-else>-</span>
							</i>
						</div>
					</div>
					<div class="item" v-if="detailInfo.ZNSBLX == 'ZD'">
						<p class="label">振动能量</p>
						<div class="rp">

							<i class="pd-txt2">
								<span v-if="detailInfo.ZDQD == 1">弱</span>
								<span v-if="detailInfo.ZDQD == 2">中</span>
								<span v-if="detailInfo.ZDQD == 3">强</span>
								<span v-if="detailInfo.ZDQD == '' ||detailInfo.ZDQD == null">-</span>
							</i>
						</div>
					</div>
					<div class="item" v-if="detailInfo.ZNSBLX == 'ZD'">
						<p class="label">是否受周边影响</p>
						<div class="rp">
							<i class="pd-txt2">
								<span v-if="detailInfo.SFSYX === '1'">是</span>
								<span v-if="detailInfo.SFSYX === '0'">否</span>
								<span v-if="detailInfo.SFSYX == ''||detailInfo.SFSYX == null">-</span>
							</i>
						</div>
					</div>

					<div class="item" v-if="detailInfo.ZNSBLX == 'ZD'">
						<p class="label">设备运行规律</p>
						<div class="rp">
							<i class="pd-txt2">
								<!-- 旧版的数据传的中文 -->
								<template v-if="detailInfo.YXGL">
									<span v-if="detailInfo.YXGL && detailInfo.YXGL == '平稳'">平稳</span>
									<span v-else-if="detailInfo.YXGL && detailInfo.YXGL == '间歇'">间歇</span>
									<span v-else-if="detailInfo.YXGL.type == 1">平稳</span>
									<span v-else-if="detailInfo.YXGL.type == 2">间歇</span>
									<span v-else-if="detailInfo.YXGL.type == ''">-</span>
									<span v-else>-</span>
								</template>
								<template v-else>
									<span >-</span>
								</template>
								
							</i>
						</div>
					</div>
					<div class="item" v-if="detailInfo.YXGL && detailInfo.YXGL.type == '2'">
						<p class="label">设备运行周期</p>
						<div class="rp">
							<i class="pd-txt2">{{ detailInfo.YXGL.circle || '-'}}</i>

						</div>
					</div>
					<div class="item">
						<p class="label">关联生产线</p>
						<div class="rp">
							<i class="pd-txt2">{{ detailInfo.SCXMC || '-'}}</i>
						</div>
					</div>
					<div class="item" v-if="detailInfo.SFFC == '1'">
						<p class="label">防拆功能</p>
						<div class="rp">
							<i v-if="detailInfo.FCBJZT == '0'" class="pd-txt2">未启用</i>
							<i v-else-if="detailInfo.FCBJZT == '1'" class="pd-txt2">启用</i>
							<i v-else-if="detailInfo.FCBJZT == '2'" class="pd-txt2 warning">报警中</i>
							<i v-else class="pd-txt2">-</i>
							<span class="cancel-warn"  v-if="detailInfo.FCBJZT == '2'"  @click="cancelWarn">解除</span>
						</div>
					</div>
					<div class="item remark">
						<p class="label">备注信息</p>
						<div class="rp zy-textarea1">
							<i class="rfont pd-txt2">{{ detailInfo.BZ || '-'}}</i>
						</div>
					</div>
					<div class="item nfx" v-if="azFileList.length >0">
						<p class="label star">安装照片</p>
						<div class="gap"></div>
						<ul class="pd-ulpic1">
							<li v-for="(item, index) in azFileList">
								<image mode="scaleToFill" :src="getFileUrl(item)" alt=""
									@click="previewImage(azFileList, index)" />
							</li>
						</ul>
					</div>
				</div>
				<div class="gap"></div>

				<tab :dataTypeArr="dataTypeArr" :dataType="dataType" @changeDataType="change"></tab>

				<p class="pd-txt3" v-show="dataType == 'upData'">
					默认展示最新上报的三组数据信息。
				</p>
				<p class="pd-txt3" v-show="dataType == 'upState'">
					展示最新上传的三组状态数据，其中设备电压、信号功率、信号质量的数据若小于其最小范围值会标红。
				</p>
				<div class="gap"></div>
				<!-- 上报数据 -->
				<div v-if="dataType == 'upData'">
					<EquipDataList :list="vibrationList"></EquipDataList>

				</div>
				<!-- 上报状态 -->
				<div v-if="dataType == 'upState'">
					<EquipStateList :list="threeList"></EquipStateList>
				</div>
			</div>
		</section>
	</body>
</template>

<script>
	import {
		getGgdmz
	} from '@/api/iot/ggdmz.js';
	import {
		ScsbInfo,
		getFilelist,
		newStateList,
		commocode,
		getDropWarn
	} from '@/api/iot/enterprise.js';
	import {
		getVibration
	} from '@/api/iot/realtime.js';
	import ReportData from './reportData.vue';
	import ReportStatus from './reportStatus.vue';
	import Trend from './trend.vue';
	import {
		DOWNLOAD_URLZDY,
		UPLOAD_URL,
		DELETFILE_URLZDY
	} from '@/common/config.js';
	import enterprise_store from '../enterprise.store.js';
	import equipmentCard from '@/pages/realtime/components/equipmentCard';
	import EquipStateList from '@/pages/component/EquipStateList';
	import EquipDataList from '@/pages/component/EquipDataList';

	import tab from '@/pages/realtime/components/tab';
	export default {
		components: {
			ReportData,
			ReportStatus,
			Trend,
			equipmentCard,
			tab,
			EquipStateList,
			EquipDataList
		},
		watch: {},
		data() {
			return {
				detailInfo: {
					YXGL: {
						type: ""
					},
					circle: '', //间歇周期
					type: '',
					ZDQD: '', //振动能量
					SFSYX: '', //是否受周边影响
				},
				dataTypeArr: [{
						name: '上报数据',
						value: 'upData'
					},
					{
						name: '上报状态',
						value: 'upState'
					}
				],
				dataType: 'upData',
				options: {
					info: {},
					sbInfo: {},
					enterpInfo: {}
				},
				azFileList: [],
				nodata: true,
				threeData: [{
						name: '检测时间',
						value: 'JCSJ'
					},
					{
						name: '设备状态',
						value: 'ERR'
					},
					{
						name: '设备电压',
						value: 'VOL_ZT'
					},
					{
						name: '信号功率',
						value: 'SP_ZT'
					},
					{
						name: '信号质量',
						value: 'RSRQ_ZT'
					},
					{
						name: '振动能量',
						value: 'NL_LV'
					}
				],
				vibrationData: [{
						name: '设备IMEI',
						value: 'IMEI'
					},
					{
						name: '检测时间',
						value: 'JCSJ'
					},
					{
						name: '上报时间',
						value: 'SBSJ',
						style: 'color:#f8be45'
					},
					{
						name: 'X轴振幅',
						value: 'X'
					},
					{
						name: 'Y轴振幅',
						value: 'Y'
					},
					{
						name: 'Z轴振幅',
						value: 'Z'
					},
					{
						name: 'X轴频率',
						value: 'X_FREQ'
					},
					{
						name: 'Y轴频率',
						value: 'Y_FREQ'
					},
					{
						name: 'Z轴频率',
						value: 'Z_FREQ'
					}
				],
				threeList: [], //上报状态
				vibrationList: [], //上报数据
				shakeList: [ //振动能量
					{
						label: '弱',
						value: '1'
					},
					{
						label: '中',
						value: '2'
					},
					{
						label: '强',
						value: '3'
					},
				],
				influenceList: [ //周边影响
					{
						label: '是',
						value: '1'
					},
					{
						label: '否',
						value: '0'
					},
				],
			};
		},
		onShow() {},
		onLoad(option) {
			if (option.SBID) {
				console.log(option.SBID)
				this.options.info.SBID = option.SBID
			}
			if (option.info) {
				this.options.info = JSON.parse(decodeURIComponent(option.info));
			}

			if (option.sbInfo) {
				this.options.sbInfo = JSON.parse(decodeURIComponent(option.sbInfo));
			}

			if (option.enterpInfo) {
				this.options.enterpInfo = JSON.parse(decodeURIComponent(option.enterpInfo));
			}

		},
		created() {
			this.initFormData();
		},
		mounted() {
			this.getCommonCode();
			this.getFileList();
		},
		methods: {
			//解除脱落预警
			cancelWarn(){
				console.log(this.detailInfo,'detailInfo')
				let self = this;
				uni.showModal({
				    title: '提示',
				    content: '确认解除脱落预警?',
				    success: function (res) {
				        if (res.confirm) {
				            console.log('用户点击确定');
							let SBID = self.detailInfo.SBID;
				            getDropWarn(SBID).then((res) => {
				                uni.showToast({
				                    title: '解除成功',
				                    duration: 5000
				                }).then(() => {
				                    setTimeout(() => {
				                        self.getDetailInfo();
										let pages =
											getCurrentPages(); // 当前页面
										//刷新上一页面的设备列表
										let beforePage =pages[pages.length -2]; // 上一页
										if (beforePage.$vm) {
											beforePage.$vm.getScsbList &&beforePage.$vm.getScsbList();
										} else {
											beforePage
												.getScsbList &&
												beforePage
												.getScsbList();
											
										}
										
				                    }, 500);
				                });
				            });
				        } else if (res.cancel) {
				            console.log('用户点击取消');
				        }
				    }
				});
			},		
			
			//振动能量
			getShakeText(val) {
				let arr = []
				arr = this.shakeList.filter(item => {
					return item.value == val
				})
				return arr[0].label
			},
			//获取公共代码
			getCommonCode() {
				//生产设备--振动能量
				commocode({
					code: 'ZDSB_ZDQD'
				}).then(res => {
					if (res.data && res.data.length) {
						this.shakeList = res.data;
					}
				})
				//生产设备--周边影响
				commocode({
					code: 'ZDSB_SFSYX'
				}).then(res => {
					if (res.data && res.data.length) {
						this.influenceList = res.data;
					}
				})
			},
			initFormData() {
				ScsbInfo({
					SBID: this.options.info.SBID
				}).then((res) => {
					this.detailInfo = res.data[0];
					this.detailInfo.YXGL = JSON.parse(this.detailInfo.YXGL)
					this.getnewStateList();
					this.getVibrationList();
				});
			},
			//请求设备详情
			getDetailInfo(){
				ScsbInfo({
					SBID: this.options.info.SBID
				}).then((res) => {
					this.detailInfo = res.data[0];
					this.detailInfo.YXGL = JSON.parse(this.detailInfo.YXGL)
				});
			},
			// 添加设备
			toEdit() {
				uni.navigateTo({
					url: `/pages/enterprise/cwsb/addCwEquipment?type=edit&detail=${encodeURIComponent(
			        JSON.stringify(this.detailInfo))}&info=${encodeURIComponent(
                    JSON.stringify(this.options.info))}`
				});

			},

			getFileList() {
				getFilelist({
					pageSize: 100000,
					pageNum: 1,
					YWSJID: this.options.info.SBID,
					LXDMS: 'ZDFJ',
					ZLXDMS: 'WRYZP,GYLCT,ZDSB'
				}).then((res) => {
					let fileData = res[0];
					if (
						fileData &&
						fileData.zlxList &&
						fileData.zlxList.length > 0
					) {
						fileData.zlxList.forEach((list) => {
							if (list.ZLXDM == 'ZDSB') {
								this.azFileList = list.fileList;
							}
						});
					}
				});
			},
			getFileUrl(item) {
				return DOWNLOAD_URLZDY + item.WJID;
			},
			getFileUrlPic(item) {
				return DOWNLOAD_URLZDY + item.SST_WJID;
			},
			//最新上报的震动数据
			getVibrationList() {
				getVibration({
					IMEI: this.model.IMEI,
					pageSize: this.pageSize
				}).then((res) => {
					if (res.status === '000') {
						this.vibrationList = res.data.list;
					}
				});
			},

			// tab切换
			change(option) {
				this.dataType = option.value;
			},

			//预览
			// previewImage(fileList, idx) {
			//     let fileUrls = fileList.map((file) => {
			//         return this.getFileUrl(file);
			//     });
			//     console.log('fileUrls', fileUrls);
			//     // 预览图片
			//     uni.previewImage({
			//         current: idx,
			//         urls: fileUrls
			//     });
			// },
			previewImage(fileList, index) {
				let self = this;
				let fileUrls = fileList.map((file) => {
					return this.getFileUrl(file);
				});
				console.log('fileUrls', fileUrls)
				// 预览图片
				uni.previewImage({
					current: index,
					urls: fileUrls,
					//长按保存到本地
					longPressActions: {
						itemList: ["保存图片到本地"],
						success: (data) => {
							// console.log('data', data)
							if (data.tapIndex == 0) {
								let imgurl = fileUrls[data.index];
								self.saveImage(imgurl)
							}

						},
						fail: function(err) {
							console.log(err.errMsg);
						},
					},

				});

			},

			//保存图片
			saveImage(imgurl) {
				// console.log(imgurl)
				uni.downloadFile({
					url: imgurl,
					success(res) {
						let url = res.tempFilePath
						uni.saveImageToPhotosAlbum({
							filePath: url,
							success() {
								uni.showToast({
									title: '已存至系统相册',
									icon: "success"
								})
							},
							fail(err) {
								uni.showToast({
									title: '保存失败',
									icon: "error"
								})
							}
						})
					}
				})

			},
			//获取最新上报的状态数据
			getStateList() {
				newStateList({
					IMEI: this.detailInfo.IMEI
				}).then((res) => {
					this.stateList = res.data;
					console.log('stateList', this.stateList);
				});
			},
			//最新上报的震动数据
			getVibrationList() {
				getVibration({
					IMEI: this.detailInfo.IMEI,
					pageSize: '3'
				}).then((res) => {
					if (res.data) {
						this.vibrationList = res.data.list.slice(0, 3);
						this.nodata = false;
					}
				});
			},
			//获取最新的3条数据
			getnewStateList(imei) {
				newStateList({
					IMEI: this.detailInfo.IMEI
				}).then((res) => {
					this.threeList = res.data;
				});
			}
		}
	};
</script>

<style scoped>
	html {
		-webkit-tap-highlight-color: transparent;
		height: 100%;
	}

	body {
		-webkit-backface-visibility: hidden;
		height: 100%;
	}

	.warning {
		color: #f00 !important;
	}

	.pd-tablebx image {
		height: 30rpx;
	}

	.bznr {
		display: inline-block;
		height: 60rpx;
		line-height: 60rpx;
	}

	/deep/ .u-form-item--left__content__label {
		font-size: 30rpx;
		color: #2c323f;
	}

	/deep/ .u-input__textarea {
		border-radius: 8rpx;
		height: 100rpx;
		background-color: rgb(243, 245, 249);
		padding-left: 10rpx;
	}

	/deep/ .uni-textarea-wrapper {
		height: 100% !important;
	}

	/deep/ .u-list-item {
		margin: 0;
	}

	/deep/ .uni-input-placeholder {
		padding: 0 20rpx 0 0;
		/* text-align: right; */
		font-size: 26rpx;
	}

	.listWarp {
		width: 100%;
		height: 100%;
		overflow-y: scroll;
		padding-top: 80rpx;
	}

	.listWarp p {
		width: 100%;
		padding: 20rpx;
		text-align: center;
		border-bottom: 1px solid #efefef;
	}

	.opration {
		padding: 20rpx;
		display: flex;
		justify-content: space-between;
		position: absolute;
		width: 100%;

		background-color: #fff;
	}

	.confirm {
		color: rgb(60, 170, 255);
	}

	.on {
		color: rgb(60, 170, 255);
	}

	/deep/ .u-icon {
		padding: 0 0 0 10rpx;
	}

	.tip-title {
		text-align: center;
		border-bottom: 1px solid #efefef;
		padding: 20rpx;
		font-weight: 600;
	}

	.tip-content {
		padding: 20rpx;
	}

	.tip-content p {
		/* padding: 20rpx 0; */
		font-size: 26rpx;

		color: #666;
	}

	.tip-know {
		position: absolute;
		bottom: 0;
		padding: 20rpx;
		text-align: center;
		border-top: 1px solid #efefef;
		color: rgb(60, 170, 255);
		width: 100%;
	}

	.pd-tit1 {
		border-radius: 18rpx 18rpx 0 0;
	}

	/deep/ .u-form {
		border-radius: 0 0 18rpx 18rpx;
	}

	.inner {
		height: auto;
	}

	.pd-con1 ul {
		margin-bottom: 16rpx;
	}

	.zy-form .item .bz {
		font-size: 28rpx;
		color: #333;
		line-height: 44rpx;
		position: relative;
		width: 30%;
	}

	.bz {}

	.bz+.rp .pd-txt2 {
		font-size: 14px;
		line-height: 22px;
		font-family: 'Microsoft YaHei';
		width: 266px;
		height: auto;
		resize: none;
	}
    .equip-pic{
		width:100rpx;
		height:100rpx;
		background:#edf0f3;
		padding:6rpx;
		
	}
	.cancel-warn{
		color: #fff;
		background: #4874ff;
		border-radius: 6rpx;
		padding: 8rpx 32rpx;
		font-size: 28rpx;
		line-height: 40rpx;
		margin-left:20rpx;
	}
	/* .zy-textarea1 .rfont {
    text-align: left;
} */
</style>