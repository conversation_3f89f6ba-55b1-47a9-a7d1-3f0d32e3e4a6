/** @format */

const getAppHostUrl = () => {
    if (window) {
        let location = window.document.location;
        let href = location.href;
        let path = location.pathname;

        let pathIndex = href.indexOf(path);
        let pathLength = path.length;

        return href.substring(0, pathIndex + pathLength);
    }
    return '';
};

export const parseQueryParams = (urlWithParams) => {
    let urlAndParams = urlWithParams.split('?');
    if (urlAndParams.length === 1) {
        return;
    }

    let joinedParams = urlAndParams[1];
    let paramInArray = joinedParams.split('&');
    let params = {};
    paramInArray.forEach((keyAndValue) => {
        let equalIndex = keyAndValue.indexOf('=');
        if (equalIndex !== -1) {
            let key = keyAndValue.substring(0, equalIndex);
            let value = keyAndValue.substring(equalIndex + 1);
            params[key] = value;
        }
    });
    return params;
};

/**
 * 把对象转换成查询参数
 */
export const transformObjectToUrlParams = (params) => {
    let joinedParams = '';
    if (params && Object.keys(params).length > 0) {
        for (let key in params) {
            joinedParams += `&${key}=${params[key] || ''}`;
        }
        joinedParams = joinedParams.substring(1);
    }
    return joinedParams;
};

export default {
    getAppHostUrl,
    parseQueryParams,
    transformObjectToUrlParams
};
