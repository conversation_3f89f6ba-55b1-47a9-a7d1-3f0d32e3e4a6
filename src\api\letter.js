import axios from '@/common/ajaxRequest.js'
import {ULR_BASE} from '@/common/config.js'


// 查询环境信访首页信息（超期任务数量、累计调处、检查企业数）
export const getLetterNumber = data => {
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: {
		  service : 'QUERY_HJXF_HOME_SERVICE'
	  }
    });
};


// 查询催督办消息列表 QUERY_HJXF_CDB_SERVICE
export const getLetterMessage = data => {
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: {
		  ...data,
		  service : 'QUERY_HJXF_CDB_SERVICE'
	  }
    });
};


// 催督办消息提醒确认COMFIRM_HJXF_CDBMSG
export const getLetterMessageComfirm = data => {
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: {
		  ...data,
		  service : 'COMFIRM_HJXF_CDBMSG'
	  }
    });
};



// 查询信访台账列表 QUERY_HJXF_RWTZ_SERVICE
export const getLetterCase = data => {
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: {
		  ...data,
		  service : 'QUERY_HJXF_RWTZ_SERVICE'
	  }
    });
};


// 查询个人的待办任务列表（包含催办、督办、退回标识的案件）QUERY_HJXF_DBRW_SERVICE
export const getLetterTask = data => {
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: {
		  ...data,
		  service : 'QUERY_HJXF_DBRW_SERVICE'
	  }
    });
};


// SOLR_SEARCHER_QUERYDATA
export const getAllSearch = data => {
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: {
		  ...data,
		  service : 'SOLR_SEARCHER_QUERYDATA'
	  }
    });
};


