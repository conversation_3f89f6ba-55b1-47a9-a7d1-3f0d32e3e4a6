//android自定义浏览器加水印闪退，需要压缩分辨率高的图片
let scaleImage = true

const resolveImageDimens = (imageUrl) => {
    return new Promise((resolve, reject) => {
        uni.getImageInfo({
            src: imageUrl,
            success(imageInfo) {
                resolve([
                    imageInfo.width,
                    imageInfo.height
                ])
            },
            fail(error) {
                reject(`获取图片尺寸出错: ${error}`)
            }
        })
    })
}

const base64ToBlobFile = (base64, fileName) => {
    let parts = base64.split(',')
    let mime = parts[0].match(/:(.*?);/)[1]
    let raw = window.atob(parts[1])
    let rawLength = raw.length
    let blobPart = new Uint8Array(rawLength)
    for(let i = 0; i < rawLength; i++) {
        blobPart[i] = raw.charCodeAt(i)
    }
    return new File([blobPart], fileName, { type: mime })
}

const enableScaleImage = () => {
    scaleImage = true
}

const shouldScaleImage = () => {
    return scaleImage
}

export default {
    resolveImageDimens,
    base64ToBlobFile,
    enableScaleImage,
    shouldScaleImage
}