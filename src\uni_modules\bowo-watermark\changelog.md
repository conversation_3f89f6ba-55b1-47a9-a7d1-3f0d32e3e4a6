## 1.1.6（2023-03-24）
1. 优化画布节点加载时机。在调用接口的时候再挂载canvas，减少资源消耗
2. 增加绘制完成清空画布的操作
## 1.1.5（2023-02-06）
1. 修复IOS H5平台添加水印仍然有超出系统限制尺寸的问题

## 1.1.4（2023-02-03）
1. 舍弃enableScaleImage设置接口，总是自动缩放图片
2. 修复IOS H5平台添加水印超出系统限制尺寸问题

## 1.1.3（2022-05-28）
放开enableScaleImage到所有平台有效

## 1.1.2（2022-05-26）
修复在Android Webview上加载页面，选择相册图片或拍照，由于图片尺寸太大，应用闪退问题

## 1.1.1（2022-05-15）
兼容微信小程序

## 1.1.0（2022-05-11）
1. paintWatermark增加一个参数：fileName
2. paintWatermark的Promise resolve返回值修改为对象，包含path和file两个属性，在H5端有file属性
3. 
## 1.0.5（2022-05-11）
增加字体大小属性

## 1.0.4（2022-05-10）
修复H5端导出的水印图片是Blob地址，无法用于上传的问题

## 1.0.3（2022-05-10）
修改paintWatermark方法，增加传递水印文字模板参数

## 1.0.2（2022-05-10）
修改H5端返回blob地址

## 1.0.1（2022-05-10）
1. 删除原来绑定图片路径属性
2. 提供直接绘制水印并导出水印图片方法，返回Promise

## 1.0.0（2022-05-09）
初次发布
