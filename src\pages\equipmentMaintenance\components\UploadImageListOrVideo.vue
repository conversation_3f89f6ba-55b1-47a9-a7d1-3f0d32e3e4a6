<!-- @format -->

<template>
    <div v-show="isNeedShow">
        <div class="gap"></div>
        <div class="zy-form">
            <div class="nfx">
                <p
                    class="label"
                    :class="{ star: !isDisabled }"
                    @click="showInstallIllustration = !showInstallIllustration"
                >
                    {{ title }}
                </p>
                <div class="gap"></div>
                <ul class="pd-ulpic1">
                    <li v-for="(item, index) in arrUploadImage" :key="index">
                        <div class="innerbox">
                            <!-- 图片 -->
                            <image
                                v-if="item.fileType === '1'"
                                class="pic"
                                mode="aspectFill"
                                :src="getFileUrl(item)"
                                alt=""
                                @click="toPreview(item)"
                            />
                            <!--视频-->
                            <view
                                v-if="item.fileType === '2'"
                                class="file-area"
                            >
                                <view @click="toPreview(item)">
                                    <u-icon
                                        name="play-circle-fill"
                                        color="#1890FF"
                                        size="80"
                                    ></u-icon>
                                    <view class="name">{{
                                        item.fileName || '-'
                                    }}</view>
                                </view>
                            </view>
                            <image
                                v-show="!isDisabled"
                                mode="scaleToFill"
                                src="@/static/app/images/cls.png"
                                class="delImg"
                                @click="delFile(item)"
                            />
                        </div>
                    </li>
                    <li v-show="getShowAddImage()">
                        <div class="innerbox">
                            <image
                                mode="scaleToFill"
                                @click="toShowChooseSheet()"
                                src="@/static/app/workbench/images/addtu.png"
                                class="addpic pic"
                            />
                        </div>
                    </li>
               
                </ul>
            </div>
        </div>
        <!-- #ifdef APP-PLUS -->
        <bowo-watermark ref="watermarkPainter" :fontSize="14" color="#fff" />
        <!-- #endif -->
        <!-- #ifndef APP-PLUS -->
        <bowo-watermark ref="watermarkPainter" :fontSize="14" color="#fff" />
        <!-- #endif -->
        <u-action-sheet
            :list="uploadTypeList"
            v-model="showUploadTypeSheet"
            :tips="uploadTypeTips"
            :border-radius="20"
            @click="chooseUploadType"
        ></u-action-sheet>

       
        <u-popup
            v-model="showPreview"
            border-radius="0"
            mode="center"
            width="100%"
            height="100%"
            :mask="false"
            close-icon-size="20"
            closeable
            :custom-style="{ background: '#000' }"
            @close="closePreview"
        >
            <view class="preview-content">
                <div class="preview-file">
                    <image
                        :src="getFileUrl(previewItem)"
                        mode="aspectFit"
                        style="width: 100%; height: 100%"
                        v-if="previewItem.fileType === '1'"
                    ></image>
                    <video
                        :src="videoPreviewItem.url"
                        id="preview-video"
                        :autoplay="true"
                        controls
                        :show-progress="true"
                        :show-center-play-btn="true"
                        v-if="videoPreviewItem.fileType === '2'"
                    ></video>
                </div>
            </view>
        </u-popup>
    </div>
</template>

<script>
import { getFilelist, deletefile, downloadFile } from '@/api/iot/appendix.js';
import {
    API_LOGIN_SERVICE_URL,
    LOGIN_ULR_BASE,
    UPLOAD_URL,
    DOWNLOAD_URLZDY,
    PREVIEW_FILE_URL
} from '@/common/config.js';
export default {
    name: 'DataCollectionAppUploadImage',
    props: {
        options: {
            type: Object,
            default: () => {}
        },
        formData: {
            type: Object,
            default: () => {}
        },
        // arrUploadType: {
        //     type: Array,
        //     default: () => []
        // },
        childTypeKeyword: {
            type: String,
            default: ''
        },
        fileTypeKeyword: {
            type: String,
            default: ''
        },
        maxLength: {
            type: Number,
            default: 3
        },
        uploadId: {
            type: String,
            default: ''
        },
        title: {
            type: String,
            default: '安装照片'
        },
        isDisabled: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            arrUploadImage: [],
            showInstallIllustration: false,
            isNeedShow: false,
            showUploadType: false,
            uploadType: 'pic',
            uploadTypeList: [
                {
                    text: '图片'
                },
                {
                    text: '视频'
                }
            ],
            uploadTypeTips: {
                text: '请选择上传文件类型',
                color: '#909399',
                fontSize: 32
            },
            showUploadTypeSheet: false,
            showPreview: false,

            previewItem: {
                WJID: '',
                fileType: ''
            },
            videoPreviewItem: {
                url: '',
                fileType: ''
            }
        };
    },

    mounted() {
        this.isNeedShow = this.isDisabled;
    },

    methods: {
        // 关闭预览
        closePreview() {
            this.previewItem = {
                WJID: '',
                fileType: ''
            };
        },

        //预览
        toPreview(item) {
            if (item.fileType === '1') {
                this.showPreview = true;
                // 添加延时加载 避免popup不显示
                setTimeout(() => {
                    this.previewItem = {
                        WJID: item.WJID,
                        fileType: item.fileType
                    };
                }, 500);
            } else if (item.fileType === '2') {
                this.showPreview = true;
                let videoPreviewItem = {
                    WJID: item.WJID,
                    fileType: item.fileType
                };
                this.downloadVideo(videoPreviewItem);
            }
        },
        //查看-下载视频
        downloadVideo(videoPreviewItem) {
            let that = this;
            uni.showLoading({
                title: '加载中'
            });
            uni.downloadFile({
                url: that.getFileUrl(videoPreviewItem), //仅为示例，并非真实的资源
                success: (res) => {
                    if (res.statusCode === 200) {
                        that.videoPreviewItem = {
                            url: res.tempFilePath,
                            fileType: '2' //1图片，2视频
                        };

                        // 自动播放
                        setTimeout(() => {
                            let videoContxt = uni.createVideoContext(
                                'preview-video',
                                that
                            );
                            videoContxt.play();
                        }, 500);
                    }
                    uni.hideLoading();
                },
                error: (e) => {
                    uni.hideLoading();
                }
            });

            uni.hideLoading();
            // 自动播放
            setTimeout(() => {
                let videoContxt = uni.createVideoContext('preview-video', that);
                videoContxt.play();
            }, 500);
        },
        //  上传图片
		toShowChooseSheet() {
		     this.addFile(this.childTypeKeyword);
		},
        // 打开文件上传选择类型弹窗,选择视频或者图片
		//toShowChooseSheet() {
        //     if (this.arrUploadImage.length >= this.maxLength) {
        //         uni.showToast({
        //             title: '最多只能上传3个附件',
        //             icon: 'none'
        //         });
        //         return false;
        //     } else {
        //         this.showUploadTypeSheet = true;
        //     }
        // },
        // 选择上传文件类型
        chooseUploadType(index) {
            if (index === 0) {
                // 图片
                this.addFile(this.childTypeKeyword);
            } else if (index === 1) {
                // 视频
                this.chooseVideo(this.childTypeKeyword);
            }
        },
        //是否展示添加按钮
        getShowAddImage() {
            //编辑状态 且小于3条才显示
            let flag = false;
            flag =
                this.arrUploadImage.length < this.maxLength && !this.isDisabled;
            return flag;
        },
        //获取文件list
        getImageFileList() {
            let obj = {
                pageSize: 100000,
                pageNum: 1,
                YWSJID: this.uploadId,
                LXDMS: this.fileTypeKeyword,
                ZLXDMS: ''
            };
            getFilelist(obj)
                .then((res) => {
                    let fileData = res[0];
                    if (fileData?.zlxList?.length) {
                        fileData.zlxList.forEach((list) => {
                            if (list.ZLXDM === this.childTypeKeyword) {
                                let fileList = list.fileList;
                                fileList.forEach((item) => {
                                    if (item.WJMC.endsWith('.mp4')) {
                                        item.fileType = '2';
                                    } else {
                                        item.fileType = '1';
                                    }
                                });
                                this.arrUploadImage = list.fileList;
                            }
                            if (
                                !this.arrUploadImage.length &&
                                this.options.zt == 1
                            ) {
                                this.isNeedShow = false;
                            }
                        });
                    }
                })
                .catch((err) => {
                    console.log('err', err);
                });
        },

        //删除文件
        delFile(file) {
            if (this.timer) {
                clearTimeout(this.timer);
            }
            this.timer = setTimeout(() => {
                let self = this;
                uni.showModal({
                    title: '提示',
                    content: '确认删除?',
                    success: function (res) {
                        if (res.confirm) {
                            console.log('用户点击确定');
                            deletefile(file.WJID).then((res) => {
                                uni.showToast({
                                    title: '删除成功',
                                    duration: 500
                                }).then(() => {
                                    setTimeout(() => {
                                        self.getImageFileList();
                                    }, 500);
                                });
                            });
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    }
                });
            }, 500);
        },
        // 上传视频
        chooseVideo(zlx) {
            let self = this;
            uni.chooseVideo({
                sourceType: ['album', 'camera'],
                maxDuration: 30,
                success: (res) => {
                    const tempFilePath = res.tempFilePath;
                    uni.showLoading();

                    uni.uploadFile({
                        url: UPLOAD_URL,
                        filePath: tempFilePath,
                        name: 'file',
                        formData: {
                            pageSize: 1000,
                            pageNum: 1,
                            YWSJID: self.uploadId,
                            LXDM: self.fileTypeKeyword,
                            ZLXDM: zlx
                        },
                        timeout: 6000,
                        success: function (res) {
                            uni.showToast({
                                title: '上传成功',
                                icon: 'none'
                            });
                            console.log('上传成功');
                            self.getImageFileList();
                            uni.hideLoading();
                        },
                        fail: function (err) {
                            console.log('上传失败');
                            uni.showToast({
                                title: '上传失败',
                                icon: 'none'
                            });
                            uni.hideLoading();
                        }
                    });
                }
            });
        },
        //添加文件
        addFile(zlx) {
            let self = this;
            let resultAddress = '';
            let latitudeAndLongitude = '';
            //上传图片前先获取定位信息
            /*#ifdef APP-PLUS*/
            uni.getLocation({
                type: 'gcj02',
                geocode: true,
                success: function (result) {
                    const {
                        latitude,
                        longitude,
                        address: {
                            province,
                            city,
                            district,
                            street,
                            streetNum,
                            poiName,
                            cityCode
                        }
                    } = result;
                    resultAddress = `${province}${city}${district}${street}${streetNum}${poiName}`;
                     latitudeAndLongitude = `${latitude.toFixed(5)}°N ${longitude.toFixed(5)}°E`;
                    uni.chooseImage({
                        count: 1, //默认值
                        sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
                        success: function (res) {
                            let len = res.tempFilePaths.length;
                            for (let i = 0; i < len; i++) {
                                self.uploadFile(
                                    res,
                                    zlx,
                                    i,
                                    resultAddress,
                                    latitudeAndLongitude
                                );
                            }
                        }
                    });
                },
                fail: (res) => {
                    uni.showModal({
                        title: '请开启获取定位权限',
                        showCancel: false
                    });
                    // uni.chooseImage({
                    //     count: 1, //默认值
                    //     sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
                    //     success: function (res) {
                    //         let len = res.tempFilePaths.length;
                    //         for (let i = 0; i < len; i++) {
                    //             self.uploadFile(
                    //                 res,
                    //                 zlx,
                    //                 i,
                    //                 resultAddress,
                    //                 latitudeAndLongitude
                    //             );
                    //         }
                    //     }
                    // });
                }
            });
            /*#endif*/
            /*#ifdef H5*/
            uni.chooseImage({
                count: 1, //默认值
                sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
                success: function (res) {
                    let len = res.tempFilePaths.length;
                    for (let i = 0; i < len; i++) {
                        self.uploadFile(
                            res,
                            zlx,
                            i,
                            resultAddress,
                            latitudeAndLongitude
                        );
                    }
                }
            });
            /*#endif*/
        },

        //上传图片
        async uploadFile(
            res,
            zlx,
            i,
            resultAddress = '',
            latitudeAndLongitude = ''
        ) {
            let f = res.tempFilePaths[i];
			let {size,type} = res.tempFiles[0];
            uni.showLoading({
                title: '上传中'
            });
            let self = this;
            let multiLineTexts = []; //水印文本
            // imageUrl: 图片bloburl
            // multilineTexts: 水印文字数组，一行文字是就是一个item,
            // fileName，文件名称
            if (!resultAddress || !latitudeAndLongitude) {
                multiLineTexts = [
                    `时间：${self.$dayjs().format('YYYY-MM-DD HH:mm:ss')}`
                ];
            } else {
                multiLineTexts = [
                    `时间：${self.$dayjs().format('YYYY-MM-DD HH:mm:ss')}`,
                    `地点：${resultAddress}`,
                    `经纬度：${latitudeAndLongitude}`
                ];
            }
            const result = await self.$refs.watermarkPainter.paintWatermark(
                f,
                multiLineTexts,
                ''
            );
            let watermarkPainter = result.path;

            uni.uploadFile({
                url: UPLOAD_URL,
                filePath: watermarkPainter,
                name: 'file',
                formData: {
					WJDX:size/1024,
					WJLX:type,
                    pageSize: 1000,
                    pageNum: 1,
                    YWSJID: self.uploadId,
                    LXDM: this.fileTypeKeyword,
                    ZLXDM: zlx
                },
                timeout: 6000,
                success: function (res) {
                    uni.showToast({
                        title: '上传成功',
                        icon: 'none'
                    });
                    console.log('上传成功');
                    self.getImageFileList();
                    uni.hideLoading();
                },
                fail: function (err) {
                    console.log('上传失败');
                    uni.showToast({
                        title: '上传失败',
                        icon: 'none'
                    });
                    uni.hideLoading();
                }
            });
        },

        //获取文件路径
        getFileUrl(item) {
            return DOWNLOAD_URLZDY + item.WJID;
        },

        //预览
        previewImage(fileList, index) {
            let fileUrls = fileList.map((file) => {
                return this.getFileUrl(file);
            });
            // 预览图片
            uni.previewImage({
                current: index,
                urls: fileUrls
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.mymask {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1000;
    width: 100%;
    height: 100%;
}
.pd-ulpic1 {
    margin-left: -10rpx;
    li {
        width: 33.3%;
        height: 140rpx;
        background-color: #fff;
        border-radius: 10rpx;
        margin: 0;
        padding: 0 6rpx;
    }
    .innerbox {
        position: relative;
        width: 100%;
        height: 100%;
        .pic {
            width: 100%;
            height: 100%;
        }
        .addpic {
            border-radius: 0;
        }
    }

    .delImg {
        width: 40rpx;
        height: 40rpx;
        border-radius: 10rpx;
        z-index: 1001;
    }
    .pic-box {
        position: relative;
        width: 213.76815rpx;
    }
    .name {
        text-align: center;
        color: #999;
        font-size: 22rpx;
        line-height: 44rpx;
    }
}
.pd-dltxt1 dt,
.pd-dltxt1 dd {
    white-space: wrap;
}
.zy-form {
    padding-left: 10rpx;
}
.media-box {
    position: absolute;
    left: 0;
    top: 0;
    width: 100vh;
    height: 100vh;
    background: #000;
}
::v-deep .u-drawer__scroll-view {
    padding: 0;
}
.preview-content {
    width: 100%;
    height: 100%;
    padding-top: 80rpx;
    padding-bottom: 20rpx;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    background: #000;
    .close-btn {
        position: absolute;
        right: 0;
        top: 0;
        background: rgba(71, 85, 105, 0.3);
        border-radius: 0px 10rpx 0px 10rpx;
        display: inline-block;
        width: 40rpx;
        height: 40rpx;
        cursor: pointer;
        text-align: center;
        color: #fff;
        z-index: 9;
    }
    .preview-file {
        width: 100%;
        height: calc(100% - 80rpx);
    }
    video {
        width: 98%;
        height: 98%;
    }
}
</style>
