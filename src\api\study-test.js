import axios from '@/common/ajaxRequest.js'
import {ULR_BASE} from '@/common/config.js'

// 学习考试
export const queryStudyTest = data => {
	data.service = 'QUERY_USER_STUDY_INFO';
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};

// 学习考试-考试列表
export const getExamination = data => {
  data.service = 'QUERY_EXAMINATION';
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};
// 获取考试试题
export const getExaminationTopic = data => {
  data.service = 'QUERY_EXAMINATION_TOPIC';
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};
// 提交答案 接口
export const submitExamination  = data => {
  data.service = 'SUBMIT_EXAMINATION';
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};

// 我的考试列表
export const myExamination  = data => {
  data.service = 'QUERY_EXAMINATION_RECORD';
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};
// 我的考试详情
export const detailsExamination  = data => {
  data.service = 'QUERY_EXAMINATION_RESULT';
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};
// 错题库
export const errorExamination  = data => {
  data.service = 'QUERY_ERROR_SUBJECT';
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};

// 知识学习 频道
export const journalismChannel  = data => {
  data.service = 'QUERY_CHANNEL';
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};
// 获取文章接口
export const journalismArticle  = data => {
  data.service = 'QUERY_ARTICLE_LIST';
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};
// 获取文章详情接口
export const journalismDETAIL = data => {
  data.service = 'QUERY_ARTICLE_DETAIL';
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};
// 评论发布和评论点赞接口
export const journalismADD_COMMENT = data => {
  data.service = 'ADD_COMMENT';
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};
// 增加学习积分接口

export const journalismADD_STUDY_SCORE = data => {
  data.service = 'ADD_STUDY_SCORE';
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};

// 收藏文章接口
export const journalismADD_COLLECTION = data => {
  data.service = 'ADD_COLLECTION';
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};
// 获取收藏文章接口
 
export const journalismQUERY_COLLECTION = data => {
  data.service = 'QUERY_COLLECTION';
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};

//知识竞赛
// export const queryYjxx = data => {
// 	data.service = 'QUERY_YWXJ_YJXX_SERVICE';
//     return axios.request({
//       method: 'post',
//       url: ULR_BASE,
//       data: data
//     });
// };

// 进入竞赛页面 
// export const queryYjxxQr = data => {
// 	data.service = 'QUERY_YWXJ_YJQR_SERVICE';
//     return axios.request({
//       method: 'post',
//       url: ULR_BASE,
//       data: data
//     });
// };

// // 考试内容 
// export const queryYjxxQr = data => {
// 	data.service = 'QUERY_YWXJ_YJQR_SERVICE';
//     return axios.request({
//       method: 'post',
//       url: ULR_BASE,
//       data: data
//     });
// };

// // 提交答案 
// export const queryYjxxQr = data => {
// 	data.service = 'QUERY_YWXJ_YJQR_SERVICE';
//     return axios.request({
//       method: 'post',
//       url: ULR_BASE,
//       data: data
//     });
// };