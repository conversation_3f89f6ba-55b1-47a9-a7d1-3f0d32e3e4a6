<!-- @format -->

<template>
    <div>
        <div class="zy-line ac jb" v-show="scList.length">
            <p class="qiye-til1">生产设备</p>
            <ul class="shebei-tuli">
                <li>
                    <i style="background-color: #7dcd27"></i>
                    <span>启动</span>
                </li>
                <li>
                    <i style="background-color: #ff7054"></i>
                    <span>停止</span>
                </li>
                <li>
                    <i style="background-color: #888888"></i>
                    <span>离线</span>
                </li>
            </ul>
        </div>
        <div class="qiye-tu" v-show="scList.length">
            <view>
                <!-- #ifdef APP-PLUS || H5 -->
                <view
                    style="width: 100%; height: 500rpx"
                    :prop2="option2"
                    :change:prop2="echarts.updateEcharts2"
                    id="echarts2"
                    class="echarts"
                    ref="echarts2"
                ></view>
                <!-- #endif -->
                <!-- #ifndef APP-PLUS || H5 -->
                <view>非 APP、H5 环境不支持</view>
                <!-- #endif -->
            </view>
        </div>
        <div class="zy-line ac jb" v-show="zwList.length">
            <p class="qiye-til1">治污设备</p>
        </div>

        <div class="qiye-tu" v-show="zwList.length">
            <view>
                <!-- #ifdef APP-PLUS || H5 -->
                <view
                    style="width: 100%; height: 400rpx"
                    :prop1="option1"
                    :change:prop1="echarts.updateEcharts1"
                    id="echarts1"
                    class="echarts"
                    ref="echarts1"
                ></view>
                <!-- #endif -->
                <!-- #ifndef APP-PLUS || H5 -->
                <view>非 APP、H5 环境不支持</view>
                <!-- #endif -->
            </view>
        </div>
        <div class="zy-line ac jb" v-show="otherEquipList.length">
            <p class="qiye-til1">其他设备</p>
        </div>

        <div class="qiye-tu" v-show="otherEquipList.length">
            <view>
                <!-- #ifdef APP-PLUS || H5 -->
                <view
                    style="width: 100%; height: 400rpx"
                    :otherEquipOption="otherEquipOption"
                    :change:otherEquipOption="echarts.updateOtherEquipEchart"
                    id="otherEquipEchart"
                    class="echarts"
                    ref="otherEquipEchart"
                ></view>
                <!-- #endif -->
                <!-- #ifndef APP-PLUS || H5 -->
                <view>非 APP、H5 环境不支持</view>
                <!-- #endif -->
            </view>
        </div>
        <div
            class="qiye-tu"
            v-show="scList.length || zwList.length || otherEquipList.length"
        >
            <div class="gap"></div>
            <div class="ic-full" @click="toRunningData"></div>
        </div>
        <div
            class="no-data"
            v-show="!scList.length && !zwList.length && !otherEquipList.length"
        >
            暂无数据
        </div>

        <div class="gap"></div>
        <div class="zy-yj-cell1" v-if="picList.length">
            <div class="zy-line ac jb">
                <p class="qiye-til1">安装点位</p>
            </div>
            <div>
                <div
                    class="cell1-bd"
                    v-for="(item, index) in picList"
                    :key="index"
                >
                    <p class="zy-yj-txt2" @click="togglePic(item)">
                        <span>{{ item.SBMC }}</span>
                        <image
                            class="arrow"
                            src="@/static/app/images/xiajiantou.png"
                        ></image>
                    </p>
                    <ul class="zy-yj-list1" v-show="item.isShow">
                        <li v-for="(item2, index) in item.WJIDS" :key="index">
                            <image
                                mode="scaleToFill"
                                style="width: 100%; height: 120px"
                                :src="getFileUrl(item2)"
                                alt=""
                                @click="previewImage(item.WJIDS, index)"
                            />
                        </li>
                    </ul>
                    <div
                        v-if="item.WJIDS.length == 0"
                        style="color: #999; text-align: center"
                    >
                        暂无安装照片
                    </div>
                </div>
            </div>
            <div class="gap"></div>
            <div class="gap"></div>
        </div>
    </div>
</template>

<script>
import { debounce } from 'lodash';
import { DOWNLOAD_URLZDY } from '@/common/config.js';
import { setOption } from '@/common/setWarnStateOption.js';
import {
    sbsxzt,
    qyztrl,
    getScrl,
    getDwzb
} from '../../../../api/iot/runningData.js';
import { monitorPicture } from '@/api/iot/warning.js';
export default {
    inject: ['getEnterpriseData'],
    computed: {
        enterpriseData() {
            return this.getEnterpriseData();
        }
    },
    props: {
        currentscx: {
            type: String,
            default: ''
        },
        dateStr: {
            type: String,
            default: ''
        }
    },

    data() {
        return {
            option1: {},
            option2: {},
            otherEquipOption: {},
            picList: [],
            scList: [],
            zwList: [],
            otherEquipList: []
        };
    },
    methods: {
        init() {
            this.$nextTick(() => {
                this.option2 = {};
                this.option1 = {};
                this.otherEquipOption = {};
                this.gettjt();
                this.getMonitorPicture();
            });
        },

        //切换图片显示隐藏
        togglePic(item) {
            item.isShow = !item.isShow;
        },
        //查询设备图片接口
        getMonitorPicture() {
            let obj = {};
            obj = {
                SCXID: this.currentscx
            };

            monitorPicture(obj).then((res) => {
                if (res.status == '000') {
                    if (res.data && res.data.length && res.data.length > 0) {
                        this.picList = res.data;
                        this.picList.forEach((item) => {
                            this.$set(item, 'isShow', false);
                            //item.isShow = false;
                            if (item.WJIDS) {
                                item.WJIDS = item.WJIDS.split(',');
                            } else {
                                item.WJIDS = [];
                            }
                        });
                    }
                }
            });
        },
        getFileUrl(item) {
            return DOWNLOAD_URLZDY + item;
        },
        //预览
        previewImage(fileList, index) {
            let fileUrls = fileList.map((file) => {
                return this.getFileUrl(file);
            });
            // 预览图片
            uni.previewImage({
                current: index,
                urls: fileUrls
            });
        },
        gettjt(scrq) {
            let that = this;
            sbsxzt({
                SCXID: this.currentscx,
                DATE: this.dateStr
            }).then((res) => {
                this.$nextTick(() => {
                    uni.$emit('sendData', {
                        arrSendData: res.data || []
                    });
                });
                //存储生产设备list
                uni.$emit('sendTrendData', {
                    equipList: res.data || []
                });
                res?.data?.zwList.forEach((x) => {
                    x.name = x.SBMC;
                    // x.ztList = JSON.parse(x.ztList);
                    if (x.ztList && x.ztList.length > 0) {
                        x.list = x.ztList;
                        x.list.forEach((y) => {
                            y.end = that
                                .$dayjs(y.end)
                                .format('YYYY-MM-DD HH:mm');
                        });
                    }
                });
                res?.data?.scList.forEach((x) => {
                    x.name = x.SBMC;
                    // x.ztList = JSON.parse(x.ztList);
                    if (x.ztList && x.ztList.length > 0) {
                        x.list = x.ztList;
                        x.list.forEach((y) => {
                            // y.end = that
                            // 	.$dayjs(y.end)
                            // 	.add(1, 'minute')
                            // 	.format('YYYY-MM-DD HH:mm');
                            y.end = that
                                .$dayjs(y.end)
                                .format('YYYY-MM-DD HH:mm');
                        });
                    }
                });
                res?.data?.qtList.forEach((x) => {
                    x.name = x.SBMC;
                    // x.ztList = JSON.parse(x.ztList);
                    if (x.ztList && x.ztList.length > 0) {
                        x.list = x.ztList;
                        x.list.forEach((y) => {
                            // y.end = that
                            // 	.$dayjs(y.end)
                            // 	.add(1, 'minute')
                            // 	.format('YYYY-MM-DD HH:mm');
                            y.end = that
                                .$dayjs(y.end)
                                .format('YYYY-MM-DD HH:mm');
                        });
                    }
                });
                this.scList = res.data.scList || [];
                this.zwList = res.data.zwList || [];
                this.otherEquipList = res.data.qtList || [];

                // let option2 = res.data.scList;
                // let option1 = res.data.zwList;
                // let newArr1 = res.data.scList;
                // let newArr2 = res.data.zwList;
                this.$nextTick(() => {
                    if (this.otherEquipList.length) {
                        this.$parent.isPHEquip = true;
                    }
                    this.option2 = {};
                    this.option1 = {};
                    this.otherEquipOption = {};
                    this.option2 = this.setChart(this.scList);
                    this.option1 = this.setChart(this.zwList);
                    this.otherEquipOption = this.setChart(this.otherEquipList);
                });
            });
        },
        setChart(data) {
            let statusObj = {
                1: {
                    color: '#ff7054',
                    label: '停止'
                },

                2: {
                    color: '#7dcd27',
                    label: '开启'
                },

                0: {
                    color: '#999',
                    label: '离线'
                }
            };

            let x = [];

            let minutes = 0;

            let jszj = '';

            if (this.dateStr == this.$dayjs().format('YYYY-MM-DD')) {
                minutes = this.getcurrentallminute();

                jszj = this.getcurrenthhmm();

                //minutes = 1440;

                //jszj = '23:59';
            } else {
                minutes = 1440;

                jszj = '23:59';
            }

            for (let i = 0; i < minutes; i++) {
                let num = i;

                let h = Math.floor(num / 60);

                let min = num % 60;

                h = (h <= 9 ? '0' : '') + h;

                min = (min <= 9 ? '0' : '') + min;
                x.push(h + ':' + min);
            }
            let option = setOption(data, x);
            return option;
        },
        getcurrentallminute() {
            let hour = this.$dayjs().format('HH');
            let minutes = this.$dayjs().minute();
            return hour * 60 + minutes + 1;
        },
        getcurrenthhmm() {
            return this.$dayjs().add(1, 'minute').format('HH:mm');
        },
        toRunningData() {
            uni.navigateTo({
                url: `/pages/runningData/Index?SCXID=${this.currentscx}&DATE=${this.dateStr}`
            });
        }
    }
};
</script>

<script module="echarts" lang="renderjs">
let myChart1;
let myChart2;
let myChartOtherEquip;
let insertStr = function(source, start, newStr) {
				//source 要插入的字符串
				//start 开始位置
				// newStr 插入的字符串
				return source.slice(0, start) + newStr + source.slice(start)
			}
			//重置renderjs的option
let resetOption = function(newValue){
				let option = JSON.parse(JSON.stringify(newValue));
				option.yAxis.axisLabel.formatter = function(value) {
					if (value === '治污设备' || value === '生产设备' || value === '其他设备') {
						return '{black|' + value + '}'
					} else {
						return '{white|' + value + '}'
					}
				}
				option.xAxis.axisLabel.formatter = function(v) {
					let value = newValue.xAxis.data[v] || ''
					let str = '  ' + insertStr(value, 6, ' \n')
					return str || ''
				}

				// 监听 service 层数据变更
				option.tooltip.formatter = function(obj) {
					let data = obj && obj.data
					return `<div style="background:rgba(0,0,0,.7);color:#fff;padding:6px;border-radius:2px;font-size:12px;">
							  <div> ${obj.name}</div>
							  <div> 开始时间：${data.start}</div>
							  <div> 结束时间：${data.end} </div>
							  <div>  状态：<span style="color:${obj.seriesName == '停止' ? '#ff7054' : obj.seriesName == '开启' ? '#7dcd27' : '#888'};">${obj.seriesName}</span> </div>
							 </div>`
					}
				option.tooltip.confine = true;
				return option
			}
export default {
	data() {
		return {
			chartw: "",
			charth: '',
			flag: false,
		}
	},
	onLoad() {

	},
	mounted() {
		if (typeof window.echarts === 'function') {
			this.initEcharts()
		} else {
			// 动态引入较大类库避免影响页面展示
			const script = document.createElement('script')
			// view 层的页面运行在 www 根目录，其相对路径相对于 www 计算
			script.src = 'static/echarts4.9.0.js'
			script.onload = this.initEcharts.bind(this)
			document.head.appendChild(script)
		}

	},
	methods: {
		initEcharts() {
			let echarts1 = document.getElementById('echarts1');
			echarts1.setAttribute("style", "display:block;height:500px,width:100%;");
            myChart1 = echarts.init(echarts1)
            let option1 = {};
			if (this.option1 && this.option1.tooltip && this.option1.tooltip.yAxis) {
				option1 = {
					...this.option1
				};

				option1.tooltip.formatter = function(obj) {
					let data = obj.data;
					return `<p>${data.name}</p>
			              <p>开始时间：${data.start}</p>
			              <p>结束时间：${data.end}</p>
			              <p>状态：${data.statusLabel}</p>
			            `;
				}
				option1.tooltip.confine = true;
			}
			myChart1 && myChart1.setOption(option1)




			let echarts2 = document.getElementById('echarts2');
			echarts2.setAttribute("style", "display:block;height:500px,width:100%;");
			myChart2 = echarts.init(echarts2)
			let option2 = {};
			if (this.option2 && this.option2.tooltip) {
				option2 = {
					...this.option2
				};

			}
			myChart2 && myChart2.setOption(option2)

            let otherEquipEchart = document.getElementById('otherEquipEchart');
			otherEquipEchart.setAttribute("style", "display:block;height:500px,width:100%;");
			myChartOtherEquip = echarts.init(otherEquipEchart)
			let otherEquipOption = {};
			if (this.otherEquipOption && this.otherEquipOption.tooltip) {
				otherEquipOption = {
					...this.otherEquipOption
				};
				otherEquipOption.tooltip.formatter = function(obj) {
					let data = obj.data;
					return `<p >${data.name}</p>
			              <p>开始时间：${data.start}</p>
			              <p>结束时间：${data.end}</p>
			              <p>状态：${data.statusLabel}</p>
			            `;
				}
			}
			myChartOtherEquip && myChartOtherEquip.setOption(otherEquipOption)


		},
		updateEcharts1(newValue, oldValue, ownerInstance, instance) {
			let echarts1 = document.getElementById('echarts1');
			echarts1.style.height = newValue && newValue.yAxis && newValue.yAxis.data.length * 32 + 44 + 'px';
			// 解决在切换tab时把option1和option2改成了{}，导致没有formatter，tooltip等参数而报错
			if (!newValue || Object.keys(newValue).length === 0) {
				return;
			}
			let option =  resetOption(newValue)
			myChart1 && myChart1.clear()
			myChart1 && myChart1.setOption(option)
			myChart1 && myChart1.resize()
		},

		updateEcharts2(newValue, oldValue, ownerInstance, instance) {

			let echarts2 = document.getElementById('echarts2');
			echarts2.style.height = newValue && newValue.yAxis && newValue.yAxis.data.length * 32 + 44 + 'px';
			//自定义状态的文字和颜色
			// 解决在切换tab时把option1和option2改成了{}，导致没有formatter，tooltip等参数而报错
			if (!newValue || Object.keys(newValue).length === 0) {
				return;
			}
           let option =  resetOption(newValue)
			myChart2 && myChart2.clear()
			myChart2 && myChart2.setOption(option)
			myChart2 && myChart2.resize()
		},
        //更新其他设备的echarts 配置项
		updateOtherEquipEchart(newValue, oldValue, ownerInstance, instance) {
			let otherEquipEchart = document.getElementById('otherEquipEchart');
			otherEquipEchart.style.height = newValue && newValue.yAxis && newValue.yAxis.data.length * 32 + 44 + 'px';
			//自定义状态的文字和颜色
			if (!newValue || Object.keys(newValue).length === 0) {
				return;
			}
		    let option =  resetOption(newValue)
			myChartOtherEquip && myChartOtherEquip.clear()
			myChartOtherEquip && myChartOtherEquip.setOption(option)
			myChartOtherEquip && myChartOtherEquip.resize()
		},


		format(date, fmt) {
			var o = {
				"M+": date.getMonth() + 1, //月份
				"d+": date.getDate(), //日
				"h+": date.getHours(), //小时
				"m+": date.getMinutes(), //分
				"s+": date.getSeconds(), //秒
				"q+": Math.floor((date.getMonth() + 3) / 3), //季度
				"S": date.getMilliseconds() //毫秒
			};
			if (/(y+)/.test(fmt)) {
				fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
			}
			for (var k in o) {
				if (new RegExp("(" + k + ")").test(fmt)) {
					fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k])
						.length)));
				}
			}
			return fmt;
		},
	}
}
</script>

<style>
.zy-yj-txt2 {
    padding: 0;
}
.arrow {
    display: block;
    width: 40rpx;
    height: 40rpx;
}

.qiye-tu {
    position: relative;
}

.ic-full {
    position: absolute;
    right: 26rpx;
    bottom: -26rpx;
}

.no-data {
    text-align: center;
    color: #999;
    line-height: 50rpx;
    font-size: 24rpx;
}

.zy-yj-txt2 {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.zy-yj-cell1 .cell1-hd {
    padding: 0px;
}
.cell1-bd {
    padding: 10rpx 0;
}

.zy-yj-cell1 .cell1-bd:last-child {
    border-bottom: 0.5px solid #ddd;
}
</style>
