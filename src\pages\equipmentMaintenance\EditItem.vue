<!-- @format -->

<!-- 新增，详情 -->
<template>
    <section class="main">
        <div class="inner">
            <u-form
                labelPosition="left"
                :model="formData"
                :rules="rules"
                ref="uForm"
            >
                <div class="xqmod">
                    <div class="yy-tit1">
                        <h1>运维信息</h1>
                    </div>
                    <div style="padding: 0 18px">
                        <u-form-item
                            v-show="options.type != 'add'"
                            class="item"
                            label="运维编号"
                            prop="jlbh"
                            borderBottom
                            ref="item1"
                        >
                            <u-input
                                :clearable="false"
                                disabled
                                v-model="formData.jlbh"
                            ></u-input>
                        </u-form-item>
                        <u-form-item
                            class="item"
                            label="生成时间"
                            prop="cjsj"
                            borderBottom
                            ref="item1"
                        >
                            <u-input
                                :clearable="false"
                                disabled
                                v-model="formData.cjsj"
                            ></u-input>
                        </u-form-item>
                        <u-form-item
                            class="item"
                            label="运维时间"
                            prop="ywsj"
                            borderBottom
                            ref="item1"
                        >
                            <u-input
                                :clearable="false"
                                disabled
                                v-model="formData.ywsj"
                                @click="show = isDisabled ? false : true"
                            ></u-input>
                        </u-form-item>
                        <u-form-item
                            class="item"
                            label="运维企业"
                            prop="wrymc"
                            borderBottom
                            ref="item1"
                            @click.native="inputFunc(formData.wrymc)"
                        >
                            <u-input
                                :clearable="false"
                                :disabled="
                                    (options.zt && options.zt === '0') ||
                                    options.zt === '1'
                                "
                                v-model="formData.wrymc"
                                @input="inputFunc(formData.wrymc)"
                                placeholder="请选择企业"
                            ></u-input>
                            <u-icon
                                slot="right"
                                v-show="!options.zt && options.zt !== '0'"
                                name="arrow-right"
                            ></u-icon>
                        </u-form-item>
                        <u-form-item
                            class="item"
                            label="运维人员"
                            prop="ywr"
                            borderBottom
                            ref="item1"
                        >
                            <u-input
                                :disabled="isDisabled"
                                v-model.trim="formData.ywr"
                                :clearable="false"
                            ></u-input>
                        </u-form-item>
                        <u-form-item
                            class="item"
                            label="联系电话"
                            prop="ywrlxdh"
                            borderBottom
                            ref="item1"
                        >
                            <u-input
                                :clearable="false"
                                placeholder="请输入联系电话"
                                :disabled="isDisabled"
                                v-model.trim="formData.ywrlxdh"
                            ></u-input>
                        </u-form-item>
                    </div>
                </div>
            </u-form>
            <div v-if="options.zt && options.zt == 1">
                <div class="xqmod">
                    <div class="yy-tit1 maintain-detail">
                        <h1>处理结果</h1>
                    </div>
                </div>
                <div class="zy-form zy-form-result">
                    <div class="item">
                        <p class="label" style="flex: 5">运维里程</p>
                        <div class="rp pd-btn1">
                            <span>{{ formData.ywlc || '-' }}</span>
                            <span>公里</span>
                        </div>
                    </div>
                    <div class="item">
                        <p class="label" style="flex: 5">总耗时</p>
                        <div class="rp pd-btn1">
                            <span>{{ formData.ywhs || '-' }}</span>
                            <span>小时</span>
                        </div>
                    </div>
                    <div class="item">
                        <p class="label" style="flex: 5">备注说明</p>
                        <div class="rp pd-btn1">
                            <span>{{ formData.bz || '-' }}</span>
                        </div>
                    </div>
                    <!-- <div class="item">
                        <p class="label" style="flex: 5">现场拍照/视频</p>
                        <div class="rp pd-btn1">
                            <span></span>
                        </div>
                    </div> -->
                    <div class="upload-img-box">
                        <UploadImageListOrVideo
                            ref="refUploadImageListOrVideo"
                            fileTypeKeyword="YWJL"
                            childTypeKeyword="XCZP"
                            :uploadId="options.ywid"
                            :isDisabled="true"
                            :options="options"
                            title="现场拍照"
                        >
                        </UploadImageListOrVideo>
                    </div>
                </div>
            </div>
            <div v-if="getShowMaintainDetail()">
                <div class="xqmod">
                    <div class="yy-tit1 maintain-detail">
                        <h1>
                            运维明细
                            <span
                                class="detail-num"
                                v-show="arrMaintenanceDetail.length"
                                >{{ arrMaintenanceDetail.length || '0' }}</span
                            >
                        </h1>

                        <div
                            class="maintain-detail-icon"
                            :class="{
                                'maintain-detail-icon-show': !isDisabled
                            }"
                        >
                            <u-icon
                                v-show="arrMaintenanceDetail.length"
                                @click="delMaintenanceDetailList"
                                name="minus-circle"
                                color="#4874ff"
                                size="18"
                                style="margin-right: 20rpx"
                            ></u-icon>
                            <u-icon
                                @click="showOperationType"
                                name="plus-circle"
                                color="#4874ff"
                                size="18"
                            ></u-icon>
                        </div>
                    </div>
                </div>
                <div class="zy-form">
                    <div
                        class="item"
                        @click="toDetail(item)"
                        v-for="(item, index) in arrMaintenanceDetail"
                        :key="index"
                    >
                        <image
                            v-show="!isDisabled && item.isSelected"
                            @click.stop="chooseDelete(item)"
                            mode="widthFix"
                            class="choose"
                            src="@/static/images/choose.png"
                        />
                        <image
                            v-show="!isDisabled && !item.isSelected"
                            @click.stop="chooseDelete(item)"
                            mode="widthFix"
                            class="unchoose"
                            src="@/static/images/unchoose.png"
                        />

                        <p
                            class="label wider"
                            :class="{
                                'page-finish': isDisabled
                            }"
                        >
                            {{ getOperationType(item) }}
                            {{ item.imei.substr(-6) }}
                        </p>
                        <image
                            src="@/static/app/images/arwrt1.png"
                            class="pd-arw1"
                        />
                    </div>
                </div>
            </div>

            <div class="gap"></div>
            <div class="gap"></div>
            <div class="gap"></div>
            <ul class="foot-btnbox flx1 ac jb" v-if="!isDisabled">
                <li class="confirmbtn" @click="saveData('finish')">完成</li>
                <li @click="saveData('save')">暂存</li>
            </ul>
        </div>
        <!-- 时间选择器 -->
        <u-picker
            mode="time"
            v-model="show"
            :params="timeParams"
            @confirm="
                (t) => {
                    selectTime(t);
                }
            "
        ></u-picker>

        <!-- 运维类型选择器 -->
        <u-select
            value-name="ywlx"
            v-model="isShowOperationType"
            :list="arrOperationType"
            @confirm="confirmOperationType"
        ></u-select>
        <!-- 企业选择器 -->
        <u-select
            v-model="showEnterprise"
            :list="enterpriseList"
            label-name="WRYMC"
            value-name="WRYBH"
            @cancel="cancelEnterprise"
            @confirm="changeEnterprise"
        ></u-select>
        <!-- 运维闭环组件 -->
        <ResultDialog
            :formData="formData"
            :options="options"
            :isDisabled="isDisabled"
            v-show="showDialog"
            @close="showDialog = false"
        ></ResultDialog>
    </section>
</template>

<script>
import { getQyxx } from '@/api/iot/enterprise.js';
import { getList } from '@/api/iot/realtime.js';
import { guid } from '@/utils/uuid.js';
import ResultDialog from './components/ResultDialog';
import { getValid } from '@/utils/validData.js';
import {
    deleteMaintenanceDetailList,
    getFormdataByYwid,
    saveFormdata,
    maintenanceDetailList,
    commitFormdata
} from '@/api/iot/equipmentMaintenance';
import UploadImageListOrVideo from './components/UploadImageListOrVideo';
export default {
    //import引入的组件需要注入到对象中才能使用
    name: 'editItem',
    components: { ResultDialog, UploadImageListOrVideo },
    data() {
        return {
            show: false,
            timeParams: {
                year: true,
                month: true,
                day: true,
                hour: true,
                minute: true,
                second: true
            },
            options: {},
            formData: {
                ywid: '',
                wrymc: '',
                ywrlxdh: '',
                ywr: ''
            },
            rules: {
                wrymc: {
                    required: true,
                    message: '请选择企业'
                },
                ywr: {
                    required: true,
                    message: '请输入运维人员'
                },
                ywrlxdh: {
                    required: true,
                    message: '请输入联系电话'
                }
            },
            showDialog: false,
            showCompany: false,
            enterpriseList: [],
            showEnterprise: false, //展示企业选择器
            userInfo: {}, //账号信息
            isDisabled: true, //是否可编辑
            isShowOperationType: false, //显示运维类型选择
            arrOperationType: [
                //运维类型（1-更换设备，2-更换电池，3-调整位置，4-拆除设备，5-信号优化，6-例行巡检）
                {
                    label: '更换设备',
                    url: 'replaceEquipment',
                    ywlx: '1'
                },
                {
                    label: '更换电池',
                    url: 'replaceBattery',
                    ywlx: '2'
                },
                {
                    label: '调整位置',
                    url: 'adjustPosition',
                    ywlx: '3'
                },
                {
                    label: '拆除设备',
                    url: 'removeEquipment',
                    ywlx: '4'
                },
                {
                    label: '信号优化',
                    url: 'signalOptimization',
                    ywlx: '5'
                },
                {
                    label: '例行巡检',
                    url: 'routineInspection',
                    ywlx: '6'
                },
                {
                    label: '设备丢失',
                    url: 'equipLost',
                    ywlx: '7'
                }
            ],
            arrMaintenanceDetail: [],
            arrDeleteIds: []
        };
    },

    computed: {},
    watch: {},
    onLoad(options) {
        this.options = options;
    },
    onReady() {
        if (this.options.type == 'add') {
            this.isDisabled = false;
            uni.setNavigationBarTitle({
                title: '新增运维记录'
            });
            this.formData.ywid = guid();
            this.options.ywid = this.formData.ywid;
            this.formData.cjsj = this.$dayjs().format('YYYY-MM-DD HH:mm:ss');
            this.formData.ywsj = this.$dayjs().format('YYYY-MM-DD HH:mm:ss');
        } else if (this.options.type == 'edit') {
            this.isDisabled = false;
            uni.setNavigationBarTitle({
                title: '记录详情'
            });
            this.getData();
            if (this.options.zt === '1') {
                this.$refs.refUploadImageListOrVideo.getImageFileList();
            }
        }
    },
    created() {},
    mounted() {
        //获取账号信息
        this.userInfo = uni.getStorageSync('userInfo');
        this.formData.ywrlxdh = this.userInfo.mobile || '';
        this.formData.ywr = this.userInfo.name || '';
        this.getMaintenanceDetailList();
    },
    methods: {
        //展示运维明细判断
        getShowMaintainDetail() {
            let flag = false;
            flag =
                (this.options.zt && this.options.zt === '0') ||
                (this.options.zt &&
                    this.options.zt === '1' &&
                    this.arrMaintenanceDetail.length);
            return flag;
        },
        //选中运维记录
        chooseDelete(item) {
            item.isSelected = !item.isSelected;
            this.arrDeleteIds.push(item.mxid);
        },
        //删除运维记录明细
        async delMaintenanceDetailList() {
            if (!this.arrDeleteIds.length) {
                uni.showToast({
                    title: '请选择要删除的明细',
                    duration: 1500,
                    icon: 'none'
                });
                return;
            }
            let deletedIdStr = this.arrDeleteIds.join(',');
            await deleteMaintenanceDetailList(deletedIdStr);
            uni.showToast({
                title: '删除成功',
                duration: 1000,
                icon: 'success'
            });
            setTimeout(() => {
                this.arrDeleteIds = [];
                this.getMaintenanceDetailList();
                let pages = getCurrentPages();
                //刷新上一页面的设备列表
                let beforePage = pages[pages.length - 2]; // 上一页
                if (beforePage.$vm) {
                    beforePage.$vm.freshData && beforePage.$vm.freshData();
                } else {
                    beforePage.freshData && beforePage.freshData();
                }
            }, 1000);
        },

        //运维记录明细
        async getMaintenanceDetailList() {
            let data = await maintenanceDetailList(this.options.ywid);
            data = data.filter((item) => item.ywlx != '');
            this.arrMaintenanceDetail = data;
            this.arrMaintenanceDetail.forEach((item) => {
                this.$set(item, 'isSelected', false);
            });
        },
        //获取运维类型
        getOperationType(item) {
            let name = '';
            let target = this.arrOperationType.find(
                (v) => item.ywlx === v.ywlx
            );
            name = target.label;
            return name;
        },
        //跳转运维详情
        toDetail(item) {
            this.toMaintenanceDetail(item.ywlx, 'detail', item.mxid);
        },
        //显示运维类型选择
        showOperationType() {
            this.isShowOperationType = true;
        },
        //选中设备类型
        confirmOperationType(v) {
            console.log('v ', v);
            this.toMaintenanceDetail(v[0].value, 'add');
        },
        //跳转详情路由
        /**
         * @params ywlx 运维类型
         * @params editType add：添加 edit：编辑 detail:详情展示
         * @params detailId   运维明细id
         */
        toMaintenanceDetail(ywlx, editType, detailId = '') {
            let target = this.arrOperationType.find(
                (item) => ywlx === item.ywlx
            );
            let targetStr = target.url;
            let capitalizedStr =
                targetStr.charAt(0).toUpperCase() + targetStr.slice(1);
            let url = '';
            if (editType == 'detail') {
                url = `/pages/equipmentMaintenance/${capitalizedStr}?mxid=${detailId}&type=${editType}`;
            } else {
                let wrymcCode = encodeURIComponent(this.formData.wrymc);
                url = `/pages/equipmentMaintenance/${capitalizedStr}?ywlx=${ywlx}&ywid=${this.options.ywid}&type=${editType}&wrymc=${wrymcCode}&wrybh=${this.formData.wrybh}`;
            }

            uni.navigateTo({
                url: url
            });
        },
        //输入企业
        inputFunc(keyword) {
            if (keyword?.length < 2) {
                if (this.timer) {
                    clearTimeout(this.timer);
                }
                this.timer = setTimeout(() => {
                    uni.showToast({
                        title: '请输入2位以上字符搜索',
                        icon: 'none'
                    });
                }, 500);
                return;
            } else {
                if (this.timer) {
                    clearTimeout(this.timer);
                }
                this.timer = setTimeout(() => {
                    this.getEnterpriseList();
                }, 500);
            }
        },
        //选中企业
        changeEnterprise(v) {
            this.formData.wrymc = v[0].label;
            this.formData.wrybh = v[0].value;
            this.options.wrymc = this.formData.wrymc;
        },
        //取消选择企业
        cancelEnterprise(v) {
            console.log({ v });
        },

        // 初始化企业数据
        async getEnterpriseList(v) {
            let self = this;
            let params = {
                // pageSize: 1000,
                // pageNum: 1,
                ALLSEARCH: this.formData.wrymc,
                ZT: '1', //已安装状态
                ORGID: this.userInfo
            };
            let { data } = await getQyxx(params);
            this.enterpriseList = data;
            if (this.enterpriseList.length) {
                uni.hideKeyboard();
                this.showEnterprise = true;
            } else {
                this.formData.wrymc = '';
                uni.showModal({
                    title: '提示',
                    cancelText: '取消',
                    content: '请输入正确的企业名称',
                    showCancel: false,
                    success: function (res) {
                        if (res.confirm) {
                            console.log('用户点击确定');
                            self.formData.wrymc = '';
                            return;
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                            self.formData.wrymc = '';
                            return;
                        }
                    }
                });
            }
        },
        getData() {
            getFormdataByYwid(this.options.ywid).then((r) => {
                this.formData = r;
                // 状态1代表已完成 0代表处理中
                if (r.zt == 1) {
                    this.isDisabled = true;
                }
            });
        },

        async saveData(v) {
            await getValid(this.rules, this.formData);
            if (this.formData.ywrlxdh.length != 11) {
                uni.showToast({
                    title: '请输入11位电话号码',
                    icon: 'none'
                });
                return;
            }
            if (v === 'save') {
                saveFormdata({
                    ...this.formData
                }).then((r) => {
                    if (r.success) {
                        uni.showToast({
                            title: '暂存成功'
                        });

                        let pages = getCurrentPages(); // 当前页面
                        let beforePage = pages[pages.length - 2]; // 上一页
                        if (beforePage.$vm) {
                            // beforePage.$vm.freshData &&
                            //     beforePage.$vm.freshData();
                            beforePage.$vm.showState = '0';
                        } else {
                            // beforePage.freshData && beforePage.freshData();
                            beforePage.showState = '0';
                        }

                        //如果没有zt字段，添加zt字段，显示运维明细添加操作出来
                        if (!this.options.hasOwnProperty('zt')) {
                            this.$set(this.options, 'zt', '0');
                        }
                    }
                });
            } else if (v === 'finish') {
                // if (!this.arrMaintenanceDetail.length) {
                //     uni.showToast({
                //         title: '请输入运维明细',
                //         icon: 'none'
                //     });
                //     return;
                // }
                this.showDialog = true;
            }
        },
        selectTime(v) {
            console.log(v);
            this.formData.ywsj = `${v.year}-${v.month}-${v.day} ${v.hour}:${v.minute}:${v.second}`;
        }
    }
};
</script>
<style lang="scss" scoped>
.inner {
    ::v-deep .u-form {
        padding-left: 0;
        padding-right: 0;
    }
}

.maintain-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .maintain-detail-icon {
        // width: 13%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        display: none;
    }
    .maintain-detail-icon-show {
        display: block;
    }
    .detail-num {
        margin-left: 10rpx;
        color: rgb(72, 116, 255);
    }
}
.foot-btnbox {
    z-index: 1;
}
.zy-form {
    padding: 0 20rpx 100rpx 20rpx;
    .choose,
    .unchoose {
        width: 60rpx;
        height: 60rpx;
        padding: 13rpx;
        position: relative;
        z-index: 1;
        margin-right: 4px;
        margin-left: -8rpx;
    }
}
.upload-img-box {
    padding: 0 8rpx 0 18rpx;
}
.xqmod {
    .item {
        padding: 8rpx 0 !important;
        ::v-deep .u-form-item--left__content__label {
            font-size: 14px !important;
        }

        ::v-deep .u-input__input {
            font-size: 16px !important;
            text-align: right;
        }
    }
}
.zy-form-result {
    padding: 0 20rpx 21rpx 10rpx;
    .item {
        padding: 26rpx;
    }
}
.page-finish {
    padding-left: 10rpx;
}
</style>
