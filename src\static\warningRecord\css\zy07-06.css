.sw-backbtn{position: absolute; left: 0; top: 0; width: 82.1256rpx; height: 79.71015rpx; background: url(~@/static/warningRecord/images/sw_backic.png) no-repeat center; background-size: 21.739125rpx;}

.sw-con1 .inner{padding-left: 28.985475rpx; padding-right: 28.985475rpx;}

.sw-ictxt1{font-size: 31.401rpx; color: #333; background: url(~@/static/warningRecord/images/sw_ic1a.png) no-repeat left center; background-size: 48.30915rpx; padding-left: 63.405825rpx; font-weight: bold; line-height: 48.30915rpx;}

.sw-ulaxis1 li:before{content: ''; position: absolute; left: 13.2850501rpx; top: 42.270525rpx; bottom: 0; width: 1px; border-left: 1px dashed #4c6eff;}
.sw-ulaxis1 li{position: relative; padding-bottom: 24.154575rpx;}
.sw-ulaxis1 li h1{padding-left: 44.6859751rpx; font-size: 28.985475rpx; color: #333; position: relative;}
.sw-ulaxis1 li h1:before{content: ''; position: absolute; left: 0; top: 6.0386251rpx; width: 26.570025rpx; height: 26.570025rpx; border-radius: 50%; border: 3.623175rpx solid #4c6eff; background: #f5f5f5; box-sizing: border-box;}
.sw-ulaxis1 li h2{background: #fff; border-radius: 18.1159501rpx; padding: 24.154575rpx 22.94685rpx 24.154575rpx 22.94685rpx; margin-left: 44.6859751rpx; margin-top: 18.1159501rpx;}
.sw-ulaxis1 li h2 p{font-size: 28rpx; color: #666;}
.sw-ulaxis1 li h2 p + p{ padding-top: 18.1159501rpx;}
.sw-ulaxis1 li + li{margin-top: 6.0386251rpx;}

.header.bluebg {
    background-color: #4c6eff;
}

.yy-ultbs1 {
    display: flex;
    justify-content: space-around;
    background: #fff;
    height: 79.71015rpx;
}

.yy-ultbs1 li {
    font-size: 28.985475rpx;
    color: #666;
    line-height: 79.71015rpx;
    position: relative;
}

.yy-ultbs1 li.on {
    color: #4c6eff;
}

.yy-ultbs1 li.on:after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: 0;
    width: 84.54105rpx;
    height: 7.24635rpx;
    background: #4c6eff;
    border-radius: 7.24635rpx 7.24635rpx 0 0;
    transform: translateX(-50%);
}

.yy-ullst1 {
    background: #fff;
    padding-left: 28.985475rpx;
}

.yy-ullst1>li {
    padding: 27.77775rpx 28.985475rpx 27.77775rpx 0;
    display: flex;
    justify-content: space-between;
    position: relative;
}

.yy-ullst1>li em {
    font-size: 28.985475rpx;
    color: #666;
    width: 30%;
    display: inline-block;
}

.yy-ullst1>li+li {
    border-top: 0.6039rpx solid #eee;
}

.yy-ullst1>li .rfont {
    font-size: 28.985475rpx;
    color: #333;
    flex: 1;
    display: inline-block;
    text-align: right;
    line-height: 50.724675rpx;
}
.yy-ullst1>li .rfont.txtlef{
    text-align: left;
}
.zy-yj-cell1{
    background-color: #fff;
    margin-bottom: 18.1159501rpx;
}
.zy-yj-cell1:last-child{
    margin-bottom: 0;
}
.zy-yj-cell1 .cell1-hd{
    height: 84.54105rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 28.985475rpx;
}
.zy-yj-cell1 .cell1-bd{
    border-top: 0.6039rpx solid #ddd;
}
.zy-yj-til1{
    font-size: 28.985475rpx;
    color: #333;
    line-height: 44rpx;
    padding-left: 28.985475rpx;
    background: url(~@/static/warningRecord/images/zy_til1_bg.png) 0 center no-repeat;
    background-size: 10.8696rpx 28.985475rpx;
}

.zy-yj-more{
    padding-right: 33.8164501rpx;
    font-size: 28rpx;
    color: #666;
    background: url(~@/static/warningRecord/images/zy_more1.png) right center no-repeat;
    background-size: 13.2850501rpx;
}
.zy-yj-block1{
    height: 84.54105rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 28.985475rpx;
    background-color: #f0f9ff;
    border-bottom: 0.6039rpx solid #ddd;
}
.zy-yj-txt1{
    font-size: 28rpx;
    color: #42677f;
    line-height: 84.54105rpx;
}
.zy-yj-tips{
    display: flex;
    border-radius: 6.0386251rpx;
    overflow: hidden;
}
.zy-yj-tips span{
    font-size: 28rpx;
    color: #fff;
    line-height: 44.6859751rpx;
    width: 75.483075rpx;
    text-align: center;
}
.zy-yj-tips span:nth-of-type(1){
    background-color: #7dcf27;
}
.zy-yj-tips span:nth-of-type(2){
    background-color: #fe7154;
}
.zy-yj-tips span:nth-of-type(3){
    background-color: #888888;
}
.zy-yj-txt2{
    font-size: 28.985475rpx;
    color: #333;
    line-height: 77.2947rpx;
    padding: 0 28.985475rpx;
}
.zy-yj-list1{
    display: flex;
    flex-wrap: wrap;
    padding: 0 19.9275rpx;
}
.zy-yj-list1 li{
    width: 33.33%;
    box-sizing: border-box;
    padding: 0 9.0579751rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 18.1159501rpx;
}
.zy-yj-list1 li img{
    width: 100%;
}
.zy-yj-tu{
    overflow-x: auto;
    padding-left: 28.985475rpx;
}
.zy-yj-tu img{
    height: 344.2029rpx;
}

.qiye-til1 {
    font-size: 34.722225rpx;
    color: #333;
    line-height: 76.388925rpx;
    padding-left: 27.77775rpx;
    background: url(~@/static/warningRecord/images/qiye-til1.png) 0 center no-repeat;
    background-size: 10.416675rpx;
}
.qiye-shebei .item {
    display: flex;
    align-items: center;
    height: 86.1110999rpx;
}

.qiye-shebei .item .ic {
    width: 36.805575rpx;
    height: 3.4722rpx;
    position: relative;
    margin-right: 20.83335rpx;
}

.qiye-shebei .item .ic i {
    position: absolute;
    top: -4.1667rpx;
    left: 13.19445rpx;
    width: 11.1111rpx;
    height: 11.1111rpx;
    border-radius: 50%;
}
.lr27-3-btns {
    display: flex;
    justify-content: space-around;
    box-shadow: 0px 2px 27px 0px rgba(0, 0, 0, 0.07);
    background: #fff;
}

.lr27-3-btns > li {
    font-size: 31.9444501rpx;
    color: #999;
    cursor: pointer;
    line-height: 106.94445rpx;
}

.lr27-3-btns > li.on {
    position: relative;
    color: #498cff;
    font-weight: bold;
}

.lr27-3-btns > li.on::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: calc(50% - 22.2222rpx);
    width: 44.4444751rpx;
    height: 6.944475rpx;
    border-radius: 3.4722rpx;
    background: #498cff;
}

.lr27-3-txt {
    display: flex;
    justify-content: space-between;
    margin: 27.77775rpx 20.8333501rpx 13.888875rpx;
}

.lr27-3-txt span {
    font-size: 24.999975rpx;
    color: #989898;
}

.lr27-3-txt span i {
    color: #498cff;
}

.lr27-3-txt select {
    font-size: 29.166675rpx;
    color: #171717;
    background: url(~@/static/warningRecord/images/lr27-12-3.png)
        right center/21.527775rpx 12.5000251rpx no-repeat;
    padding-right: 34.722225rpx;
    margin-right: 10.9722001rpx;
}

.lr27-m5 h2 {
    position: relative;
    font-size: 29.166675rpx;
    color: #333;
    font-weight: bold;
    line-height: 38.888925rpx;
    background: url(~@/static/warningRecord/images/lr27-12-1.png)
        left center/39.58335rpx 38.888925rpx no-repeat;
    padding-left: 73.611075rpx;
    margin-bottom: 21.3888751rpx;
}

.lr27-m5 h2 .ts {
    position: absolute;
    top: -6.944475rpx;
    right: -43.0555499rpx;
    width: 122.22225rpx;
    height: 70.138875rpx;
    font-size: 22.2222rpx;
    color: #fff;
    text-align: center;
    line-height: 50.83335rpx;
    background: url(~@/static/warningRecord/images/lr27-12-2.png)
        no-repeat;
    background-size: 100% 100%;
}

.lr27-m5 p {
    font-size: 26.3889rpx;
    color: #666;
    line-height: 64.7222251rpx;
}

.lr27-m5 p .v {
    font-size: 26.3889rpx;
    color: #171717;
}

.lr27-m5 p .k.r {
    margin-left: 106.94445rpx;
}


.yj-fankui{
    padding: 20.8333501rpx;
}
.yj-txt1{
    font-size: 24.999975rpx;
    color: #989898;
    line-height: 37.5rpx;
}
.yj-txt1 span{
    color: #357FFF;
}
.yj-add{
    width: 138.8889rpx;
    height: 62.499975rpx;
    background: #357FFF url(~@/static/warningRecord/images/zy_add.png) 29.166675rpx center no-repeat;
    background-size: 23.611125rpx;
    border-radius: 31.250025rpx;
    font-size: 24.999975rpx;
    color: #FFFFFF;
    line-height: 62.499975rpx;
    padding-left: 57.638925rpx;
}
.fk-details{}
.fk-details li{
    background: #fff;
    border-radius: 11.1111rpx;
    padding: 10.4166751rpx 24.999975rpx;
    margin-bottom: 20.8333501rpx;
	
}
.fk-details li h3{
    font-size: 29.166675rpx;
    font-weight: bold;
    color: #333333;
    line-height: 83.333325rpx;
    padding-left: 43.05555rpx;
    background: url(~@/static/warningRecord/images/zy_qiye.png) 0 center no-repeat;
    background-size: 28.47225rpx;
}
.fk-details li .p1{
    font-size: 29.166675rpx;
    color: #666666;
    line-height: 62rpx;
	width:80px;
	text-align: right;
}
.fk-details li .p2{
    font-size: 29.166675rpx;
    color: #171717;
    line-height: 62rpx;
	width: calc(100% - 90px);
	text-align: left;
}
.yj-fujian{
    font-size: 29.166675rpx;
    color: #333333;
    line-height: 83.333325rpx;
    padding-left: 43.05555rpx;
    background: url(~@/static/warningRecord/images/zy_fujian.png) 0 center no-repeat;
    background-size: 27.77775rpx;
}
.yj-imgs{
    display: flex;
    align-items: center;
    margin-bottom: 20.8333501rpx;
}
.yj-imgs img{
    width: 193.05555rpx;
    height: 193.05555rpx;
    margin-right: 20.8333501rpx;
}
.mask{position: fixed; left: 0; top: 0; right: 0; bottom: 0; background: rgba(0,0,0,.4); z-index: 1000; display: none;}
.yj-alert{
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    width: 586.111125rpx;
    height: 900rpx;
    background: #FFFFFF;
    border-radius: 24.30555rpx;
    z-index: 1111;
    box-sizing: border-box;
    padding:  34.722225rpx;
}
.yj-alert h3{
    font-size: 34.722225rpx;
    font-weight: bold;
    color: #1F1F39;
    line-height: 38.194425rpx;
    text-align: center;
}
.yj-alert .p1{
    font-size: 29.166675rpx;
    color: #171717;
    line-height: 43.05555rpx;
    margin-top: 45.833325rpx;
    margin-bottom: 20.8333501rpx;
}
.yj-alert .textarea1{
    width: 100%;
    height: 180.5555251rpx;
    background: #FCFCFC;
    border-radius: 5.55555rpx;
    border: 0.694425rpx solid #E3E3E3;
    font-size: 29.166675rpx;
    color: #333;
    line-height: 43.05555rpx;
    box-sizing: border-box;
    padding: 13.888875rpx 20.8333501rpx;
    resize: none;
    margin-bottom: 76.388925rpx;
    font-family:"Microsoft YaHei";
}
.yj-alert .btn1{
    width: 219.44445rpx;
    height: 79.861125rpx;
    background: #FFFFFF;
    border-radius: 19.4444251rpx;
    border: 0.694425rpx solid #547FFD;
    box-sizing: border-box;
    font-size: 25.694475rpx;
    color: #547FFD;
    line-height: 79.861125rpx;
    text-align: center;
    margin: 0 20.8333501rpx;
}
.yj-alert .btn2{
    width: 219.44445rpx;
    height: 79.861125rpx;
    background: #357FFF;
    border-radius: 19.4444251rpx;
    font-size: 25.694475rpx;
    color: #FFFFFF;
    line-height: 79.861125rpx;
    text-align: center;
    margin: 0 20.8333501rpx;
}
