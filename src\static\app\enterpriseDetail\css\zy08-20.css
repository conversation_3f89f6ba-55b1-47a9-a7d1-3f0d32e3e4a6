.page-qiye {
    background: #f5f7fd url(~@/static/app/enterpriseDetail/images/qiye-header.png) center 0 no-repeat;
    background-size: 100% auto;
    height: 100%;
}

.page-qiye .header {
    background: none;
    position: relative;
}

.ic-back {
    position: absolute;
    left: 30.1932rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 21.7391249rpx;
    height: 36.2319rpx;
    background: url(~@/static/app/enterpriseDetail/images/backic.png) 0 0 no-repeat;
    background-size: 100%;
}

.qiye-tabs1 {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20.83335rpx;
    padding: 0 24.3055499rpx;
}

.qiye-tabs1 span {
    font-size: 34.722225rpx;
    color: #d7e1ff;
    line-height: 76.388925rpx;
    position: relative;
    margin: 0 20.83335rpx;
    position: relative;
}

.qiye-tabs1 span img {
    position: absolute;
    right: -31.9444501rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 31.94445rpx;
    height: 30.555525rpx;
}

.qiye-tabs1 span.cur {
    color: #fff;
}

.qiye-tabs1 span.cur::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 5.55555rpx;
    background-color: #fff;
    border-radius: 3.4722rpx;
}

.qiye-board {
    background-color: #fff;
    margin: 0 24.3055499rpx;
    border-radius: 17.361075rpx;
    padding: 20.83335rpx;
}

.qiye-info {
    margin: 0 24.3055499rpx;
    border-radius: 17.361075rpx;
    padding: 20.83335rpx 27.77775rpx;
    background: #fff url(~@/static/app/enterpriseDetail/images/qiye-info.png) right bottom no-repeat;
    background-size: 100%;
}

.qiye-info li {
    display: flex;
}

.qiye-info li .lp {
    width: 166.66665rpx;
}

.qiye-info li .rp {
    flex: 1;
    display: flex;
}

.qiye-info li .p1 {
    font-size: 30.555525rpx;
    color: #999;
    line-height: 55.555575rpx;
    letter-spacing: 0.694425rpx;
}

.qiye-info li .p2 {
    font-size: 30.555525rpx;
    color: #333;
    line-height: 55.555575rpx;
    letter-spacing: 0.694425rpx;
}

.qiye-info li .tel {
    padding-right: 41.6667rpx;
    background: url(~@/static/app/enterpriseDetail/images/qiye-tel.png) right center no-repeat;
    background-size: 23.611125rpx;
}

.qiye-info li .local {
    padding-right: 41.6667rpx;
    background: url(~@/static/app/enterpriseDetail/images/qiye-local.png) right 13.888875rpx no-repeat;
    background-size: 27.77775rpx;
}

.qiye-data1 {
    display: flex;
    flex-wrap: wrap;
}

.qiye-data1 li {
    display: flex;
    align-items: center;
    width: 50%;
    box-sizing: border-box;
    margin: 27.77775rpx 0;
}

.qiye-data1 li .ic {
    width: 90.2778rpx;
    height: 90.2778rpx;
    margin: 0 34.722225rpx;
}

.qiye-data1 li .ic img {
    width: 90.2778rpx;
    height: 90.2778rpx;
}

.qiye-data1 li .rp {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.qiye-data1 li .p1 {
    font-size: 30.555525rpx;
    color: #666;
}

.qiye-data1 li .p2 {
    font-size: 37.5rpx;
    color: #333;
}

.zy-line {
    display: flex;
}

.zy-line.ac {
    align-items: center;
}

.zy-line.jb {
    justify-content: space-between;
}

.zy-line.jc {
    justify-content: center;
}

.qiye-til1 {
    font-size: 34.722225rpx;
    color: #333;
    line-height: 76.388925rpx;
    padding-left: 27.77775rpx;
    background: url(~@/static/app/enterpriseDetail/images/qiye-til1.png) 0 center no-repeat;
    background-size: 10.416675rpx;
}

.qiye-quanping img,
.qiye-quanping {
    width: 34.0278rpx;
    height: 34.0278rpx;
}

.qiye-tu img {
    width: 100%;
}

.qiye-tabs2 {
    display: flex;
    justify-content: flex-start;
	flex-wrap: wrap;
}

.qiye-tabs2 .item {
    width: 200rpx;
    height: 70.8333rpx;
    background-color: #ededed;
    border-radius: 6.944475rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
	margin-bottom: 12rpx;
	    padding: 4rpx;
	    margin-right: 12rpx;
}

.qiye-tabs2 .item p {
    font-size: 24.999975rpx;
    color: #333;
    line-height: 29.166675rpx;
    text-align: center;
}

.qiye-tabs2 .item .lamp {
    position: absolute;
    top: -10.4166751rpx;
    right: -10.4166751rpx;
    width: 31.94445rpx;
    height: 30.555525rpx;
}

.qiye-tabs2 .item.cur {
    background-color: #3d6bf0;
}

.qiye-tabs2 .item.cur p {
    color: #fff;
}


.qiye-riqi {
    font-size: 27.77775rpx;
    color: #4874ff;
    width: 201.388875rpx;
    text-align: center;
    line-height: 55.555575rpx;
    border-radius: 10.416675rpx;
    overflow: hidden;
    border: 0.694425rpx solid #4874ff;
}

.shebei-tuli {
    display: flex;
}

.shebei-tuli li {
    display: flex;
    align-items: center;
    margin-left: 24.3055499rpx;
}

.shebei-tuli li i {
    width: 26.3889rpx;
    height: 26.3889rpx;
    border-radius: 2.777775rpx;
    margin-right: 15.2778rpx;
}

.shebei-tuli li span {
    font-size: 27.77775rpx;
    color: #666;
}

.qiye-shebei {
    border-top: 0.694425rpx solid #eee;
}

.qiye-shebei .item {
    display: flex;
    align-items: center;
    height: 86.1110999rpx;
}

.qiye-shebei .item .ic {
    width: 36.805575rpx;
    height: 3.4722rpx;
    position: relative;
    margin-right: 20.83335rpx;
}

.qiye-shebei .item .ic i {
    position: absolute;
    top: -4.1667rpx;
    left: 13.19445rpx;
    width: 11.1111rpx;
    height: 11.1111rpx;
    border-radius: 50%;
}

.qiye-shebei .item .p1 {
    font-size: 30.555525rpx;
    color: #333;
}

.qiye-shebei .item .qiye-radio {
    margin-left: auto;
}

.qiye-radio {
    display: flex;
    align-items: center;
}

.qiye-radio input {
    display: none;
}

.qiye-radio i {
    width: 31.94445rpx;
    height: 31.94445rpx;
    background: url(~@/static/app/enterpriseDetail/images/qiye-radio-no.png);
    background-size: 100% 100%;
}

.qiye-radio input:checked~i {
    background-image: url(~@/static/app/enterpriseDetail/images/qiye-radio-yes.png);
}

.qiye-tabs4 {
    display: flex;
    border-radius: 10.416675rpx;
    overflow: hidden;
    border: 0.694425rpx solid #4874ff;
}

.qiye-tabs4 span {
    font-size: 31.94445rpx;
    color: #4874ff;
    width: 295.138875rpx;
    text-align: center;
    line-height: 76.388925rpx;
}

.qiye-tabs4 span.cur {
    color: #fff;
    background-color: #4874ff;
}

.qiye-ultbs1 {
    position: absolute;
    top: 116.6667rpx;
    left: 0;
    right: 0;
    background: #fff;
    display: flex;
    justify-content: space-evenly;
    height: 84.7222499rpx;
}

.qiye-ultbs1 li {
    font-size: 31.94445rpx;
    color: #666;
    line-height: 84.7222499rpx;
}

.qiye-ultbs1 li.on {
    color: #4874ff;
    background: url(~@/static/app/enterpriseDetail/images/botbar.png) no-repeat center bottom;
    background-size: 84.54105rpx;
}

.qiye-ultbs1 li sub {
    font-size: 22.94685rpx;
}

.qiye-inner {
    padding-top: 201.388875rpx;
}

.qiye-data2 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 33.3333rpx;
}

.qiye-data2 .p1 {
    font-size: 29.166675rpx;
    color: #333;
    line-height: 97.2221999rpx;
}

.qiye-data2 .p1 span {
    color: #de7518;
}

.qiye-add {
    font-size: 31.94445rpx;
    color: #4874ff;
    padding-left: 47.22225rpx;
    background: url(~@/static/app/enterpriseDetail/images/qiye-add.png) 0 center no-repeat;
    background-size: 33.3333rpx;
}

.qiye-data3 {
    margin: 0 24.3055499rpx;
    background-color: #fff;
    border-radius: 17.361075rpx;
    padding: 0 27.77775rpx;
}

.qiye-data3 .hd {
    height: 102.77775rpx;
    border-bottom: 0.694425rpx solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.qiye-data3 .til {
    padding-left: 48.6110999rpx;
    font-size: 34.722225rpx;
    color: #333;
    line-height: 102.77775rpx;
    background: url(~@/static/app/enterpriseDetail/images/qiye-data3-til.png) 0 center no-repeat;
    background-size: 36.805575rpx;
}

.qiye-data3 .p1 {
    font-size: 29.166675rpx;
    color: #999;
}

.qiye-data3 .p2 {
    font-size: 29.166675rpx;
    color: #666;
    line-height: 69.44445rpx;
}

.qiye-data3 .p3 {
    font-size: 29.166675rpx;
    color: #333;
}

.qiye-data3 .item1 {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.qiye-data3 .item2 {}

.qiye-data3 .imgs {
    display: flex;
    align-items: center;
}

.qiye-data3 .imgs img {
    width: 166.66665rpx;
    height: 166.66665rpx;
    margin-right: 20.83335rpx;
}

.qiye-alert1 {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 30.1932rpx 30.1932rpx 0 0;
    background: #fff;
    z-index: 1000;
    padding: 0 34.722225rpx;
    padding-top: 20.83335rpx;
}

.qiye-alert-til {
    font-size: 37.5rpx;
    color: #000033;
    line-height: 83.333325rpx;
}

.qiye-close {
    width: 34.0278rpx;
    height: 34.0278rpx;
    background: url(~@/static/app/enterpriseDetail/images/qiye-close.png);
    background-size: 100%;
}

.qiye-textarea1 {
    width: 100%;
    height: 166.66665rpx;
    box-sizing: border-box;
    padding: 20.83335rpx;
    font-size: 31.94445rpx;
    color: #333;
    line-height: 55.555575rpx;
    background-color: #efefef;
    resize: none;
    border-radius: 6.944475rpx;
    font-family: "Microsoft YaHei";
}

.qiye-fujian-add {
    display: flex;
}

.qiye-fujian-add img {
    width: 166.66665rpx;
    height: 166.66665rpx;
    margin-right: 20.83335rpx;
}

.qiye-bot-btns {
    display: flex;
    margin: 0 -34.722225rpx;
}

.qiye-bot-btns .btn1 {
    flex: 1;
    font-size: 29.166675rpx;
    color: #333;
    background-color: #ebebeb;
    height: 90.2778rpx;
    text-align: center;
    line-height: 90.2778rpx;
}

.qiye-bot-btns .btn2 {
    flex: 1;
    font-size: 29.166675rpx;
    color: #fff;
    background-color: #4874ff;
    height: 90.2778rpx;
    text-align: center;
    line-height: 90.2778rpx;
}

.tabs1-con {
    height: calc(100% - 213.8889rpx);
    overflow: auto;
}