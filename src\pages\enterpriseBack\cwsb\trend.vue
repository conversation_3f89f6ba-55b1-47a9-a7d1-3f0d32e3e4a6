<!-- @format -->

<template>
    <div></div>
</template>

<script>
import * as echarts from 'echarts';
import { getZxsj } from '@/api/iot/enterprise.js';
export default {
    props: {
        info: {
            type: Object,
            default: () => {
                return {};
            }
        },
        sbInfo: {
            type: Object,
            default: () => {
                return {};
            }
        }
    },
    data() {
        return {
            pageSize: '',
            enterpriseInfo: {},
            list: [],
            data: {
                pollutantData: [] //24小时污染物浓度数据
            }
        };
    },
    mounted() {
        this.enterpriseInfo = uni.getStorageSync('userInfo');
        this.getZxsj();
    },
    methods: {
        pollutantChange(val) {
            // 获取不同污染物的24小时污染浓度
            this.pollutantData = [];
            // Math.floor(Math.random() * (max - min)) + min

            let j = 0;
            for (let i = 0; i < 24; i++) {
                let mData = '2020-12-22 ';

                this.pollutantData.push({
                    aqiLevelMark: '', //AQI等级标识
                    pm10Iaqi: '', //pm10aqi
                    color: '', //颜色
                    o3: Math.floor(Math.random() * (100 - 0)), //o3浓度
                    level: '', //监管级别
                    latitude: '', //纬度
                    longitude: '', //经度
                    pm10: Math.floor(Math.random() * (200 - 0)), //pm10浓度
                    co: Math.floor(Math.random() * (100 - 0)), //co浓度
                    o3Iaqi: '', //o3aqi
                    no2: Math.floor(Math.random() * (200 - 0)), //no2浓度
                    coIaqi: '', //coAQI
                    areaCode: '330600', //行政区代码
                    areaName: '绍兴市', //行政区名称
                    pm25: Math.floor(Math.random() * (300 - 0)), //pm25浓度
                    aqiLevel: '', //AQI等级
                    monitorDate: mData + String(++j).padStart(2, '0'), //监测时间
                    so2: Math.floor(Math.random() * (200 - 0)), //so2浓度
                    aqi: Math.floor(Math.random() * (300 - 0)), //aiq
                    pm25Iaqi: '', //pm25aqi
                    no2Iaqi: '', //no2aqi
                    maxItem: '', //首要污染物
                    so2Iaqi: '' //so2aqi
                });
            }
        },
        getZxsj() {
            let { IMEI } = this.sbInfo;
            getZxsj({ IMEI: IMEI, pageSize: this.pageSize }).then((res) => {
                if (res.data_array && res.data_array.length > 0) {
                    this.list = res.data_array;
                }
            });
        }
    }
};
</script>

<style scoped>
.pd-dlbx1 {
    background: #fff;
    border-radius: 9px;
    margin: 0;
    padding: 9px;
}
.pd-ulbx1 {
    margin: 0;
}
</style>
