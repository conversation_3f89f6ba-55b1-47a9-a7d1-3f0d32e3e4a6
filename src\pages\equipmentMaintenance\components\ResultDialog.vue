<!-- @format -->

<!-- 添加运维结果弹窗 -->
<template>
    <div class="">
        <div class="mask" style="display: block"></div>
        <div class="yy0706-alert1">
            <div class="hd">
                <h1 class="tit1">添加运维结果</h1>
                <image
                    src="~@/static/equipmentMaintenance/images/yy0706-cls.png"
                    alt=""
                    class="yy0706-cls"
                    @click="$emit('close')"
                />
            </div>
            <div class="bd">
                <div class="flx1 ac jb yy0706-srow">
                    <div class="f1"><i class="redx">*</i>运维里程:</div>
                    <div class="flx1 ac jb rfont">
                        <input
                            type="text"
                            placeholder="请输入"
                            class="pd-inptxt1"
                            v-model="info.ywlc"
                            maxlength="4"
                        /><span class="dw">公里</span>
                    </div>
                </div>
                <div class="flx1 ac jb yy0706-srow">
                    <div class="f1"><i class="redx">*</i>总耗时:</div>
                    <div class="flx1 ac jb rfont">
                        <input
                            type="text"
                            placeholder="请输入"
                            class="pd-inptxt1"
                            v-model="info.ywhs"
                            maxlength="4"
                        /><span class="dw">小时</span>
                    </div>
                </div>
                <div class="flx1 ac jb yy0706-srow">
                    <div class="f1"><i class="redx">*</i>备注说明:</div>
                </div>
                <textarea
                    class="form-textarea"
                    name=""
                    id=""
                    v-model="info.bz"
                    placeholder="请输入备注说明"
                ></textarea>
                <UploadImageListOrVideo
                    ref="refUploadImageListOrVideo"
                    fileTypeKeyword="YWJL"
                    childTypeKeyword="XCZP"
                    :uploadId="options.ywid"
                    :isDisabled="isDisabled"
                    :options="options"
                    title="现场拍照"
                >
                </UploadImageListOrVideo>
                <!-- <p class="yy0706-photof">现场拍照/录像</p>
                <image
                    src="~@/static/equipmentMaintenance/images/yy0706-photo.png"
                    alt=""
                    class="yy0706-uptu"
                /> -->
                <p class="yy0706-confirmbtn" @click="submitData">确定</p>
            </div>
        </div>
    </div>
</template>

<script>
import { getValid } from '@/utils/validData.js';
import { commitFormdata, saveFormdata } from '@/api/iot/equipmentMaintenance';
import UploadImageListOrVideo from './UploadImageListOrVideo';

export default {
    name: 'ResultDialog',
    props: {
        formData: {
            type: Object,
            default: () => {}
        },
        options: {
            type: Object,
            default: () => {}
        },
        isDisabled: {
            type: Boolean,
            default: true
        }
    },
    components: {
        UploadImageListOrVideo
    },
    data() {
        return {
            info: {
                ywlc: '',
                ywhs: '',
                bz: ''
            },
            rules: {
                ywlc: {
                    required: true,
                    message: '请输入运维里程'
                },
                ywhs: {
                    required: true,
                    message: '请输入总耗时'
                },
                bz: {
                    required: true,
                    message: '请输入备注说明'
                }
            }
        };
    },
    computed: {},
    watch: {},
    created() {},
    mounted() {},
    methods: {
        async submitData() {
            await getValid(this.rules, this.info);
            try {
                this.info.ywid = this.options.ywid;
                await saveFormdata(this.formData);
                await commitFormdata(this.info);
                this.$emit('close');
                let pages = getCurrentPages(); // 当前页面
                let beforePage = pages[pages.length - 2]; // 上一页
                if (beforePage.$vm) {
                    beforePage.$vm.freshData && beforePage.$vm.freshData('1');
                    beforePage.$vm.showState = '1';
                } else {
                    beforePage.freshData && beforePage.freshData('1');
                    beforePage.showState = '1';
                }
                uni.navigateBack({
                    delta: 1
                });
            } catch (error) {
                console.log('error', error);
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.mask {
    z-index: 1;
}
.yy0706-alert1 {
    z-index: 2;
}

.form-textarea {
    margin: 30rpx 10rpx 0 10rpx;
    text-align: left;
    width: 100%;
    border: 2rpx solid #e5e5e5;
    border-radius: 8rpx;
    padding: 8rpx;
    height: 140rpx;
    ::v-deep .uni-textarea-textarea {
        text-align: left;
    }
}
</style>
