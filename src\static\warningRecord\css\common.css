@charset "utf-8";

/*common*/
.gap{height: 18.1159501rpx;}
.mask{position: fixed; left: 0; top: 0; right: 0; bottom: 0; background: rgba(0,0,0,.4); z-index: 1000; display: none;}

/*page*/
.header{background: #4c6eff; position: absolute; left: 0; right: 0; top: 0; z-index: 999; height: 79.71015rpx;}
.title{text-align: center; font-size: 32.608725rpx; color: #fff; line-height: 79.71015rpx;}

.main{overflow-y: auto; overflow-x: hidden; -webkit-overflow-scrolling: touch; height: 100%;}
.inner{padding: 79.71015rpx 0 90.5796751rpx;}

.footer{position: absolute; left: 0; right: 0; bottom: 0; z-index: 999;}
