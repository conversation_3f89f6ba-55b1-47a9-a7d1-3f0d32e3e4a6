<template>
	<div>
		<div class="zy-yj-cell1">
			<div class="cell1-bd">
				<div class="gap"></div>
				<div>
					<div class="zy-yj-block1">
						<p class="zy-yj-til1">近24小时振动监控状态数据</p>
						<div class="zy-yj-tips">
							<span>开启</span>
							<span>关闭</span>
							<span>离线</span>
						</div>
					</div>
					<div class="gap"></div>
					<div class="zy-yj-tu" style="padding-left: 8px;">
						<div v-show="pollut_List.length == 0" style="color:#999;text-align: center;">暂无数据</div>
						<!-- #ifdef APP-PLUS || H5 -->
						<view style="width: 100%;height:100rpx;overflow:hidden;" :prop="pollutOption"
							:change:prop="echarts.updatePollutEcharts" id="pollutEcharts" class="echarts"></view>
						<!-- #endif -->
						<!-- #ifndef APP-PLUS || H5 -->
						<view>非 APP、H5 环境不支持</view>
						<!-- #endif -->
						<div class="maskbox" ref='echartsMaskBox' :style='{bottom:bottomRef}'></div>
					</div>
					</view>
				</div>
				<div class="gap"></div>
			</div>
		</div>
		<div class="zy-yj-cell1">
			<div class="cell1-hd">
				<p class="zy-yj-til1">安装点位</p>
			</div>
			<div>
				<div class="cell1-bd" v-for="(item,index) in picList" :key="index">
					<!-- <p class="zy-yj-txt2" @click="toEquitDetail(item.SBID)">{{item.SBMC}}</p> -->
					<p class="zy-yj-txt2">{{item.SBMC}}</p>
					<ul class="zy-yj-list1" v-if="item.WJIDS.length != 0">
						<li v-for="(item2,index) in item.WJIDS">
							<image mode="scaleToFill" style="width:100%;height:120px;" :src="getFileUrl(item2)" alt=""
								@click="previewImage(item.WJIDS,index)" />
						</li>
					</ul>
					<div v-if="item.WJIDS.length == 0" style="color:#999;text-align: center;">暂无安装照片</div>
					<div class="gap"></div>
				</div>
			</div>
			<div class="gap"></div>
			<div class="gap"></div>

		</div>
	</div>

</template>

<script>
import { debounce } from 'lodash';
	import {
		DOWNLOAD_URLZDY
	} from '@/common/config.js';
	import {
		monitorCxztByID,
		monitorPicture
	} from '@/api/iot/warning.js'
	export default {
		components: {

		},
		props: {
			warning: {
				type: Object,
				default: function() {}
			}
		},
		watch: {
			warning: {
				handler(newVal, oldVal) {
					this.info = newVal;
					if (this.info) {
						//ZW-治污，TC-停产，GZ-故障
						if (this.info.YJLX == 'ZW' || this.info.YJLX == 'TC') {
							this.debouncedPollutionWarning();
						} else if (this.info.YJLX == 'GZ') {
							// this.getFaultWarning();
						}
						this.debouncedMonitorPicture()
					}

				},
				immediate: true,
				deep: true

			}

		},
        created() {
            this.debouncedPollutionWarning =debounce(this.getPollutionWarning,1500)
            this.debouncedMonitorPicture =debounce(this.getMonitorPicture,1500)
        },
		data() {
			return {
				bottomRef: '0px',
				obj: {},
				option: {
					series: [{
						type: 'bar',
						data: [11, 12, 15, 16, 13, 12, 14]
					}],
					xAxis: {
						data: ['a', 'b', 'c', 'd', 'e', 'f', 'g']
					},
					yAxis: {},
					tooltip: {}
				},
				info: {
					YJSJ: '', //日期
					SCXID: '', //生产线id
					YJBH: '' //预警编号
				},
				pulltionData: [],
				faultData: [],
				picList: [],

				pollutOption: {},
				product_List: [],
				pollut_List: [],
				picTabArr: [{
						name: '状态图',
						value: 'state'
					},
					{
						name: '趋势图',
						value: 'trend'
					},
				],
				curPic: 'state',
				arrSendData: [], //根据预警事件id请求到的数据
                debouncedPollutionWarning:null,
                debouncedMonitorPicture:null

			};
		},
		mounted() {
			// uni.$on('changeTab', () => {
			// 	setTimeout(()=>{
			// 		this.$nextTick(() => {
			// 			this.pollutOption = {}
			// 			this.getPollutionWarning()
			// 		});
			// 	},0)

			// });

		},
		methods: {
			//近24小时振动监控数据：（治污预警和停产预警）
			getPollutionWarning() {
				if (!this.warning) {
					return;
				}
				let obj = {
					YJID: this.warning.ID
				};
				monitorCxztByID(obj).then(res => {
					if (res.data) {
						this.arrSendData = res.data;
					}
					if(res.status === '500'){
						return
					}
					res.data && res.data.zwList.forEach(x => {
						x.name = x.SBMC;
						// x.ztList = JSON.parse(x.ztList);
						if (x.ztList && x.ztList.length > 0) {
							x.list = x.ztList;
							x.list.forEach(y => {
								y.start = this
									.$dayjs(y.start)
									.format('YYYY/MM/DD HH:mm:ss');
								y.end = this
									.$dayjs(y.end)
									.format('YYYY/MM/DD HH:mm');
							});
						}
					});
					res.data && res.data.scList.forEach(x => {
						x.name = x.SBMC;
						if (x.ztList && x.ztList.length > 0) {
							x.list = x.ztList;
							x.list.forEach(y => {
								y.start = this
									.$dayjs(y.start)
									.format('YYYY/MM/DD HH:mm:ss');
								y.end = this
									.$dayjs(y.end)
									.format('YYYY/MM/DD HH:mm');
							});
						}
					});

					this.pollut_List = res.data && res.data.zwList;
					this.product_List = res.data && res.data.scList;

					let warnStart = this.$dayjs(res.data.startT).format("YYYY/MM/DD HH:mm");
					let warnEnd = this.$dayjs(res.data.endT).format("YYYY/MM/DD HH:mm");
					//获取ztList里的endTime
					let ztListEndTime =res.data.zwList[0].ztList[res.data.zwList[0].ztList.length - 1].end
					//获取ztList里的startTime
					let ztListStartTime = res.data.zwList[0].ztList[0].start

					//x轴的开始时间startTime是 预警开始时间startT  和ztlist 第一个开始时间 start 对比的较小值
					//x轴的结束时间endTime是 预警结束时间endT  和ztlist 最后一个结束时间 end 对比的较大值
					let diffEndTime =  this.$dayjs(res.data.endT).diff(this.$dayjs(ztListEndTime), 'minutes')
					let diffstartTime =this.$dayjs(res.data.startT).diff(this.$dayjs(ztListStartTime), 'minutes')
					let startTime = diffstartTime > 0 ? ztListStartTime : res.data.startT.format("YYYY/MM/DD HH:mm");
					let endTime = diffEndTime > 0 ? this.$dayjs(res.data.endT).format("YYYY/MM/DD HH:mm") : ztListEndTime;
					//console.log('startTime', startTime)
					//console.log('endTime', endTime)
					//计算治污设备遮罩层位置
					this.bottomRef = this.pollut_List.length * 24 + (this.pollut_List.length - 1) * 6 + 26 + 'px'
					//如果治污设备数据不为空
					if (this.pollut_List.length != 0) {
						let obj = {
							name: '治污设备',
							SBMC: '',
							SBXH: '',
							ztList: [{
								start: this.$dayjs(startTime).format("YYYY/MM/DD 00:00:00"),
								end: this.$dayjs(startTime).format("YYYY/MM/DD 00:00:00")
							}]
						}
						let nullObj = {
							name: '',
							SBMC: '',
							SBXH: '',
							ztList: [{
								start: this.$dayjs(startTime).format("YYYY/MM/DD 00:00:00"),
								end: this.$dayjs(startTime).format("YYYY/MM/DD 00:00:00")
							}]
						}
						this.pollut_List.push(nullObj, obj, nullObj)
					}
					this.pollut_List = this.pollut_List?.concat(this.product_List)

					this.pollutOption = {};
					this.$nextTick(() => {
						this.pollutOption = this.setChart(this.pollut_List, startTime, endTime, warnStart,
							warnEnd);
					})
				})
			},
			setChart(data, startTime, endTime, warnStart, warnEnd) {
				let statusObj = {
					1: {
						// color: '#ff7054',
						color: 'rgba(255,100,94,.7)',
						label: '停止'
					},

					2: {
						// color: '#7dcd27',
						color: 'rgba(117, 189, 35,.7)',
						label: '开启'
					},

					0: {
						color: 'rgba(190,190, 190,.8)',
						label: '离线'
					},
				};
				let x = [];
				let minutes = 0;
				let kssj = '';
				let jssj = '';

				//获取开始时间的 0点0分
				let zeroTime = this.$dayjs(startTime).format("YYYY-MM-DD 00:00");
				//计算开始时间到0点0分经过了几分钟，获得x轴开始的分钟
				let diffEndTimeZeroTime = this.$dayjs(endTime).diff(this.$dayjs(zeroTime), 'minutes')
				//计算结束时间到0点0分经过了几分钟，获得x轴结束的分钟
				let diffStartTimeZeroTime = this.$dayjs(startTime).diff(zeroTime, 'minutes')

			     //获取月 获取日期
				let curMonth = this.$dayjs(startTime).format('MM')
				let curDate = this.$dayjs(startTime).format('DD')
				//计算x轴坐标
				for (let i = diffStartTimeZeroTime; i <= diffEndTimeZeroTime; i++) {

					let num = i;

					let h = Math.floor(num / 60);
					let m = curMonth
					let d = (h >= 24 ? this.$dayjs(startTime).add(parseInt(h / 24), 'day').format('DD') : curDate)
					h = (h >= 24 ? h - (parseInt(h / 24)) * 24 : h);
					let min = num % 60;

					h = (h <= 9 ? '0' : '') + h;

					min = (min <= 9 ? '0' : '') + min;

					x.push(m + '/' + d + ' ' + h + ':' + min);

				}
				let YJStartTime = warnStart.slice(5, 16)
				let YJEndTime = warnEnd.slice(5, 16)
				let YJStartTimeIndex = x.findIndex(i => i == YJStartTime)
				let YJEndTimeIndex = x.findIndex(i => i == YJEndTime)

				this.$nextTick(() => {
					uni.$emit('sendData', {
						x,
						startTime,
						endTime,
						arrSendData: this.arrSendData,
						warnStart,
						warnEnd
					});
				})

				let y = [];
				let lines = [];

				data.forEach((v, i) => {
					y.push(v.name || '');
					//背景状态（如果数据中有可以去掉）
					lines.push({
						z: -1,
						type: 'lines',
						coordinateSystem: 'cartesian2d',
						silent: true,
						lineStyle: {
							width: v.name == '治污设备' || v.name == '' ? 20 : 24,
							color: '#ddd',
							opacity: v.name == '治污设备' || v.name == '' ? 0 : 10
						},
						data: [{
							coords: [
								[startTime.slice(5,16), v.name || ''],
								[endTime.slice(5,16), v.name || '']
							]
						}],

					});

					v.list && v.list.forEach((item, j) => {
						// console.log('item',item)
						let xVal1 = item.start.slice(5, 16);
						// console.log('xVal1',xVal1)
						let xVal2 = item.end.slice(5, 16);
						let obj = statusObj[v.list[j].status];
						lines.push({
							name: obj.label,
							type: 'lines',
							coordinateSystem: 'cartesian2d',
							lineStyle: {
								width: v.name == '治污设备' || v.name == '' ? 20 : 24,
								color: obj.color || '#f0f0f0',
								opacity: 1
							},
							data: [{
								name: v.name || '',

								start: item.start,

								end: item.end,

								statusLabel: obj.label,

								coords: [
									[xVal1, v.name || ''],
									[xVal2, v.name || '']
								]
							}]
						});
					});
				});
				let titleText = '   生产设备'

				lines.push({
					z: -1,
					type: 'bar',
					coordinateSystem: 'cartesian2d',
					silent: true,
					markArea: {
						itemStyle: {
							color: 'rgba(255, 173, 177, .4)'
						},
						data: [
							[{
									name: `预警时间段\n  ${warnStart.slice(5,16)}~\n${warnEnd.slice(5,16
									)}`,
									xAxis: YJStartTimeIndex,
								},
								{
									xAxis: YJEndTimeIndex
								}
							]
						]
					}
				})
				let insertStr = function(source, start, newStr) {
					//source 要插入的字符串
					//start 开始位置
					// newStr 插入的字符串
					return source.slice(0, start) + newStr + source.slice(start)
				}
				let option = {
					tooltip: {
						show: true,
						backgroundColor: "rgba(255,255,255,0)",
						padding: [0, 0],
						axisPointer: {
							show: false,
							type: 'cross',
							crossStyle: {
								color: '#4874ff'
							},
							label: {
								color: '#fff',
								backgroundColor: '#4874ff',
								// formatter: function (params) {
								//  console.log('params',params)
								//  return params.value
								//           //return echarts.format.formatTime('yyyy-MM-dd', params.value);
								//  },
							}
						},
					},

					title: {
						zlevel: 999,
						text: titleText,
						textStyle: {
							color: '#444',
							fontSize: 16,
							fontWeight: 'bold'
						},
					},
					grid: {
						top: 50,
						left: 16,
						bottom: 40,
						right: 20
					},
					xAxis: {
						type: 'category',
						data: x,
						interval: 0,
						axisLabel: {
							show: true,
							textStyle: {
								color: '#666'
							},
							formatter: function(value) {
								// console.log('value', value)
								value = ' ' + insertStr(value, 6, '\n')
								return value
							},
						},
						axisLine: {
							show: true,
							lineStyle: {
								color: '#999'
							}
						}
					},
					yAxis: {
						type: 'category',
						data: y,
						axisLabel: {
							interval: 0,
							show: true,
							formatter: function(value) {
								if (value == '治污设备') {
									return `{b|${value}}`
								} else {
									return `{a|${value}}`
								}
								return value
							},
							inside: true,
							padding: [2, 0, 0, 0],
							rich: {
								a: {
									color: '#fff',
									lineHeight: 30,
									fontFamily: 'digital',
									fontSize: 14,

								},
								b: {
									color: '#444',
									lineHeight: 30,
									fontFamily: 'digital',
									fontSize: 16,
									fontWeight: 'bold',

								},
							},
						},
						axisTick: {
							show: false
						},
						axisLine: {
							show: false,
							lineStyle: {
								color: '#f0f0f0'
							}
						},
						z: 3
					},
					series: lines
				};
				// console.log(JSON.stringify(option))
				return option;
			},

			//获取日期相差的分钟数
			getMintues(startT, endT) {
				const calcSecond =
					this.$dayjs(endT).diff(this.$dayjs(startT), 'minutes')
				return calcSecond
			},


			getFileUrl(item) {
				return DOWNLOAD_URLZDY + item
			},
			//预览
			previewImage(fileList, index) {
				let fileUrls = fileList.map(file => {
					return this.getFileUrl(file)
				})
				// 预览图片
				uni.previewImage({
					current: index,
					urls: fileUrls
				});
			},
			//查询设备图片接口
			getMonitorPicture() {
				let obj = {};
				// if(this.info.YJLX == "TC"){
				// 	obj = {
				// 		SBID:`${this.info.SCSBID}`
				// 	}
				// 	console.log('obj',obj);
				// }else{
				// 	 obj = {
				// 		SBID:`${this.info.SCSBID},${this.info.ZWSBID}`
				// 	}
				// }

				obj = {
					SBID: `${this.warning.SCSBID},${this.warning.ZWSBID}`
				};

				monitorPicture(obj).then(res => {
					if (res.status == '000') {
						//	console.log('res.data.length',res.data.length);
						//console.log('res.data',res.data);
						if (res.data && res.data.length && res.data.length > 0) {
							this.picList = res.data;
							this.picList.forEach(item => {
								if (item.WJIDS) {
									item.WJIDS = item.WJIDS.split(',')
								} else {
									item.WJIDS = [];
								}
							})
							console.log('res', this.picList);
						}

					}

				})
			},
			//跳转安装设备详情
			toEquitDetail(v) {
				uni.navigateTo({
					url: `/pages/enterprise/cwsb/cwEquipmentDetail?SBID=${v}`
				});
			},
			//跳转
			toNumberRecord(v) {
				uni.navigateTo({
					url: `/views/warningRecord/NumberingRecord?YJBH=${this.info.YJBH}`
				});
			},
			back() {
				uni.navigateBack({
					delta: 1
				});
			},
		}
	};
</script>



<script module="echarts" lang="renderjs">
	let myChart
	let pollutChart
	export default {
		mounted() {
			if (typeof window.echarts === 'function') {
				this.initEcharts()
			} else {
				// 动态引入较大类库避免影响页面展示
				const script = document.createElement('script')
				// view 层的页面运行在 www 根目录，其相对路径相对于 www 计算
				script.src = 'static/echarts4.9.0.js'
				script.onload = this.initEcharts.bind(this)
				document.head.appendChild(script)
			}

		},
		methods: {
			initEcharts() {
				this.$nextTick(function() {
					var pollutdiv = document.getElementById("pollutEcharts");
					pollutdiv.setAttribute("style", "display:block;height:500px,width:100%;");
					pollutChart = echarts.init(document.getElementById('pollutEcharts'))
					//观测更新的数据在 view 层可以直接访问到
					pollutChart && pollutChart.setOption(this.pollutOption)
					// var div = document.getElementById("echarts2");
					////  div.setAttribute("style","display:block;height:500px,width:100%;");
					//myChart = echarts.init(document.getElementById('echarts2'))
					// 观测更新的数据在 view 层可以直接访问到
					//	myChart && myChart.setOption(this.productOption)
				})

			},

			updatePollutEcharts(newValue, oldValue, ownerInstance, instance) {
				if (!newValue) {
					return;
				}
				let pollutdiv = document.getElementById('pollutEcharts');
				pollutdiv.style.height = newValue.yAxis && newValue.yAxis.data.length * 30 + 100 + 'px';
				// 监听 service 层数据变更
				newValue.tooltip.formatter = function(obj) {
					let data = obj && obj.data;
					return `<div style="background:${data.statusLabel== '停止'?'#ff7054':(data.statusLabel== '开启'?'#7dcd27':'#888')};color:#fff;padding:6px;opacity:0.9;border-radius:6px;border:1px solid #ddd;font-size:12px;">          ${data.name}</br>
						   开始时间：${data.start.slice(0,16)} </br>
						   结束时间：${data.end} </br>
						   状态：${data.statusLabel} </br>
						 </div>`;
				};

				newValue.tooltip.confine = true;
				newValue.tooltip.position = function(point, params, dom, rect, size) {
					pollutdiv.style.transform = 'translateZ(0)' //处理ios,不显示tooltip
				}
				let insertStr = function(source, start, newStr) {
					//source 要插入的字符串
					//start 开始位置
					// newStr 插入的字符串
					return source.slice(0, start) + newStr + source.slice(start)
				}
				newValue.xAxis.axisLabel.formatter = function(value) {
						value = '  ' + insertStr(value, 6, ' \n')
						return value
					}
					newValue.yAxis.axisLabel.formatter = function(value) {
						if (value == '治污设备') {
							return `{b|${value}}`
						} else {
							return `{a|${value}}`
						}
					}

				pollutChart && pollutChart.clear()
				pollutChart && pollutChart.setOption(newValue)
				pollutChart && pollutChart.resize()
			},
			pollut(event, ownerInstance) {
				// 调用 service 层的方法
				ownerInstance.callMethod('onViewClick', {
					test: 'test'
				})
			}
		}
	}
</script>


<style scoped>
	.zy-yj-tu {
		position: relative;
	}

	.maskbox {
		position: absolute;
		left: 0;
		bottom: 60px;
		background: rgba(255, 0, 0, .0);
		width: 100%;
		height: 88px;
	}
</style>
