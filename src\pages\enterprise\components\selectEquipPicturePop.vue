<template>
	<div class="pd-botdlg">
		<i class="dlgcls" @click="close"></i>
		<div class="gap"></div>
		<div class="gap"></div>
		<div class="pd-con1">
			<div class="pd-tit1">配置图例</div>
			<scroll-view class="pd-ulpic select-picbox" scroll-y="true" @scrolltolower="lower">
				<div class="item" v-for="(item, index) in arrMaterialLibrary" :key="index">
					<div class="tu">
						<image class="pic" mode="aspectFit" :src="getFileUrl(item)" alt="" />
						<i class="select" :class="{'isSelected':item.isSelected}" @click="selectPic(item)"></i>
					</div>
					<p class="pic-name">{{ item.TLMC }}</p>
				</div>
				<div class="no-more" v-show="noMore">----------------  没有更多啦！ ----------------</div>
				<div class="no-data" v-show="!arrMaterialLibrary.length">暂无数据！</div>
			</scroll-view>
			<div class="gap"></div>
			<div class="submit-btn">
				<button class="xj20-btn1" @click="save">
					确定
				</button>
				<button class="xj20-btn1 gray" @click="close">
					取消
				</button>
			</div>
			<div class="gap"></div>
			<div class="gap"></div>
		</div>
	</div>
</template>


<script>
	import {
		getMaterialLegendList,
		saveEquipLegnend
	} from '@/api/iot/planeFigure.js';
	import {
		DOWNLOAD_URLZDY,
		UPLOAD_URL,
		LOGIN_ULR_BASE,
	} from '@/common/config.js';
	import EquipStateList from '@/pages/component/EquipStateList';
	export default {
		data() {
			return {
				addEquipPictureWJID: '', //当前选中的图片id
				initArrMaterialLibrary: [], //全部图片
				arrMaterialLibrary: [], //查询到的图片
				curPicXH: '',
				curWJID: '',
				curSRC: '',
				noMore: false, //没有更多了
				pageSize: 12, //每页数量
				totalPages: 0, //总页数
				currentPage: 1, //当前页
			};
		},
		props: {
			SBID: {
				type: String,
				default: ''
			},
			isShowAddEquipPicture: {
				type: Boolean,
				default: false
			},
			showMask: {
				type: Boolean,
				default: false
			}
		},
		onLoad(option) {

		},
		onHide() {
			// #ifdef APP-PLUS
			// 取消监听键盘高度
			uni.offKeyboardHeightChange((res) => {});
			// #endif
		},
		created() {},
		mounted() {
			this.getLibrary()
		},
		methods: {
			//过滤返回的素材数据
			filterMaterial(arr) {
				let arrFilter = arr.filter((e) => {
					if (
						e.WJID != '1680063472830022167552' &&
						e.WJID != '1680064132370086142976' &&
						e.WJID != '1680492539731022687744' &&
						e.WJID != '1680492551145023375872' &&
						e.WJID != '1680492529811024449024' &&
						e.WJID != '1680492561564020582400' &&
						e.WJID != '1680492420515021897216' &&
						e.WJID != '1680492436472019824640' &&
						e.WJID != '1680492518042019521536' &&
						e.WJID != '1680492571035020930560' &&
						e.WJID != '1680492377317019968000' &&
						e.WJID != '1680492356696019968000' &&
						e.WJID != '1680492387586023859200' &&
						e.WJID != '1680492367712019521536' &&
						e.WJID != '1680063769834019173376' &&
						e.WJID != '1680064157904006135808' &&
						e.WJID != '1680492398367019521536' &&
						e.WJID != '1680492408875023117824' &&
						e.WJID != '1680064144628097304576'
					) {
						return e;
					}
				});
				return arrFilter;
			},
			//请求素材库响应查询素材库并更新素材的画板
			async getLibrary(searchKey = '') {
				let params = {};
				if (searchKey) {
					params = {
						tlmc: searchKey
					};
				}
				const res = await getMaterialLegendList(params);
				this.initArrMaterialLibrary = this.filterMaterial(res) || [];
				this.initArrMaterialLibrary.forEach((e) => {
					if (e.WJID == this.addEquipPictureWJID) {
						this.$set(e, 'isSelected', true)
					} else {
						this.$set(e, 'isSelected', false)
					}
				});
				this.arrMaterialLibrary = [...this.initArrMaterialLibrary]
				// 计算总页数
				this.totalPages = Math.ceil(this.initArrMaterialLibrary.length / this.pageSize);
				this.arrMaterialLibrary = this.arrMaterialLibrary.slice(0, this.pageSize)
			},
			//滚动到底部
			lower() {
				if(!this.arrMaterialLibrary.length){
					return
				}
				if (this.currentPage < this.totalPages) {
					this.currentPage++;
					this.arrMaterialLibrary = this.initArrMaterialLibrary.slice(0, this.pageSize * this.currentPage)
				} else {
					this.noMore = true;
				}
			},
			//选择
			selectPic(item) {
				this.arrMaterialLibrary.forEach((e) => {
					e.isSelected = false;
				});
				this.curPicXH = item.XH;
				this.curWJID = item.WJID;
				this.curSRC = this.getFileUrl(item.WJID);
				item.isSelected = true;
			},
			getFileUrl(item) {
				return DOWNLOAD_URLZDY + item.WJID;
			},
			//保存
			async save() {
				try {
					let params = {
						SBID: this.SBID || '', //设备ID
						TLXH: this.curPicXH //图例序号
					};
					await saveEquipLegnend(params);
					uni.showToast({
						title: '配置图例成功',
						icon: 'success'
					});
					this.close();
					//更新画布里面的对应的设备的图片以及画板里面设备得图片
					this.$emit('updateDiagramEquipPicture', {
						WJID: this.curWJID
					});
				} catch (error) {
					console.log('error', error);
					uni.showToast({
						title: '配置图例失败',
						icon: 'error'
					});
				}
			},
			close() {
				this.$emit('update:isShowAddEquipPicture', false)
				this.$emit('update:showMask', false)
			}
		}
	};
</script>

<style scoped lang="less">
	.select-picbox {
		display: flex;
		flex-wrap: wrap;
		height: 560rpx;
		overflow-y: auto;
		padding:20rpx 0;

		/deep/ .uni-scroll-view-content {
			display: flex;
			flex-wrap: wrap;
		}

		.item {
			width: 25%;
			padding: 6rpx;
		}

		.tu {
			width: 100%;
			height: 140rpx;
			background: #edf0f3;
			position: relative;
			overflow: hidden;

		}

		.tu .pic {
			width: 100%;
			height: 100%;
			padding: 10rpx;

		}

		.select {
			width: 34rpx;
			height: 34rpx;
			position: absolute;
			top: 0rpx;
			right: 0rpx;
			background: url('~@/static/app/images/blackfill.png') bottom left no-repeat;
			background-size: 38rpx 36rpx;
			opacity: 0.3;
		}

		.isSelected {
			background: url('~@/static/app/images/bluefill.png') bottom left no-repeat;
			background-size: 38rpx 36rpx;
			opacity: 1;
		}

		.pic-name {
			font-size: 20rpx;
			line-height: 40rpx;
			text-overflow: ellipsis;
			overflow: hidden;
			white-space: nowrap;
		}

		.no-more,.no-data {
			width: 100%;
			text-align: center;
			color:#a8a8a8;
			padding:10rpx;
			transition:all .2s ease-in .1s;
		}
	}

	.submit-btn {
		display: flex;
		justify-content: center;
		align-items: center;

		.xj20-btn1 {
			background: #4874ff;
			color: #fff;
			border-radius: 30rpx;
			line-height: 60rpx;
			height: 60rpx;
			padding: 0 40rpx;
			margin: 0 20rpx;
		}

		.gray {
			background-color: #f5f5f5;
			color: #555;
		}

	}
</style>