/** @format */

//代码值请求回调缓存，用于缓存同样的代码值高频的请求，避免重复请求
const DEBOUNCE_REQUEST_HANDLERS = {};

export const debounceRequest = (requestKey, handler, request) => {
    let timer = DEBOUNCE_REQUEST_HANDLERS[requestKey];
    if (timer) {
        clearTimeout(timer.id);
        timer.handlers.push(handler);
        let resetTimerId = startRequestTimer(requestKey);
        timer.id = resetTimerId;
    } else {
        let timerId = startRequestTimer(requestKey);
        DEBOUNCE_REQUEST_HANDLERS[requestKey] = {
            id: timerId,
            handlers: [],
            request
        };
    }
};

const startRequestTimer = (requestKey) => {
    let timerId = setTimeout(() => {
        let debounceTimer = DEBOUNCE_REQUEST_HANDLERS[requestKey];
        let delayHandlers = debounceTimer.handlers;
        debounceTimer.request(delayHandlers);
        delete DEBOUNCE_REQUEST_HANDLERS[requestKey];
    }, 300);
    return timerId;
};
