<template>
	<div class="tabs1-con">
		   <div class="lr-main-all pb">
			   <ul class="lr27-3-btns">
				   <li
					   :class="{ on: curTab == item.value }"
					   v-for="item in tabArr"
					   :key="item.value"
					   @click="changeTab(item)"
				   >
					   {{ item.label }}
				   </li>
			   </ul>
			   <p class="lr27-3-txt">
				   <span
					   >共<i style="display: inline">{{
						   warningList.length || 0
					   }}</i
					   >个预警</span
				   >
				   <u-button
					   type="primary"
					   shape="square"
					   size="mini"
					   @click="select"
					   >选择预警类型</u-button
				   >
			   </p>
	
			   <div class="scroll">
				   <div
					   class="lr27-m5"
					   @click="toDetail(item)"
					   v-for="item in warningList"
					   :key="item.YJBH"
				   >
					   <h2>
						   {{ item.YJLX_CH }} {{ item.FSSJ }}
						   <span v-if="false" class="ts">待处理</span>
					   </h2>
					   <p>
						   <span class="k">预警产线：</span>
						   <span class="v">{{ item.SCXMC || '-' }}</span>
					   </p>
					   <p>
						   <span class="k">预警设备：</span>
						   <span class="v">{{ equitments(item) }}</span>
					   </p>
					   <p>
						   <span class="k">持续时长：</span>
						   <span class="v">{{ calcTime(item) }}</span>
						   <span class="k r">预警次数：</span>
						   <span class="v">{{ item.YJCS || '-' }}次</span>
					   </p>
				   </div>
					<u-empty v-if="warningList.length == 0" text="暂无数据" mode="list"></u-empty>
			   </div>
		   </div>
		   <template v-if="show">
			   <u-select
				   v-model="show"
				   :default-value="defaultValue"
				   :list="list"
				   @confirm="confirm"
			   ></u-select>
		   </template>
	</div>
 
</template>
<script>
import { getWarningEventList } from '../../../api/iot/runningData.js';
export default {
	props:{
		wrybh:{
			type:String,
			default:''
		},
	},
    data() {
        return {
            show: false,
            list: [
                {
                    value: '',
                    label: '全部'
                },
                {
                    value: 'ZW',
                    label: '治污预警'
                },
                {
                    value: 'TC',
                    label: '停产预警'
                }
            ],
            tabArr: [
                {
                    value: '1',
                    label: '正在发生'
                },
                {
                    value: '0',
                    label: '已解除'
                }
            ],
            curTab: '1',
            warningType: '',
            warningList: [],
            companyInfo: {},
            defaultValue: [0]
        };
    },
    onShow() {
        this.companyInfo = uni.getStorageSync('companyInfo');
        this.getWarningEventList();
    },
    methods: {
        select() {
            this.show = true;
        },
        // 回调参数为包含columnIndex、value、values
        confirm(e) {
            this.warningType = e[0].value;
            this.getWarningEventList();
            this.show = false;
        },
        calcTime(time) {
            const { YJSJ, FSSJ } = time;
            const startTime = this.$dayjs(YJSJ);
            const cutTime = this.$dayjs(FSSJ);
            const calcHours = this.$dayjs(startTime).diff(
                this.$dayjs(cutTime),
                'hours'
            );
            const calcSecond =
                this.$dayjs(startTime).diff(this.$dayjs(cutTime), 'minutes') -
                calcHours * 60;
            return `${calcHours}小时${calcSecond}分钟`;
        },
        changeTab(item) {
            this.curTab = item.value;
            this.warningType = '';
            this.defaultValue = [0];
            this.getWarningEventList();
        },
        getWarningEventList() {
            //const { WRYBH } = this.companyInfo;
            let obj = {
                WRYBH:this.wrybh,
                YJZT: this.curTab,
                YJLX: this.warningType
            };
            getWarningEventList(obj).then((res) => {
                if (res.status === '000') {
                    this.warningList = res.data;
                }
            });
        },
        toDetail(item) {
            uni.navigateTo({
                url:'/pages/warningRecord/Layout?YJID='+ item.ID + '&type=' + item.YJLX
            });
        },
        equitments(item) {
            //当生产设备名称或治污设备名称有一个为null时显示另一个
            const { SCSBMC, ZWSBMC } = item;
            if (!SCSBMC) return ZWSBMC;
            if (!ZWSBMC) return SCSBMC;
            return SCSBMC + ',' + ZWSBMC;
        }
    }
};
</script>

<style scoped>
	.lr-main-all{
		  height: 100%;
	}
.scroll {
    overflow-y: auto;
    height: calc(100% - 220rpx);
}
</style>
