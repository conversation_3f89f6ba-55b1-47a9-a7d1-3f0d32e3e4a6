/** @format */
import originalAxios from 'axios-miniprogram';
import axios from '@/common/ajaxRequest.js';

import { ULR_BASE } from '@/common/config.js';

// 列表
export const getList = (data) => {
    const CancelToken = originalAxios.CancelToken;
    const source = CancelToken.source();

    let promise = axios.request({
        method: 'get',
        url: ULR_BASE + `/monitor/yjxx/list`,
        params: data,
        cancelToken: source.token
    });
    promise.cancel = () => {
        promise.cancel = null;
        source.cancel('Operation canceled by the user.');
    };
    return promise;
};

// 预警详情
export const getInfo = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/monitor/yjxx/info',
        params: data
    });
};

//近24小时振动监控数据：（治污预警和停产预警）
export const monitorCxzt = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/waringPush/cxzt',
        params: data
    });
};

// 改密码
export const sendPass = (data) => {
    return axios.request({
        method: 'post',
        url: ULR_BASE + '/system/modifypw',
        data: data
    });
};

// 信息推送
export const sendMsg = (data) => {
    return axios.request({
        method: 'post',
        url: ULR_BASE + '/push/client/bind',
        data: data
    });
};

// 预警详情
export const getWarnList = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/warning/record',
        params: data
    });
};

// 设备运行状态
export const sbsxzt = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/monitor/cxzt',
        params: data
    });
};

//振动趋势
export const getZdqs = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/baseinfo/zdqs',
        params: data
    });
};

//振动趋势区域时间颜色
export const ycsbqj = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/baseinfo/ycsbqj',
        params: data
    });
};

// 预警内容页
export const yjxxInfo = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/waringPush/yjxx/info',
        params: data
    });
};

//近24小时振动监控数据：（故障预警）
export const monitorZdsj = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/monitor/sbxq/zdsj',
        params: data
    });
};

//查询设备图片接口
export const monitorPicture = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/waringPush/sbxx/picture/list',
        params: data
    });
};

// 近24小时振动监控数据：（故障预警）
export const getZdsj = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/monitor/sbxq/zdsj',
        params: data
    });
};

//根据预警ID查询24小时状态图
export const warnStateData = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/waringPush/znsb/cxzt',
        params: data
    });
};

//近24小时振动监控数据：（治污预警和停产预警）
export const monitorCxztByID = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/waringPush/znsb/cxzt',
        params: data
    });
};

//查询ph设备电压和ph值的列表接口
export const getPHDataList = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/monitor/ph/sssj',
        params: data
    });
};
