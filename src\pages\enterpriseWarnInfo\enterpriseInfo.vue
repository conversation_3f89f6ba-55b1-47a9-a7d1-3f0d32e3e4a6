<template>
	<!-- 企业信息 -->
	<div class="zx-page" ref="topLevel" style="background: rgb(241, 241, 241);">
		<!-- <header class="header" style="position: fixed;">
			<i class="pd-backbtn" @click="back()"></i>
			<h1 class="title">{{ enterpriseInfo.WRYMC }}</h1>
		</header> -->
		<section class="main" style="height: auto;overflow: hidden;">
			<div class="inner" style="height: auto;overflow: hidden;">
				<div class="gap"></div>
				<div class="zy-data2" style="padding: 0 0;">
					<div class="item" @click="toDetail" style="padding-left: 30rpx;">
						<p class="label ico1b">企业详情</p>
						<div class="rp w70">
							<image src="../../static/app/images/arwrt1.png" class="pd-arw1" />
						</div>
					</div>
					<div class="item"  @click="toWarningRecord" style="padding-left: 30rpx;">
						<p class="label ico2b">预警记录<sub>{{ yjsl.SL }}</sub></p>
						<div class="rp w70">
							<image src="../../static/app/images/arwrt1.png" class="pd-arw1" />
						</div>
					</div>
					<!-- <div class="box" style="padding: 0rpx 30rpx;">
						<div class="zy-tip" @click="toWarningRecord">
							<p class="p1">预警记录</p>
							<div class="zy-qipao">{{ yjsl.SL }}</div>
							<i class="ic-more orange" style="margin-left: auto;"></i>
						</div>
						<div class="gap"></div>
						<div class="gap"></div>
					</div> -->
					<div class="box hour-data-top" style="padding: 0rpx 30rpx;">
						<div class="zy-line jb">
							<p class="zy-til1 ic3">当前状态</p>
							<p class="zy-time">更新时间：{{ sjsj || '--' }}</p>
						</div>

						<ul class="zy-data3">
							<li v-for="(item, index) in sbslList" :key="index">
								<p class="p1">{{ item.SBLX == '生产' ? '生产设备' : '治污设备' }}</p>
								<p class="p2">
									<span>{{ item.ZCSL }}</span>
									/{{ item.ZSL }}
								</p>
							</li>
							<u-empty mode="data" v-if="!sbslList.length"></u-empty>
						</ul>
					</div>
					<!-- :style="{'height': hourBoxHeight}" -->
					<!-- :class="{'fixed': isTop == 1}" -->
					<div class="box hour-data-box">
						<hoursData v-if='false' :wrybh="wrybh" :headerHeight='headerHeight' :scrollHeight="scrollHeight" :houseData='houseData'></hoursData>
						<!-- //新的振动数据 -->
						<hoursData2  :wrybh="wrybh" :headerHeight='headerHeight' :scrollHeight="scrollHeight" :houseData2='houseData'></hoursData2>
					</div>
				</div>
			</div>
		</section>
		<div class="fixed fixed-box">
		</div>
	</div>
</template>
<script>
	import {
		getInfo,
		getXssj
	} from '../../api/iot/realtime.js';
	import hoursData from './hoursData';
	import hoursData2 from './hoursData2';
	export default {
		components: {
			hoursData,
			hoursData2,
		},
		data() {
			return {
				wrybh: '',
				enterpriseInfo: {},
				xzqhdm: '',
				currentscx: '',
				yjsl: {},
				sbslList: [],
				sjsj: '',
				datetimerange: [
					this.$dayjs()
					.subtract(1, 'day')
					.format('YYYY-MM-DD hh:mm'),
					this.$dayjs().format('YYYY-MM-DD hh:mm')
				],
				hourBoxHeight: 'auto',
				headerHeight: 0,
				childOverflow: 'hidden',
				myScroll: Number.MAX_SAFE_INTEGER,
				isTop: 0,
				scrollHeight: 0,
				houseData:{}
			};
		},
		mounted() {},
		onLoad(option) {
			let userinfo = uni.getStorageSync('user_info');
			this.xzqhdm = userinfo.orgid;
			this.wrybh = option.WRYBH;
			this.initInfo();
			this.handleRect();
		},
		methods: {
			initInfo() {
				getInfo({
					WRYBH: this.wrybh,
					XZQHDM: this.xzqhdm,
				}).then(res => {
					this.houseData = res.data
					console.log('this.houseData',this.houseData);
					this.enterpriseInfo = res.data.qyjbxx;
					uni.setNavigationBarTitle({
						title: this.enterpriseInfo.WRYMC
					})
					this.sjsj = res.data.sbgxsj;
					let yjslLen = res.data.yjsl.length;
					if (yjslLen) {
						this.yjsl = res.data.yjsl[0];
					}
					this.sbslList = res.data.sbslList;
				});
			},
			// 唤起系统导航app进行导航
			opLocaltion() {
				uni.getLocation({
					success: res => {
						uni.openLocation({
							latitude: parseFloat(this.enterpriseInfo.WD),
							longitude: parseFloat(this.enterpriseInfo.JD),
							name: this.enterpriseInfo.WRYMC,
							scale: 8
						});
					}
				});
			},
			toDetail() {
				uni.navigateTo({
					url: './detailInfo?wrybh=' + this.enterpriseInfo.WRYBH + '&wrymc=' + this.enterpriseInfo
						.WRYMC
				});
			},
			//
			toWarningRecord(v) {
				uni.navigateTo({
					url: './warningRecord?wrybh=' + this.enterpriseInfo.WRYBH + '&wrymc=' + this.enterpriseInfo
						.WRYMC
				});
			},
			back() {
				uni.navigateBack({
					delta: 1
				});
			},
			handleRect() {
				const self = this;
				this.$nextTick(() => {
					setTimeout(function() {
						let queryDom = uni.createSelectorQuery().in(self) // 使用api并绑定当前组件
						queryDom
							.select('.fixed-box')
							.boundingClientRect((res) => {
								self.scrollHeight = res.height;
							})
							.exec()
					}, 500);

				})

			},
			onScroll() {

			}
		}
	};
</script>

<style>
	body {
		height: auto;
	}
</style>
<style scoped>
	.zx-page {
		background: none;
	}

	.zy-data1 {
		padding: 0;
	}
	
	.zy-data2{
		border-radius: 0;
	}
	
	.zy-data3{
		padding: 0 0 0 42.270525rpx;
	}

	.zy-tip {}

	.zy-tip .zy-qipao {
		margin-top: -36rpx;
	}

	.on {
		color: #007aff;
	}

	.fixed {
		position: fixed;
		background: #FFFFFF;
		z-index: 9;
		top: 0;
		bottom: 0;
	}

	.zx-page .inner {
		margin-top: 0;
	}
</style>
