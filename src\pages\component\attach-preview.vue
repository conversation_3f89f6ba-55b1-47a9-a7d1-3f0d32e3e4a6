<!--
 * @Author: your name
 * @Date: 2021-05-06 15:15:51
 * @LastEditTime: 2021-05-10 15:13:41
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /SmartFormWeb/pages/component/attach-preview.vue
-->

<template>
	<previewImage :rotateBtn="true"
				  @closePreview="closePreview"
				  ref="previewImage"
				  :opacity="1"
				  :imgs="imagesList"></previewImage>
</template>

<script>
	import previewImage from '@/components/kxj-previewImage/kxj-previewImage.vue';
	export default {
		components: {
			previewImage
		},

		data() {
			return {
				imagesList: [], //预览的附件内容
				index: ''
			};
		},

		onLoad(options) {
			this.imagesList = JSON.parse(decodeURIComponent(options.list))
			this.index = options.index
		},

		mounted() {
			this.$refs.previewImage.open(parseInt(this.index))
		},

		methods: {
			closePreview() {
				uni.navigateBack({
					delta: 1
				});
			}
		}
	};
</script>

<style scoped>

</style>
