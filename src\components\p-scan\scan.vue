<template>
<view class='scancode'>
		<view id="barcode"></view>
		<image class="scanFromPic" src="../mi-loading/loading.gif"></image>
</view>
</template>

<script>
export default {
	data() {
		return {
			// #ifdef APP-PLUS
			barcode: [
				plus.barcode.QR,
				plus.barcode.EAN13,
				plus.barcode.EAN8,
				plus.barcode.UPCA,
				plus.barcode.UPCE,
				plus.barcode.CODABAR,
				plus.barcode.CODE39,
				plus.barcode.CODE93,
				plus.barcode.CODE128,
				plus.barcode.ITF
			] //码类型
			// #endif
		};
	},
	created() {
		var statusBarHeight = uni.getSystemInfoSync().statusBarHeight; //状态栏
		var height = '100vh';
		var pages = getCurrentPages();
		var page = pages[pages.length - 1];
		// #ifdef APP-PLUS
		var currentWebview = page.$getAppWebview();
		this.barcode = plus.barcode.create('barcode', this.barcode, {
			top: '0',
			left: '0px',
			width: '100%',
			height: height, //180px
			position: 'absolute',
			frameColor: '#5bb4fc',
			scanbarColor: '#5bb4fc'
		});
		this.barcode.onmarked = this.onmarked;
		this.barcode.onmarked = this.onmarked;
		currentWebview.append(this.barcode);
		const res = uni.getSystemInfoSync();
		if (res.platform == 'android') {
			//安卓机
			this.barcode.start();
		}
		// #endif
	},
	onUnload() {
		clearTimeout(this.t);
	},
	methods: {
		scanFromPic() {
			plus.gallery.pick(
				function(path) {
					plus.barcode.scan(path, this.onmarked, this.onerror);
				},
				function(err) {
			  alert('选择相片失败: ' + JSON.stringify(err.message));
				}
			);
		},
		onmarked(type, result) {
			// console.log(type +':'+ result);

			this.$emit('getCode', result);
			this.t = setTimeout(() => {
				this.barcode.start();
			}, 200);
		},
		onerror() {}
	}
};
</script>

<style lang="less">
	.scancode{
		width: 100vw;
		height: 100vh;
		position: relative;
	}
#barcode{
	z-index: 66665;
}
.scanFromPic {
	top: 60rpx;
	position: absolute;
	z-index: 66666;
}
</style>
