<!-- @format -->
<template>
    <!-- 例行巡检 -->
    <section class="main" style="padding-bottom: 0; margin-bottom: 30.1932rpx">
        <div class="inner">
            <div class="gap"></div>
            <div class="zy-form">
                <!-- 设备基本信息 -->
                <GetIMEIMsg
                    @updateInfo="updateInfo"
                    ref="refGetIMEIMsg"
                    :options="options"
                ></GetIMEIMsg>
                <div class="item">
                    <p class="label">设备阈值</p>
                    <div class="rp">
                        <input
                            type="text"
                            disabled
                            v-model="info.qtyz"
                            class="zy-input1"
                            placeholder="扫码后获取设备阈值"
                        />
                    </div>
                </div>
                <div class="item">
                    <p class="label">检测周期</p>
                    <div class="rp">
                        <input
                            type="text"
                            disabled
                            v-model="info.ywmx.jczq"
                            class="zy-input1"
                            placeholder="扫码后获取检测周期"
                        />
                    </div>
                </div>
                <div class="item">
                    <p class="label">上报周期</p>
                    <div class="rp">
                        <input
                            type="text"
                            disabled
                            v-model="info.ywmx.bsjg"
                            class="zy-input1"
                            placeholder="扫码后获取上报周期"
                        />
                    </div>
                </div>
                <div class="item">
                    <p class="label">信号质量</p>
                    <div class="rp">
                        <input
                            type="text"
                            disabled
                            v-model="info.ywmx.xhzl"
                            class="zy-input1"
                            placeholder="扫码后获取信号质量"
                        />
                    </div>
                </div>
                <div class="item">
                    <p class="label">电池电量</p>
                    <div class="rp">
                        <input
                            type="text"
                            disabled
                            v-model="info.ywmx.dcdl"
                            class="zy-input1"
                            placeholder="扫码后获取电池电量"
                        />
                    </div>
                </div>
                <div class="item">
                    <p class="label">信号优化建议</p>
                    <div class="rp">
                        <input
                            type="text"
                            disabled
                            v-model="info.ywmx.xhyhjy"
                            class="zy-input1"
                            placeholder="扫码后获取信号优化建议"
                        />
                    </div>
                </div>
                <div class="item">
                    <p class="label">延迟报送情况</p>
                    <div class="rp">
                        <input
                            type="text"
                            disabled
                            v-model="info.ywmx.ycbsqk"
                            class="zy-input1"
                            placeholder="扫码后获取延迟报送情况"
                        />
                    </div>
                </div>
                <div class="item">
                    <p class="label">预警无效率</p>
                    <div class="rp">
                        <input
                            type="text"
                            disabled
                            v-model="info.ywmx.yjwxl"
                            class="zy-input1"
                            placeholder="扫码后获取预警无效率"
                        />
                    </div>
                </div>
                <div class="item">
                    <p class="label" style="flex: 5">巡检结果</p>
                    <div class="rp pd-btn1" v-show="options.type === 'add'">
                        <span
                            :class="{ on: info.ywmx.xjjg === '正常' }"
                            class="click-btn"
                            @click="chooseResult('正常')"
                        >
                            正常
                        </span>
                        <span
                            class="click-btn"
                            :class="{ on: info.ywmx.xjjg === '异常' }"
                            @click="chooseResult('异常')"
                        >
                            异常
                        </span>
                    </div>
                    <div class="rp pd-btn1" v-show="options.type === 'detail'">
                        <span>
                            {{ info.ywmx.xjjg || '-' }}
                        </span>
                    </div>
                </div>
                <div class="item" v-show="info.ywmx.xjjg === '异常'">
                    <p
                        class="label"
                        :class="{ star: options.type === 'add' }"
                        style="flex: 5"
                    >
                        下一步处理
                    </p>
                    <div class="rp pd-btn1" v-show="options.type === 'add'">
                        <span
                            class="click-btn click-btn2"
                            :class="{ on: info.ywmx.xybcl == item }"
                            v-for="item in arrDueType"
                            :key="item"
                            @click="chooseDueType(item)"
                        >
                            {{ item }}
                        </span>
                    </div>
                    <div class="rp pd-btn1" v-show="options.type === 'detail'">
                        <span>
                            {{ info.ywmx.xybcl }}
                        </span>
                    </div>
                </div>
            </div>
            <!-- 采集结果 -->
            <GetLastedResult
                :imei="info.imei"
                ref="refGetLastedResult"
            ></GetLastedResult>

            <div class="gap"></div>
            <div class="gap"></div>
            <div class="zy-bot-btn1" v-show="!isDisabled" @click="save">
                保存
            </div>
            <div class="gap"></div>
            <div class="gap"></div>
        </div>
    </section>
</template>

<script>
import { maintenanceDetail } from '@/api/iot/equipmentMaintenance';
import { guid } from '@/utils/uuid.js';
import GetIMEIMsg from './components/GetIMEIMsg';
import GetLastedResult from './components/GetLastedResult';
import useSaveForm from './hook/useSaveForm';
import { arrayFindIndex } from '../../components/ly-tree/tool/util';
const { handleSave } = useSaveForm();
export default {
    data() {
        return {
            curResult: 'normal',
            options: {},
            info: {
                imei: '',
                ywmx: {
                    xjjg: '正常',
                    xybcl: '',
                    bsjg: '',
                    dcdl: '',
                    jczq: '',
                    xhyhjy: '',
                    xhzl: '',
                    ycbsqk: '',
                    yjwxl: ''
                },
                qtyz: ''
            },
            rules: {
                xybcl: {
                    required: false,
                    message: '请选择下一步处理'
                }
            },
            isDisabled: false, //是否不可编辑
            arrDueType: ['暂不处理', '调整位置', '拆除设备', '更换电池']
        };
    },
    components: {
        GetIMEIMsg,
        GetLastedResult
    },
    watch: {},
    onLoad(options) {
        this.options = options;
    },
    onHide() {},
    created() {},
    mounted() {
        if (this.options.type === 'add') {
            this.info.ywlx = this.options.ywlx;
            this.info.ywid = this.options.ywid;
            this.info.mxid = guid();
        }
        if (this.options.type === 'detail') {
            uni.setNavigationBarTitle({
                title: '运维明细详情'
            });
            this.isDisabled = true;
            this.getMaintenanceDetail();
        }
    },
    onBackPress() {},
    methods: {
        //请求明细
        async getMaintenanceDetail(id) {
            let data = await maintenanceDetail(this.options.mxid);
            for (const key in data) {
                if (key == 'ywmx') {
                    let innerData = data['ywmx'];
                    for (const key1 in innerData) {
                        this.$set(
                            this.info.ywmx,
                            key1,
                            innerData[key1] === '' ? '-' : innerData[key1]
                        );
                    }
                } else {
                    this.$set(
                        this.info,
                        key,
                        data[key] === '' ? '-' : data[key]
                    );
                }
            }
            let index = this.info.ywmx?.qtyz?.indexOf('#') || 0;
            this.info.qtyz = this.info.ywmx?.qtyz?.slice(0, index) || '-';
            this.$refs.refGetIMEIMsg.updateInfo(this.info);
        },
        chooseResult(v) {
            if (this.options.type === 'detail') {
                return;
            }
            this.info.ywmx.xjjg = v;
            this.rules.xybcl.required = v === '异常';
        },
        //选择下一步处理
        chooseDueType(v) {
            this.info.ywmx.xybcl = v;
        },
        //更新表单数据
        updateInfo(payload) {
            this.info = {
                ...this.info,
                ...payload,
                ywmx: {
                    ...payload.ywmx,
                    xjjg: this.info.ywmx.xjjg,
                    xybcl: this.info.ywmx.xybcl
                }
            };
            let index = this.info.ywmx?.qtyz?.indexOf('#') || 0;
            this.info.qtyz = this.info.ywmx?.qtyz?.slice(0, index) || '-';
        },
        //保存
        async save() {
            let objRules = {};
            let objValiData = {};
            Object.assign(objRules, this.$refs.refGetIMEIMsg.rules, this.rules);
            Object.assign(
                objValiData,
                this.info,
                this.info.ywmx,
                this.$refs.refGetIMEIMsg.info
            );
            let pages = getCurrentPages(); // 当前页面
            handleSave(objRules, objValiData, this.info, pages, false);
        }
    }
};
</script>

<style scoped lang="less">
section {
    background: #f1f1f1;
}
.zy-form .item .rp {
    flex-wrap: wrap;
    .click-btn {
        height: 26px;
        line-height: 26px;
        border-radius: 300px;
        font-size: 14px;
        color: #999;
        background: #f1f1f1;
        border: none;
        padding: 0 16px;
        margin: 0 10rpx;
    }
    .click-btn2 {
        margin: 10rpx;
    }
    .on {
        background: #4874ff;
        color: #fff;
    }
}
.pd-btn1 button {
    margin-bottom: 20rpx;
}
</style>
