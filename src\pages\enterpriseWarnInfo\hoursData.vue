<template>
	<!-- 企业信息 -->
	<div class='hour-box-wrapper'  style="padding: 0rpx 30rpx;">
		<p class="zy-til1 ic4" >近24小时振动数据</p>
		<div class="gap"></div>
		<ul class="zy-tabs1">
			<li :class="currentscx==item.SCXID?'cur':''" @click="changescx(item)" v-for="(item, index) in scxlist">
			{{ item.SCXMC }}
				<!-- <div class="zy-qipao" v-if='item.YCSL > 0'>!</div> -->
			</li>
		</ul>
		<div class="gap"></div>
		<div class="zy-line jb ac">
			<p class="zy-time">
				<!-- {{datetimerange[0]}}~{{datetimerange[1]}} -->
				<uni-datetime-picker @change="change" disabled v-model="datetimerange" type="datetimerange" rangeSeparator="至" :hide-second="true" :clear-icon="false" />
			</p>
			<!-- <i class="ic-more"></i> -->
		</div>
		<div class="gap"></div>
		<ul class="zy-data4">
			<li class="li1">启动</li>
			<li class="li2">停止</li>
			<li class="li3">离线</li>
		</ul>
		<div class="gap"></div>
		<!-- :style="{'height': hourBoxHeight}" -->
		<div class="time-data-wrap" style="position: relative;overflow-y: auto;overflow-x: hidden;background-color: white;"  :style="{'height': hourBoxHeight}"   id="table">
			<div class='mark'>2</div>
			<div class="left">
				<div class="th" style="display: flex;margin: 0 5rpx;top:0">
					<div style="" class="week"></div>
				</div>
				<div class="tb" style='display: flex;flex-direction: column;'>
					<div v-for="(row, index) in hoursData" :key="index" class="time-24" style='text-align: center;'>{{ row.key }}</div>
				</div>
			</div>
			<div class="right" style="position: relative;">
				<div class="th" ref="th" id="th" style="display: flex;margin: 0 5rpx;top:0">
					<div v-for="(item, index) in header" :key="index"  class="th-item" @click="toEquipmentInfo(item)" style='width: 200rpx;background-color: #e4eaff;color: #4874ff;border-radius: 5rpx;padding-left: 5rpx;'>{{ item&&item.SBMC.slice(0,4) }}
					<image src="../../static/images/chan.png" mode="" style="margin-left: 8rpx;" v-if="item.SBLX=='生产'"></image>
					<image src="../../static/images/zhi.png" mode="" style="margin-left: 8rpx;" v-if="item.SBLX=='治污'"></image>
					<image src="../../static/images/bei.png" mode="" style="margin-left: 8rpx;" v-if="item.ZBGX=='备'"></image>
					<image src="../../static/images/yong.png" mode="" style="margin-left:8rpx;" v-if="item.ZBGX=='主'"></image>
					</div>
				</div>
				<div class="tb" id="tb" @scroll="table.scrollEvent" ref="table" style='display: flex;flex-direction: column;'>
					<div v-for="(row, index) in hoursData" :key="index" style="display: flex" class="tr">
						<!-- { 0: '离线', 1: '停止', 2: '启动', '': '' } -->
						<div
							v-for="(item, index) in row.hours"
							class="hours-data status td"
							:class="item && item.val == '0' ? 'c-gray' : item && item.val == '1' ? 'c-red' : 'c-green'"
						></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { getInfo, getXssj } from '../../api/iot/realtime.js';
export default {
	data() {
		return {
			WRYBH: '',
			userinfo: uni.getStorageSync('user_info'),
			//userinfo: JSON.parse(window.localStorage.getItem('user_info')),
			enterpriseInfo: {},
			xzqhdm: '',
			scxlist: [],
			currentscx: '',
			currentscxname:'',
			datetimerange: [
				this.$dayjs()
					.subtract(1, 'day')
					.format('YYYY-MM-DD HH:mm'),
				this.$dayjs().format('YYYY-MM-DD HH:mm')
			],
			header: [],
			hoursData: [],
			hourBoxHeight: 'auto',
		};
	},
	props: {
		wrybh: {
			type: String,
			default: ''
		},
		headerHeight:{
			type: Number,
			default: 0
		},
		scrollHeight:{
			type: Number,
			default: 0
		}
	},
	watch: {
		wrybh: {
			handler: function(nv) {
				console.log('nv',nv);
				this.WRYBH = nv;
				this.initScxInfo();
			},
			immediate: true
		},
		
	},
	mounted() {
		console.log('userinfo',this.userinfo);
	},
	methods: {
		// 獲取生产线信息
		initScxInfo() {
			getInfo({ WRYBH: this.WRYBH, XZQHDM: this.userinfo.orgid }).then(res => {
				this.scxlist = res.data.scxList;
				let scxLen = res.data.scxList.length;
				if (scxLen) {
					this.currentscx = res.data.scxList[0].SCXID;
					this.currentscxname = res.data.scxList[0].SCXMC;
				}
				this.initXssj();
				this.handleRect();
			}).catch(err=>{
				console.log('err',err);
			})
		},
		// 小时数据
		initXssj() {
			getXssj({ SCXID: this.currentscx, startT: this.datetimerange[0], endT: this.datetimerange[1] }).then(res => {
				const qtztMap = { 0: '离线', 1: '停止', 2: '启动', '': '' };
				let scsbList = res.data.scxssjList;
				let zwxssjList = res.data.zwxssjList;

				let allequipment = scsbList.concat(zwxssjList);

				let columns = [];
				let columns1 = {};
				let colIndex = 1;
				let data = [];

				allequipment.forEach(item => {
					//
					if (!columns1[item.SBMC]) {
						columns1[item.SBMC] = colIndex++;
					}
					let index = columns.findIndex(items => {
						return items.SBMC == item.SBMC;
					});
					if (index == -1) {
						columns.push({
							SBMC: item.SBMC,
							SBLX: item.SBLX,
							ZBGX: item.ZBGX,
							SBID: item.SBID,
							IMEI: item.IMEI
						});
					}
					var hours = item.SJSJ&&item.SJSJ.slice(11, 16);
					if (!data[hours]) {
						data[hours] = { key: item.SJSJ&&item.SJSJ.slice(11, 16), hours: [] };
					}
					var curColIndex = columns1[item.SBMC] - 1;

					data[hours]['hours'][curColIndex] = { val: item.QTZT, sbmc: item.SBMC, label: qtztMap[item.QTZT] };
				});
				console.log(data);
				// 表头数据
				this.header = columns;
				this.hoursData=[]
				// 解决视图不更新
				for (let i in data) {
					this.hoursData.push(data[i]);
				}
			});
		},
		// 生产线点击
		changescx(val) {
			this.currentscx = val.SCXID;
			this.currentscxname = val.SCXMC;
			this.initXssj();
		},
		// 时间控件change事件
		change(v) {
			this.datetimerange = v;
			this.initXssj();
		},
		// 到生产线详情页
		toEquipmentInfo(v) {
			console.log(v);
			uni.navigateTo({
				url: `./equipmentInfo?SBID=${v.SBID}&IMEI=${v.IMEI}&SBMC=${v.SBMC}&SCXMC=${this.currentscxname}`
			});
		},
		handleRect(){
			const self = this;
			this.$nextTick(()=>{
				setTimeout(function() {
					let queryDom = uni.createSelectorQuery().in(self) // 使用api并绑定当前组件
					let promiseTop = new Promise((resolve)=>{
						queryDom
							.select('.hour-box-wrapper')
							.boundingClientRect((res) => {
								let top = res.top;
								// console.log(res)
								
								resolve(top);
							})
							.exec()
							
					})
					
					let promiseBottom = new Promise((resolve)=>{
						queryDom
							.select('.time-data-wrap')
							.boundingClientRect((res) => {
								let bottom = res.top;
								// console.log(res)
								
								resolve(bottom);
							})
							.exec()
							
					})
					
					Promise.all([promiseTop, promiseBottom]).then(res=>{
						console.log( self.screenHeight,res)
						
						// let hourBoxHeight = (self.screenHeight - (res[1] - res[0]) - self.headerHeight);
						
						// if(!plus.navigator.hasNotchInScreen()){
						// 	hourBoxHeight = hourBoxHeight - plus.navigator.getStatusbarHeight();
						// }
						
						let hourBoxHeight = (self.scrollHeight - (res[1] - res[0]));
						self.hourBoxHeight = hourBoxHeight + 'px';
					})
				}, 500);
				
					
			})
			
		},
	}
};
</script>
<script module="table" lang="renderjs">
export default{
	data(){
		return{}
	},
	methods:{
		scrollEvent(){
			// 头部随滚动条滚动
			this.$refs.th.style.transform =  `translate(-${this.$refs.table.scrollLeft}px)`
		}
	},
	mounted() {
	}
}
</script>
<style scoped lang="scss">
.c-red {
	background-color: #ff7048;
}
.c-gray {
	background-color: #888888;
}
.c-green {
	background-color: #7dcd27;
}
.on {
	color: #007aff;
}

.time-data-wrap {
	display: flex;
	width: 100vw;
	position: relative;
}

.time-data-wrap .left {
	width: 120rpx;
	position: relative;
	z-index: 1;
}


/* #ifdef APP-PLUS */
// .time-data-wrap .left div {
// 	width: 120rpx;
// 	height: 48rpx;
// 	box-sizing: border-box;
// 	background-color: #fff;
// 	margin: 10rpx 0;
// 	border: 1px solid #0000FF;
// }
/* #endif */
/* #ifdef H5 */
// .time-data-wrap .left div {
// 	width: 120rpx;
// 	height: 48rpx;
// 	box-sizing: border-box;
// 	background-color: #fff;
// 	margin: 8rpx 0;
// }
/* #endif */


.time-data-wrap .right .tb {
	width: calc(100vw - 146rpx);
	overflow: auto;
}

.time-data-wrap .left .tb div {
	float: left;
	height: 48rpx;
	box-sizing: border-box;
	margin:10rpx 5rpx;
}

.time-data-wrap .right  {
	
}

.time-data-wrap .right .tb div.tr {
	float: left;
	height: 48rpx;
	box-sizing: border-box;
	margin:10rpx 5rpx;
}

.time-data-wrap .right .tb div.td {
	margin:0 5rpx;
}

.th {
	position: sticky;
	top: 120rpx;
}
.th div {
	width: 200rpx;
	white-space: nowrap;
	margin:10rpx 5rpx;
	overflow: hidden;
	height: 48rpx;
	display: flex;
	align-items: center;
}

.hours-data {
	width: 200rpx;
	border-radius: 12.077325rpx;
	flex-shrink: 0;
}
.mark{
	background-color: #fff;
	color: #fff;
	width: 28rpx;
	height:40vh;
	right: 0;
	z-index: 666;
	position: absolute;
}

.th-item{
	box-sizing: border-box;
}
.th-item image{
	height: 35rpx;
	width: 35rpx;
}
.zy-tabs1{
	flex-wrap: wrap;
	li{
		margin-top: 10rpx;
	}
}


</style>