<!-- @format -->
<template>
    <!-- 调整位置 -->
    <section class="main" style="padding-bottom: 0; margin-bottom: 30.1932rpx">
        <div class="inner">
            <div class="gap"></div>
            <div class="zy-form">
                <!-- 设备基本信息 -->
                <GetIMEIMsg
                    @updateInfo="updateInfo"
                    ref="refGetIMEIMsg"
                    :options="options"
                ></GetIMEIMsg>
                <SetCircleData
                    :options="options"
                    :isDisabled="isDisabled"
                    :info.sync="info"
                ></SetCircleData>
            </div>
            <UploadImage
                ref="refUploadImage"
                :options="options"
                :arrUploadType="arrUploadType"
                :uploadId="info.mxid"
                :isDisabled="isDisabled"
                title="新位置拍照"
            >
                <ul class="pd-ulpic1 sample">
                    <li>
                        <image
                            mode="aspectFit"
                            src="@/static/images/sample/sb1.jpg"
                            alt=""
                        />
                        <p>近景</p>
                    </li>
                    <li>
                        <image
                            mode="aspectFit"
                            src="@/static/images/sample/sb3.jpg"
                            alt=""
                        />
                        <p>中景</p>
                    </li>
                    <li>
                        <image
                            mode="aspectFit"
                            src="@/static/images/sample/sb2.jpg"
                            alt=""
                        />
                        <p>远景</p>
                    </li>
                </ul>
                <div class="gap"></div>
                <div class="gap"></div>
                <dl class="pd-dltxt1">
                    <dt>说明：</dt>
                    <dd>
                        <p>1、近景需把设备IMEI号拍出来；</p>
                    </dd>
                    <dd>
                        <p>2、中景能看清楚安装在产治污设备上的什么位置；</p>
                    </dd>
                    <dd>
                        <p>
                            3、远景能看清楚产治污设备的全貌与在车间内大概位置；
                        </p>
                    </dd>
                </dl>
            </UploadImage>
            <!-- 采集结果 -->
            <GetLastedResult
                :imei="info.imei"
                ref="refGetLastedResult"
            ></GetLastedResult>
            <div class="gap"></div>
            <div class="gap"></div>
            <div class="zy-bot-btn1" v-show="!isDisabled" @click="save">
                保存
            </div>
            <div class="gap"></div>
            <div class="gap"></div>
        </div>
    </section>
</template>

<script>
import { maintenanceDetail } from '@/api/iot/equipmentMaintenance';
import { guid } from '@/utils/uuid.js';
import GetIMEIMsg from './components/GetIMEIMsg';
import GetLastedResult from './components/GetLastedResult';
import SetCircleData from './components/SetCircleData';
import UploadImage from './components/UploadImage';
import useSaveForm from './hook/useSaveForm';
const { handleSave } = useSaveForm();
export default {
    data() {
        return {
            options: {},
            info: {
                mxid: '', //明细id
                imei: '',
                ywmx: {
                    jczq: 0,
                    bsjg: 0,
                    qtyz: '0#0'
                },
                qtyz: 0
            },
            rules: {},
            arrUploadType: [
                {
                    name: '近景',
                    LXDM: 'YWMX', //类型代码
                    ZLXDM: 'TZWZ1' //子类型代码
                },
                {
                    name: '中景',
                    LXDM: 'YWMX',
                    ZLXDM: 'TZWZ2'
                },
                {
                    name: '远景',
                    LXDM: 'YWMX',
                    ZLXDM: 'TZWZ3'
                }
            ],
            isDisabled: false //是否不可编辑
        };
    },
    components: {
        GetIMEIMsg,
        GetLastedResult,
        UploadImage,
        SetCircleData
    },
    watch: {
        'info.qtyz': {
            handler(newV, oldV) {
                this.info.ywmx.qtyz = this.info.qtyz + '#' + this.info.qtyz;
            }
        }
    },
    onLoad(options) {
        this.options = options;
    },
    onHide() {},
    created() {},
    mounted() {
        if (this.options.type === 'add') {
            uni.setNavigationBarTitle({
                title: '调整位置'
            });
            this.info.ywlx = this.options.ywlx;
            this.info.ywid = this.options.ywid;
            this.info.mxid = guid();
        }
        if (this.options.type === 'detail') {
            uni.setNavigationBarTitle({
                title: '运维明细详情'
            });
            this.isDisabled = true;
            this.getMaintenanceDetail();
        }
    },
    onBackPress() {},
    methods: {
        //请求明细
        async getMaintenanceDetail(id) {
            let data = await maintenanceDetail(this.options.mxid);
            for (const key in data) {
                if (key == 'ywmx') {
                    let innerData = data['ywmx'];
                    for (const key1 in innerData) {
                        this.$set(
                            this.info.ywmx,
                            key1,
                            innerData[key1] === '' ? '-' : innerData[key1]
                        );
                    }
                } else {
                    this.$set(
                        this.info,
                        key,
                        data[key] === '' ? '-' : data[key]
                    );
                }
            }
            let index = this.info.ywmx?.qtyz?.indexOf('#') || 0;
            this.info.qtyz = this.info.ywmx?.qtyz?.slice(0, index) || '-';
            this.$refs.refGetIMEIMsg.updateInfo(this.info);
            this.$nextTick(() => {
                this.$refs.refUploadImage.getImageFileList();
            });
        },
        //更新表单数据
        updateInfo(payload) {
            this.info = {
                ...this.info,
                ...payload,
                ywmx: {
                    ...payload.ywmx
                }
            };
            this.info.qtyz = this.extractBeforeFirstHash(payload.ywmx.qtyz);
        },
        //截取井前的字符
        extractBeforeFirstHash(str) {
            let index = str.indexOf('#');
            if (index !== -1) {
                return str.substring(0, index);
            } else {
                return str;
            }
        },
        //保存
        async save() {
            let objRules = {};
            let objValiData = {};
            Object.assign(objRules, this.$refs.refGetIMEIMsg.rules, this.rules);
            Object.assign(
                objValiData,
                this.info,
                this.info.ywmx,
                this.$refs.refGetIMEIMsg.info
            );
            let pages = getCurrentPages(); // 当前页面
            let arrUploadImage = this.$refs.refUploadImage.arrUploadImage;
            handleSave(
                objRules,
                objValiData,
                this.info,
                pages,
                true,
                arrUploadImage,
                true
            );
        }
    }
};
</script>

<style scoped lang="less">
section {
    background: #f1f1f1;
}
</style>
