<!-- @format -->

<template>
	<div class="login-content">
		<div class="logo">
			<image mode="widthFix" src="../../static/app/images/login_logo.png" alt=""></image>
			<div class="logo-text">智能运维</div>
		</div>


		<div class="login-box" :style="{'margin-top': marginTop+'px'}">
			<div class="login-box-name">
				<image src="../../static/app/images/ic_user_login.png" alt="" />
				<input type="text" placeholder="请输入账号" required clearable :focus="focusVal" v-model="username">
			</div>
			<div class="login-box-password">
				<image src="../../static/app/images/ic_password_login.png" alt="" />
				<input :type="passwordType" placeholder="请输入密码" v-model="password" class="pwd-input">
				<em class="eyes">
					<image src="../../static/app/images/eye_open.png" v-show="passwordType == 'text'"
						@click="passwordType = 'password'" />
				</em>
				<em class="eyes">
					<image src="../../static/app/images/eye_close.png" v-show="passwordType == 'password'"
						@click="passwordType = 'text'" />
				</em>

			</div>
			<div class="forget-password">
				<!-- <label for=""><input type="checkbox">记住密码</label> -->
				<!-- <label for=""><input type="checkbox">自动登录</label> -->

				<checkbox style="
				        font-size: 46rpx;
				        color: #666;
				        zoom: 60%;
				    " :checked="savePass" @click="checkChange()">
					记住密码
				</checkbox>
				<checkbox style="
				        font-size: 46rpx;
				        color: #666;
				        zoom: 60%;
				        margin-left: 40rpx;
				    " :checked="isAutoLogin" @click="autoLogin()">
					自动登录
				</checkbox>
			</div>
			<button class="btn" type="button" @tap="loginByPwd" v-show="username && password">登录</button>

			<div class="disabled-btn" v-show="!username || !password">登录</div>
		</div>
		<!-- <p class="message">技术支持：深圳市博安达信息技术股份有限公司</p> -->

		<view v-if="showUpgrade" class="flex-column-layout upgrade-tip-mask">
			<view class="flex-column-layout upgrade-progress-tip">
				<text style="width: 100%; text-align: left">正在更新升级数据</text>
				<view style="width: 100%"><progress :percent="upgradeProgress" active active-mode="forwards"
						stroke-width="3" show-info /></view>
			</view>
		</view>
	</div>
</template>

<script>
	import {
		userCustoms
	} from '@/api/iot/version.js';
	import service from '../../service.js';
	import {
		cacheAreas
	} from '@/common/district.js'
	import {
		mapState,
		mapMutations
	} from 'vuex';
	import mInput from '../../components/m-input.vue';
	import loginService from '@/api/login-service.js';
	// #ifdef APP-PLUS
	import appService from '@/api/app-service.js';
	// #endif
	import {
		getCommoHylx
	} from '@/api/iot/enterprise.js';
	import {
		arrayToTree
	} from '@/common/arrayToTree.js';
	export default {
		components: {
			mInput
		},
		data() {
			return {
				focusVal: false,
				passwordType: 'password',
				loginType: 0,
				loginTypeList: ['密码登录', '免密登录'],
				mobile: '',
				code: '',
				providerList: [],
				hasProvider: false,
				username: '',
				password: '',
				isAgree: false,
				positionTop: 0,
				isDevtools: false,
				codeDuration: 0,
				savePass: false,
				isAutoLogin: false,
				showUpgrade: false,
				upgradeProgress: 0,
				screenHeight: 0,
				marginTop: 0,
				industryList: [],
			};
		},

		created() {
			this.focusVal = this.username == '' ? true : false;
		},

		onLoad() {
			let _self = this;
			let promisePwd = new Promise((resolve, reject) => {
				uni.getStorage({
					key: 'save_password',
					success: function(r) {
						_self.savePass = r.data || false;
						if (_self.savePass) {
							uni.getStorage({
								key: 'password',
								success: function(r) {
									_self.password = r.data;
									resolve();
								}
							});
						}
					}
				});
			});

			let promiseName = new Promise((resolve, reject) => {
				uni.getStorage({
					key: 'username',
					success: function(r) {
						_self.username = r.data;

						_self.focusVal = _self.username == '' ? true : false;

						resolve();
					}
				});
			});

			// let promiseAgree = new Promise((resolve,reject)=>{
			// 	uni.getStorage({
			// 		key: 'isAgree',
			// 		success: function(r) {
			// 			_self.isAgree = r.data;
			// 			resolve();
			// 		}
			// 	});
			// })

			uni.getStorage({
				key: 'autoLogin',
				success: (r) => {
					_self.isAutoLogin = r.data;
				}
			});

			Promise.all([promisePwd, promiseName]).then(() => {
				if (_self.password && _self.username) {
					// 是否自动登录
					uni.getStorage({
						key: 'autoLogin',
						success: (r) => {
							if (r.data === true) {
								_self.loginByPwd();
							}
						}
					});
				}
			});

			// uni.getStorage({
			// 	key: 'autoLogin',
			// 	success: function(r) {

			// 	}
			// });
		},

		computed: {
			pageListStyle() {
				return {
					height: 'calc(100vh)'
				};
			}
			// mapState(["forcedLogin"])
		},
		onReady() {
			this.initPosition();

			// #ifdef MP-WEIXIN
			this.isDevtools = uni.getSystemInfoSync().platform === 'devtools';
			// #endif
		},

		mounted() {
		
			// #ifdef APP-PLUS
			const self = this;
			self.screenHeight =
				uni.getSystemInfoSync().screenHeight -
				plus.navigator.getStatusbarHeight();
			//监听键盘高度变化
			uni.onWindowResize((res) => {
				let windowHeight = res.size.windowHeight;

				if (windowHeight >= self.screenHeight) {
					self.marginTop = 0;
					return;
				}

				let queryDom = uni.createSelectorQuery().in(self); // 使用api并绑定当前组件
				queryDom
					.select('.pwd-input')
					.boundingClientRect((res) => {
						self.navTop = res.top + 160;
						console.log(res.bottom);

						if (res.bottom > windowHeight) {
							self.marginTop = -(res.bottom - windowHeight + 44);
						}
					})
					.exec();
			});
			// #endif
			// uni.removeStorageSync('token');
			//检测版本更新；
			this.checkAppUpgrade();
		},

		methods: {
			...mapMutations(['login']),
			checkChange() {
				this.savePass = !this.savePass;
				uni.setStorage({
					key: 'save_password',
					data: this.savePass,
					success: function(r) {}
				});
			},
			initPosition() {
				/**
				 * 使用 absolute 定位，并且设置 bottom 值进行定位。软键盘弹出时，底部会因为窗口变化而被顶上来。
				 * 反向使用 top 进行定位，可以避免此问题。
				 */
				this.positionTop = uni.getSystemInfoSync().windowHeight - 100;
			},
			loginByPwd() {
				// 账号 testym
				// 密码 a.123456
				/**
				 * 客户端对账号信息进行一些必要的校验。
				 * 实际开发中，根据业务需要进行处理，这里仅做示例。
				 */
				// if (!this.isAgree) {
				// 	uni.showToast({
				// 		icon: 'none',
				// 		duration: 2000,
				// 		title: '请阅读并同意《用户协议》《隐私政策》'
				// 	});
				// 	return;
				// }
				if (this.username.length < 3) {
					uni.showToast({
						icon: 'none',
						duration: 2000,
						title: '账号最短为 3 个字符'
					});
					return;
				}

				uni.setStorage({
					key: 'username',
					data: this.username,
					success: function(r) {}
				});

				// uni.setStorage({
				// 	key: 'password',
				// 	data: this.password,
				// 	success: function(r) {}
				// });
				const data = {
					username: this.username,
					password: this.password
				};
				let _self = this;
				this.listenLogin();
				uni.showLoading({
					title: '登录中...'
				});
				loginService.loginByPassword(this.username, this.password);

			},

			listenLogin() {
				let _self = this;
				uni.$on('onLoginSuccess', (params) => {
					uni.$off('onLoginSuccess');
					uni.hideLoading();
					_self.onLoginSuccess();
					
				});

				uni.$on('onLoginFail', (error) => {
					console.log(error);
					uni.$off('onLoginFail');
					uni.hideLoading();
					let errorMsg =
						typeof error === 'object' ?
						error.error_msg :
						'登录出错，请检查网络';
					// uni.showModal({
					// 	title: `登录失败: ${errorMsg || "登录出错，请检查网络"}`,
					// });
					// this.$showModal({
					// 	title: '登录失败',
					// 	content: ` ${errorMsg || '登录出错，请检查网络'}`,
					// });
					uni.showToast({
						title: errorMsg,
						icon: 'none',
						duration: 2000
					});
				});
			},

			onLoginSuccess() {
				cacheAreas();
				this.$store.commit('login', this.username);
				uni.reLaunch({
					url: '/pages/enterprise/Index'
				});

				if (this.savePass) {
					uni.setStorage({
						key: 'password',
						data: this.password,
						success: function(r) {}
					});
				}

				//登录成功请求行业信息
				this.getIndustryType()
				
				//登录成功请求客户数据列表将///存到本地
				this.getCustomList()

			},
			//查询客户列表
			async getCustomList() {
				const {
					data
				} = await userCustoms({})
				let orgid  = uni.getStorageSync('ORGID');
				let customList = data
				let objMatch = customList.find(obj => obj.ORGID === orgid);
				uni.setStorageSync('XZQHDM',objMatch.XZQHDM)
			},
			//行业类型
			getIndustryType() {
				getCommoHylx().then(res => {
					if (res.status === '000') {
						res.data.forEach(item => {
							item.text = item.label;
						})
						let industryList = arrayToTree(res.data, null)
						uni.setStorage({
							key: 'storage_industryData',
							data: industryList,
						});
					}
				})
			},

			onLoginFail(err) {},

			toMain(userName) {
				this.login(userName);
				/**
				 * 强制登录时使用reLaunch方式跳转过来
				 * 返回首页也使用reLaunch方式
				 */
				if (this.forcedLogin) {
					uni.reLaunch({
						url: '/pages/warning/Index'
					});
				} else {
					uni.navigateBack();
				}
			},
			toPolicy(index) {
				if (index === 1) {
					uni.navigateTo({
						url: '/pages/login/userAgreement'
					});
				} else {
					uni.navigateTo({
						url: '/pages/login/privacyPolicy'
					});
				}
			},

			//获取版本信息
			checkAppUpgrade() {
				// #ifdef APP-PLUS
				let _self = this;
				appService.setProgressUpdater((percent) => {
					_self.showUpgrade = true;
					_self.upgradeProgress = percent;
					if (percent === 100) {
						_self.showUpgrade = false;
					}
				});
				appService.checkMPAppUpgrade();
				// #endif
			},

			changeAgree() {
				this.isAgree = !this.isAgree;
				uni.setStorage({
					key: 'isAgree',
					data: this.isAgree,
					success: function(r) {}
				});
			},

			autoLogin() {
				this.isAutoLogin = !this.isAutoLogin;
				uni.setStorage({
					key: 'autoLogin',
					data: this.isAutoLogin,
					success: function(r) {}
				});
			}
		}
	};
</script>

<style scoped>
	html {
		-webkit-tap-highlight-color: transparent;
		height: 100%;
	}

	body {
		-webkit-backface-visibility: hidden;
		height: 100%;
	}

	.login-box {
		/* top: 280px */
	}

	.inputwrap .iptclear2 {
		width: 28rpx;
		height: 28rpx;
		position: absolute;
		right: 20rpx;
		top: 50%;
		margin-top: -14rpx;
		transform: none;
	}

	.inputwrap .eyepic {
		height: 24rpx;
		width: 28rpx;
		position: absolute;
		right: 20rpx;
		top: 50%;
		margin-top: -12rpx;
	}

	.upgrade-tip-mask {
		position: fixed;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		z-index: 9999;
		justify-content: flex-start;
	}

	.upgrade-progress-tip {
		width: 72%;
		padding: 20rpx;
		border-radius: 6rpx;
		background-color: #fff;
		box-shadow: 0 0 20rpx 0 #ccc;
		position: fixed;
		top: 70rpx;
		left: 50%;
		margin-left: -36%;
	}

	.disabled-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		background-color: #dddddd;
		border-radius: 34.4174999rpx;
		color: #333333;
		border: none;
		margin: 0 auto;
		height: 68.8425rpx;
		font-size: 27.78rpx;
	}

	.psw {
		outline: none;
		border: none;
	}

	/* .logincell {
		padding: 187px 18px 28px;
	} */

	/*登录*/
	.login-content {
		background: url("~@/static/app/images/login-back.jpg") no-repeat 0 0;
		background-size: 100% 1006.038675rpx;
		position: absolute;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
	}

	/* .login-content>image {
		position: absolute;
		width: 214.975875rpx;
		height: 159.4203rpx;
		top: 138.8925rpx;
		left: 50%;
		-webkit-transform: translate(-50%, 0);
		-moz-transform: translate(-50%, 0);
		-ms-transform: translate(-50%, 0);
		-o-transform: translate(-50%, 0);
		transform: translate(-50%, 0);
	} */

	.login-content .logo {
		width: 100vw;
		position: absolute;
		top: 138.8925rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.login-content .logo image {
		width: 96rpx;
		height: 96rpx;
	}

	.logo .logo-text {
		display: flex;
		align-items: center;
		font-size: 36rpx;
		color: #FFFFFF;
		margin-top: 18rpx;
	}

	.login-box {
		position: absolute;
		top: 421.05rpx;
		left: 50%;
		transform: translate(-50%, 0);
		background-color: white;
		border-radius: 12.075rpx;
		padding: 18.1125rpx 57.3675rpx 57.3675rpx;
		width: 71%;
		box-shadow: 0 6.0375rpx 18.1125rpx rgba(229, 229, 229, .27);
		box-sizing: initial;
	}

	.login-box>div {
		display: flex;
		align-items: center;
	}

	.login-box>div.login-box-name,
	.login-box>div.login-box-password {
		border-bottom: 1px solid #ddd;
		padding: 20.535rpx 0;
		margin-top: 39.2475rpx;
	}

	.login-box>div.forget-password {
		font-size: 25.3623rpx;
		color: #666;
		margin: 24.1575rpx 0 72.4649999rpx 0;
		justify-content: space-between;
	}

	.login-box>div.forget-password input[type=checkbox] {
		border: 1px solid #999;
		width: 26.570025rpx;
		height: 26.570025rpx;
		margin-right: 12.077325rpx;
		vertical-align: -10%;
	}

	.login-box>div.forget-password input[type=checkbox]:checked {
		background: #3580ff url(~@/static/app/images/okic1.png) no-repeat center;
		background-size: 18.11595rpx;
		border-color: #3580ff;
	}

	.login-box>div image {
		width: 24.1545749rpx;
		height: 28rpx;
	}

	.login-box>div input::-webkit-input-placeholder {
		color: #bbb;
	}

	.login-box>div input {
		font-size: 30.1932rpx;
		padding-left: 21.1349999rpx;
	}

	.login-box-password .eyes image {
		width: 31.401rpx;
		height: 20.5313999rpx;

	}

	.login-box-password em.eyes img {
		width: 32.608725rpx;
	}

	.login-box-password em.eyes {
		margin-left: auto;
	}

	.login-box .btn {
		display: block;
		width: 100%;
		background-color: #4874ff;
		border-radius: 34.4174999rpx;
		color: white;
		border: none;
		box-shadow: 0 0 6.0375rpx #4874ff;
		margin: 0 auto;
		height: 68.8425rpx;
		font-size: 27.78rpx;
	}

	.login-content p.message {
		position: absolute;
		font-size: 18.1125rpx;
		color: #999;
		text-align: center;
		width: 100%;
		left: 0;
		bottom: 33.21rpx;
	}

	.login-box>div .pwd-input {
		width: 100%;
		border: none;
		font-size: 30.1932rpx;
		padding-left: 21.1349999rpx;
		line-height: 1.4em;
		outline: none;
	}
</style>