import districtSource from '@/common/city.data.js';
import axios from '@/common/ajaxRequest.js';
import {
	ULR_BASE
} from '@/common/config.js';
/**
 * 获取全国省份及直辖市
 */
export const getProvinces = () => {
	return districtSource.map(district => {
		return {
			code: district.value,
			name: district.label
		}
	});
}

/**
 * 获取全国省份及直辖市
 */
export const getProvince = (provinceCode) => {
	let matchProvinces = districtSource.filter(province => {
		return province.value === provinceCode;
	});

	let province = matchProvinces.length > 0 ? matchProvinces[0] : null;
	if (province) {
		return {
			code: province.value,
			name: province.label
		}
	}
	return null;
}

/**
 * 获取省份下所有市
 * @param {String} provinceCode 省份行政区划
 */
export const getProvinceCities = (provinceCode) => {
	let matchProvinces = districtSource.filter(province => {
		return province.value === provinceCode;
	});

	let province = matchProvinces.length > 0 ? matchProvinces[0] : null;
	if (province) {
		return province.children.map(city => {
			return {
				code: city.value,
				name: city.label
			}
		});
	}
	return [];
}

const buildAreaTree = (data) => {
	//查找省级
	let provinces = data.filter(item => {
		return item.FDM === '000000';
	}).map(item => {
		return {
			...item,
			value: item.XZQHDM,
			text: item.XZQH
		}
	})

	//查找每个省的市级数据
	for (let i = 0; i < provinces.length; i++) {
		let province = provinces[i];
		let citys = data.filter(item => {
			return item.FDM === province.XZQHDM;
		}).map(item => {
			return {
				...item,
				value: item.XZQHDM,
				text: item.XZQH
			}
		})

		// 查找区县数据
		for (let j = 0; j < citys.length; j++) {
			let city = citys[j];
			let district = data.filter(item => {
				return item.FDM === city.XZQHDM;
			}).map(item => {
				return {
					...item,
					value: item.XZQHDM,
					text: item.XZQH
				}
			})
			city.children = district;
		}

		province.children = citys;
	}

	uni.setStorage({
		key: 'storage_key_area',
		data: provinces,
		success: function() {
			console.log('success');
		}
	});

}

const buildAreaTreeFour = (data) => {
	//查找省级
	let provinces = data.filter(item => {
		return item.FDM === '000000';
	}).map(item => {
		return {
			...item,
			value: item.XZQHDM,
			text: item.XZQH
		}
	})

	//查找每个省的市级数据
	for (let i = 0; i < provinces.length; i++) {
		let province = provinces[i];
		let citys = data.filter(item => {
			return item.FDM === province.XZQHDM;
		}).map(item => {
			return {
				...item,
				value: item.XZQHDM,
				text: item.XZQH
			}
		})

		// 查找区县数据
		for (let j = 0; j < citys.length; j++) {
			let city = citys[j];
			let districts = data.filter(item => {
				return item.FDM === city.XZQHDM;
			}).map(item => {
				return {
					...item,
					value: item.XZQHDM,
					text: item.XZQH
				}
			})
			
			// 查找街道数据
			for (let j = 0; j < districts.length; j++) {
				let district = districts[j];
				let street = data.filter(item => {
					return item.FDM === district.XZQHDM;
				}).map(item => {
					return {
						...item,
						value: item.XZQHDM,
						text: item.XZQH
					}
				})
				district.children = street;
			}
			city.children = districts;
		}

		province.children = citys;
	}

	uni.setStorage({
		key: 'storage_key_area_four',
		data: provinces,
		success: function() {
			console.log('success');
		}
	});

}

export const cacheAreas = () => {
	axios.request({
		method: 'GET',
		url: ULR_BASE + '/az/xzqh',
	}).then(res => {
		//解析并缓存区划数据
		let allData = res.data;
		if (allData && allData.length > 0) {
			buildAreaTree(allData);
			buildAreaTreeFour(allData);
		}
	});
}

export const getAreaTree = () => {
	return uni.getStorageSync('storage_key_area')
}

export const getAreaTreeFour = () => {
	return uni.getStorageSync('storage_key_area_four')
}
