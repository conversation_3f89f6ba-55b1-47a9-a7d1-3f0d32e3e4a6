<!-- @format -->

<template>
    <div>
        <div class="weak-signal">
            <div class="state-btn-title">
                <div class="pd-modhd">
                    <strong>车间信息</strong>
                </div>
                <div class="state-btn" @click="getWorkshopData">状态刷新</div>
            </div>
            <div class="inner">
                <div>
                    <dl
                        class="pd-dlbx1"
                        v-for="(item, index) in workshopList"
                        :key="index"
                    >
                        <dt @click="toDetail(item)">
                            <strong>{{ item.SBMC || '-' }}</strong>
                            <image
                                src="/static/app/images/arwrt1.png"
                                class="pd-arw1"
                            />
                        </dt>
                        <dd>
							<p class="p1">
							    <span>所属产线：</span>
							    <i style="color: #4874ff">{{
							        item.CXMC || '-'
							    }}</i>
							</p>
							<p class="p1">
							    <span>检测时间：</span>
							    <i style="color: #4874ff">{{
							        item.SBSJ || '-'
							    }}</i>
							</p>
                            <p class="p1">
                                <span>信号功率：</span>
                                <i style="color: #4874ff">{{
                                    item.SP || '-'
                                }}</i>
                            </p>
                            <p class="p1">
                                <span>信噪比：</span
                                ><i style="color: #4874ff">{{
                                    item.SINR || '-'
                                }}</i>
                            </p>
                            <p class="p1">
                                <span>信号质量：</span
                                ><i style="color: #4874ff">{{
                                    item.RSRQ || '-'
                                }}</i>
                            </p>
                            <p class="p1">
                                <span>小区基站：</span
                                ><i style="color: #4874ff">{{
                                    item.PCI || '-'
                                }}</i>
                            </p>
                        </dd>
                        <div class="gap-gray"></div>
                    </dl>
                </div>
            </div>
        </div>
        <div class="gap"></div>
        <div class="weak-signal">
            <div class="state-btn-title">
                <div class="pd-modhd">
                    <strong>优化情况记录</strong>
                </div>
                <div class="state-btn" @click="$emit('update:showAddDialog',true)">
                    新增记录
                </div>
            </div>

            <div class="inner">
                <dl
                    class="pd-dlbx1"
                    v-for="item in optimizationList"
                    :key="item.FKID"
                >
                    <dd>
                        <p class="p1">
                            <span>优化内容：</span>
                            <i style="color: #4874ff">{{ item.FKNR || '-' }}</i>
                        </p>
                        <p class="p1">
                            <span>优化时间：</span
                            ><i style="color: #999">{{ item.FKSJ || '-' }}</i>
                        </p>
                    </dd>
                </dl>
            </div>
        </div>

    </div>
</template>

<script>
import AddDialog from './AddDialog.vue';
import { zdsblist, yhjllist } from '@/api/iot/weakSignal.js';
export default {
    components: {
        AddDialog
    },
    props: {
        info: {
            type: Object,
            default: () => {}
        },
		showAddDialog:{
			type:Boolean,
			default:false
		}
    },
    data() {
        return {
            workshopList: [],
            optimizationList: [], //优化记录列表
            editId: '',
            firstSituation: {} //优化记录列表第一条
        };
    },
    watch: {
        info: {
            handler: function (newV, oldV) {
                if (newV) {
                    this.getWorkshopData();
                    this.getOptimization();
                }
            },
            deep: true
        }
    },
    mounted() {
		//更新优化记录数据
		uni.$on('getOptimization',()=>{
			this.getOptimization()
		})
	},
    methods: {
        toDetail(item) {
            uni.navigateTo({
                url: `/pages/enterprise/WeakSignalDetail?equip=${encodeURIComponent(
                    JSON.stringify(item)
                )}&info=${encodeURIComponent(JSON.stringify(this.info))}`
            });
        },
        //添加记录信息弹窗
        hide() {
            this.showAddDialog = false;
        },
        //获取车间信息
        getWorkshopData() {
            let obj = {
                WRYBH: this.info.WRYBH,
                FXID: this.info.FXID
            };
            zdsblist(obj).then((res) => {
                if (res.status === '000') {
                    this.workshopList = res.data;
                }
            });
        },
        //获取优化情况记录
        getOptimization() {
            let obj = {
                WRYBH: this.info.WRYBH,
                FXID: this.info.FXID
            };
            yhjllist(obj).then((res) => {
                if (res.status === '000') {
                    this.optimizationList = res.data || [{}];
					//将第一条数据发送给弹窗
					uni.$emit('getOptimizationList',  this.optimizationList[0] )
                }
            });
        }
    }
};
</script>

<style scoped>
.inner {
    padding: 10px;
    background: #fff;
}

.state-btn-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    padding: 20rpx;
}
.state-btn {
    background: #4874ff;
    color: #fff;
    border-radius: 10rpx;
    width: 120rpx;
    line-height: 44rpx;
    text-align: center;
    font-size: 22rpx;
    height: 44rpx;
}

.state-btn-title .pd-modhd {
    border-bottom: none;
    padding-left: 10rpx;
    height: 30px;
    padding-bottom: 0px;
}
.pd-dlbx1 {
    background: #f1f1f1;
    border-radius: 6px;
    margin-bottom: 10px;
    border: 1px solid #eee;
}
.pd-dlbx1 dt {
    border-bottom: 1px solid #e3e3e3;
}
.pd-dlbx1 dd p.p1 {
    background-image: none;
}
.pd-dlbx1 dd p.p1 {
    background-image: none;
    position: relative;
    display: flex;
    flex-direction: row;
}
.pd-dlbx1 dd p.p1 i {
    width: calc(100% - 150rpx);
}
.pd-dlbx1 dd p.p1 span {
    display: block;
    width: 150rpx;
    text-align: left;
}
.pd-dlbx1 dd p.p1::after {
    content: '';
    position: absolute;
    left: 5rpx;
    top: 50%;
    width: 6px;
    height: 6px;
    border-radius: 0;
    background: #4874ff;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
}
</style>
