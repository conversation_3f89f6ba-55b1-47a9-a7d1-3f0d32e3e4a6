import axios from '@/common/ajaxRequest.js'
import {ULR_BASE} from '@/common/config.js'


// 查询试题列表信息默认20条 QUERY_KS_ST_LIST
export const getExerciseList = data => {
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: {
		  service : 'QUERY_KS_ST_LIST'
	  }
    });
};

// 错题保存 ADD_ERROR_ST
export const addErrorExercise = data => {
    return axios.request({
	  showLoading: false,
      method: 'post',
      url: ULR_BASE,
      data: {
		  ...data,
		  service : 'ADD_ERROR_ST'
	  }
    });
};

// 查询错题列表 QUERY_ERROR_ST_LIST
export const getErrorList = data => {
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: {
		  ...data,
		  service : 'QUERY_ERROR_ST_LIST'
	  }
    });
};


// 查询用户试卷列表 QUERY_USER_SJ_LIST
export const getUserExamList = data => {
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: {
		  ...data,
		  service : 'QUERY_USER_SJ_LIST'
	  }
    });
};


// 查询用户试卷列表 QUERY_USER_SJ_LIST
export const AddExam = data => {
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: {
		  ...data,
		  service : 'ADD_USER_SJ_ST'
	  }
    });
};


// 更新用户试卷答题记录 UPDATE_USER_SJ_ST
export const UpadateExamItem = data => {
    return axios.request({
	  showLoading: false,
      method: 'post',
      url: ULR_BASE,
      data: {
		  ...data,
		  service : 'UPDATE_USER_SJ_ST'
	  }
    });
};


// 更新用户考试记录,提交考试 UPDATE_USER_KS_JL
export const UpadateExam = data => {
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: {
		  ...data,
		  service : 'UPDATE_USER_KS_JL'
	  }
    });
};


// 查询用户考试答题详情 
export const GetExamDetail = data => {
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: {
		  ...data,
		  service : 'QUERY_USER_KS_DT_XQ'
	  }
    });
};

//查询用户考试成绩列表 QUERY_USER_KSCJ_LIST
export const GetExamList = data => {
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: {
		  ...data,
		  service : 'QUERY_USER_KSCJ_LIST'
	  }
    });
};


