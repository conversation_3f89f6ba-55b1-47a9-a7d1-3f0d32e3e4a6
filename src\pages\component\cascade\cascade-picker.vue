<template>
	<picker-view
		class="cascade-picker"
		:value="cursorIndexs"
		@change="onChange">
		<picker-view-column 
			v-for="(column, colIndex) in columnArray"
			:key="colIndex">
			<view 
				style="text-align: center; line-height: 60rpx;"
				v-for="(item, index) in column"
				:key="index">
				{{item[displayField] || item}}
			</view>
		</picker-view-column>
	</picker-view>
</template>

<script>
	export default {
		name: 'CascadePicker',
		props: {
			range: {
				type: Array,
				default: () => {
					return [];
				}
			},
			
			displayField: {
				type: String,
				default: 'text'
			},
			
			column: {
				type: Number,
				default: 2
			},
			
			value: {
				type: Array,
				default: null
			}
		},
		
		data() {
			let indexs = new Array(this.column);
			indexs.fill(0);
			if(this.value) {
				indexs = this.value;
			}
			return {
				columnArray: [],
				cursorIndexs: indexs,
				//因为钉钉不支持属性类型为Function的prop，所以loader只能通过setLoader方法设置
				loader: null
			}
		},
		
		mounted() {
			if(this.range && this.range.length > 0) {
				this.columnArray = this.range;
			}
			this.$emit('mounted');
		},
		
		methods: {
			setLoader(loader) {
				this.loader = loader;
				if(this.loader) {
					this.initRangeWithLoader();
				} else {
				}
			},
			
			/**
			 * 使用loder初始化列数据
			 */
			initRangeWithLoader(parent) {
				if(this.columnArray && this.columnArray.length === 0){
					this.loader(null)
						.then(firstColumnItems => {
							let currentColumn = 0;
							this.$set(this.columnArray, currentColumn, firstColumnItems);
							this.onColumnChange(currentColumn, 0);
						})
						.catch(error => {
						})
				}
			},
			
			/**
			 * 列的值变更回调
			 * @param {Object} event
			 */
			onChange(event) {
				let indexs = event.detail.value;
				for(let i = 0; i < this.cursorIndexs.length; i++) {
					let changeIndex = indexs[i];
					let originalIndex = this.cursorIndexs[i];
					if(changeIndex !== null && changeIndex !== originalIndex) {
						this.onColumnChange(i, changeIndex);
						break;
					}
				}
			},
			
			/**
			 * 选择指定列
			 * @param {Object} column 选择的列
			 * @param {Object} itemIndex 选择的列的项
			 */
			onColumnChange(column, itemIndex) {
				this.$set(this.cursorIndexs, column, itemIndex);
				if(column < this.column - 1 && this.loader) {
					let selectedItem = this.columnArray[column][itemIndex];
					this.loader(selectedItem)
						.then(nextColumnItems => {
							let nextColumn = column + 1;
							this.$set(this.columnArray, nextColumn, nextColumnItems);
							this.onColumnChange(nextColumn, 0);
						})
						.catch(error => {
						})
				} else {
					this.$emit('change', this.cursorIndexs, this.columnArray);
				}
			}
		}
	}
</script>

<style scoped>
	.cascade-picker {
		width: 100%;
		height: 400rpx;
	}
</style>
