<!-- @format -->

<template>
    <div style="height: 100%; position: relative">
        <header class="header blue2">
            <i class="ic-back" @click="back"></i>
            <div class="title">{{ title || '-' }}</div>
        </header>

        <div>
            <div class="xqmod xqmod-top">
                <div class="yy-tit1">
                    <h1>报备信息</h1>
                </div>
                <ul
                    class="pd-ullst1"
                    :class="{
                        'pd-ullst1-detail': pageType == 'detail'
                    }"
                >
                    <li class="flex">
                        <em><label class="redx">*</label>报备人员</em>
                        <input
                            type="text"
                            :disabled="pageType == 'detail'"
                            placeholder="请输入报备人员"
                            v-model="info.BBR"
                            class="pd-inptxt1"
                        />
                    </li>

                    <li class="flex">
                        <em><label class="redx">*</label>联系电话</em>
                        <input
                            type="text"
                            :disabled="pageType == 'detail'"
                            placeholder="请输入联系电话"
                            v-model="info.BBRDH"
                            class="pd-inptxt1"
                        />
                    </li>
                    <li class="flex">
                        <em><label class="redx">*</label>报备类型：</em>
                        <i class="slec backtext" @click="toggleWarnType">{{
                            info.YJLX == '1'
                                ? '企业或产线停产/停工/完工'
                                : info.YJLX == '2'
                                ? '设备故障'
                                : info.YJLX == '3'
                                ? '减排期间持续生产'
                                : '请选择报备类型'
                        }}</i>
                    </li>
                    <li class="flex">
                        <em><label class="redx">*</label>开始时间</em>
                        <!-- 新增页 -->
                        <span v-if="pageType == 'add'">
                            <!-- {{ info.KSSJ || '请选择开始时间' }} -->
                            <u-picker
                                mode="time"
                                v-model="showSelectKSSJ"
                                :params="params"
                                @confirm="sdConfirmKSSJ"
                            >
                            </u-picker>
                            <span
                                class="timefont"
                                v-if="pageType == 'add'"
                                @click="toggleKSSJ"
                                :class="{ graytext: info.KSSJ == '' }"
                                >{{ info.KSSJ || '请选择开始时间' }}</span
                            >
                        </span>
                        <!-- 详情页 -->
                        <span v-if="pageType == 'detail'" class="timefont2">{{
                            info.KSSJ || '-'
                        }}</span>
                    </li>
                    <li class="flex">
                        <em><label class="redx">*</label>结束时间</em>
                        <!-- 新增页 -->
                        <span v-if="pageType == 'add'">
                            <u-picker
                                mode="time"
                                :showForever="showForever"
                                v-model="showSelectJZSJ"
                                :params="params"
                                @confirm="sdConfirmJZSJ"
                                @forever="setForever"
                            >
                            </u-picker>
                            <span
                                class="timefont"
                                v-if="pageType == 'add'"
                                @click="toggleJZSJ"
                                :class="{ graytext: info.JZSJ == '' }"
                                >{{ info.JZSJ || '请选择结束时间' }}</span
                            >
                        </span>

                        <!-- 详情页 -->
                        <span v-if="pageType == 'detail'" class="timefont2">{{
                            info.JZSJ || '永久'
                        }}</span>
                    </li>
                    <li class="flex">
                        <em><label class="redx">*</label>报备企业</em>
                        <input
                            class="pd-inptxt1 enterprise"
                            type="text"
                            :class="{ slec: info.WRYMC == '' }"
                            :disabled="pageType == 'detail' || showEnterprise"
                            placeholder="请输入报备企业"
                            v-model="info.WRYMC"
                            @input="inputFunc(info.WRYMC)"
                        />
                    </li>
                    <li class="flex">
                        <em
                            ><label class="redx"
                                ><i v-if="info.YJLX == '2'">*</i></label
                            >
                            <span v-if="info.YJLX == '2'">报备治污线</span>
                            <span v-else-if="info.YJLX == '1'">报备生产线</span>
                            <span v-else-if="info.YJLX == '3'">报备生产线</span>
                            <span v-else>报备产治污线</span>
                        </em>
                        <!-- 报备治污线 -->
                        <i
                            v-if="info.YJLX == '2'"
                            :class="{
                                slec:
                                    info.BBCXMC == '' || info.BBCXMC == '请选择'
                            }"
                            @click="toggleDuePollut"
                            >{{ info.BBCXMC || '请选择' }}</i
                        >
                        <!-- 报备生产线 -->
                        <i
                            v-else-if="info.YJLX == '1' || info.YJLX == '3'"
                            :class="{
                                slec:
                                    info.BBCXMC == '' || info.BBCXMC == '请选择'
                            }"
                            @click="toggleDuePollut"
                        >
                            {{ info.BBCXMC || '请选择' }}
                        </i>
                        <i v-else class="slec"> {{ info.BBCXMC }}</i>

                        <!-- <i v-if="pageType == 'detail'" class="slec"
						 @click="toggleDuePollut">
						 {{
						    info.BBCXMC || '-'
						  }}
						</i> -->
                    </li>
                    <li class="flex">
                        <em
                            ><label class="redx"
                                ><i v-if="info.YJLX == '2'">*</i></label
                            >报备设备<br
                        /></em>
                        <i
                            :class="{
                                slec:
                                    info.BBSBMC == '' || info.BBSBMC == '请选择'
                            }"
                            @click="toggleZywrShow"
                            >{{ info.BBSBMC || '请选择' }}</i
                        >
                    </li>
                    <li class="flex">
                        <em><label class="redx">*</label>报备原因</em>
                        <input
                            type="text"
                            :disabled="pageType == 'detail'"
                            placeholder="请输入报备原因"
                            v-model="info.BBYY"
                            class="pd-inptxt1"
                        />
                    </li>
                    <!-- <swiper class="swiper">
						<swiper-item v-for='(item,index) in enterpriseFileList' :key='index' class="slide">
							<div class="video">
								<video v-if="getFileType(item.WJMC) == 'video'" class="videoPlay"
									src="https://img.cdn.aliyun.dcloud.net.cn/guide/uniapp/%E7%AC%AC1%E8%AE%B2%EF%BC%88uni-app%E4%BA%A7%E5%93%81%E4%BB%8B%E7%BB%8D%EF%BC%89-%20DCloud%E5%AE%98%E6%96%B9%E8%A7%86%E9%A2%91%E6%95%99%E7%A8%8B@20200317.mp4"
									controls>
								</video>
							</div>
							<image v-if="getFileType(item.WJMC) == 'image'" class="img" mode="scaleToFill"
								:src="getFileUrl(item)" alt="" style="width:600rpx;height:400rpx" />
						</swiper-item>
					</swiper>
					<!-- <v-swiper :list="list4" keyName="url" :autoplay="false"></v-swiper> -->

                    <!-- <cl-upload v-model="enterpriseFileList" ></cl-upload> -->

                    <li class="nfx">
                        <em class="zmzl">
                            <label class="redx">*</label>企业证明文件
                        </em>
                        <div class="gap"></div>
                        <AppendixImage
                            ref="appendix_ZMCL"
                            :list.sync="fileList"
                            fileTypeKeyword="ZDFJ"
                            childTypeKeyword="ZMCL"
                            :isCanEdit="isCanEdit"
                            :uploadId="info.BBID"
                        ></AppendixImage>
                        <!-- <Appendix ref="appendix_ZMCL" :list.sync="enterpriseFileList" childTypeKeyword="ZMCL"
							:isCanEdit="pageType == 'add'" :uploadId="info.BBID" :isShowVideo="isShowVideo" @chooseMediaType="chooseMediaType">
						</Appendix> -->
                    </li>
                    <li class="nfx">
                        <em class="zmzl"
                            ><label class="redx">*</label>现场照片
                        </em>
                        <div class="gap"></div>
                        <AppendixImage
                            ref="appendix_QYBB_ZP"
                            :list.sync="mediaList"
                            fileTypeKeyword="ZDFJ"
                            childTypeKeyword="QYBB_ZP"
                            :isCanEdit="isCanEdit"
                            :uploadId="info.BBID"
                        ></AppendixImage>
                        <!-- <Appendix ref="appendix_QYBB_ZP" :list.sync="mediaList" childTypeKeyword="1"
							:isCanEdit="pageType == 'add'" :uploadId="info.BBID" :isShowVideo="isShowVideo"
							  @chooseMediaType="chooseMediaType">
						</Appendix> -->
                    </li>
                </ul>
            </div>

            <div class="xqmod" v-if="pageType == 'detail'">
                <div class="yy-tit1">
                    <h1>审核信息</h1>
                </div>
                <ul class="pd-ullst1">
                    <li class="flex" v-if="curReviewState == 'YSH'">
                        <em><label class="redx">*</label>审核时间</em>
                        <i class="rfont">
                            <!-- 待审核 -->
                            <!-- 	<span v-if="curReviewState == 'DSH'">
								<u-picker mode="time" v-model="showSelectSHSJ" :params="params" @confirm="sdConfirm">
								</u-picker>
								<span class="timefont" @click="showSelectSHSJ = true"
									:class="{graytext:model.SHSJ == ''}">{{model.SHSJ || '请选择审核时间'}}</span>
							</span> -->
                            <!-- 已审核 -->
                            <span class="timefont2">{{
                                model.SHSJ || '-'
                            }}</span>
                        </i>
                    </li>
                    <li class="flex">
                        <em><label class="redx">*</label>是否通过</em>
                        <span class="rbtnbox" v-if="curReviewState == 'DSH'">
                            <p
                                class="passbtn2"
                                :class="{ ispass: isPass == '2' }"
                                @click="changeReview('2')"
                            >
                                驳回
                            </p>
                            <p
                                class="passbtn2"
                                :class="{ ispass: isPass == '3' }"
                                @click="changeReview('3')"
                            >
                                通过
                            </p>
                        </span>
                        <span class="rbtnbox" v-if="curReviewState == 'YSH'">
                            <span v-if="model.SHZT == '2'">驳回</span>
                            <span v-if="model.SHZT == '3'">通过</span>
                        </span>
                    </li>
                    <li class="flex">
                        <em><label class="redx">*</label>备注信息</em>
                        <input
                            type="text"
                            placeholder="请输入备注"
                            v-model="model.SHXX"
                            :disabled="curReviewState == 'YSH'"
                            class="rfont text-right"
                        />
                    </li>
                </ul>
            </div>
        </div>
        <div class="gap garygap"></div>
        <div class="gap"></div>
        <div class="gap"></div>
        <div class="gap"></div>
        <div class="gap"></div>
        <ul
            class="foot-btnbox flx1 ac jb"
            v-if="
                (pageType == 'detail' && curReviewState == 'DSH') ||
                pageType == 'add'
            "
        >
            <li @click="cancelReview">取消</li>
            <li class="confirmbtn" @click="save">确定</li>
        </ul>

        <!-- 企业选择器 -->
        <u-select
            label-name="WRYMC"
            value-name="WRYBH"
            v-model="showEnterprise"
            :list="enterpriseList"
            @cancel="cancelEnterprise"
            @confirm="changeEnterprise"
        ></u-select>

        <!-- 生产线/治污线选择器 -->
        <u-select
            label-name="CXMC"
            value-name="CXID"
            v-model="showDuePollut"
            :list="duePollutList"
            @confirm="changeDuePollut"
        ></u-select>

        <!--报备设备选择器单选 -->
        <u-select
            label-name="SBMC"
            value-name="SBID"
            v-model="zywrShow"
            :list="duePollutEquipList"
            @confirm="changeEquip"
        ></u-select>

        <!-- 报备设备选择器 多选-->
        <!-- <u-popup
	            v-model="zywrShow"
	            mode="bottom"
	            width="100%"
	            height="44%"
	            border-radius="20"
	        >
	            <div class="opration">
	                <span @click="cancel" class="cancel">取消</span>
	                <span @click="confirm" class="confirm">确定</span>
	            </div>
	            <div class="listWarp">
	                <p @click="selectAllWrw" :class="isChooseAllEquip ? 'on' : ''">
	                    全部
	                </p>
	                <p
	                    v-for="(item, index) in duePollutEquipList"
	                    @click="selectWrw(item)"
	                    :class="selectWrwList.includes(item) ? 'on' : ''"
	                    :key="index"
	                >
	                    {{ item.SBMC + '-' + item.SBID.slice(0, 8) }}
	                </p>
	            </div>
	        </u-popup> -->

        <!-- 媒体选择器 -->
        <u-popup
            v-model="mediaPopShow"
            mode="bottom"
            width="100%"
            height="25%"
            border-radius="20"
        >
            <div class="listWarp">
                <p @click="toUploadMedia('image')">上传照片</p>
                <p @click="toUploadMedia('video')">上传视频</p>
                <p @click="cancelMediaPop">取消</p>
                <!-- 	<p v-for="(item, index) in List" @click="selectWrw(item)"
					:class="selectWrwList.includes(item) ? 'on' : ''">
					{{ item.SBMC + '-' +item.SBID.slice(0,8) }}
				</p> -->
            </div>
        </u-popup>

        <!-- 报备类型选择器 -->
        <u-select
            v-model="showWarnType"
            :list="warnTypeList"
            @confirm="changeWarnType"
        ></u-select>
    </div>
</template>

<script>
import { guid } from '@/common/uuid.js';
import { getQyxx } from '@/api/iot/enterprise.js';
import { getList } from '@/api/iot/realtime.js';
import {
    getCzwxList,
    getZwsbList,
    getZdsbList,
    getQybbAdd,
    getExamine
} from '@/api/iot/filing.js';
import Appendix from '@/pages/component/Appendix';
import AppendixImage from '@/pages/component/AppendixImage';
import {
    API_LOGIN_SERVICE_URL,
    LOGIN_ULR_BASE,
    UPLOAD_URL,
    DOWNLOAD_URLZDY,
    PREVIEW_FILE_URL
} from '@/common/config.js';
export default {
    name: '',
    components: {
        Appendix,
        AppendixImage
    },
    data() {
        return {
            fileList: [],
            model: {
                BBID: '', //报备id
                SHR: '', //审核人
                SHSJ: '', //审核时间
                SHZT: '', //审核状态
                SHXX: '' //审核信息
            },
            info: {
                BBID: '1111',
                BBSJ: '2022-10-10',
                BBYY: '',
                BBQX: '2022-06-22 21:12至2022-08-22 21:12',
                SHZT: '1',
                WRYMC: '',
                WRYBH: '',
                KSSJ: ''
            },
            pageType: '', //add新增报备 detail 查看详情
            rules: {
                BBR: {
                    required: true,
                    message: '请填写报备人员'
                },
                BBRDH: {
                    required: true,
                    message: '请填写联系电话'
                },
                KSSJ: {
                    required: true,
                    message: '请选择开始时间'
                },
                JZSJ: {
                    required: true,
                    message: '请选择结束时间'
                },

                YJLX: {
                    required: true,
                    message: '请填写预警类型'
                },
                WRYMC: {
                    required: true,
                    message: '请填写报备企业'
                },
                BBCXMC: {
                    required: false,
                    message: '请选择报备产线'
                },
                BBSB: {
                    required: false,
                    message: '请选择报备设备'
                },
                BBYY: {
                    required: true,
                    message: '请填写报备原因'
                }
            },
            showCalendar: false,
            curDateParam: '',
            duePollutList: [], //治污线列表
            showDuePollut: false,
            userinfo: {},
            enterpriseList: [], //企业列表
            showEnterprise: false,
            duePollutEquipList: [], //设备列表
            showDuePollutEquip: false,
            selectWrwList: [], //选中设备list
            zywrShow: false,
            timer: null,
            isPass: '',
            curReviewState: '', //详情页当前审核状态
            modelRules: {
                SHSJ: {
                    required: true,
                    message: '请填写审核时间'
                },
                SHZT: {
                    required: true,
                    message: '请选择审核状态'
                },
                SHXX: {
                    required: true,
                    message: '请填写审核备注信息'
                }
            },
            params: {
                year: true,
                month: true,
                day: true,
                hour: true,
                minute: true,
                second: false
            },
            showSelectKSSJ: false, //开始时间选择器显示隐藏
            showSelectJZSJ: false, //结束时间选择器显示隐藏
            showSelectSHSJ: false, //审核时间选择器显示隐藏
            mediaList: [],
            mediaPopShow: false,
            showWarnType: false, //报备预警类型
            warnTypeList: [
                {
                    label: '企业或产线停产/停工/完工',
                    value: '1'
                },
                {
                    label: '设备故障',
                    value: '2'
                },
                {
                    label: '减排期间持续生产',
                    value: '3'
                }
            ],
            isShowVideo: true,
            isCanEdit: true,
            title: '',
            showForever: true,
            // 上一次选择的企业
            currentSelectCompany: null,
            //上一次选择的生产线
            currentSelectScx: null,
            // 上一次选择的生产设备
            currentSelectSb: null
        };
    },
    async onLoad(options) {
        let userinfo = uni.getStorageSync('user_info');
        this.userinfo = userinfo;
        this.pageType = options.type; //新增add 详情 detail

        if (this.pageType == 'add') {
            //新增报备
            this.title = '新增报备';
            this.info = {
                BBID: guid(),
                SHZT: '1', //新增状态为待审核
                BBR: this.userinfo.name,
                BBRDH: this.userinfo.mobile,
                KSSJ: this.$dayjs().format('YYYY-MM-DD HH:mm'),
                JZSJ: '',
                YJLX: '1', //报备类型
                WRYBH: '', //报备企业
                BBCX: '', //报备产线
                BBCXMC: '',
                BBSB: '', //报备设备
                BBSBMC: '', //报备设备
                BBYY: '', //报备原因
                ORGID: this.userinfo.ORGID
            };
        } else if (this.pageType == 'detail') {
            this.isCanEdit = false;
            //报备详情
            this.title = '报备详情';
            this.curReviewState = options.reviewState; //判断是否需要审核
            let info = JSON.parse(decodeURIComponent(options.info));

            for (let key in info) {
                if (
                    info[key] == '' ||
                    info[key] == null ||
                    info[key] == 'null'
                ) {
                    info[key] = '-';
                }
            }
            this.info = info;
            console.log('info', this.info);
            this.info.KSSJ = this.info.KSSJ.slice(0, 16);
            this.info.JZSJ =
                this.info.JZSJ == '-' ? '永久' : this.info.JZSJ.slice(0, 16);
            if (this.curReviewState == 'YSH') {
                this.model.SHXX = this.info.SHXX || '-';
                this.model.SHZT = this.info.SHZT;
                this.model.SHSJ = this.info.SHSJ.slice(0, 16);
            }
            this.model.BBID = this.info.BBID;
            this.model.SHR = this.userinfo.id;
            //获取附件list
            this.$nextTick(() => {
                this.$refs.appendix_ZMCL.getFileList();
                this.$refs.appendix_QYBB_ZP.getFileList();
            });
        }
    },
    mounted() {},
    methods: {
        toggleKSSJ() {
            this.showSelectKSSJ = true;
            this.isShowVideo = false;
        },
        toggleJZSJ() {
            this.showSelectJZSJ = true;
            this.isShowVideo = false;
        },

        //获取视频路径
        getVideoUrl(item) {
            //console.log(PREVIEW_FILE_URL + item.MLSY.slice(35));
            return PREVIEW_FILE_URL + item.MLSY.slice(35);
        },
        //获取文件路径
        getFileUrl(item) {
            return DOWNLOAD_URLZDY + item.WJID;
        },
        // 报备设备单选
        changeEquip(v) {
            this.currentSelectSb = v[0];
            this.info.BBSB = v[0].value;
            this.info.BBSBMC = v[0].label;
        },
        //预警类型报备选择器
        changeWarnType(v) {
            this.info.YJLX = v[0].value;
            //如果报备类型为设备故障，报备治污线和报备设备为必填
            if (this.info.YJLX == 2) {
                this.rules.BBCXMC.required = true;
                this.rules.BBSB.required = true;
            } else {
                this.rules.BBCXMC.required = false;
                this.rules.BBSB.required = false;
            }
            //如果报备类型为企业或产线停产/停工/完工,结束时间可选永久
            if (this.info.YJLX == 1) {
                this.showForever = true;
            } else {
                this.showForever = false;
            }
            //报备类型切换清空结束时间 报备设备 报备产线
            this.info.JZSJ = '';
            this.info.BBCX = '';
            this.info.BBCXMC = '';
            this.info.BBSB = '';
            this.info.BBSBMC = '';
            if (this.info.WRYBH) {
                this.getDuePollutList();
            }
        },
        //获取产治污线
        async getDuePollutList() {
            //切换生产线or治污线
            let type = '';
            if (this.info.YJLX == '2') {
                type = 'ZWX';
            } else if (this.info.YJLX == '1' || this.info.YJLX == '3') {
                type = 'SCX';
            }
            let { sjqx } = this.userinfo;
            let params = {
                ORGID: sjqx,
                WRYBH: this.info.WRYBH,
                SJLX: type //SCX 生产线 ZWX 治污线
            };
            const { data } = await getCzwxList(params);
            this.duePollutList = data;
            if (this.duePollutList.length > 0) {
                this.duePollutList.unshift({
                    CXMC: '请选择',
                    CXID: ''
                });
            }
        },
        //显示选择报备预警类型
        toggleWarnType() {
            if (this.pageType == 'detail') {
                return;
            }
            this.showWarnType = true;
            this.isShowVideo = false;
        },
        //取消选择
        cancelMediaPop() {
            this.mediaPopShow = false;
        },
        //选择媒体类型
        chooseMediaType(v) {
            //拿到哪个组件需要上传
            this.curChildTypeKeyword = v;
            this.mediaPopShow = true;
            this.isShowVideo = false;
        },
        //去上传图片/视频
        toUploadMedia(mediaType) {
            if (this.curChildTypeKeyword == 'ZMCL') {
                this.$refs.appendix_ZMCL.addFile('ZMCL', mediaType);
            } else if (this.curChildTypeKeyword == 'QYBB_ZP') {
                this.$refs.appendix_QYBB_ZP.addFile('QYBB_ZP', mediaType);
            }
            this.mediaPopShow = false;
        },
        //上传视频
        // toUploadVideo(childTypeKeyword){
        // 	if(this.curChildTypeKeyword == 'ZMCL'){
        // 		this.$refs.appendix_ZMCL.addImage()
        // 	}else if(this.curChildTypeKeyword == 'QYBB_ZP'){
        // 		this.$refs.appendix_QYBB_ZP.addVideo()
        // 	}
        // },

        //显示选择报备产线
        toggleDuePollut() {
            if (this.pageType == 'detail') {
                return;
            } else if (this.info.WRYBH == '') {
                //选择产线前先选择企业
                uni.showModal({
                    title: '提示',
                    cancelText: '取消',
                    content: '请先填写有效的报备企业',
                    success: function (res) {
                        if (res.confirm) {
                            console.log('用户点击确定');
                            return;
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                            return;
                        }
                    }
                });
            } else if (this.duePollutList.length == 0) {
                //无产线可选
                wx.showToast({
                    icon: 'none',
                    title: '暂无相关产线数据'
                });

                return;
            } else {
                this.showDuePollut = true;
            }
        },
        //显示选择报备设备
        toggleZywrShow() {
            if (this.pageType == 'detail') {
                return;
            } else if (this.info.BBCX == '') {
                //选择设备前先选择产线
                let text =
                    this.info.YJLX == '2'
                        ? '请先填写报备治污线'
                        : '先填写报备生产线';
                uni.showModal({
                    title: '提示',
                    cancelText: '取消',
                    content: text,
                    success: function (res) {
                        if (res.confirm) {
                            console.log('用户点击确定');
                            return;
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                            return;
                        }
                    }
                });
            } else if (this.duePollutEquipList.length == 0) {
                //无设备可选
                wx.showToast({
                    icon: 'none',
                    title: '暂无相关设备数据'
                });
            } else {
                this.zywrShow = true;
            }
        },

        //审核时间
        sdConfirm(v) {
            let time =
                v.year +
                '-' +
                v.month +
                '-' +
                v.day +
                '  ' +
                v.hour +
                ':' +
                v.minute;
            this.model.SHSJ = time;
        },
        //时间差对比
        diffTime(endTime, startTime) {
            let diff = 0;
            diff = this.$dayjs(endTime).diff(startTime, 'min');
            return diff;
        },
        //开始时间
        sdConfirmKSSJ(v) {
            let time =
                v.year +
                '-' +
                v.month +
                '-' +
                v.day +
                '  ' +
                v.hour +
                ':' +
                v.minute;
            if (this.info.JZSJ) {
                if (this.diffTime(this.info.JZSJ, time) < 0) {
                    let self = this;
                    uni.showModal({
                        title: '提示',
                        cancelText: '取消',
                        content: '结束时间需要大于开始时间',
                        success: function (res) {
                            if (res.confirm) {
                                console.log('用户点击确定');
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        }
                    });
                } else {
                    this.info.KSSJ = time;
                }
            } else {
                this.info.KSSJ = time;
            }
            this.isShowVideo = true;
        },
        //设为永远
        setForever() {
            this.info.JZSJ = '永久';
        },
        //结束时间
        sdConfirmJZSJ(v) {
            let time =
                v.year +
                '-' +
                v.month +
                '-' +
                v.day +
                '  ' +
                v.hour +
                ':' +
                v.minute;
            if (this.info.KSSJ) {
                if (this.diffTime(time, this.info.KSSJ) <= 0) {
                    let self = this;
                    uni.showModal({
                        title: '提示',
                        cancelText: '取消',
                        content: '结束时间需要大于开始时间',
                        success: function (res) {
                            if (res.confirm) {
                                console.log('用户点击确定');
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        }
                    });
                } else {
                    this.info.JZSJ = time;
                }
            } else {
                this.info.JZSJ = time;
            }
        },
        //修改审核状态
        changeReview(v) {
            this.isPass = v;
            this.$set(this.model, 'SHZT', v);
        },
        //全选非全选
        selectAllWrw() {
            if (this.duePollutEquipList.length == this.selectWrwList.length) {
                this.selectWrwList = []; // 判断是否已全部选中，是则清空已选列表
            } else {
                this.selectWrwList = [];
                this.duePollutEquipList.forEach((item) => {
                    this.selectWrwList.push(item); // 否则将未选中的全部加入已选列表中
                });
            }
        },
        //选择治污设备
        selectWrw(wrw) {
            let index = this.selectWrwList.findIndex((item) => {
                return item.SBID == wrw.SBID;
            });
            if (index != -1) {
                //存在去除
                this.selectWrwList.splice(index, 1);
            } else {
                this.selectWrwList.push(wrw);
            }
        },

        //取消选择治污设备
        cancel() {
            this.zywrShow = false;
            this.selectWrwList = [];
        },
        //治污设备选择
        confirm() {
            this.zywrShow = false;
            if (this.selectWrwList.length == this.duePollutEquipList.length) {
                this.info.BBSBMC = '全部';
                this.info.BBSB = '';
            } else {
                this.info.BBSBMC = this.selectWrwList
                    .map((item) => item.SBMC + '-' + item.SBID.slice(0, 8))
                    .join(',');
                this.info.BBSB = this.selectWrwList
                    .map((item) => item.SBID)
                    .join(',');
            }
        },
        inputFunc(keyword) {
            if (keyword && keyword.length < 2) {
                if (this.timer) {
                    clearTimeout(this.timer);
                }
                this.timer = setTimeout(() => {
                    uni.showToast({
                        title: '请输入2位以上字符搜索',
                        icon: 'none'
                    });
                }, 500);
                return;
            } else {
                if (this.timer) {
                    clearTimeout(this.timer);
                }
                this.timer = setTimeout(() => {
                    if (this.info.WRYMC) {
                        this.getEnterpriseList();
                    } else {
                        //输入企业为空时
                        this.showEnterprise = false;
                        this.info.WRYBH = '';
                        //清空企业列表，治污线列表，设备列表，以及选中的值
                        this.enterpriseList = [];
                        this.duePollutEquipList = [];
                        this.info.BBCX = '';
                        this.info.BBCXMC = '';
                        this.selectWrwList = [];
                        this.info.BBSB = '';
                        this.info.BBSBMC = '';
                    }
                }, 500);
            }
        },
        // 初始化企业数据
        async getEnterpriseList(v) {
            let self = this;
            let params = {
                // pageSize: 1000,
                // pageNum: 1,
                ALLSEARCH: this.info.WRYMC,
                ZT: '1', //已安装状态
                ORGID: this.userInfo
            };
            let { data } = await getQyxx(params);
            this.enterpriseList = data;
            if (this.enterpriseList.length) {
                uni.hideKeyboard();
                this.showEnterprise = true;
            } else {
                this.formData.wrymc = '';
                uni.showModal({
                    title: '提示',
                    cancelText: '取消',
                    content: '请输入正确的企业名称',
                    showCancel: false,
                    success: function (res) {
                        if (res.confirm) {
                            console.log('用户点击确定');
                            self.formData.wrymc = '';
                            return;
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                            self.formData.wrymc = '';
                            return;
                        }
                    }
                });
            }
        },
        // 初始化企业数据
        // async initEnterpriseList(v) {
        //     let params = {
        //         pageSize: 1000,
        //         pageNum: 1,
        //         ZT: '',
        //         WRYMC: this.info.WRYMC,
        //         XZQHDM: '',
        //         url: 'all'
        //     };
        //     let { data } = await getList(params);
        //     this.enterpriseList = data;
        //     if (this.enterpriseList.length != 0) {
        //         uni.hideKeyboard();
        //         this.showEnterprise = true;
        //         this.isShowVideo = false;
        //     } else {
        //         this.info.WRYMC = '';
        //         uni.showModal({
        //             title: '提示',
        //             cancelText: '取消',
        //             content: '请输入正确的企业名称',
        //             success: function (res) {
        //                 if (res.confirm) {
        //                     console.log('用户点击确定');
        //                     this.info.WRYMC = '';
        //                     return;
        //                 } else if (res.cancel) {
        //                     console.log('用户点击取消');
        //                     this.info.WRYMC = '';
        //                     return;
        //                 }
        //             }
        //         });
        //     }
        // },
        //获取治污设备
        async getDuePollutEquipList() {
            let { sjqx } = this.userinfo;
            let params = {
                ORGID: sjqx,
                CXID: this.info.BBCX
            };
            const { data } = await getZdsbList(params);
            this.duePollutEquipList = data;
            if (this.duePollutEquipList.length > 0) {
                this.duePollutEquipList.unshift({
                    SBMC: '请选择',
                    SBID: ''
                });
            }
            //选中设备数组先清空再全选
            //this.selectWrwList = [];
            //this.selectWrwList = this.duePollutEquipList.map((v) => v);
        },
        //治污线选择器
        changeDuePollut(v) {
            this.currentSelectScx = v[0];
            this.info.BBCX = v[0].value;
            this.info.BBCXMC = v[0].label;
            //清空设备
            this.selectWrwList = [];
            this.info.BBSBMC = '';
            this.info.BBSB = '';
            //获取生产or治污设备
            this.getDuePollutEquipList();
        },
        //选择展示得设备名称
        getEquipName(item) {
            let str = '';
            if (item.SBMC == '全部') {
                str = item.SBMC;
            } else {
                str = item.SBMC + '-' + item.SBID.slice(0, 8);
            }
            return str;
        },
        // 取消企业选择
        cancelEnterprise() {
            // if(Object.keys(this.currentSelectCompany).length){
            // 	// 如果上一次选择过企业，用户想要更换，但是点击了取消,需要回显上一次选择的企业，产线，设备等
            // 	this.info.WRYBH = this.currentSelectCompany.value
            // 	this.info.WRYMC = this.currentSelectCompany.label
            // 	this.info.BBCX = this.currentSelectScx.value
            // 	this.info.BBCXMC = this.currentSelectScx.label
            // 	this.info.BBSB = this.currentSelectSb.value
            // 	this.info.BBSBMC = this.currentSelectSb.label
            // }else{
            // 	this.info.WRYMC = ''
            // 	this.info.WRYBH=''
            // 	this.info.BBCX = ''
            // 	this.info.BBCXMC = ''
            // 	this.info.BBSB = ''
            // 	this.info.BBSBMC = ''
            // }

            this.info.WRYMC = '';
            this.info.WRYBH = '';
        },
        //企业选择
        async changeEnterprise(v) {
            this.currentSelectCompany = v[0];
            this.info.WRYBH = v[0].value;
            this.info.WRYMC = v[0].label;
            this.isShowVideo = true;
            //清空产线和设备
            this.info.BBCX = '';
            this.info.BBCXMC = '';
            this.info.BBSB = '';
            this.info.BBSBMC = '';
            //获取生产线or治污线
            this.getDuePollutList();
        },
        async saveAdd() {
            //提交生成报备时间
            this.info.BBSJ = this.$dayjs().format('YYYY-MM-DD HH:mm');

            let rules = Object.keys(this.rules);
            for (let i = 0; i < rules.length; i++) {
                let field = rules[i];
                let requires = this.rules[field];

                if (
                    (!this.info[field] || !this.info[field].length) &&
                    requires.required
                ) {
                    uni.showToast({
                        icon: 'none',
                        title: requires.message
                    });
                    return;
                }
            }
            if (this.fileList.length == 0) {
                uni.showToast({
                    icon: 'none',
                    title: '请输入企业证明文件'
                });
                return;
            }
            if (this.mediaList.length == 0) {
                uni.showToast({
                    icon: 'none',
                    title: '请输入现场照片/视频'
                });
                return;
            }

            //校验通过后  如果结束时间是永久要改为空 产线和设备是请选择 重置请求参数
            let params = JSON.parse(JSON.stringify(this.info));
            if (this.info.JZSJ == '永久') {
                delete params.JZSJ;
            }
            if (this.info.BBCXMC == '请选择') {
                params.BBCXMC = '';
            }
            if (this.info.BBSBMC == '请选择') {
                params.BBSBMC = '';
            }

            const { data } = await getQybbAdd(params);
            if (data.status == 500) {
                uni.showToast({
                    title: '报备提交失败',
                    duration: 1000
                });
            } else {
                uni.showToast({
                    title: '报备提交成功',
                    duration: 1000
                }).then(() => {
                    setTimeout(() => {
                        let pages = getCurrentPages(); // 当前页面
                        let beforePage = pages[pages.length - 2]; // 上一页
                        if (beforePage.$vm) {
                            beforePage.$vm.getList();
                        } else {
                            beforePage.getList();
                        }
                        uni.navigateBack({
                            delta: 1
                        });
                    }, 1000);
                });
            }
        },

        async saveReview() {
            //提交生成审核时间
            this.model.SHSJ = this.$dayjs().format('YYYY-MM-DD HH:mm');
            let rules = Object.keys(this.modelRules);
            for (let i = 0; i < rules.length; i++) {
                let field = rules[i];
                let requires = this.modelRules[field];

                if (
                    (!this.model[field] || !this.model[field].length) &&
                    requires.required
                ) {
                    uni.showToast({
                        icon: 'none',
                        title: requires.message
                    });
                    return;
                }
            }
            const { data, status } = await getExamine(this.model);
            if (status == 500) {
                uni.showToast({
                    icon: 'none',
                    title: '上传失败'
                });
            } else {
                uni.showToast({
                    title: '审核提交成功',
                    duration: 1000
                }).then(() => {
                    setTimeout(() => {
                        let pages = getCurrentPages(); // 当前页面
                        let beforePage = pages[pages.length - 2]; // 上一页
                        if (beforePage.$vm) {
                            beforePage.$vm.getList();
                        } else {
                            beforePage.getList();
                        }
                        uni.navigateBack({
                            delta: 1
                        });
                    }, 1000);
                });
            }
        },
        cancelReview() {
            if (this.pageType == 'detail') {
                for (let k in this.model) {
                    this.model[k] = '';
                }
            } else {
                for (let k in this.info) {
                    this.info[k] = '';
                }
            }
        },
        save() {
            if (this.timer) {
                clearTimeout(this.timer);
            }
            this.timer = setTimeout(() => {
                if (this.pageType == 'add') {
                    //新增报备
                    this.saveAdd();
                } else if (this.pageType == 'detail') {
                    //报备详情页面 审核表单提交
                    this.saveReview();
                }
            }, 500);
        },
        back() {
            uni.navigateBack({
                delta: 1
            });
        },
        getFileType(fileName) {
            let suffix = ''; // 后缀获取
            let result = ''; // 获取类型结果
            if (fileName) {
                const flieArr = fileName.split('.'); // 根据.分割数组
                suffix = flieArr[flieArr.length - 1]; // 取最后一个
            }
            if (!suffix) return false; // fileName无后缀返回false
            suffix = suffix.toLocaleLowerCase(); // 将后缀所有字母改为小写方便操作
            console.log('后缀', suffix);
            // 匹配图片
            const imgList = ['png', 'jpg', 'jpeg', 'bmp', 'gif']; // 图片格式
            result = imgList.find((item) => item === suffix);
            if (result) return 'image';
            // 匹配txt
            const txtList = ['txt'];
            result = txtList.find((item) => item === suffix);
            if (result) return 'txt';
            // 匹配excel
            const excelList = ['xls', 'xlsx'];
            result = excelList.find((item) => item === suffix);
            if (result) return 'excel';
            // 匹配word
            const wordList = ['doc', 'docx'];
            result = wordList.find((item) => item === suffix);
            if (result) return 'word';
            // 匹配pdf
            const pdfList = ['pdf'];
            result = pdfList.find((item) => item === suffix);
            if (result) return 'pdf';
            // 匹配ppt
            const pptList = ['ppt', 'pptx'];
            result = pptList.find((item) => item === suffix);
            if (result) return 'ppt';
            // 匹配zip
            const zipList = ['rar', 'zip', '7z'];
            result = zipList.find((item) => item === suffix);
            if (result) return 'zip';
            // 匹配视频
            const videoList = [
                'mp4',
                'm2v',
                'mkv',
                'rmvb',
                'wmv',
                'avi',
                'flv',
                'mov',
                'm4v'
            ];
            result = videoList.find((item) => item === suffix);
            if (result) return 'video';
            // 匹配音频
            const radioList = ['mp3', 'wav', 'wmv'];
            result = radioList.find((item) => item === suffix);
            if (result) return 'radio';
            // 其他文件类型
            return 'other';
        }
    }
};
</script>

<style scoped>
.date-ipt {
    font-size: 32rpx;
    text-align: right;
}

.text-right {
    text-align: right;
}

.garygap {
    background-color: #f1f2f6;
}

.pd-ulpic1 {
    flex-wrap: wrap;
    align-items: flex-start;
}

.pd-ulpic1 li {
    position: relative;
    width: 33.33%;
    margin-left: 0;
    margin-top: 12rpx;
}

.pd-ulpic1 li .delImg {
    position: absolute;
    right: 0;
    top: 0;
    width: 48rpx;
    height: 48rpx;
}

.pd-pic {
    height: 360rpx;
}

.rbtnbox .passbtn2 {
    background-color: #eee;
    color: #666;
}

.rbtnbox .ispass {
    background-color: #2b6cf9;
    color: #fff;
}

.xqmod-top {
    padding-top: 114rpx;
}

.pd-ullst1-detail li .slec {
    padding-right: 0rpx;
    background: none;
    color: #333;
}

.pd-ullst1 > li em {
    font-size: 30rpx;
    color: #666;
    width: 28%;
    display: inline-block;
}

.pd-ullst1 > li i {
    flex: 1;
    text-align: right;
}

.pd-ullst1 > li i.text-left {
    text-align: left;
}

.pd-ullst1 li {
    background: none;
}

.pd-ullst1 li.flex {
    background: none;
    display: flex;
    justify-content: space-between;
}

.pd-ullst1 li.nfx .zmzl {
    width: 100%;
}

.pd-ulpic1.pd-ulpic2 li {
    background-color: #f6f7f9;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 12rpx;
    position: relative;
    margin: 0 8rpx;
}

.rbtnbox .passbtn2 {
    padding-top: 0;
}

.listWarp {
    width: 100%;
    height: 100%;
    overflow-y: scroll;
    position: relative;
    z-index: 99;
}

.listWarp p {
    width: 100%;
    padding: 20rpx;
    text-align: center;
    border-bottom: 1px solid #efefef;
}

.opration {
    padding: 20rpx;
    display: flex;
    justify-content: space-between;
    width: 100%;
    background-color: #fff;
}

.confirm {
    color: rgb(60, 170, 255);
}

.on {
    color: rgb(60, 170, 255);
}

/deep/ .u-text[data-v-1382fc48] {
    display: none;
}

.graytext {
    color: #c1c1c1;
}

.timefont {
    font-size: 31rpx;
}

.timefont2 {
    font-size: 31rpx;
}

.backtext {
    color: #333;
}

.enterprise /deep/ .uni-input-input {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
