<template>
	<!-- 底部滚动消息 -->
	<div class="message-box ">
		<div class="pd-notice1 trans">状态最近更新时间：{{time}}共{{ zwycNum }}家{{type}}</div>
	</div>
</template>

<script>
import { getList } from '../../api/iot/realtime.js';
export default {
	data() {
		return {
			count: 0,
		};
	},
	props: {
		time:{
			type: String,
			default(){
				return this.$dayjs().format('YYYY-MM-DD HH:mm:ss');
			}
		},
		zwycNum: {
			type: Number,
			default: 0
		},
		type:{
			type: String,
			default: ''
		}
	},
	watch: {
		zwycNum: {
			handler: function(nv) {
				this.count = nv;
			},
			immediate: true
		}
	}
};
</script>

<style scoped lang="scss">
	/* #ifdef APP-PLUS */
	.message-box {
		position: fixed;
		width: 100%;
		height: 60rpx;
		background-color:#6d90ff;
		bottom: 0rpx;
	}
	/* #endif */
	/* #ifdef H5 */
	.message-box {
		position: fixed;
		width: 100%;
		height: 60rpx;
		background-color:#6d90ff;
		bottom: 100rpx;
	}
	/* #endif */

@keyframes move {
	0% {
		transform: translate(0, 0);
	}
	10% {
		transform: translate(0, 0);
	}
	100% {
		transform: translate(-100%, 0);
	}
}
.trans {
	animation: move infinite 10s linear;
}
</style>
