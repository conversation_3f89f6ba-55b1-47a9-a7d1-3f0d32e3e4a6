import store from '../store';

export default {
	// 输入的时候校验
	inputFunc(template){
		let obj = {
			isChange: false,
			reg: ''
		}
		if (template.hasOwnProperty('YSSJLX')) {
			let type = template.YSSJLX;
			switch (type) {
				case 'PHONENUM': //手机号
				case 'mobile':
				case 'positiveinteger':
				case 'INTEGER':
				case 'LOCATION_JD':
				case 'LOCATION_WD':
					obj.isChange = true;
					obj.reg = /[^\d.]/g	
					break;
				case 'phoneOrMobile':
					obj.isChange = true;       
					obj.reg = /[^\d-]/g
					break;
				case 'FLOAT':
				case 'intOrFloat':
					obj.isChange = true;
					obj.reg = /[^\d.+-]/g
					break;
				case 'idcard':
				case 'CARDID': 
					obj.isChange = true;
					obj.reg = /[^\dxX]/g    
					break;
				
			}
		}
		
		return obj;
	},
	
	// 鼠标移开的时候校验
	blurFunc(template, value, label) {
		// 先判断是否必填 must == 1 的时候必填
		if (template.must == '1') {
			if (value == '') {
				uni.showToast({
					title: `${label}没有输入或输入有误`,
					duration: 2000,
					icon: 'none'
				});
			}
		}
		// 如果有返回这个字段就，判断类型
		if (template.hasOwnProperty('YSSJLX')) {
			let reg = '';
			let type = template.YSSJLX;
			let text = '';
			
			// 校验格式
			this.filterInput(type, label, value);
		}
	},
	//针对提交的校验, 第三个参数，就是距离顶部的距离
	saveTemplateForm(data, that, fixHeight) {
		let verifyList = store.state.verifyList
		let checktype = true
		for (let i in verifyList) {
			if (verifyList[i].type === '1') {
				let re = this.filterInput(verifyList[i].type, verifyList[i].name, data[verifyList[i].field]);
				if (!re) {
					let ele = '#' + verifyList[i].id;
					that.$nextTick(() => {
						uni.createSelectorQuery().in(that).select(ele).boundingClientRect(function(e) {
							that.$refs.templateForm.changeScrollTop(e.top, fixHeight);
						}).exec()
					})
					return;
				}
			}
			if (!data[verifyList[i].field]) {
				if (verifyList[i].type === '1') {
					uni.showToast({
						title: `'${verifyList[i].name}'没有输入或输入有误`,
						duration: 2000,
						icon: 'none'
					});
					checktype = false
					let ele = '#' + verifyList[i].id;

					that.$nextTick(() => {
						uni.createSelectorQuery().in(that).select(ele).boundingClientRect(function(e) {
							that.$refs.templateForm.changeScrollTop(e.top, fixHeight);
						}).exec()
					})
					return verifyList[i].name
				}
			}
		}
		if (checktype) {
			return checktype
		}
	},

	//针对特殊字段的校验
	filterInput(type, name, value) {
		let tag = true;
		let reg;
		let text = '';
		switch (type) {
			case 'email':
				reg = /[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/
				text = '邮箱';
				break;
			case 'idcard':
			case 'CARDID': //身份证
				reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
				text = '身份证';
				break;
			case 'PHONENUM': //手机号
			case 'mobile':
				reg = /^1[3|4|5|7|8][\d]{9}$/
				text = '手机号';
				break;
			case 'phoneOrMobile':
				reg = /^((0\d{2,3}-\d{7,8})|(1[3|4|5|7|8][\d]{9}))$/
				text = '电话或手机';
				break;
			case 'FLOAT': //浮点
				reg = /^[-|\+]?\d+\.\d+$/
				text = '浮点';
				break;
			case 'positiveinteger':
			case 'INTEGER': //整数
				reg = /^[+|-]?\d+$/
				text = '整数';
				break;
			case 'intOrFloat': 
				reg = /^([+|-]?\d+)|([-|\+]?\d+\.\d+)$/
				text = '整数和浮点数';
				break;
			case 'LOCATION_JD': //经度
				reg = /^-?((0|1?[0-7]?[0-9]?)(([.][0-9]{1,4})?)|180(([.][0]{1,4})?))$/
				text = '经度';
				break;
			case 'LOCATION_WD': //纬度
				reg = /^-?((0|[1-8]?[0-9]?)(([.][0-9]{1,4})?)|90(([.][0]{1,4})?))$/
				text = '纬度';
				break;
			case 'chinese':
				reg = /^[\u0391-\uFFE5A-Za-z]+$/
				text = '中文';
				break;
			case 'english':
				reg = /^[A-Za-z0-9]+$/
				text = '英文';
				break;

		}
		if (value && text) {
			if (!reg.test(value)) {
				uni.showToast({
					title: `'${name}'必须填写为${text}格式，请校验后重新输入`,
					duration: 2000,
					icon: 'none'
				});
				tag = false;
			}
		}
		return tag;
	},

	mrzGetForm(data) {

	}
}
