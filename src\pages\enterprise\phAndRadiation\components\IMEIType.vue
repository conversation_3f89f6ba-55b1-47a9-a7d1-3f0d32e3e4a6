<!-- @format -->

<template>
    <div class="zy-form">
        <div class="item">
            <p class="label star">设备名称</p>
            <div class="rp" v-show="!isDetailPageShow()">
                <input
                    type="text"
                    v-model="form.SBMC"
                    class="zy-input1"
                    placeholder="请填写设备名称"
                    @confirm="matchEquipType"
                />
            </div>
            <div class="rp detail" v-show="isDetailPageShow()">
                {{ form.SBMC || '-' }}
            </div>
        </div>
        <div class="item">
            <p class="label star">IMEI号</p>
            <div class="rp" v-show="!isDetailPageShow()">
                <input
                    type="text"
                    v-model="form.IMEI"
                    class="zy-input1"
                    placeholder="请扫描或手动输入后6位数"
                    style="margin-right: 18rpx; font-size: 31rpx"
                    @input="search(form.IMEI)"
                    @click="dealCanNotEditIMEI"
                />
                <image
                    src="@/static/app/images/sysic.png"
                    class="pd-sysbtn"
                    @click="scanCode()"
                />
            </div>
            <div class="rp detail" v-show="isDetailPageShow()">
                {{ form.IMEI || '-' }}
            </div>
        </div>
        <div class="item">
            <p class="label star">监控仪类型</p>
            <div class="rp pd-btn1" v-show="!isDetailPageShow()">
                <button type="button" class="on" v-show="form.ZNSBLX === 'RAD'">
                    辐射
                </button>
                <button type="button" class="on" v-show="form.ZNSBLX === 'PH'">
                    pH
                </button>
            </div>
            <div class="rp pd-btn1 detail" v-show="isDetailPageShow()">
                <span v-show="form.ZNSBLX === 'RAD'"> 辐射 </span>
                <span v-show="form.ZNSBLX === 'PH'"> pH </span>
            </div>
        </div>
        <u-select
            value-name="IMEI"
            label-name="IMEI"
            mode="single-column"
            :list="imeiList"
            v-model="imeiShow"
            @confirm="selectImei"
        ></u-select>
    </div>
</template>

<script>
import { getScxsbList, getValidate, znsb } from '@/api/iot/enterprise.js';
export default {
    name: 'DataCollectionAppIMEIType',
    props: {
        //父组件表单
        model: {
            type: Object,
            default: () => {}
        },
        //设备类型
        equipType: {
            type: String,
            default: ''
        },
        //页面类型
        pageType: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            form: {
                SBMC: '',
                IMEI: '',
                ZNSBLX: ''
            },
            relativeEquips: [],
            imeiList: [],
            imeiShow: false
        };
    },
	watch: {
		form: {
			handler(newVal, oldVal) {
				if (this.pageType == 'add') {
					//this.backPage = true;
					this.$emit("update:backPage",true)
				}
			},
			deep: true,
		},
	},
    mounted() {
        //新增就获取当前时间
        if (this.pageType === 'add') {
            this.form.AZSJ = this.$dayjs().format('YYYY-MM-DD HH:mm:ss');
        }
    },

    methods: {
        //详情显示，新增编辑隐藏
        isDetailPageShow() {
            return this.pageType === 'detail';
        },
        //匹配设备类型
        async matchEquipType() {
            //如果已经有产线，先校验产线上是否已经有重名设备
            if (this.model.SCXID) {
                try {
                    await this.validIsHadEquipName(this.model.SCXID);
                    //ph,辐射暂时不查询设备图片
                    //this.toQueryEquipPics();
                    this.$emit('update:model', {
                        ...this.model,
                        SBMC: this.form.SBMC
                    });
                    console.log('model', this.model);
                } catch (error) {
                    //若重名则清空设备名称
                    console.log('error');
                    this.form.SBMC = '';
                }
            } else {
                // this.toQueryEquipPics();
            }
        },
        //校验生产线是否已经存在设备名称
        validIsHadEquipName(scxid) {
            return new Promise((resolve, reject) => {
                //选择生产线前校验生产线上是否已经存在相同的名称的设备
                getScxsbList({
                    SCXID: scxid
                }).then((res) => {
                    this.relativeEquips = res.data || [];
                    let objSameNameEquip = this.relativeEquips.find(
                        (item) => item.SBMC === this.form.SBMC
                    );
                    if (objSameNameEquip) {
                        uni.showToast({
                            title: '该生产线已有设备重名，请重新输入',
                            icon: 'none'
                        });
                        return reject();
                    } else {
                        return resolve();
                    }
                });
            });
        },
        //安装时间超过24小时不可以编辑
        async dealCanNotEditIMEI() {
            return new Promise((resolve, reject) => {
                if (this.canNotEditIMEI) {
                    uni.showModal({
                        title: '提示',
                        content: '安装时间已经超过24小时，IMEI号不可以编辑！',
                        showCancel: false,
                        success: function (res) {
                            console.log('用户点击确定');
                        }
                    });
                    reject('安装时间已经超过24小时，IMEI号不可以编辑');
                } else {
                    resolve();
                }
            });
        },
        //搜索
        async search(imei) {
            try {
                await this.dealCanNotEditIMEI();
                if (imei && imei.length < 5) {
                    if (this.timer) {
                        clearTimeout(this.timer);
                    }
                    this.timer = setTimeout(() => {
                        uni.showToast({
                            title: '请先输入5位以上的IMEI号',
                            icon: 'none'
                        });
                    }, 500);
                    return;
                }
                if (imei && imei.length >= 5) {
                    if (this.timer) {
                        clearTimeout(this.timer);
                    }
                    this.timer = setTimeout(() => {
                        this.getImei(imei);
                    }, 500);
                }
            } catch (error) {
                console.log('error', error);
            }
        },
        // 查询完整imei码
        getImei(imei) {
            znsb({
                IMEI: imei
                // IMEI: '513710'
                // IMEI: '34040'
            }).then((res) => {
                if (!res.data || res.data.length === 0) {
                    uni.showToast({
                        title: '未匹配到IMEI号，请检查输入！',
                        icon: 'none'
                    });
                    this.isGetIMEI = false;
                    this.isShowSetCircle = false;
                } else {
                    uni.hideKeyboard(); //隐藏软键盘
                    this.imeiList = res?.data?.filter(
                        (item) => item.ZNSBLX === 'PH' || item.ZNSBLX === 'RAD'
                    );
                    this.imeiShow = true;
                    this.isGetIMEI = true;
                }
            });
        },

        // 选择查出来的imei码
        selectImei(v) {
            // 先校验选择的码是不是已经被绑定了
            getValidate({
                IMEI: v[0].value
                // IMEI:'865257111065919'
                //IMEI:'865257111065919'
            }).then((res) => {
                // 选择的码有效
                if (res.data.length === 0) {
                    this.form.IMEI = v[0].value;

                    let objChooseIMEI = this.imeiList.find(
                        (item) => item.IMEI === v[0].value
                    );
                    this.$emit('update:model', {
                        ...this.model,
                        IMEI: this.form.IMEI,
                        SFFC: objChooseIMEI.SFFC
                    });
                    //获取到正确的imei之后可以设置阈值
                    //this.isShowSetCircle = true;
                    let obj = this.imeiList.find(
                        (e) => e.IMEI == this.form.IMEI
                    );
                    this.form.ZNSBLX = obj.ZNSBLX;

                    this.$emit('setEquipType', this.form.ZNSBLX);

                    // this.model.SFFC = obj.SFFC;
                    // this.model.FCBJZT = '0';
                    //this.changeShowThreeList();
                }
                // 码已经被绑了
                else if (res.data && res.data.length > 0) {
                    this.form.IMEI = '';
                    //this.isShowSetCircle = false;
                    //this.isGetIMEI = false
                    let msg = res.data[0].TEXT;
                    uni.showToast({
                        title: msg,
                        icon: 'none',
                        duration: 2000
                    });
                    // 查出多个码时，继续弹下拉框
                    if (this.imeiList.length > 1) {
                        setTimeout(() => {
                            this.imeiShow = true;
                        }, 1000);
                    }
                }
            });
        },

        async scanCode() {
            try {
                await this.dealCanNotEditIMEI();
                uni.scanCode({
                    scanType: ['qrCode'],
                    success: (res) => {
                        console.log('res', res);
                        this.fromScan = true;
                        let imei = res.result.split(';')[0];
                        if (imei != '') {
                            this.search(imei);
                        }
                        // this.getnewStateList(this.model.IMEI)
                    },
                    fail: (err) => {
                        uni.showToast({
                            title: '未识别到二维码！',
                            icon: 'none'
                        });
                    }
                });
            } catch (error) {}
        }
    }
};
</script>

<style lang="scss" scoped></style>
