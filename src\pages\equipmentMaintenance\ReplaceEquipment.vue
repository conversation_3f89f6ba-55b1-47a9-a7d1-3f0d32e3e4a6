<!-- @format -->
<template>
    <!-- 更换设备 -->
    <section
        class="main equipment-maintenance"
        style="padding-bottom: 0; margin-bottom: 30.1932rpx"
    >
        <div class="inner">
            <div class="gap"></div>
            <div class="zy-form">
                <!-- 设备基本信息 -->
                <GetIMEIMsg
                    @updateInfo="updateInfo"
                    ref="refGetIMEIMsg"
                    :isDisabled="isDisabled"
                    :options="options"
                ></GetIMEIMsg>
                <!-- 查询imei -->
                <SearchIMEI
                    :isDisabled="isDisabled"
                    @updateNewIMEI="updateNewIMEI"
                    ref="refSearchIMEI"
                    :options="options"
                ></SearchIMEI>
                <div class="item">
                    <p class="label" :class="{ star: options.type === 'add' }">
                        更换原因
                    </p>
                    <div class="rp" :class="{ 'detail-type': isDisabled }">
                        <div class="zy-selectBox" @click="showReplaceReason">
                            <p
                                v-show="!info.ywmx.ghyy && !isDisabled"
                                class="res"
                            >
                                请选择更换原因
                            </p>
                            <p else class="res">{{ info.ywmx.ghyy }}</p>
                        </div>
                    </div>
                </div>
                <SetCircleData
                    :options="options"
                    :isDisabled="isDisabled"
                    :info.sync="info"
                ></SetCircleData>
            </div>
            <UploadImage
                ref="refUploadImage"
                :options="options"
                :arrUploadType="arrUploadType"
                :uploadId="info.mxid"
                :isDisabled="isDisabled"
            ></UploadImage>
            <!-- 采集结果 -->
            <GetLastedResult
                :imei="info.ywmx.new_imei"
                ref="refGetLastedResult"
            ></GetLastedResult>

            <div class="gap"></div>
            <div class="gap"></div>
            <div class="zy-bot-btn1" v-show="!isDisabled" @click="save">
                保存
            </div>
            <div class="gap"></div>
            <div class="gap"></div>
        </div>
        <u-select
            mode="single-column"
            :list="arrReplaceReason"
            v-model="isShowReplaceReason"
            @confirm="selectReplaceReason"
        ></u-select>
    </section>
</template>

<script>
import { maintenanceDetail } from '@/api/iot/equipmentMaintenance';
import { guid } from '@/utils/uuid.js';
import GetIMEIMsg from './components/GetIMEIMsg';
import SearchIMEI from './components/SearchIMEI';
import GetLastedResult from './components/GetLastedResult';
import SetCircleData from './components/SetCircleData';
import UploadImage from './components/UploadImage';
import useSaveForm from './hook/useSaveForm';
const { handleSave } = useSaveForm();

export default {
    data() {
        return {
            options: {},
            info: {
                mxid: '', //明细id
                ywid: '', //运维id
                ywlx: '', //运维类型
                ywmx: {
                    new_imei: '',
                    ghyy: '',
                    jczq: 0,
                    bsjg: 0,
                    qtyz: '0#0'
                },
                qtyz: 0
            },
            rules: {
                new_imei: {
                    required: true,
                    message: '请输入新imei号',
                    trigger: 'change'
                },
                ghyy: {
                    required: true,
                    message: '请输入更换原因',
                    trigger: 'change'
                },
                jczq: {
                    required: true,
                    message: '请输入检测周期指令',
                    trigger: 'change'
                },
                bsjg: {
                    required: true,
                    message: '请输入上报周期',
                    trigger: 'change'
                },
                qtyz: {
                    required: true,
                    message: '请输入设备阈值',
                    trigger: 'change'
                }
            },
            arrUploadType: [
                {
                    name: '近景',
                    LXDM: 'YWMX', //类型代码
                    ZLXDM: 'GHSB1' //子类型代码
                },
                {
                    name: '中景',
                    LXDM: 'YWMX',
                    ZLXDM: 'GHSB2'
                },
                {
                    name: '远景',
                    LXDM: 'YWMX',
                    ZLXDM: 'GHSB3'
                }
            ],
            arrReplaceReason: [
                {
                    label: '信号弱优化无果',
                    value: '信号弱优化无果'
                },
                {
                    label: '硬件故障',
                    value: '硬件故障'
                },
                {
                    label: '其它问题',
                    value: '其它问题'
                }
            ],
            isShowReplaceReason: false,
            isDisabled: false //是否不可编辑
        };
    },
    components: {
        GetIMEIMsg,
        GetLastedResult,
        SearchIMEI,
        UploadImage,
        SetCircleData
    },
    watch: {
        'info.qtyz': {
            handler(newV, oldV) {
                this.info.ywmx.qtyz = this.info.qtyz + '#' + this.info.qtyz;
            }
        }
    },
    onLoad(options) {
        this.options = options;
    },
    onHide() {},
    created() {},
    mounted() {
        if (this.options.type === 'add') {
            this.info.ywlx = this.options.ywlx;
            this.info.ywid = this.options.ywid;
            this.info.mxid = guid();
        }
        if (this.options.type === 'detail') {
            uni.setNavigationBarTitle({
                title: '运维明细详情'
            });
            this.isDisabled = true;
            this.getMaintenanceDetail();
        }
    },
    onBackPress() {},
    methods: {
        //请求明细
        async getMaintenanceDetail(id) {
            let data = await maintenanceDetail(this.options.mxid);
            for (const key in data) {
                if (key == 'ywmx') {
                    let innerData = data['ywmx'];
                    for (const key1 in innerData) {
                        this.$set(
                            this.info.ywmx,
                            key1,
                            innerData[key1] === '' ? '-' : innerData[key1]
                        );
                    }
                } else {
                    this.$set(
                        this.info,
                        key,
                        data[key] === '' ? '-' : data[key]
                    );
                }
            }
            let index = this.info.ywmx?.qtyz?.indexOf('#') || 0;
            this.info.qtyz = this.info.ywmx?.qtyz?.slice(0, index) || '-';
            this.$refs.refGetIMEIMsg.updateInfo(this.info);
            this.$refs.refSearchIMEI.updateInfo(this.info.ywmx);
            this.$nextTick(() => {
                this.$refs.refUploadImage.getImageFileList();
            });
        },

        //显示选择器：更换原因
        showReplaceReason() {
            if (this.isDisabled) {
                return;
            }
            this.isShowReplaceReason = true;
        },
        //选择更换原因
        selectReplaceReason(v) {
            this.info.ywmx.ghyy = v[0].value;
        },
        //更新表单数据
        updateInfo(payload) {
            this.info = {
                ...this.info,
                ...payload,
                ywmx: {
                    ...payload.ywmx,
                    ghyy: this.info.ywmx.ghyy,
                    new_imei: this.info.ywmx.new_imei
                }
            };
            this.info.qtyz = this.extractBeforeFirstHash(payload.ywmx.qtyz);
        },
        //截取井前的字符
        extractBeforeFirstHash(str) {
            let index = str.indexOf('#');
            if (index !== -1) {
                return str.substring(0, index);
            } else {
                return str;
            }
        },
        //更新新的imei
        updateNewIMEI(imei) {
            console.log('imei', imei);
            if (imei == this.info.imei) {
                uni.showToast({
                    title: '新，旧IMEI不可相同',
                    icon: 'none'
                });
                this.$refs.refSearchIMEI.info.imei = '';
                return;
            }
            this.info.ywmx.new_imei = imei;
        },
        //保存
        async save() {
            let objRules = {};
            let objValiData = {};
            Object.assign(objRules, this.$refs.refGetIMEIMsg.rules, this.rules);
            Object.assign(
                objValiData,
                this.info,
                this.info.ywmx,
                this.$refs.refGetIMEIMsg.info
            );
            let pages = getCurrentPages(); // 当前页面
            console.log('info', this.info);
            let arrUploadImage = this.$refs.refUploadImage.arrUploadImage;
            handleSave(
                objRules,
                objValiData,
                this.info,
                pages,
                true,
                arrUploadImage,
                true
            );
        }
    }
};
</script>

<style scoped lang="less">
section {
    background: #f1f1f1;
}
</style>
