/**
 * /*
 *
 * @format
 * @Author: your name
 * @Date: 2021-05-31 11:37:05
 * @LastEditTime: 2021-08-23 17:21:10
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /YDZF_APP/common/config.js
 */

//const API_SERVICE_URL = 'http://api-mobile.iotdi.com.cn:9011/api_mobile'; //腾讯云测试环境地址
const API_SERVICE_URL = 'http://************:9011/api_mobile';//腾讯云测试环境地址
//const API_LOGIN_SERVICE_URL = 'http://iot-manage.iotdi.com.cn/iotManage'; //腾讯云登录和附件地址
const API_LOGIN_SERVICE_URL = 'http://************:8388/iotManage'; //测试环境地址
// #ifdef APP-PLUS
export const DOWNLOAD_URL = `${API_SERVICE_URL}/webapp/downloadFile`;
export const ULR_BASE = API_SERVICE_URL; //接口请求地址
export const IOTMANAGE_URL = API_LOGIN_SERVICE_URL; //iotmanage接口请求地址
export const LOGIN_ULR_BASE = API_LOGIN_SERVICE_URL; //登录和附件地址
export const URL_BASE_ENTERPRISE_DETAIL = API_SERVICE_URL;
export const UPLOAD_URL = `${API_LOGIN_SERVICE_URL}/platform/file/filemanagecontroller/upload`;
export const DOWNLOAD_URLZDY = `http://************:8388/openApi/common/filedowload/`;
export const DELETFILE_URLZDY = `${API_LOGIN_SERVICE_URL}/platform/file/filemanagecontroller/deletefile/`;
// #endif

// #ifdef H5
export const IOTMANAGE_URL = 'iotManage'; //iotmanage接口请求地址
export const URL_BASE_ENTERPRISE_DETAIL =
    'http://api-mobile.iotdi.com.cn:9011/dataservice/'; //江山预警详情页接口地址
export const ULR_BASE = 'api_mobile'; //腾讯云测试环境地址
export const LOGIN_ULR_BASE = 'iotManage';
export const UPLOAD_URL = `/iotManage/platform/file/filemanagecontroller/upload`;
export const DOWNLOAD_URLZDY = `/iotManage/platform/file/filemanagecontroller/downloadfilebyid/`;
export const DELETFILE_URLZDY = `/iotManage/platform/file/filemanagecontroller/deletefile/`;
// #endif
