<template>
    <div>
<!-- 		<div class="zy-yj-cell1">
			 <div class="cell1-hd" @click="toNumberRecord">
				<p class="zy-yj-til1">预警编号</p>
				<p class="zy-yj-more">
				{{info.YJBH == null || info.YJBH == ''?'--':info.YJBH}}
				</p>
			</div> 
		</div> -->
		

		<div class="zy-yj-cell1">
			<div class="cell1-hd">
				<p class="zy-yj-til1">
				{{info.SCXMC == ''?info.ZWXMC:info.SCXMC}}
				近24小时振动监控状态数据</p>
			</div>
			<div class="cell1-bd">
				<div class="zy-yj-block1">
					<p class="zy-yj-txt1">生产设备</p>
					<div class="zy-yj-tips">
						<span>开启</span>
						<span>关闭</span>
						<span>离线</span>
					</div>
				</div>
				<div class="gap"></div>
				
				<div class="zy-yj-tu" style="padding-left: 8px;">
					<div v-show="product_List.length == 0" style="color:#999;text-align: center;">暂无数据</div>
					<!-- #ifdef APP-PLUS || H5 -->
					<view  style="width: 100%;height:500rpx"   :prop="productOption"
					 :change:prop="echarts.updateEcharts" id="echarts" class="echarts"></view>
					<!-- #endif -->
					<!-- #ifndef APP-PLUS || H5 -->
					<view>非 APP、H5 环境不支持</view>
					<!-- #endif -->
			<!-- 		<div
					    v-show="product_List.length != 0"
						ref="product_charts"
						style="height: 300px; width: 500px;"
					></div> -->
				</div>
				<div class="gap"></div>
				<div v-show="this.info.YJLX != 'TC'">
					<div class="zy-yj-block1" >
						<p class="zy-yj-txt1">治污设备</p>
						<div class="zy-yj-tips">
							<span>开启</span>
							<span>关闭</span>
							<span>离线</span>
						</div>
					</div>
					<div class="gap"></div>
					<div class="zy-yj-tu" style="padding-left: 8px;">
					<div v-show="pollut_List.length == 0" style="color:#999;text-align: center;">暂无数据</div>
					<!-- #ifdef APP-PLUS || H5 -->
					<view  style="width: 100%;height:100rpx"   :prop1="pollutOption"
					 :change:prop1="echarts.updatePollutEcharts"  id="pollutEcharts" class="echarts"></view>
					<!-- #endif -->
					<!-- #ifndef APP-PLUS || H5 -->
					<view>非 APP、H5 环境不支持</view>
					<!-- #endif -->
				<!-- 	<div
					    v-show="pollut_List.length != 0"
						ref="pollut_charts"
					 	style="height: 300px; width: 500px;"
					></div> -->
				</div>

				</view> 
				
				</div>
				<div class="gap"></div>
			</div>

		</div>

		<div class="zy-yj-cell1">
			<div class="cell1-hd">
				<p class="zy-yj-til1">安装点位</p>
			</div>
			<div>
				<div class="cell1-bd" v-for="item in picList" >
					<p class="zy-yj-txt2">{{item.SBMC}}</p>
					<ul class="zy-yj-list1">
					
						<li v-for="(item2,index) in item.WJIDS">
							<image mode="scaleToFill" style="width:100%;height:120px;" :src="getFileUrl(item2)" alt="" @click="previewImage(item.WJIDS,index)"/>
						</li>
		
					</ul>
					<div v-if="item.WJIDS.length == 0" style="color:#999;text-align: center;">暂无数据</div>
					<div class="gap"></div>
				</div>
			</div>
		
		</div>
	</div>
	
</template>

<script>
	// import * as echarts from 'echarts';
	import { DOWNLOAD_URLZDY } from '@/common/config.js';
	import {monitorCxzt,monitorZdsj,monitorPicture} from '@/api/warning.js'
	import TendencyChart from './TendencyChart.vue';
	export default {
		components:{
			TendencyChart
		},
		props:{
			warning:{
				type:Object,
				default:function(){}
			}
		},
		watch:{
			warning:{
				handler(newVal,oldVal){
					this.info = newVal;
					if(this.info){
						//console.log('infonew',this.info);
						//ZW-治污，TC-停产，GZ-故障
						if(this.info.YJLX == 'ZW' ||this.info.YJLX == 'TC'){
							this.getPollutionWarning();
						}else if(this.info.YJLX == 'GZ'){
							this.getFaultWarning();
						}
						this.getMonitorPicture()
					}
					
		     	},
				//immediate: true,
				deep:true
				
			}
			
		},
		data() {
			return {
				obj:{},
				option: {
							series: [
								{
									type: 'bar',
									data: [11, 12, 15, 16, 13, 12, 14]
								}
							],
							xAxis: {
								data: ['a', 'b', 'c', 'd', 'e', 'f', 'g']
							},
							yAxis: {},
							tooltip: {}
						},
				info:{
					YJSJ:'', //日期
					SCXID:'', //生产线id
					YJBH: '' //预警编号
				},
				pulltionData:[],
				faultData:[],
				picList:[],
				productOption:{},
				pollutOption:{},
				product_List :[],
				pollut_List :[],
				curEquit:
				dateStr:
			};
		},
		mounted() {
		},
		methods: {
			//近24小时振动监控数据：（治污预警和停产预警）
			getPollutionWarning(){
				let obj = {
					SCXID:`${this.info.SCXID},${this.info.ZWXID},`,
					startT:this.$dayjs(this.info.FSSJ).format("YYYY/MM/DD 00:00:00"),
					endT:this.$dayjs(this.info.YJSJ).format("YYYY/MM/DD HH:mm:ss"),
				}
				this.obj = obj;
				monitorCxzt(obj).then(res=>{
					res.data.zwList.forEach(x => {
						x.name = x.SBMC;
						// x.ztList = JSON.parse(x.ztList);
						if(x.ztList && x.ztList.length > 0){
							x.list = x.ztList;
							x.list.forEach(y => {
								y.start = this
									.$dayjs(y.start)
									.format('YYYY/MM/DD HH:mm:ss');
								y.end = this
									.$dayjs(y.end)
									.format('YYYY/MM/DD HH:mm');
							});
						}
					});
					res.data.scList.forEach(x => {
						x.name = x.SBMC;
						// x.ztList = JSON.parse(x.ztList);
						if(x.ztList && x.ztList.length > 0){
							x.list = x.ztList;
							x.list.forEach(y => {
								y.start = this
									.$dayjs(y.start)
									.format('YYYY/MM/DD HH:mm:ss');
									y.end = this
										.$dayjs(y.end)
										.format('YYYY/MM/DD HH:mm');
							});
						}
					});
					this.pollut_List = res.data.zwList;
					this.product_List = res.data.scList;
					this.pollutOption = {};
					this.productOption = {};
					this.pollutOption = this.setChart(this.pollut_List);
					this.productOption = this.setChart(this.product_List);
				})
			},
			
			setChart(data) {
										let statusObj = {
											1: {
												color: '#ff7054',
												label: '停止'
											},
			
											2: {
												color: '#7dcd27',
												label: '开启'
											},
			
											0: {
												color: '#f0f0f0',
												label: '离线'
											},
										};
										let x = [];
										let minutes = 0;
										let kssj = '';
										let jssj = '';
			
									let curDate = this.$dayjs(this.info.YJSJ).format("YYYY-MM-DD 00:00:00");
									minutes = this.getMintues(curDate,this.info.YJSJ);
									 kssj = this.$dayjs(curDate).format("HH:mm");
									 jssj = this.$dayjs(this.info.YJSJ).format("HH:mm");
						               let totalMin = minutes + 2;
					
										for (let i = 0; i < totalMin ; i++) {
											let num = i;
			
											let h = Math.floor(num / 60);
			
											let min = num % 60;
			
											h = (h <= 9 ? '0' : '') + h;
			
											min = (min <= 9 ? '0' : '') + min;
											x.push( h + ':' + min);
			
										}
			
			
										let y = [];
			
										let lines = [];
			
										data.forEach((v, i) => {
											y.push(v.name);
											//背景状态（如果数据中有可以去掉）
											lines.push({
												z: 1,
												type: 'lines',
												coordinateSystem: 'cartesian2d',
												silent: true,
												lineStyle: {
													width: 10,
													color: '#f0f0f0',
													opacity: 1
												},
												data: [{
													coords: [
														['00:00', v.name],
														[jssj, v.name]
													]
												}]
											});
			
											v.list &&v.list.forEach((item, j) => {
												let xVal1 = item.start.slice(11, 16);
												let xVal2 = item.end.slice(11, 16);
												let obj = statusObj[v.list[j].status];
												lines.push({
													name: obj.label,
													type: 'lines',
													coordinateSystem: 'cartesian2d',
													lineStyle: {
														width: 24,
														color: obj.color || '#f0f0f0',
														opacity: 1
													},
													data: [{
														name: v.name,
			
														start: item.start,
			
														end: item.end,
			
														statusLabel: obj.label,
			
														coords: [
															[xVal1, v.name],
															[xVal2, v.name]
														]
													}]
												});
											});
										});
			
										let option = {
											tooltip: {
												show: true,
												axisPointer: {
													type: 'cross',
													crossStyle: {
														color: '#4874ff'
													},
													label: {
														 color: '#fff',
														 backgroundColor : '#4874ff'
													 }
												},
												backgroundColor: "rgba(255,255,255,0)",
												padding: [0, 0],
											},
												grid: {
													top: 10,
													left: 16,
													bottom:20,
													right:20
												},
				
												xAxis: {
													type: 'category',
													data: x,
													interval: 0,
													axisLabel: {
														show: true,
														textStyle: {
															color: '#999'
														},
														// formatter(v) {
														//     let s = v.split(':')[1];
														//     if (s % 15 === 0) {
														//         return v;
														//     }else{
														//         return '';
														//     }
														// }
													},
													axisLine: {
														show: true,
														lineStyle: {
															color: '#f0f0f0'
														}
													}
												},
												yAxis: {
													type: 'category',
													data: y,
													axisLabel: {
														interval: 0,
														show: true,
														textStyle: {
															color: '#fff'
														},
														inside: true,
														padding: [2, 0, 0,0],
													   rich: {
															a: {
																color: 'transparent',
																lineHeight: 30,
																fontFamily: 'digital',
																fontSize: 14,
															},
														},
													},
													axisTick: {
														show: false
													},
													axisLine: {
														show: false,
														lineStyle: {
															color: '#f0f0f0'
														}
													},
													z:3
												},
												series: lines
											};
											
										return option;
						},
						getMintues(startT,endT){
								const calcSecond =
									this.$dayjs(endT).diff(this.$dayjs(startT), 'minutes') 
								return calcSecond
						},
			getFileUrl(item){
				//console.log(DOWNLOAD_URLZDY + item)
				return DOWNLOAD_URLZDY + item
			},

			//预览图片
			previewImage(fileList, index) {
				let self = this;
				let fileUrls = fileList.map((file) => {
					return this.getFileUrl(file);
				});
				// 预览图片
				uni.previewImage({
					current: index,
					urls: fileUrls,
					//长按保存到本地
					longPressActions: {
						itemList: ["保存图片到本地"],
						success: (data) => {
							// console.log('data', data)
							if (data.tapIndex == 0) {
								let imgurl = fileUrls[data.index];
								self.saveImage(imgurl)
							}
			
						},
						fail: function(err) {
							console.log(err.errMsg);
						},
					},
			
				});
			
			},
			
			//保存图片
			saveImage(imgurl) {
				uni.downloadFile({
					url: imgurl,
					success(res) {
						let url = res.tempFilePath
						uni.saveImageToPhotosAlbum({
							filePath: url,
							success() {
								uni.showToast({
									title: '已存至系统相册',
									icon: "success"
								})
							},
							fail(err) {
								uni.showToast({
									title: '保存失败',
									icon: "error"
								})
							}
						})
					}
				})
			},
			//查询设备图片接口
			getMonitorPicture(){
				let obj = {};
				// if(this.info.YJLX == "TC"){
				// 	console.log(111111);
				// 	obj = {
				// 		SBID:`${this.info.SCSBID}`
				// 	}
				// 	console.log('obj',obj);
				// }else{
				// 	 obj = {
				// 		SBID:`${this.info.SCSBID},${this.info.ZWSBID}`
				// 	}
				// }
				
				obj = {
					SBID:`${this.info.SCSBID},${this.info.ZWSBID}`
				}

				monitorPicture(obj).then(res=>{
					if(res.status == '000'){
					//	console.log('res.data.length',res.data.length);
						//console.log('res.data',res.data);
						if(res.data && res.data.length && res.data.length>0){
							this.picList = res.data;
							this.picList.forEach(item=>{
								if(item.WJIDS){
									item.WJIDS = item.WJIDS.split(',')
								}else{
									item.WJIDS = [];
								}
							})
							console.log('res',this.picList);
						}
						
					}
				
				})
			},
             //跳转
			toNumberRecord(v) {
				uni.navigateTo({
					url: `/views/warningRecord/NumberingRecord?YJBH=${this.info.YJBH}`
				});
			},
			back() {
				uni.navigateBack({
					delta: 1
				});
			},
		}
	};
</script>



<script module="echarts" lang="renderjs">
	let myChart
		let pollutChart
	export default {
		mounted() {
				if (typeof window.echarts === 'function') {
					this.initEcharts()
				} else {
					// 动态引入较大类库避免影响页面展示
					const script = document.createElement('script')
					// view 层的页面运行在 www 根目录，其相对路径相对于 www 计算
					script.src = 'static/echarts2.js'
					script.onload = this.initEcharts.bind(this)
					document.head.appendChild(script)
				}
			
		},
		methods: {
			initEcharts() {
				this.$nextTick(function(){
					var pollutdiv = document.getElementById("pollutEcharts");
					pollutdiv.setAttribute("style","display:block;height:500px,width:100%;");
					pollutChart = echarts.init(document.getElementById('pollutEcharts'))
			    	//观测更新的数据在 view 层可以直接访问到
					pollutChart && pollutChart.setOption(this.pollutOption)

					 var div = document.getElementById("echarts");
                     div.setAttribute("style","display:block;height:500px,width:100%;");
					myChart = echarts.init(document.getElementById('echarts'))
					// 观测更新的数据在 view 层可以直接访问到
					myChart && myChart.setOption(this.productOption)
				})

			},
			updateEcharts(newValue, oldValue, ownerInstance, instance) {
				if (!newValue) {
					return;
				}
				//console.log('update');
				let div = document.getElementById('echarts');
				div.style.height = newValue.yAxis.data.length * 30 + 40 + 'px';
				// 监听 service 层数据变更
				newValue.tooltip.formatter = function(obj) {
						let data = obj.data;
						return `<div style="background:${data.statusLabel== '停止'?'#ff7054':(data.statusLabel== '开启'?'#7dcd27':'#888')};color:#fff;padding:6px;opacity:0.9;border-radius:6px;border:1px solid #ddd;font-size:12px;">          ${data.name}</br>
						   开始时间：${data.start.slice(0,16)} </br>
						   结束时间：${data.end} </br>
						   状态：${data.statusLabel} </br>
						 </div>`;
					};
					newValue.tooltip.confine = true;
					newValue.tooltip.position = function (point, params, dom, rect, size) {
					     pollutdiv.style.transform = 'translateZ(0)'  //处理ios,不显示tooltip
					 }
					myChart && myChart.clear()
					myChart && myChart.setOption(newValue)
					myChart && myChart.resize()
			},
			onClick(event, ownerInstance) {
				// 调用 service 层的方法
				ownerInstance.callMethod('onViewClick', {
					test: 'test'
				})
			},
			updatePollutEcharts(newValue, oldValue, ownerInstance, instance) {
				if (!newValue) {
					return;
				}
				let pollutdiv = document.getElementById('pollutEcharts');
				pollutdiv.style.height = newValue.yAxis.data.length*30 + 40 + 'px';
				// 监听 service 层数据变更
				newValue.tooltip.formatter = function(obj) {
						let data = obj.data;
						return `<div style="background:${data.statusLabel== '停止'?'#ff7054':(data.statusLabel== '开启'?'#7dcd27':'#888')};color:#fff;padding:6px;opacity:0.9;border-radius:6px;border:1px solid #ddd;font-size:12px;">          ${data.name}</br>
						   开始时间：${data.start.slice(0,16)} </br>
						   结束时间：${data.end} </br>
						   状态：${data.statusLabel} </br>
						 </div>`;
					};
				newValue.tooltip.confine = true;
				 newValue.tooltip.position = function (point, params, dom, rect, size) {
				      pollutdiv.style.transform = 'translateZ(0)'  //处理ios,不显示tooltip
				  }
				 pollutChart && pollutChart.clear()
				pollutChart && pollutChart.setOption(newValue)
				pollutChart && pollutChart.resize()
			},
			 pollut(event, ownerInstance) {
				// 调用 service 层的方法
				ownerInstance.callMethod('onViewClick', {
					test: 'test'
				})
			}
		}
	}
</script>


<style scoped>
/* 	*{
	touch-action: none;
	
	} */
</style>