/*
 * @Author: your name
 * @Date: 2021-05-07 16:52:35
 * @LastEditTime: 2021-05-07 16:57:01
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /SmartFormWeb/api/map-navigation.js
 */

export default {
    /**
     * @description: 
     * @param {*} mc 污染源的地址或名称
     * @param {*} jd 污染源经度
     * @param {*} wd 污染源纬度
     * @return {*}
     */
    getMapNavigation(mc,jd,wd){
        let url = "";
        if(mc){
            if (plus.os.name == "Android") { //判断是安卓端
                plus.nativeUI.actionSheet({ //选择菜单
                    title: "选择地图应用",
                    cancel: "取消",
                    buttons: [{
                        title: "腾讯地图"
                    }, {
                        title: "百度地图"
                    }, {
                        title: "高德地图"
                    }]
                }, function(e) {
                    switch (e.index) {
                        //下面是拼接url,不同系统以及不同地图都有不同的拼接字段
                        case 1:
                            //注意referer=xxx的xxx替换成你在腾讯地图开发平台申请的key
                            url = `qqmap://map/geocoder?coord=${wd},${jd}&referer=xxx`;
                            break;
                        case 2:
                            url =
                                `baidumap://map/marker?location=${wd},${jd}&title=${mc}&coord_type=gcj02&src=andr.baidu.openAPIdemo`;
                            break;
                        case 3:
                            url =
                                `androidamap://viewMap?sourceApplication=appname&poiname=${mc}&lat=${wd}&lon=${jd}&dev=0`;
                            break;
                        default:
                            break;
                    }
                    if (url != "") {
                        url = encodeURI(url);
                        //plus.runtime.openURL(url,function(e){})调起手机APP应用
                        plus.runtime.openURL(url, function(e) {
                            plus.nativeUI.alert("本机未安装指定的地图应用");
                        });
                    }
                })
            }
        }else{
            uni.showToast({
                title: '暂无地址可导航',
                duration: 2000,
                icon: 'none'
            });
        }
    }
}