<template>
	<view style="width: 100%;height:100%">
		<!-- #ifdef APP-PLUS || H5 -->
		<view :prop="option" :change:prop="echarts.updateEcharts" id="echarts" class="echarts" ref="echarts"></view>
		<!-- #endif -->
		<!-- #ifndef APP-PLUS || H5 -->
		<view>非 APP、H5 环境不支持</view>
		<!-- #endif -->
		<div class="ic-full" @click="back"></div>
	</view>
</template>

<script>
import { getZdsj } from '../../api/iot/realtime.js';
export default {
	data() {
		return {
			IMEI: '',
			SBMC: '',
			SBID: '',
			markLine: '',
			datetimerange: [
				this.$dayjs()
					.subtract(1, 'day')
					.format('YYYY-MM-DD HH:mm'),
				this.$dayjs().format('YYYY-MM-DD HH:mm')
			],
			end: this.$dayjs().format('YYYY-MM-DD HH:mm'),
			fullScreen: false,
			option: {
				color: [
					'#409EFF'
					// '#E88511', '#1C8B14', '#14AACF',  '#A825C9', '#781929', '#2C8B14'
				],
				legend: {
					show: false,
					data: [
						'振动能量'
						// 'X轴', 'Y轴', 'Z轴', 'X轴主频', 'Y轴主频', 'Z轴主频'
					],
					selected: {
						振动能量: true
						// X轴主频: false,
						// Y轴主频: false,
						// Z轴主频: false,
						// X轴: false,
						// Y轴: false,
						// Z轴: false
					}
				},
				grid: {
				},
				tooltip: {
					trigger: 'axis',
					formatter: function(params) {
						var res = '振动时间：' + params[0].name;
						for (var i = 0; i < params.length; i++) {
							res += '<br>' + params[i].seriesName + '：' + params[i].data;
						}
						return res;
					}
				},
				dataZoom: [
					{
						show: true,
						realtime: true,
						start: 0,
						end: 10,
						xAxisIndex: [0, 1]
					}
				],
				xAxis: {
					name: '检查时间',
					scale: true,
					type: 'category',
					data: ['2020-09-01', '2020-10-01']
					// axisLabel: {
					//     interval: 2,
					//     rotate: -20,
					//     formatter: function (val) {
					//         return val;
					//     }
					// }
				},
				yAxis: [
					{
						type: 'value',
						name: '能　     \n量　　 ',
						nameLocation: 'middle',
						nameGap: 10,
						nameRotate: 0,
						nameTextStyle: {
							fontSize: 14
						},
						//默认以千分位显示，不想用的可以在这加一段
						axisLabel: {
							//调整左侧Y轴刻度， 直接按对应数据显示
							show: true,
							showMinLabel: true,
							showMaxLabel: true,
							formatter: function(value) {
								return value;
							}
						}
					},
					{
						type: 'value',
						name: '　　　　主\n　　　　频',
						nameLocation: 'center',
						nameGap: 10,
						nameRotate: 0,
						nameTextStyle: {
							fontSize: 16
						},
						//默认以千分位显示，不想用的可以在这加一段
						axisLabel: {
							//调整左侧Y轴刻度， 直接按对应数据显示
							show: true,
							showMinLabel: true,
							showMaxLabel: true,
							formatter: function(value) {
								return value;
							}
						}
					}
				],
				series: [
					// {
					// 	yAxisIndex: 0,
					// 	itemStyle: {
					// 		normal: {
					// 			lineStyle: {
					// 				color: '#E88511'
					// 			}
					// 		}
					// 	},
					// 	data: [],
					// 	name: 'X轴',
					// 	type: 'line'
					// },
					// {
					// 	yAxisIndex: 0,
					// 	itemStyle: {
					// 		normal: {
					// 			lineStyle: {
					// 				color: '#1C8B14'
					// 			}
					// 		}
					// 	},
					// 	data: [],
					// 	name: 'Y轴',
					// 	type: 'line'
					// },
					// {
					// 	yAxisIndex: 0,
					// 	itemStyle: {
					// 		normal: {
					// 			lineStyle: {
					// 				color: '#14AACF'
					// 			}
					// 		}
					// 	},
					// 	data: [],
					// 	name: 'Z轴',
					// 	type: 'line'
					// },
					// {
					// 	yAxisIndex: 1,
					// 	itemStyle: {
					// 		normal: {
					// 			lineStyle: {
					// 				color: '#342BE2'
					// 			}
					// 		}
					// 	},
					// 	data: [],
					// 	name: 'X轴主频',
					// 	type: 'line'
					// },
					// {
					// 	yAxisIndex: 1,
					// 	itemStyle: {
					// 		normal: {
					// 			lineStyle: {
					// 				color: '#A825C9'
					// 			}
					// 		}
					// 	},
					// 	data: [],
					// 	name: 'Y轴主频',
					// 	type: 'line'
					// },
					// {
					// 	yAxisIndex: 1,
					// 	itemStyle: {
					// 		normal: {
					// 			lineStyle: {
					// 				color: '#781929'
					// 			}
					// 		}
					// 	},
					// 	data: [],
					// 	name: 'Z轴主频',
					// 	type: 'line'
					// },
					{
						yAxisIndex: 0,
						itemStyle: {
							normal: {
								lineStyle: {
									color: '#2C8B14'
								}
							}
						},
						markLine: {
							symbol: ['circle', 'none'],
							data: [
								{
									silent: false,
									lineStyle: {
										type: 'solid',
										color: '#3398DB'
									},
									label: {
										position: 'end'
									},
									yAxis: 0
								},
								// {
								// 	silent: false,
								// 	lineStyle: {
								// 		type: 'solid',
								// 		color: '#3398DB'
								// 	},
								// 	label: {
								// 		position: 'end'
								// 	},
								// 	yAxis: 0
								// }
							]
						},
						data: [],
						name: '振动能量',
						type: 'line'
					}
				]
			}
		};
	},
	mounted() {},
	onLoad(options) {
		// #ifdef APP-PLUS
		plus.screen.lockOrientation('landscape-primary'); //横屏
		// #endif
		this.IMEI = options.imei;
		this.SBID = options.sbid;
		this.SBMC = options.sbmc;
		this.markLine = options.markLine;
		this.datetimerange = options.daterange.split(",")
		this.initChartData();
	},
	onBackPress(e) {
		// 退出页面时解除横屏
		// #ifdef APP-PLUS
		if (e.from == 'backbutton') {
			plus.screen.lockOrientation('portrait-primary'); //
			setTimeout(() => {
				uni.navigateTo({ url: `./whitePage` });
			}, 200);
			return true;
		}
		// #endif
	},
	methods: {
		initChartData() {
			getZdsj({ IMEI: this.IMEI, startT: this.datetimerange[0], endT: this.datetimerange[1] }).then(res => {
				this.initChart(res.data.list, res.data.sbjbxx);
			});
		},
		initChart(data, sbxx) {
			// 时间轴
			this.option.xAxis.data = [];
			this.option.series[0].data = [];
			// this.option.series[1].data = [];
			// this.option.series[2].data = [];
			// this.option.series[3].data = [];
			// this.option.series[4].data = [];
			// this.option.series[5].data = [];
			// this.option.series[6].data = [];

			for (var i = 0; i < data.length; i++) {
				const row = data[i];
				row.power = Math.sqrt(row.X * row.X + row.Y * row.Y + row.Z * row.Z);
				row.power = Number(row.power).toFixed(0);
				this.option.xAxis.data.push(row.JCSJ.replace(' ', '\n'));
				// this.option.series[0].data.push(row.X);
				// this.option.series[1].data.push(row.Y);
				// this.option.series[2].data.push(row.Z);
				// this.option.series[3].data.push(row.X_FREQ);
				// this.option.series[4].data.push(row.Y_FREQ);
				// this.option.series[5].data.push(row.Z_FREQ);
				this.option.series[0].data.push(row.power);
			}
			this.option.series[0].markLine.data[0].yAxis = sbxx.JYYZ.split('#')[0];
			// this.option.series[0].markLine.data[1].yAxis = sbxx.JYYZ.split('#')[0];
		},
		back() {
			// 退出页面时解除横屏
			// #ifdef APP-PLUS
			plus.screen.lockOrientation('portrait-primary'); //
			setTimeout(() => {
				uni.navigateTo({
					url: './whitePage'
				});
			}, 200);
			// #endif
		}
	}
};
</script>
<script module="echarts" lang="renderjs">
let myChart
export default {
	data(){
		return{
			chartw:"",
			charth:'',
			flag:false
		}
	},
	onLoad() {


	},
	mounted() {

		if (typeof window.echarts === 'function') {
			this.initEcharts()
		} else {
			// 动态引入较大类库避免影响页面展示
			const script = document.createElement('script')
			// view 层的页面运行在 www 根目录，其相对路径相对于 www 计算
			script.src = 'static/echarts.js'
			script.onload = this.initEcharts.bind(this)
			document.head.appendChild(script)
		}
	},
	methods: {
		initEcharts() {
		myChart = echarts.init(document.getElementById('echarts'))
		// 观测更新的数据在 view 层可以直接访问到
		myChart&&myChart.setOption(this.option)
		},
		updateEcharts(newValue, oldValue, ownerInstance, instance) {
			// 监听 service 层数据变更
			// #ifdef APP-PLUS
				document.getElementById('echarts').style.width=plus.screen.resolutionWidth+'px'
				document.getElementById('echarts').style.height=(plus.screen.resolutionHeight-40)+'px'
			// #endif
		myChart&&myChart.setOption(newValue)
		myChart.resize()
		},
	}
}
</script>
<style scoped></style>
