import http from '@/common/net/http.js';
import storage from '@/common/storage.js'
import { debounceRequest } from '@/common/net/debounce-request.js'

const CACHE_PREFFIX = 'code-set'

function createCacheKey(code){
	return `${CACHE_PREFFIX}@${code}`;
}

/**
 *通过服务获取代码值
 * @param {Object} code
 */
function queryCodesRemote(code){
	return new Promise((resolve, reject) => {
		let handler = (codes) => {
			resolve(codes)
		}
		debounceRequest(`SEARCH_GGDMZ@${code}`, handler, (delayHandlers) => {
			http.post(`${http.url}`, {
				service:'SEARCH_GGDMZ',
				DMJBH: code,
				version:'1'
			}).then(resp => {
				let resetCodes = resp.map((item, index) => {
					return {
						code: item.DM,
						name: item.DMNR,
						parent: item.FDM || '',
						order: item.PXH || index
					}
				});
				resolve(resetCodes);
				saveCodesLocal(code, resetCodes);
				delayHandlers.forEach(h => {
					h(resetCodes)
				})
			})
			.catch(error => {
			})
		})
	});
}

// 如果有父亲代码的时候
function queryParentCode(code, parentCode){
	return new Promise((resolve, reject) => {
		http.post(`${http.url}`, {
			service:'SEARCH_COMMON_CODE',
			DMJBH: code,
			FDM: parentCode,
			version:'1'
		}).then(resp => {
			resolve(resp);
			// saveCodesLocal(code, resetCodes);
		})
		.catch(error => {
		})
	});
}

/**
 * 缓存代码值到本地
 * @param {Object} code
 * @param {Object} codes
 */
function saveCodesLocal(code, codes){
	let cacheKey = createCacheKey(code);
	storage.setStorageWithExpired(code, codes)
}

/**
 * 本地获取公共代码值
 * @param {Object} code
 */
function getCodesLocal(code){
	let cacheKey = createCacheKey(code);
	return storage.getEffectiveStorage(code)
}

/**
 * 根据代码集编号获取代码值定义集合
 */
const getCodeSet = (code) => {
	return new Promise((resolve, reject) => {
		let localCodes = getCodesLocal(code);
		if(localCodes){
			resolve(localCodes);
		} else {
			resolve(queryCodesRemote(code));
		}
	});
}

/**
 * 获取某个具体的代码值
 */
const getCode = (setCode, code) => {
	return new Promise((resolve, reject) => {
		getCodeSet(setCode)
			.then(codes => {
				let match = codes.filter(item => {
					return code === item.code;
				})
				
				if(match.length === 0) {
					resolve(null)
				} else {
					resolve(match[0])
				}
			})
			.catch(error => {
				reject(error);
			})
	});
}

export default {	
	getCodeSet,
	getCode,
	queryParentCode
}