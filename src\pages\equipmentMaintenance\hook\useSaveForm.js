/** @format */

import { saveFormdataDetail } from '@/api/iot/equipmentMaintenance';
import { getValid, getValidUploadImage } from '@/utils/validData.js';
export default function useSaveForm() {
    /**
     * @method 保存运维明细
     * @param {*} objRules   校验规则
     * @param {*} objValiData  需要校验的字段对象
     * @param {*} info  表单数据
     * @param {*} pages  当前页码
     * @param {*} isNeedValidImage true 是否需要校验图片
     * @param {*} arrUploadImage  上传的图片数组
     * @param {*} isSpecifyTypeImage   是否需要校验图片类型

     */
    const handleSave = async (
        objRules,
        objValiData,
        info,
        pages,
        isNeedValidImage = true,
        arrUploadImage = [],
        isSpecifyTypeImage = false
    ) => {
        try {
            await getValid(objRules, objValiData);
            if (isNeedValidImage) {
                await getValidUploadImage(arrUploadImage, isSpecifyTypeImage);
            }

            const data = await saveFormdataDetail(info);
            if (data.success === '操作成功') {
                uni.showToast({
                    title: '操作成功'
                }).then(() => {
                    saveSuccessCallback(pages);
                });
            }
        } catch (error) {
            console.log('useSaveForm-error', error);
        }
    };

    //保存成功回调
    const saveSuccessCallback = (pages) => {
        //刷新上一页面的设备列表
        let beforePage = pages[pages.length - 2]; // 上一页
        let beforeBeforePage = pages[pages.length - 3]; // 上上一页
        if (beforePage.$vm) {
            beforePage.$vm.getMaintenanceDetailList &&
                beforePage.$vm.getMaintenanceDetailList();
        } else {
            beforePage.getMaintenanceDetailList &&
                beforePage.getMaintenanceDetailList();
        }
        if (beforeBeforePage.$vm) {
            beforeBeforePage.$vm.freshData && beforeBeforePage.$vm.freshData();
        } else {
            beforeBeforePage.freshData && beforeBeforePage.freshData();
        }
        uni.navigateBack({
            delta: 1
        });
    };
    return {
        handleSave
    };
}
