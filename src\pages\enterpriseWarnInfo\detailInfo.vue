<template>
		<section class="main" >
			<div class="inner" >
				<div class="gap"></div>
				<ul class="zy-data1">
					<li @click="opLocaltion">
						<p class="p1">企业地址：</p>
						<p class="p2 zy-local">
							{{ enterpriseInfo.DWDZ&&enterpriseInfo.DWDZ.slice(0, 14) || '--' }}{{ enterpriseInfo.DWDZ&&enterpriseInfo.DWDZ.length > 13 ? '...' : '' }}
						</p>
					</li>
					<li>
						<p class="p1">所属区县：</p>
						<p class="p2">{{ enterpriseInfo.SSQX || '--' }}</p>
					</li>
					<li>
						<p class="p1">统一社会信用代码:</p>
						<p class="p2">{{ enterpriseInfo.TYSHXYDM || '--' }}</p>
					</li>
					<li>
						<p class="p1">法定代表人：</p>
						<p class="p2">{{ enterpriseInfo.FDDBR || '--' }}</p>
					</li>
					<li>
						<p class="p1">法人电话：</p>
						<p class="p2">{{ enterpriseInfo.FDDBRDH || '--' }}</p>
					</li>
					<li>
						<p class="p1">环保联系人：</p>
						<p class="p2">{{ enterpriseInfo.HBLXR || '--' }}</p>
					</li>
					<li>
						<p class="p1">环保联系人电话：</p>
						<p class="p2">{{ enterpriseInfo.HBLXRDH || '--' }}</p>
					</li>
				</ul>
			</div>
		</section>
</template>

<script>
	import {
		getInfo,
		getXssj
	} from '../../api/iot/realtime.js';
	export default {
		data() {
			return {
				wrybh: '',
				enterpriseInfo: {},
				xzqhdm: '',
			};
		},
		mounted() {},
		onLoad(option) {
			let userinfo = uni.getStorageSync('user_info');
			this.xzqhdm = userinfo.orgid;
			this.wrybh = option.wrybh;
			this.initInfo();
		},
		methods: {
			initInfo() {
				getInfo({
					WRYBH: this.wrybh,
					XZQHDM: this.xzqhdm
				}).then(res => {
					this.enterpriseInfo = res.data.qyjbxx;
					uni.setNavigationBarTitle({
						title:this.enterpriseInfo.WRYMC
					})
				});
			},
			// 唤起系统导航app进行导航
			opLocaltion() {
				uni.getLocation({
					success: res => {
						uni.openLocation({
							latitude: parseFloat(this.enterpriseInfo.WD),
							longitude: parseFloat(this.enterpriseInfo.JD),
							name: this.enterpriseInfo.WRYMC,
							scale: 8
						});
					}
				});
			},
		}
	}
</script>

<style scoped>
	
	.inner{
		padding-top: 0;
		height: 100vh;
	}
.zy-data1{
	margin: 0;
	border-radius: 0;
	font-size: 26.570025rpx;
}

.zy-data1 li{
	padding: 8rpx 0;
}

.zy-data1 li + li{
	border-top: 1px solid #EEEEEE;
}
</style>
