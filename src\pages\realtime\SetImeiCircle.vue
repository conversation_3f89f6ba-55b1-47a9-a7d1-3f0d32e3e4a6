<template>
	<view style="position: relative;height: 100%;">
		<header class="header">
			<i class="ic-back" @click="back"></i>
			<h1 class="title">{{barTitle}}</h1>
		</header>
		<div>
			<ul class="msgbox">
				<li  @click="toThresholdDialog">
					<view class="left-text">
						设置阈值：
					</view>
					<view class="setcontent">
						<span>{{threshold}}</span>
						<span class="to-detail">
							<image class="gray-icon-reverse" src="@/static/app/images/gray-arrow.png" mode="widthFix">
							</image>
						</span>
					</view>
				</li>
				<li @click="toCycleDialog">
					<view class="left-text">
						设置周期：
					</view>
					<view class="setcontent">
						<span>检测周期：</span>
						<span style="margin-right:30rpx;">{{sleepTime}}</span>
						<span>上报周期：</span>
						<span>{{sendCycle}}</span>
						<span class="to-detail" >
							<image class="gray-icon-reverse" src="@/static/app/images/gray-arrow.png" mode="widthFix">
							</image>
						</span>
					</view>
				</li>
			</ul>
		</div>

		<!-- 阈值弹窗 -->
		<div class="dialog-box" v-show="showThresholdDialog">
			<div class="head">
				<image @click="showThresholdDialog = !showThresholdDialog" class="gray-icon"
					src="@/static/app/images/gray-arrow.png" mode="widthFix"></image>
				<div class="title">设置实际阈值</div>
				<span class="send" @click="sendThreshold">发送</span>
			</div>
			<div class="input-box">
				<input class='input-msg' type="number" v-model="setThreshold" placeholder="请输入实际阈值" />
				<div class="describe-box">
					实际阈值：设置判断被监控设备运行状态的阈值范围
				</div>
			</div>
			<div class="historybox" v-if="arrHistoryThreshold.length">
				<div class="update">
					<image class="icon" mode="widthFix" src="@/static/app/images/columnicon.png"></image>
					发送记录
				</div>
				<ul v-for="item in arrHistoryThreshold">
					<li>
						<view class="left-text">设备IMEI：</view>
						<view>{{item.IMEI || '-'}}</view>
					</li>
					<li>
						<view class="left-text">上报阈值：</view>
						<view>{{item.setThreshold || '-'}}</view>
					</li>
					<li>
						<view class="left-text">提交时间：</view>
						<view>{{item.setSendTime || '-'}}</view>
					</li>
				</ul>
			</div>
		</div>

		<!-- 周期弹窗 -->
		<div class="dialog-box" v-show="showCycleDialog">
			<div class="head">
				<image @click="showCycleDialog = !showCycleDialog" class="gray-icon"
					src="@/static/app/images/gray-arrow.png" mode="widthFix"></image>
				<div class="title">设置周期</div>
				<span class="send" @click="sendNewCycle">发送</span>
			</div>
			<div class="input-box">
				<div class="input-parent">
					<span class="danwei">秒</span>
					<input class='input-msg' type="number" v-model="setSleepTime" placeholder="请输入检测周期" />
				</div>
				<div class="describe-box">
					检测周期：设置振动监控盒子多长时间采集、分析被监控设备的振动数据
				</div>
				<div class="input-parent">
					<span class="danwei">分</span>
					<input class='input-msg' type="number" v-model="setSendCycle" placeholder="请输入上报周期" />
				</div>
				<div class="describe-box">
					上报周期：设置振动监控盒子多长时间上报所有待上传的检测结果数据　
				</div>
				<div @click="setValue" class="zy-bot-btn1">设置默认值</div>
				<div class="gap"></div>
				<div class="describe-box">
					默认值说明：设置检测周期为300秒，设置上报周期为120分
				</div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
			</div>
			
			<div class="historybox" v-if="arrHistoryCycle.length">
				<div class="update">
					<image class="icon" mode="widthFix" src="@/static/app/images/columnicon.png"></image>
					发送记录
				</div>
				<ul v-for="item in arrHistoryCycle">
					<li>
						<view class="left-text">设备IMEI：</view>
						<view>{{item.IMEI || '-'}}</view>
					</li>
					<li>
						<view class="left-text">检测周期：</view>
						<view>{{item.setSleepTime || '-'}} 秒</view>
					</li>
					<li>
						<view class="left-text">上报周期：</view>
						<view>{{item.setSendCycle || '-'}} 分</view>
					</li>
					<li>
						<view class="left-text">提交时间：</view>
						<view>{{item.setSendTime || '-'}}</view>
					</li>
				</ul>
			</div>
		</div>
	</view>
</template>

<script>
	import appWatch from '@/utils/app-watch.js';
	import {
		pubCycle,
		pubthreshold,
		getZnsbInfo
	} from '@/api/iot/planeFigure.js'
	export default {
		data() {
			return {
				IMEI: '',
				barTitle: '参数设置',
				threshold: '', //阈值
				sendCycle: '', //上报周期
				sleepTime: '', //检测周期
				setThreshold: '', //阈值
				setSendCycle: '', //上报周期
				setSleepTime: '', //检测周期
				showThresholdDialog: false,
				showCycleDialog: false,
				arrHistoryThreshold: [

				], //历史阈值提交记录
				arrHistoryCycle: [], //历史周期提交记录
				userInfo: {}
			}
		},
		onLoad(option) {
			this.IMEI = option.IMEI;
			this.barTitle = this.IMEI + '参数设置'
			this.userInfo = uni.getStorageSync('userInfo');
			
			let arrHistoryThreshold = uni.getStorageSync('arrHistoryThreshold') || []
			let arrHistoryCycle = uni.getStorageSync('arrHistoryCycle') || []
			this.arrHistoryThreshold = arrHistoryThreshold.filter(item => item.IMEI === this.IMEI)
			this.arrHistoryCycle = arrHistoryCycle.filter(item => item.IMEI === this.IMEI)
		},
		mounted() {
			this.getZnsbInfo()
		},
		methods: {
			//设置默认值
			setValue(){
				this.setSleepTime = 300;
				this.setSendCycle = 120;
			},
			async getZnsbInfo() {
				let params = {
					IMEI: this.IMEI
				}
				const {
					data: {
						SLEEPTIME,
						SENDCYCLE,
						THRESHOLD
					}
				} = await getZnsbInfo(params)
				this.sleepTime = SLEEPTIME;
				this.sendCycle = SENDCYCLE;
				this.threshold = THRESHOLD.substr(0, THRESHOLD.indexOf('#'));
			},
			//设置阈值
			async sendThreshold() {
				if (this.setThreshold == '') {
					uni.showToast({
						title: '请填写阈值',
						icon: "none",
						duration: 1000
					});
					return;
				} else {
					let params = {
						"name": this.userInfo.name,
						"imeiList": [
							this.IMEI
						],
						"bz": "",
						"threshold": this.setThreshold + '#' + this.setThreshold
					}
					const res = await pubthreshold(params).catch(err => {
						console.log(err)
					})
					let historyObj = {
						IMEI: this.IMEI,
						setSendTime: this.$dayjs().format('YYYY-MM-DD HH:mm'),
						setThreshold: this.setThreshold
					}
					
					this.arrHistoryThreshold.unshift(historyObj)
					
					let arrHistoryThreshold = uni.getStorageSync('arrHistoryThreshold') || []
					arrHistoryThreshold.unshift(historyObj)
					uni.setStorageSync('arrHistoryThreshold', arrHistoryThreshold)
					
					uni.showToast({
						title: '发送成功'
					}).then(res => {
						this.showThresholdDialog = false;
					})
				}
			},
			//设置周期
			async sendNewCycle() {
				if (this.setSendCycle == '') {
					uni.showToast({
						title: '请填写上报周期',
						icon: "none",
						duration: 1000
					});
					return;
				} else if (this.setSleepTime == '') {
					uni.showToast({
						title: '请填写检测周期',
						icon: "none",
						duration: 1000
					});
					return;
				} else {
					let params = {
						"name": this.userInfo.name,
						"imeiList": [
							this.IMEI
						],
						"bz": "",
						"sendCycle": this.setSendCycle,
						"sleepTime": this.setSleepTime
					}
					const res = await pubCycle(params).catch(err => {
						console.log(err)
					})
					let historyObj = {
						IMEI: this.IMEI,
						setSendTime: this.$dayjs().format('YYYY-MM-DD HH:mm'),
						setSleepTime: this.setSleepTime,
						setSendCycle: this.setSendCycle,
					}
					
					this.arrHistoryCycle.unshift(historyObj)
					
					let arrHistoryCycle = uni.getStorageSync('arrHistoryCycle') || []
					arrHistoryCycle.unshift(historyObj)
					uni.setStorageSync('arrHistoryCycle', arrHistoryCycle)
					uni.showToast({
						title: '发送成功'
					}).then(res => {
						this.showCycleDialog = false;
					})
				}
			},
			//设置阈值弹窗
			toThresholdDialog() {
				this.showThresholdDialog = true;
				this.setThreshold = this.threshold;
			},
			//设置周期弹窗
			toCycleDialog() {
				this.showCycleDialog = true;
				this.setSleepTime = this.sleepTime;
				this.setSendCycle = this.sendCycle;
				appWatch.getClickWatchAPP({
				   url:'pages/realtime/SetImeiCircle',
				   clickModule:'设备周期设置'
				}); 
				
			},
			back() {

				uni.navigateBack({
					delta: 1
				});
			},
		}
	}
</script>

<style scoped>
	html {
		-webkit-tap-highlight-color: transparent;
		height: 100%;
	}

	body {
		-webkit-backface-visibility: hidden;
		height: 100%;
	}

	.header {
		position: relative;
	}

	.msgbox {
		padding: 10rpx;
		font-size: 30rpx;
	}

	.setcontent {
		display: flex;
	}

	.msgbox li {
		display: flex;
		justify-content: space-between;
		padding: 20rpx 14rpx;
		border-bottom: 1px solid #ddd;
	}

	.to-detail {
		margin-left: 12rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 50rpx;
	}

	.to-detail .gray-icon-reverse {
		width: 26rpx;
		transform: rotate(180deg);
	}

	.dialog-box {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: #f2f2f2;
		z-index: 1001;
		font-size: 30rpx;


	}

	.dialog-box .head {
		background: #fff;
		padding: 10rpx 20rpx;
		line-height: 60rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.dialog-box .head .title {
		font-weight: bold;
		color: #333;
	}

	.dialog-box .send {
		color: #fff;
		background: #4874ff;
		border-radius: 6rpx;
		padding: 8rpx 32rpx;
		font-size: 28rpx;
		line-height: 40rpx;
	}

	.dialog-box .input-box {
		padding: 20rpx;
	}

	.dialog-box .input-msg {
		border: 1px solid #ddd;
		background: #fff;
		border-radius: 6rpx;
		margin-bottom: 20rpx;
		line-height: 80rpx;
		padding: 6rpx 6rpx 6rpx 10rpx;
		height: 66rpx;

	}

	.dialog-box .describe-box {
		margin-bottom: 30rpx;
		font-size: 28rpx;
		line-height: 46rpx;
		color: #888;
		position: relative;
	}

	.dialog-box .input-parent {
		position: relative;
	}

	.dialog-box .danwei {
		position: absolute;
		top: 12rpx;
		right: 20rpx;
	}

	.dialog-box .historybox {
		border: 1px solid #ddd;
		border-radius: 6rpx;
		margin: 0 20rpx;
		background: #fff;
		padding: 20rpx;
	}

	.dialog-box .historybox .update {
		padding: 10rpx 0 20rpx 0;
		font-weight: bold;
		font-size: 30rpx;
		border-bottom: 1px solid #ddd;

	}

	.dialog-box .historybox .icon {
		margin-right: 10rpx;
		position: relative;
		top: 5rpx;

	}

	.dialog-box .historybox .update .icon {
		width: 30rpx;
		height: 30rpx;

	}

	.dialog-box .historybox ul {
		padding: 16rpx 0;
	}

	.dialog-box .historybox ul+ul {
		border-top: 1px solid #ddd;
	}

	.dialog-box .historybox li {
		display: flex;
		justify-content: space-between;
		align-items: center;
		line-height: 58rpx;
		color: #333;
	}

	.gray-icon {
		width: 38rpx;
	}

	.left-text {
		color: #999;
	}
	.zy-bot-btn1{
		height: 36px;
		border-radius: 23px;
		background-color: #4874ff;
		font-size: 16px;
		color: #fff;
		text-align: center;
		line-height: 36px;
		margin: 0 auto;
	}
</style>
