<!-- @format -->

<template>
	<body style="background: #f1f1f1">
		<section class="main">
			<div class="inner">
				<div class="gap"></div>
				<div class="zy-form">
					<div class="item">
						<p class="label star">生产线名称</p>
						<div class="rp">
							<input type="text" class="zy-input1 pd-txt2" v-model="model.SCXMC" placeholder="请填写生产线名称"
								@confirm="setLineName" />
						</div>
					</div>
					<div class="item">
						<p class="label star">污染物</p>
						<div class="rp">
							<div class="zy-selectBox" @click="zywrShow = true">
								<p class="res placeholder" v-if="!model.ZYWR">
									请选择污染物(可多选)
								</p>
								<p class="res input pd-txt2" v-if="model.ZYWR">
									{{ model.ZYWR }}
								</p>
							</div>
						</div>
					</div>
					<div class="item remark">
						<p class="label star">工艺流程</p>
						<div class="rp">
							<textarea auto-height maxlength="-1" :class="model.GYLC ? '' : 'two'"
								class="zy-textarea1 pd-txt2" placeholder="请填写生产工艺流程描述，如：切割+压贴+组装" v-model="model.GYLC"
								@focus="onFocus" @blur="onBlur"></textarea>
						</div>
					</div>
					<div class="item remark">
						<p class="label">备注</p>
						<div class="rp">
							<textarea auto-height maxlength="-1" :class="model.BZ ? '' : 'three'"
								class="zy-textarea1 pd-txt2" placeholder="请填写备注信息, 如：此生产线默认为生产设备只要开启一个即认为在生产，如有特殊情况请写明"
								v-model="model.BZ"></textarea>
						</div>
					</div>
				</div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="zy-bot-btn1" @click="save()">保存</div>
				<div class="gap"></div>
				<div class="gap"></div>
			</div>
		</section>

		<u-popup v-model="zywrShow" mode="bottom" width="100%" height="50%" border-radius="20">
			<div class="opration">
				<span @click="cancel" class="cancel">取消</span>
				<span @click="confirm" class="confirm">确定</span>
			</div>
			<div class="listWarp">
				<p v-for="(item, index) in zywrList" @click="selectWrw(item)"
					:class="selectWrwList.includes(item.value) ? 'on' : ''">
					{{ item.label }}
				</p>
			</div>
		</u-popup>
	</body>
</template>

<script>
	import {
		getGgdmz
	} from '@/api/iot/ggdmz.js';
	import {
		scxInfo,
		scxSave,
		scxUpdata,
		getScxList
	} from '@/api/iot/enterprise.js';
	export default {
		data() {
			return {
				enterpriseInfo: {},
				info: {},
				model: {
					ORGID: '',
					WRYBH: '',
					USER: '',
					SCXMC: '', //生产线名
					ZYWR: '', //污染物
					GYLC: '', //工艺流程
					BZ: '' //备注内容
				},
				selectWrwList: [], //选中的污染物list
				zywrShow: false,
				zywrList: [],
				rules: {
					SCXMC: {
						required: true,
						message: '请填写生产线',
						trigger: 'change'
					},
					ZYWR: {
						required: true,
						message: '请选择污染物'
					},
					GYLC: {
						required: true,
						message: '请填写工艺流程'
					}
				},
				border: false, //input 是否显示边框, 默认false
				labelWidth: 200,
				selectShow: false,
				labelPosition: 'left', //表单域提示文字的位置，left-左侧，top-上方
				errorType: ['border-bottom'], //错误的提示方式，数组形式, 可选值为：
				copy: false,
				type: 'add',
				backPage: false,
				showBottomBtn: true,
				lineList: [], //生产线
			};
		},
		onReady() {
			uni.onKeyboardHeightChange((res) => {
				console.log(res.height);
			});
		},
		onLoad(option) {
			this.enterpriseInfo = uni.getStorageSync('userInfo');
			this.type = option.type;

			this.info = JSON.parse(decodeURIComponent(option.info));
			if (this.type == 'edit') {
				this.model = this.info;
				uni.setNavigationBarTitle({
					title: '修改生产线'
				});
			}
		},
		onHide() {},
		watch: {
			model: {
				handler(newVal, oldVal) {
					if (this.type == 'add') {
					    this.backPage = true;
					}
				},
				deep: true,
			},
		
		},
		mounted() {
			this.getGgdmz();
		},
	    onBackPress() {
			if (this.type == 'edit') {
				return false;
			}
			let self = this;
			if (this.backPage) {
				uni.showModal({
					title: '提示',
					content: '当前内容未保存，是否离开?',
					success: function(res) {
						if (res.confirm) {
							console.log('用户点击确定');
							self.backPage = false;
							uni.navigateBack({
								delta: 1,
								mark: '污染源-列表-详情-新增生产线-离开',
								model: '污染源-列表'
							});
						} else if (res.cancel) {
							console.log('用户点击取消');
							self.backPage = true;
						}
					}
				});
			}
			uni.hideKeyboard();
			return this.backPage;
		},
		methods: {
			//设置产线名称
			async setLineName() {
				try {
					await this.validIsHadLineName()
				} catch (e) {
					console.log('error')
					this.model.SCXMC = ''
				}
			},
			//校验生产线是否有重名
			validIsHadLineName() {
				return new Promise((resolve, reject) => {
					let {
						sjqx
					} = this.enterpriseInfo;
					let {
						WRYBH
					} = this.info;
					getScxList({
						ORGID: sjqx,
						WRYBH: WRYBH
					}).then((res) => {
						this.lineList = res.data || [];
						let objSameNameLine = this.lineList.find(item => item.SCXMC === this.model.SCXMC);
						if (objSameNameLine) {
							uni.showToast({
								title: '该企业已有同名生产线，请重新输入',
								icon: 'none'
							});
							return reject();
						} else {
							return resolve();
						}

					});
				})

			},

			getGgdmz() {
				// 主要污染
				getGgdmz({
					code: 'CWSB_ZYWR'
				}).then((res) => {
					if (res.data && res.data.length > 0) {
						this.zywrList = res.data;
					}
				});
			},
			getInfo() {
				scxInfo({
					SCXID: this.info.SCXID
				});
			},
			confirm() {
				this.zywrShow = false;
				this.model.ZYWR = this.selectWrwList.join(',');
			},
			// 污染物  事件
			selectWrw(wrw) {
				let index = this.selectWrwList.findIndex((item) => {
					return item == wrw.value;
				});
				if (index != -1) {
					this.selectWrwList.splice(index, 1);
				} else {
					this.selectWrwList.push(wrw.value);
				}
			},
			cancel() {
				this.zywrShow = false;
				this.selectWrwList = [];
			},

			showTips() {
				this.showTip = !this.showTip;
			},

			getMore() {},

			save() {
				this.model.ORGID = uni.getStorageSync('ORGID') || '';
				this.model.WRYBH = this.info.WRYBH;
				this.model.USER = this.enterpriseInfo.name;
				let rules = Object.keys(this.rules);
				for (let i = 0; i < rules.length; i++) {
					let field = rules[i];
					let requires = this.rules[field];
					console.log(!this.model[field], requires.required);
					if (
						(!this.model[field] || !this.model[field].length) &&
						requires.required
					) {
						uni.showToast({
							title: requires.message
						});

						return;
					}
				}

				let self = this;
				if (this.type == 'edit') {
					scxUpdata(this.model).then((res) => {
						uni.showToast({
							title: '操作成功',
							duration: 1000
						}).then(() => {
							setTimeout(() => {
								// uni.navigateTo({
								// 	url: './saveScxSuccess?scxName='+this.model.SCXMC+'&info='+ encodeURIComponent(JSON.stringify(this.info))
								// });
								// uni.setStorageSync('isAddScx', true);
								let pages = getCurrentPages(); // 当前页面
								let beforePage = pages[pages.length - 2]; // 上一页
								if (beforePage.$vm) {
									beforePage.$vm.initFormData();
								} else {
									beforePage.initFormData();
								}

								//刷新上两页
								let parentPage = pages[pages.length - 3];
								if (parentPage.$vm) {
									parentPage.$vm.getScx &&
										parentPage.$vm.getScx();
									parentPage.$vm.getScsbList &&
										parentPage.$vm.getScsbList();
								} else {
									parentPage.getScx();
									parentPage.getScsbList();
								}
								this.backPage = false;
								uni.navigateBack({
									delta: 1,
									mark: '污染源-列表-详情-修改生产线',
									model: '污染源-列表'
								});
							}, 1000);
						});
					});
				} else {
					scxSave(this.model).then((res) => {
						if (res.data.msg == 'fail' || res.data.status == '500') {
							uni.showToast({
								icon: 'none',
								title: '生产线已存在请勿重复添加'
							});
						} else {
							uni.showToast({
								title: '添加成功',
								duration: 1000
							}).then(() => {
								setTimeout(() => {
									// uni.setStorageSync('isAddScx', true);
									let pages = getCurrentPages(); // 当前页面
									let beforePage = pages[pages.length - 2]; // 上一页
									if (beforePage.$vm) {
										beforePage.$vm.getScx();
									} else {
										beforePage.getScx();
									}
									this.backPage = false;
									uni.navigateBack({
										delta: 1,
										mark: '污染源-列表-详情-保存新增生产线',
										model: '污染源-列表'
									});
								}, 1000);
							});
						}
					});
				}
			},
			back() {
				uni.navigateBack({
					delta: 1
				});
			},
			onFocus() {
				this.showBottomBtn = false;
			},
			onBlur() {
				this.showBottomBtn = true;
			}
		}
	};
</script>

<style scoped>
	html {
		-webkit-tap-highlight-color: transparent;
		height: 100%;
	}

	body {
		-webkit-backface-visibility: hidden;
		height: 100%;
	}

	.pd-tablebx image {
		height: 30rpx;
	}

	.bznr {
		display: inline-block;
		height: 60rpx;
		line-height: 60rpx;
	}

	/deep/ .u-form-item--left__content__label {
		font-size: 30rpx;
		color: #2c323f;
	}

	/deep/ .u-input__textarea {
		border-radius: 8rpx;
		height: 100rpx;
		background-color: rgb(243, 245, 249);
		padding-left: 10rpx;
	}

	/deep/ .u-list-item {
		margin: 0;
	}

	/deep/ .uni-input-placeholder {
		padding: 0;
		/* text-align: right; */
		font-size: 26.570025rpx;
	}

	.listWarp {
		width: 100%;
		height: 100%;
		overflow-y: scroll;
	}

	.listWarp p {
		width: 100%;
		padding: 20rpx;
		text-align: center;
		border-bottom: 1px solid #efefef;
	}

	.opration {
		padding: 20rpx;
		display: flex;
		justify-content: space-between;
		width: 100%;
		background-color: #fff;
	}

	.confirm {
		color: rgb(60, 170, 255);
	}

	.on {
		color: rgb(60, 170, 255);
	}

	/deep/ .u-icon {
		padding: 0 0 0 10rpx;
	}

	.tip-title {
		text-align: center;
		border-bottom: 1px solid #efefef;
		padding: 20rpx;
		font-weight: 600;
	}

	.tip-content {
		padding: 20rpx;
	}

	.tip-content p {
		/* padding: 20rpx 0; */
		font-size: 26rpx;

		color: #666;
	}

	.tip-know {
		position: absolute;
		bottom: 0;
		padding: 20rpx;
		text-align: center;
		border-top: 1px solid #efefef;
		color: rgb(60, 170, 255);
		width: 100%;
	}

	.inner {
		padding-top: 0;
	}

	.zy-form .item .label {
		width: 30%;
		flex: unset;
	}

	textarea.two {
		min-height: 84rpx;
	}

	.uni-textarea-placeholder {
		white-space: pre-wrap;
		overflow: unset;
	}
</style>