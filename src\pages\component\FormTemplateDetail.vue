<!--
 * @Author: your name
 * @Date: 2021-03-17 10:49:39
 * @LastEditTime: 2021-04-07 18:20:56
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: /SmartFormWeb/pages/component/FormTemplateDetail.vue
-->
<template>
	<view class="template-form-layout"
		  style="height: calc(100vh - 80rpx);">
		<template-form :form-data="form"
					   :template="template"
					   :editable="editable"
					   :parentHeight="formLayoutHeight">
		</template-form>
	</view>
</template>

<script>
	import {
		queryTaskFormTemplate
	} from '@/api/record.js';
	import Page from '@/pages/component/Page.vue'
	import templateForm from '@/pages/form/template-form.vue'
	export default {
		data() {
			return {
				formData: {},
				title: '',
				template: {},
				editable: false,
				pageHeight: 800
			}
		},

		props: {
			form: Object,

			recordId: {
				type: String,
				default: ''
			},

			//激活状态填充色
			templateId: {
				type: String,
				default: ''
			},

			formLayoutHeight: {
				type: Number,
				default: 800
			}
		},

		computed: {
			//    formLayoutHeight: function() {
			// 	return this.pageHeight - 40
			// }
		},

		watch: {
			templateId: 'getFirstData',
			from: 'getFirstData'
		},

		components: {
			Page,
			templateForm
		},

		mounted() {
			this.getFirstData();
		},

		methods: {
			getFirstData() {
				this.formData = {};
				this.formData = JSON.parse(JSON.stringify(this.form));
				// 获取用户数据
				this.getData();
			},

			onFixedContentHeight(layout) {
				this.pageHeight = layout.height;
			},

			getData() {
				queryTaskFormTemplate('', this.templateId).then((res) => {
					this.template = res
				})
			},
		}

	}
</script>

<style>
</style>
