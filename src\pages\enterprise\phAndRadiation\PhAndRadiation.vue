<!-- @format -->
<!-- 其他设备：ph监控仪，辐射监控仪器 -->
<template>
    <div>
        <AlarmThreshold
            ref="refAlarmThreshold"
            v-show="isShowSetPh"
			:pageType="pageType"
            @setPHValue="handleSetPHValue"
        />

        <div style="background: #f1f1f1" v-show="!isShowSetPh">
            <header class="blue-header">
                <i class="ic-back" @click="handleBack"></i>
                <h1 class="lf-title">{{ currentTitle }}</h1>
            </header>
            <section
                class="main"
                style="padding-bottom: 0; margin-bottom: 30.1932rpx"
            >
                <div class="gap"></div>
                <div class="pd-modhd" v-show="pageType === 'detail'">
                    <strong>基本信息</strong>
                    <image
                        src="@/static/app/images/edtic.png"
                        class="pd-edt1"
                        @click="toEdit()"
                    />
                </div>
                <IMEIType
                    ref="refIMEIType"
                    :pageType="pageType"
                    :model.sync="model"
                    :equipType="equipType"
                    @setEquipType="setEquipType"
					:backPage.sync="backPage"
                ></IMEIType>
                <!-- 辐射 -->
              <RADEquipInfo
                    v-if="equipType === 'RAD'"
                    ref="refRADEquipInfo"
                    :pageType="pageType"
                    :fixedLine="fixedLine"
                    :model="model"
                    :info="info"
                ></RADEquipInfo>
                <!-- PH -->
                <PHEquipInfo
                    v-if="equipType === 'PH'"
                    ref="refPHEquipInfo"
                    :pageType="pageType"
                    :fixedLine="fixedLine"
                    :model="model"
                    :info="info"
                    @setShowSetPh="isShowSetPh = true"
                ></PHEquipInfo>

                <div
                    class="zy-form"
                    v-show="equipType === 'RAD' || equipType === 'PH'"
                >
                    <div class="item nfx" v-show="isShowPictureList()">
                        <p class="label star" @click="toggleInstallIllustration">
                            安装照片
                            <image
                                src="@/static/app/images/whic.png"
                                class="pd-whic1"
                            />
                        </p>
                        <div class="gap"></div>
                        <ul class="pd-ulpic1">
                            <li
                                v-for="(item, index) in azFileList"
                                :key="index"
                            >
                                <div
                                    style="
                                        position: relative;
                                        width: 213.76815rpx;
                                    "
                                >
                                    <image
                                        mode="scaleToFill"
                                        :src="getFileUrl(item)"
                                        alt=""
                                        @click="previewImage(azFileList, index)"
                                    />
                                    <image
                                        v-show="!isDetailPageShow()"
                                        mode="scaleToFill"
                                        src="@/static/app/images/cls.png"
                                        class="delImg"
                                        @click="delFile(item)"
                                    />
                                </div>
                            </li>
                            <li v-show="!isDetailPageShow()">
                                <image
                                    @click="addFile('ZDSB')"
                                    src="@/static/app/images/addpic.png"
                                    class="pd-addpic1"
                                />
                            </li>
                        </ul>
                    </div>
                    <div class="item remark">
                        <p class="label">备注信息</p>
                        <div class="rp" v-show="!isDetailPageShow()">
                            <textarea
                                auto-height
                                type="text"
                                v-model="model.BZ"
                                row="3"
                                :class="model.BZ ? '' : 'two'"
                                class="zy-textarea1 uni-input"
                                cursor-spacing="20"
                                placeholder="请输入备注信息如设备运行规律区别于“平稳”“间歇”，可特别说明下。"
                            ></textarea>
                        </div>
                        <div class="rp" v-show="isDetailPageShow()">
                            <textarea
                                disabled
                                auto-height
                                type="text"
                                v-model="model.BZ"
                                row="3"
                                :class="model.BZ ? '' : 'two'"
                                class="zy-textarea1 uni-input"
                                cursor-spacing="20"
                            ></textarea>
                        </div>
                    </div>
                </div>
                <!-- 设备最近3次数据结果 -->
                <RecentData
                    v-show="model.IMEI != ''"
                    :equipType="equipType"
                    :imei="model.IMEI"
                ></RecentData>
                <div v-show="!isDetailPageShow()">
                    <div class="gap"></div>
                    <div class="gap"></div>
                    <div
                        class="zy-bot-btn1"
                        v-show="pageType != 'detail'"
                        @click="save"
                    >
                        保存
                    </div>
                    <div class="gap"></div>
                    <div class="gap"></div>
                </div>
                <div class="mymask" v-show="showMask"></div>

                <div class="pd-botdlg" v-show="showInstallIllustration">
                    <i class="dlgcls" @click="toggleInstallIllustration"></i>
                    <div class="gap"></div>
                    <div class="gap"></div>
                    <div class="pd-con1">
                        <div class="pd-tit1">拍照示例</div>
                        <div class="gap"></div>
                        <div class="gap"></div>
                        <ul class="pd-ulpic1 sample">
                            <li>
                                <image
                                    mode="aspectFit"
                                    src="@/static/images/sample/sb1.jpg"
                                    alt=""
                                />
                                <p>近景</p>
                            </li>
                            <li>
                                <image
                                    mode="aspectFit"
                                    src="@/static/images/sample/sb3.jpg"
                                    alt=""
                                />
                                <p>中景</p>
                            </li>
                            <li>
                                <image
                                    mode="aspectFit"
                                    src="@/static/images/sample/sb2.jpg"
                                    alt=""
                                />
                                <p>远景</p>
                            </li>
                        </ul>
                        <div class="gap"></div>
                        <div class="gap"></div>
                        <dl class="pd-dltxt1">
                            <dt>说明：</dt>
                            <dd>1、近景尽量把设备IMEI号拍出来；</dd>
                            <dd>
                                2、尽量把设备所安装的设备与设备的大概位置展示出来。
                            </dd>
                        </dl>
                        <div class="gap"></div>
                        <div class="gap"></div>
                    </div>
                </div>
            </section>
        </div>
		<!-- 返回上页是否存草稿弹窗 -->
		<PageBackRemindModal v-show="isShowBackPop" :isShowBackPop.sync="isShowBackPop"  :popContent="popContent" 
		@movePage="movePage" @saveDialog="saveDialog" @stateOnPage="stateOnPage"></PageBackRemindModal>
    </div>
</template>

<script>
import IMEIType from './components/IMEIType.vue';
import PHEquipInfo from './components/PHEquipInfo.vue';
import RADEquipInfo from './components/RADEquipInfo.vue';
import RecentData from './components/RecentData.vue';
import AlarmThreshold from '@/pages/component/AlarmThreshold.vue';
	import PageBackRemindModal from '../components/PageBackRemindModal';
import {
    DOWNLOAD_URLZDY,
    UPLOAD_URL,
    LOGIN_ULR_BASE
} from '@/common/config.js';
import { guid } from '@/common/uuid.js';
import { getFilelist, deletefile, downloadFile } from '@/api/iot/appendix.js';
import {
    addRADEquip,
    editRADEquip,
    detailRADEquip,
    addPHEquip,
    editPHEquip,
    detailPHEquip,
	queryRadiationCode
} from '@/api/iot/phAndRadiation.js';
import { getValid } from '@/utils/validData.js';
import { cloneDeep } from 'lodash';

export default {
    name: 'DataCollectionAppPhAndRadiation',
    components: {
        IMEIType,
        PHEquipInfo,
        RADEquipInfo,
        RecentData,
        AlarmThreshold,
		PageBackRemindModal
    },
    data() {
        return {
            option: '',
            pageType: '',
            info: {},
            equipType: '',
            model: {
                IMEI: '',
                SBID: '',
                SBMC: '',
                BZ: ''
            },
            PHRules: {
                SBMC: {
                    required: true,
                    message: '请填写设备名称',
                    trigger: 'change'
                },
                IMEI: {
                    required: true,
                    message: '请扫描获取IMEI'
                },
                SCXID: {
                    required: true,
                    message: '请选择生产线'
                }
            },
            RADRules: {
                SBMC: {
                    required: true,
                    message: '请填写设备名称',
                    trigger: 'change'
                },
                IMEI: {
                    required: true,
                    message: '请扫描获取IMEI'
                },
                FSYBH: {
                    required: true,
                    message: '请绑定放射源编号'
                },
                SCXID: {
                    required: true,
                    message: '请绑定生产线'
                }
            },
            fileList: [],
            WRYBH: '',
            azFileList: [],
            fixedLine: false,
            showMask: false,
            showInstallIllustration: false,
            currentTitle: '',
            isShowSetPh: false,
			isShowBackPop:false,
			popContent:'当前内容未保存，是否离开？',
			backPage: false,
        };
    },
	watch: {
		model: {
			handler(newVal, oldVal) {
				if (this.pageType == 'add') {
					console.log('newVal',newVal)
					this.backPage = true;
				}
			},
			deep: true,
		},
	},
    onLoad(option) {
        this.option = option;
        this.info = JSON.parse(decodeURIComponent(this.option.info)) || {};
        this.pageType = this.option.type;
    },
    mounted() {
		
        //从列表页进入新增页面
        if (this.pageType === 'add') {
            // uni.setNavigationBarTitle({
            //     title: '新增其他设备'
            // });
            this.currentTitle = '新增其他设备';
            //this.model = uni.getStorageSync('equitDraft') || this.model;
            //this.azFileList = uni.getStorageSync('equitDraftFIleList') || this.azFileList;
            //新增需要生成新得随机设备id
            this.model.SBID = guid();

            //从生产线进入，固定生产线
            if (this.option.fixedLine) {
                this.fixedLine = true;
                this.$refs.refRADEquipInfo.form.SCXID = this.info.SCXID;
                this.$refs.refRADEquipInfo.form.SCXMC = this.info.SCXMC;
                this.$refs.refPHEquipInfo.form.SCXID = this.info.SCXID;
                this.$refs.refPHEquipInfo.form.SCXMC = this.info.SCXMC;
            }
			this.model = uni.getStorageSync('dueOtherEquipDraft') || this.model;
			this.azFileList = uni.getStorageSync('dueOtherEquipFIleList') || this.azFileList;
			if(this.model.ZNSBLX){
				this.equipType = this.model.ZNSBLX
			}
			this.setChildComponentForm()
			this.$nextTick(()=>{
				setTimeout(()=>{
					this.backPage = false
				},400)
			})
			
        }

        if (this.pageType === 'detail') {
            // uni.setNavigationBarTitle({
            //     title: '其他设备详情'
            // });
            this.currentTitle = '其他设备详情';
            this.model =
                JSON.parse(decodeURIComponent(this.option.sbInfo)) || {};
            this.getDetailData();
        }

        if (this.pageType === 'edit') {
            // uni.setNavigationBarTitle({
            //     title: '编辑其他设备'
            // });
            this.currentTitle = '编辑其他设备';
            this.model =
                JSON.parse(decodeURIComponent(this.option.sbInfo)) || {};
            this.getDetailData();
        }
    },

    methods: {
		handleBack() {
				
			if(this.pageType === 'add' && this.backPage){
					this.isShowBackPop = true;
			}else{
				uni.navigateBack({
					delta: 1
				});
			}
			
		},
		//离开页面不存草稿
		movePage(){
		    this.backPage = false;
			setTimeout(() => {
				uni.navigateBack({
					delta: 1
				});
			}, 200);
		},
		//离开页面存草稿
		saveDialog(){
			let self = this;
			let IMEIForm = this.$refs.refIMEIType.form;
			console.log('IMEIForm',IMEIForm)
			let equipFrom = {};
			if(IMEIForm){
				this.equipType = IMEIForm.ZNSBLX
			}
			if (this.equipType  === 'PH') {
			    equipFrom = { ...this.$refs.refPHEquipInfo.form };
			} else if(this.equipType === 'RAD'){
			    equipFrom = cloneDeep(this.$refs.refRADEquipInfo.form);
			    equipFrom.YXGL = JSON.stringify(equipFrom.YXGL);
			}
			Object.assign(this.model, IMEIForm, equipFrom);
			uni.setStorageSync('dueOtherEquipDraft', self.model);
			if (self.azFileList.length > 0) {
				uni.setStorageSync('dueOtherEquipFIleList', self.azFileList);
			}
			uni.showToast({
				title: '内容已保存为草稿',
				icon: 'none'
			});
			self.backPage = false;
			setTimeout(() => {
				uni.navigateBack({
					delta: 1
				});
			}, 200);
		},
		//保存成功取消草稿
		cancelDraft(){
			//保存成功清除草稿
			this.backPage =false;
			uni.removeStorageSync(
				"dueOtherEquipDraft"
			);
			uni.removeStorageSync(
				'dueOtherEquipFIleList'
			);
		},
		//不离开页面
		stateOnPage(){
			this.isShowBackPop = false;
		},
        // PH值页面关闭保存填写ph的值
        handleSetPHValue(value) {
            this.$refs.refPHEquipInfo.setForm({ QTYZ: value });
            this.isShowSetPh = false;
        },
        //是否展示图片
        isShowPictureList() {
            let flag = false;
            if (
                this.pageType === 'add' ||
                this.pageType === 'edit' ||
                (this.pageType === 'detail' && this.azFileList.length)
            ) {
                flag = true;
            }
            return flag;
        },
        //详情显示，新增编辑隐藏
        isDetailPageShow() {
            return this.pageType === 'detail';
        },
        //详情数据
        async getDetailData() {
            let { SBID, ZNSBLX } = this.model;
            this.equipType = ZNSBLX;
            this.getFileList();
            let { data } =
                ZNSBLX === 'PH'
                    ? await detailPHEquip({ SBID })
                    : await detailRADEquip({ SBID });
            this.model = { ...this.model, ...(data[0] || {}) };
			console.log('this.model',this.model)
			this.setChildComponentForm();
        },
		//设置子组件表单数据
		setChildComponentForm(){
			Object.assign(this.$refs.refIMEIType.form, {
			    SBMC: this.model.SBMC,
			    IMEI: this.model.IMEI,
			    ZNSBLX: this.model.ZNSBLX
			});
			console.log('this.$refs.refIMEIType.form',this.$refs.refIMEIType.form)
			if (this.equipType === 'PH') {
			    Object.assign(this.$refs.refPHEquipInfo.form, {
			        AZSJ: this.model.AZSJ,
			        SCXMC: this.model.CXMC || this.model.SCXMC || '-',
			        SCXID: this.model.CXID || this.model.SCXID || '-',
			        QTYZ:  this.model.QTYZ
			    });
			    this.$refs.refAlarmThreshold.setPHvalue(this.model.QTYZ);
			} else if(this.equipType === 'RAD'){
			    this.setChildForm(this.model, this.$refs.refRADEquipInfo.form);
			    this.$refs.refRADEquipInfo.form.YXGL = JSON.parse(
			        this.model.YXGL || "{}"
			    ) ;
			    this.$refs.refRADEquipInfo.form.SCXID = this.model.CXID || this.model.SCXID || '-';
			    this.$refs.refRADEquipInfo.form.SCXMC = this.model.CXMC || this.model.SCXMC || '-';
			}
		},
        /**
         * 详情页数据展示设置子组件表单数据
         * @parentForm  父组件的表单数据
         * @childForm  子组件的表单数据
         */
        setChildForm(parentForm, childForm) {
            const parentFormKeys = Object.keys(parentForm);
            for (let key in childForm) {
                let sameKey = parentFormKeys.find((item) => item === key);
                childForm[sameKey] = parentForm[sameKey];
            }
        },
        //设置设备类型
        setEquipType(payload) {
            this.model.ZNSBLX = payload;
            this.equipType = payload;
        },
        //编辑设备
        toEdit() {
            let infoParams = this.rePlaceSpecialCharacters(this.info);
            let url =
                '/pages/enterprise/phAndRadiation/PhAndRadiation?type=edit&info=' +
                encodeURIComponent(JSON.stringify(infoParams)) +
                '&sbInfo=' +
                encodeURIComponent(JSON.stringify(this.model));
            uni.navigateTo({
                url
            });
        },
        //传参前替换百分号
        rePlaceSpecialCharacters(obj) {
            let replaceObj = {
                ...obj
            };
            for (let key in replaceObj) {
                replaceObj[key] = replaceObj[key]?.replace(/\%/g, '%25');
            }
            return replaceObj;
        },
		
        async save() {
            let IMEIForm = this.$refs.refIMEIType.form;
            let equipFrom = {};
            let rules = {};
            if (this.equipType === 'PH') {
                rules = { ...this.PHRules };
                equipFrom = { ...this.$refs.refPHEquipInfo.form };
            } else {
                rules = { ...this.RADRules };
                equipFrom = cloneDeep(this.$refs.refRADEquipInfo.form);
                equipFrom.YXGL = JSON.stringify(equipFrom.YXGL);
            }

            Object.assign(this.model, IMEIForm, equipFrom);

            try {
                await getValid(rules, this.model);
				//校验生产线id
				if(this.model.SCXID.length<5){
					uni.showToast({
					    title: '生产线绑定错误',
					    icon: 'none',
					    duration: 1000
					});
					return
				}
		        //校验图片上传
                if (!this.azFileList.length) {
                    uni.showToast({
                        title: '请上传安装图片',
                        icon: 'none',
                        duration: 1000
                    });
                    return;
                }
				//如果是辐射设备，保存前校验放射源编号
				if(this.equipType === 'RAD'){
					const {data={},status} = await queryRadiationCode({FSYBH:equipFrom.FSYBH})
					if(status === '000'){
						if(data.code === '000'){
							//校验通过直接保存
							if (this.pageType === 'add') {
							    this.saveAdd();
							} else {
							    this.saveEdit();
							}
						}else{
							//校验不通过提示信息
							uni.showModal({
							    title: '提示',
							    content: `${data.msg}，是否继续保存？`,
							    success:  (res)=> {
							        if (res.confirm) {
							            console.log('用户点击确定');
							           if (this.pageType === 'add') {
							               this.saveAdd();
							           } else {
							               this.saveEdit();
							           }
							        } else if (res.cancel) {
							            console.log('用户点击取消');
							        }
							    }
							});
						}
					}
					
				}else if(this.equipType === 'PH'){
					if (this.pageType === 'add') {
					    this.saveAdd();
					} else {
					    this.saveEdit();
					}
				}
              
            } catch (error) {
                console.log(error);
            }
        },
        //添加保存
        async saveAdd() {
            let self = this;
            try {
                const { data } =
                    this.equipType === 'PH'
                        ? await addPHEquip(this.model)
                        : await addRADEquip(this.model);
                // console.log(data);
                if (data && data.status != '000') {
                    uni.showToast({
                        title: '添加失败',
                        duration: 1000
                    });
                    return;
                }
                uni.showToast({
                    title: '添加成功',
                    duration: 800
                }).then(() => {
					self.cancelDraft();
                    self.addSuccessCallback();
                });
            } catch (error) {
                uni.showToast({
                    title: '添加失败',
                    duration: 1000
                });
                console.log(error);
            }
        },
        //添加成功回调
        addSuccessCallback() {
            this.backPage = false;
            //保存成功清除草稿
            //uni.removeStorageSync('equitDraft');
            //uni.removeStorageSync('equitDraftFIleList');
            setTimeout(() => {
                let pages = getCurrentPages(); // 当前页面
                //刷新上一页面的设备列表
                let beforePage = pages[pages.length - 2]; // 上一页
                if (beforePage.$vm) {
                    beforePage.$vm.getOtherEquipList &&
                        beforePage.$vm.getOtherEquipList();
                } else {
                    beforePage.getOtherEquipList &&
                        beforePage.getOtherEquipList();
                }

                //从生产线详情进入时，需要刷新企业信息页面的生产线列表、生产设备列表
                if (this.fixedLine === true) {
                    let parentPage = pages[pages.length - 3]; //上上页
                    if (parentPage.$vm) {
                        parentPage.$vm.getOtherEquipList &&
                            parentPage.$vm.getOtherEquipList();
                    } else {
                        parentPage.getOtherEquipList &&
                            parentPage.getOtherEquipList();
                    }
                }
                uni.navigateBack({
                    delta: 1
                });
            }, 600);
        },
        //编辑保存
        async saveEdit() {
            let self = this;
            try {
                const { data } =
                    this.equipType === 'PH'
                        ? await editPHEquip(this.model)
                        : await editRADEquip(this.model);
                // console.log(data);
                if (data && data.status != '000') {
                    uni.showToast({
                        title: '编辑失败',
                        duration: 1000
                    });
                    return;
                }
                uni.showToast({
                    title: '编辑成功',
                    duration: 800
                }).then(() => {
                    self.editSuccessCallback();
                });
            } catch (error) {
                uni.showToast({
                    title: '编辑失败',
                    duration: 1000
                });
                console.log(error);
            }
        },

        //编辑成功回调
        editSuccessCallback() {
            setTimeout(() => {
                let pages = getCurrentPages(); // 当前页面
                let beforePage = pages[pages.length - 2]; // 上一页
                if (beforePage.$vm) {
                    beforePage.$vm.getDetailData?.();
                } else {
                    beforePage.getDetailData?.();
                }

                //刷新企业信息页面的其他设备列表
                let parentPage = pages[pages.length - 3];
                if (parentPage.$vm) {
                    parentPage.$vm.getOtherEquipList &&
                        parentPage.$vm.getOtherEquipList();
                } else {
                    parentPage.getOtherEquipList &&
                        parentPage.getOtherEquipList();
                }

                uni.navigateBack({
                    delta: 1
                });
            }, 1000);
        },
        toggleInstallIllustration() {
            this.showMask = !this.showMask;
            this.showInstallIllustration = !this.showInstallIllustration;
        },
        //删除图片
        delFile(file) {
            let self = this;
            uni.showModal({
                title: '提示',
                content: '确认删除照片?',
                success: function (res) {
                    if (res.confirm) {
                        console.log('用户点击确定');
                        deletefile(file.WJID).then((res) => {
                            uni.showToast({
                                title: '删除成功',
                                duration: 500
                            }).then(() => {
                                setTimeout(() => {
                                    self.getFileList();
                                }, 500);
                            });
                        });
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                }
            });
        },
        //添加文件
        addFile(zlx) {
            if (this.azFileList.length >= 3) {
                uni.showToast({
                    title: '最多只能上传3张照片',
                    icon: 'none'
                });
                return;
            }

            let self = this;
            uni.chooseImage({
                count: 1, //默认9
                sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
                success: function (res) {
                    console.log('reschooseImage', res);
                    //console.log(JSON.stringify(res.tempFilePaths));
                    self.uploadFile(res, zlx);
                }
            });
        },
        uploadFile(res, zlx) {
            console.log('resuploadFile', res);
            console.log('this.model.SBID', this.model.SBID);
            let f = res.tempFilePaths[0];
            let { size, type } = res.tempFiles[0];
            let self = this;
            uni.showLoading({
                title: '上传中'
            });
            uni.uploadFile({
                url: UPLOAD_URL,
                filePath: f,
                name: f.name,
                formData: {
                    WJDX: size / 1024,
                    WJLX: type,
                    LXDM: 'ZDFJ',
                    ZLXDM: zlx,
                    YWSJID: this.model.SBID,
                    WJMC: f.name
                },
                timeout: 60000,
                success: function (r) {
                    uni.showToast({
                        title: '上传成功',
                        icon: 'none'
                    });
                    //上传成功掉获取文件列表接口
                    self.getFileList();
                    uni.hideLoading();
                },
                fail: function (err) {
                    uni.showToast({
                        title: '上传失败',
                        icon: 'none'
                    });
                    uni.hideLoading();
                }
            });
        },
        //获取上传后的文件列表，看接口文档要传什么参数,
        getFileList() {
            getFilelist({
                pageSize: 100000,
                pageNum: 1,
                YWSJID: this.model.SBID,
                LXDMS: 'ZDFJ',
                ZLXDMS: 'WRYZP,GYLCT,ZDSB'
            }).then((res) => {
                let fileData = res[0];
                if (
                    fileData &&
                    fileData.zlxList &&
                    fileData.zlxList.length > 0
                ) {
                    fileData.zlxList.forEach((list) => {
                        if (list.ZLXDM == 'ZDSB') {
                            this.azFileList = list.fileList;
                        }
                    });
                }
            });
        },
        //预览图片
        previewImage(fileList, index) {
            let self = this;
            let fileUrls = fileList.map((file) => {
                return this.getFileUrl(file);
            });
            console.log('fileUrls', fileUrls);
            // 预览图片
            uni.previewImage({
                current: index,
                urls: fileUrls,
                //长按保存到本地
                longPressActions: {
                    itemList: ['保存图片到本地'],
                    success: (data) => {
                        // console.log('data', data)
                        if (data.tapIndex == 0) {
                            let imgurl = fileUrls[data.index];
                            self.saveImage(imgurl);
                        }
                    },
                    fail: function (err) {
                        console.log(err.errMsg);
                    }
                }
            });
        },
        getFileUrl(item) {
            return DOWNLOAD_URLZDY + item.WJID;
        }
    }
};
</script>

<style lang="scss" scoped>
.blue-header {
    background-color: rgb(72, 116, 255);
}
.lf-title {
    font-size: 32rpx;
    font-weight: bold;
}
::v-deep .uni-input-placeholder {
    text-align: right;
    font-size: 26rpx;
}
.mymask {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1000;
    width: 100%;
    height: 100%;
}


</style>
