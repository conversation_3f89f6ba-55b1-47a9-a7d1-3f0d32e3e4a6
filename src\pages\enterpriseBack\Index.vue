<!-- @format -->

<template>
    <div style="background-color: #f1f2f6" class="warp">
        <header class="header"><h1 class="title">污染源列表</h1></header>
        <section class="main">
            <div class="inner">
                <div class="zy-search1">
                    <input
                        type="text"
                        placeholder="请输入关键字进行查询"
                        v-model="searchText"
                        @input="search"
                    />
                </div>

                <ul class="zy-tabs1">
                    <li
                        :class="dataType == item.value ? 'cur' : ''"
                        v-for="(item, index) in dataTypeArr"
                        :key="index"
                        @click="change(item)"
                    >
                        <p>{{ item.name }}</p>
                        <span v-if="dataType == item.value">{{ total }}</span>
                    </li>
                </ul>
                <scroll-view
                    scroll-y
                    style="height: calc(100vh - 355rpx)"
                    @scrolltolower="getMore"
                >
                    <ul class="zy-data2">
                        <li v-for="(item, index) in list" :key="index">
                            <div class="hd">
                                <div class="ic">
                                    <img
                                        src="~@/static/images/zy-data2-ic.png"
                                        alt=""
                                    />
                                </div>
                                <h3>{{ item.WRYMC || '-' }}</h3>
                            </div>
                            <div class="bd">
                                <table @click="toEnterpriseInfo(item)">
                                    <tr>
                                        <td class="td-hd">企业地址：</td>
                                        <td>{{ item.DWDZ || '-' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="td-hd">环保联系人：</td>
                                        <td>{{ item.HBLXR || '-' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="td-hd">联系方式：</td>
                                        <td>{{ item.HBLXRDH || '-' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="td-hd">设备数量：</td>
                                        <td>{{ item.SBSL || '-' }}台</td>
                                    </tr>
                                </table>

                                <table>
                                    <tr>
                                        <td class="td-hd">监控类型：</td>
                                        <radio-group
                                            @change="
                                                (e) => radioChange(e, item)
                                            "
                                        >
                                            <label class="radio">
                                                <radio
                                                    value="工业污染源"
                                                    :checked="
                                                        item.YYCJ ==
                                                        '工业污染源'
                                                    "
                                                    class="radio"
                                                />
                                                工业污染源
                                            </label>
                                            <label class="radio">
                                                <radio
                                                    value="工业源监控"
                                                    :checked="
                                                        item.YYCJ ==
                                                        '工业源监控'
                                                    "
                                                    class="radio"
                                                />
                                                工业源监控
                                            </label>
                                            <label class="radio">
                                                <radio
                                                    value="餐饮油烟监控"
                                                    :checked="
                                                        item.YYCJ ==
                                                        '餐饮油烟监控'
                                                    "
                                                    class="radio"
                                                />
                                                餐饮油烟监控
                                            </label>
                                        </radio-group>
                                    </tr>
                                </table>

                                <div class="gap"></div>
                                <div class="anniu">
                                    <button
                                        class="zy-btn2"
                                        @click="setInformation(item)"
                                    >
                                        信息设置
                                    </button>
                                </div>
                            </div>
                        </li>
                    </ul>
                </scroll-view>
            </div>
        </section>
    </div>
</template>

<script>
import { getQyxx, updataQyxx } from '@/api/iot/enterprise.js';
export default {
    data() {
        return {
            dataTypeArr: [
                {
                    name: '全部',
                    value: ''
                },
                {
                    name: '已安装',
                    value: 'Y'
                },
                {
                    name: '未安装',
                    value: 'N'
                }
            ],
            searchText: '',
            pageSize: 15,
            dataType: '',
            total: 0,
            // 企业列表
            list: [],
            enterpriseInfo: {},
            timer: null
        };
    },
    mounted() {
        // 获取缓存中的企业信息
        this.enterpriseInfo = uni.getStorageSync('userInfo');
        this.updataQyxx();
    },
    watch: {
        searchText: {
            handler: function (val) {
                this.searchText = val;
            }
        }
    },
    onShow() {
        if (uni.getStorageSync('qyxxIsUpdate')) {
            this.initList();
            uni.setStorageSync('qyxxIsUpdate', false);
        }
    },
    methods: {
        getMore() {},
        // 获取企业信息列表，如果没有监控类型默认工业污染源
        updataQyxx() {
            let { orgid } = this.enterpriseInfo;
            let promiseArr = [];
            getQyxx({ ORGID: orgid, pageSize: this.pageSize }).then((res) => {
                if (res.data_array && res.data_array.length > 0) {
                    let data = JSON.parse(JSON.stringify(res.data_array));
                    let len = res.data_array.length;
                    for (let i = 0; i < len; i++) {
                        if (!data[i].YYCJ) {
                            let { name } = this.enterpriseInfo;
                            promiseArr.push(
                                updataQyxx({
                                    WRYBH: data[i].WRYBH,
                                    YYCJ: '工业污染源',
                                    XGR: name
                                })
                            );
                        }
                    }
                }
                // 修改完成后初始list
                Promise.all(promiseArr).then((res) => {
                    // console.log(res);
                    this.initList();
                });
            });
        },
        // 获取企业信息列表
        initList() {
            let { orgid } = this.enterpriseInfo;
            getQyxx({
                ORGID: orgid,
                pageSize: this.pageSize,
                orderBy: 'CJSJ',
                orderWay: 'DESC',
                fuzzySearch: this.searchText,
                SFAZ: this.dataType
            }).then((res) => {
                if (res.data_array && res.data_array.length > 0) {
                    this.list = res.data_array;
                    this.total = res.total_count;
                }
            });
        },
        // tab切换
        change(option) {
            this.dataType = option.value;
            this.initList();
        },
        // 信息设置
        setInformation(option) {
            uni.navigateTo({
                url:
                    '/pages/enterprise/editEnterpriseInfo?info=' +
                    encodeURIComponent(JSON.stringify(option))
            });
        },
        // 搜索
        search() {
            if (this.timer) {
                clearTimeout(this.timer);
            }
            this.timer = setTimeout(() => {
                this.initList();
            }, 400);
        },

        // evt （radio 点击事件 item 点击的企业信息 ）
        radioChange(evt, item) {
            let { name } = this.enterpriseInfo;
            updataQyxx({
                WRYBH: item.WRYBH,
                YYCJ: evt.target.value,
                XGR: name
            });
        },
        // 到企业详情页
        toEnterpriseInfo(item) {
            uni.navigateTo({
                url:
                    './enterpriseInfo?info=' +
                    encodeURIComponent(JSON.stringify(item))
            });
        }
    }
};
</script>

<style scoped>
.radio {
    transform: scale(0.7);
    font-size: 26rpx;
}
</style>
