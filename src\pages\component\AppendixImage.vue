<!-- @format -->

<template>
    <div>
        <u-empty
            text="暂无数据"
            mode="list"
            v-if="!isCanEdit && list.length == 0"
        >
        </u-empty>
        <ul class="ul-pic1">
            <li class="imgli" v-for="(item, index) in list" :key="item.WJID">
                <div class="innerbox">
                    <image
                        class="img"
                        mode="scaleToFill"
                        :src="getFileUrl(item)"
                        alt=""
                        @click="previewImage(list, index)"
                    />
                    <image
                        v-if="isCanEdit"
                        mode="aspectFill"
                        src="@/static/app/images/cls.png"
                        class="delImg"
                        @click="delFile(item)"
                    />
                </div>
            </li>

            <li v-if="isCanEdit">
                <div class="innerbox">
                    <image
                        @click="addFile(childTypeKeyword)"
                        src="@/static/app/workbench/images/addtu.png"
                        class="addpic"
                    />
                </div>
            </li>
        </ul>
        <div class="gap"></div>
    </div>
</template>

<script>
import { getFilelist, deletefile, downloadFile } from '@/api/iot/appendix.js';
import {
    API_LOGIN_SERVICE_URL,
    LOGIN_ULR_BASE,
    UPLOAD_URL,
    DOWNLOAD_URLZDY,
    PREVIEW_FILE_URL
} from '@/common/config.js';
export default {
    name: '',
    props: {
        list: {
            type: Array,
            default: () => {}
        },
        isCanEdit: {
            type: Boolean,
            default: false
        },
        childTypeKeyword: {
            type: String,
            default: ''
        },
        fileTypeKeyword: {
            type: String,
            default: ''
        },
        uploadId: {
            type: String,
            default: ''
        },
        maxImagesLength: {
            type: Number,
            default: 20
        }
    },
    data() {
        return {};
    },
    onLoad(options) {},
    mounted() {},
    methods: {
        //获取文件list
        getFileList() {
            let obj = {
                pageSize: 100000,
                pageNum: 1,
                YWSJID: this.uploadId,
                LXDMS: this.fileTypeKeyword,
                ZLXDMS: this.childTypeKeyword
            };
            getFilelist(obj)
                .then((res) => {
                    let fileData = res[0];
                    if (
                        fileData &&
                        fileData.zlxList &&
                        fileData.zlxList.length > 0
                    ) {
                        fileData.zlxList.forEach((list) => {
                            console.log('list', list);
                            if (list.ZLXDM == this.childTypeKeyword) {
                                this.$emit('update:list', list.fileList);
                            }
                        });
                    }
                })
                .catch((err) => {
                    console.log('err', err);
                });
        },

        //删除文件
        delFile(file) {
            if (this.timer) {
                clearTimeout(this.timer);
            }
            this.timer = setTimeout(() => {
                let self = this;
                uni.showModal({
                    title: '提示',
                    content: '确认删除?',
                    success: function (res) {
                        if (res.confirm) {
                            console.log('用户点击确定');
                            deletefile(file.WJID).then((res) => {
                                uni.showToast({
                                    title: '删除成功',
                                    duration: 500
                                }).then(() => {
                                    setTimeout(() => {
                                        self.getFileList();
                                    }, 500);
                                });
                            });
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    }
                });
            }, 500);
        },
        //添加文件
        addFile(zlx) {
            if (
                zlx == this.childTypeKeyword &&
                this.list.length >= this.maxImagesLength
            ) {
                uni.showToast({
                    title: '最多只能上传20张照片',
                    icon: 'none'
                });
                return;
            }
            let lenCount = this.maxImagesLength - this.list.length;

            let self = this;
            uni.chooseImage({
                count: lenCount, //默认值
                sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
                success: function (res) {
                    let len = res.tempFilePaths.length;
                    for (var i = 0; i < len; i++) {
                        self.uploadFile(res, zlx, i);
                    }
                }
            });
        },

        //上传
        uploadFile(res, zlx, i) {
            let f = res.tempFilePaths[i];
			let {size,type} = res.tempFiles[0];
            let self = this;
            // console.log(self.info.BBID)
            uni.showLoading({
                title: '上传中'
            });
            uni.uploadFile({
                url: UPLOAD_URL,
                filePath: f,
                name: 'file',
                formData: {
					WJDX:size/1024,
					WJLX:type,
                    pageSize: 100000,
                    pageNum: 1,
                    YWSJID: self.uploadId,
                    LXDM: this.fileTypeKeyword,
                    ZLXDM: zlx
                },
                timeout: 6000,
                success: function (res) {
                    uni.showToast({
                        title: '上传成功',
                        icon: 'none'
                    });
                    console.log('上传成功');
                    self.getFileList();
                    uni.hideLoading();
                },
                fail: function (err) {
                    console.log('上传失败');
                    console.log('err===>', err);
                    uni.showToast({
                        title: '上传失败',
                        icon: 'none'
                    });
                    uni.hideLoading();
                }
            });
        },
        //获取文件路径
        getFileUrl(item) {
            return DOWNLOAD_URLZDY + item.WJID;
        },

        //预览
        previewImage(fileList, index) {
            let fileUrls = fileList.map((file) => {
                return this.getFileUrl(file);
            });
            // 预览图片
            uni.previewImage({
                current: index,
                urls: fileUrls
            });
        }
    }
};
</script>

<style scoped>
.ul-pic1 {
    flex-wrap: wrap;
    align-items: space-between;
    display: flex;
}

.ul-pic1 li {
    position: relative;
    width: 33.3%;
    margin-left: 0;
    margin-top: 0px;
    padding: 8rpx;
    border-top: 0;
}

.ul-pic1 li.imgli {
    padding: 0;
}

.ul-pic1 li .delImg {
    position: absolute;
    right: 0;
    top: 0;
    width: 48rpx;
    height: 48rpx;
}

.pd-pic {
    height: 360rpx;
}

.ul-pic1 li.imgli {
    backgroung: none;
    border-top: none;
    width: 30%;
    margin: 6rpx;
}

.ul-pic1 li .innerbox {
    width: 100%;
    background-color: #f1f1f1;
    padding: 16rpx;
    height: 130rpx;
}

.ul-pic1 li.imgli .innerbox {
    padding: 0rpx;
    background: #fff;
}

.ul-pic1 li .img {
    width: 100%;
    height: 100%;
}

.addpic {
    width: 100%;
    height: 100%;
    margin: auto;
    display: block;
}
</style>
