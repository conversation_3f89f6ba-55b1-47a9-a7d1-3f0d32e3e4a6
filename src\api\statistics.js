/*
 * @Author: your name
 * @Date: 2021-06-23 14:28:39
 * @LastEditTime: 2021-06-23 14:40:17
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /YDZF_APP/api/statistics.js
 */

import axios from '@/common/ajaxRequest.js'
import { ULR_BASE, LOGIN_ULR_BASE } from '@/common/config.js'


// 获取领导看板中的执法统计功能
export const getLeaderShow = data => {
	data.service = 'QUERY_LEADERSHIP_KANBAN';
	return axios.request({
		method: 'post',
		url: ULR_BASE,
		data: data
	});
};