<template>
		<ul class="pd-ultbs1" style="position: static;">
			<li :class="dataType == item.value ? 'on' : ''" v-for="(item, index) in dataTypeArr" :key="index"
				@click="change(item)">
				<p>{{ item.name }}</p>
			</li>
		</ul>
</template>

<script>
	export default{
		props:{
			dataTypeArr:{
				type:Array,
				required:true
			},
			dataType:{
				type:String,
				required:true
			},
		},
		methods:{
			change(item){
				this.$emit('changeDataType',item)
			}
		}

	}
</script>

<style scope>
</style>
