<!-- @format -->
<template>
    <div class="board">
        <table class="ribao-table3 table-head">
            <colgroup>
                <col width="50px" />
                <col width="170px" />
                <col width="/" />
                <col width="/" />
            </colgroup>
            <thead>
                <tr>
                    <th>序号</th>
                    <th>检测时间</th>
                    <th>电压</th>
                    <th>pH值</th>
                </tr>
            </thead>
        </table>
        <div class="con" v-show="tableData.length">
            <table
                class="ribao-table3"
                style="max-height: calc(100vh - 100px); overflow-y: auto"
            >
                <colgroup>
                    <col width="50px" />
                    <col width="170px" />
                    <col width="/" />
                    <col width="/" />
                </colgroup>
                <tbody>
                    <tr v-for="(item, index) in tableData" :key="index">
                        <td>{{ index + 1 }}</td>
                        <td>{{ item.jcsj || '-' }}</td>
                        <td>{{ item.vol || '-' }}</td>
                        <td>{{ item.phz || '-' }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div v-show="!tableData.length">
            <div class="gap"></div>
            <u-empty text="暂无数据" mode="list"></u-empty>
        </div>

        <div class="gap"></div>
        <div class="qiye-board">
            <div class="zy-line ac jb">
                <p class="qiye-til1">其他设备</p>
            </div>
            <div class="qiye-shebei" v-if="otherEquipList.length">
                <radio-group
                    @change="radioChange($event, item, index)"
                    v-for="(item, index) in otherEquipList"
                    :key="item.SBXH"
                    :data-item="item"
                >
                    <label class="item radiogroup">
                        <div class="left">
                            <div class="ic" style="background-color: #3873ff">
                                <i style="background-color: #3873ff"></i>
                            </div>
                            <view>{{ item.SBMC }}</view>
                        </div>
                        <view>
                            <radio
                                style="transform: scale(0.7)"
                                :value="item.SBXH"
                                :checked="item.SBXH === curEquit.SBXH"
                            />
                        </view>
                    </label>
                </radio-group>
            </div>
        </div>
        <div v-show="!otherEquipList.length">
            <div class="gap"></div>
            <div class="gap"></div>
            <div class="gap"></div>
            <u-empty class="nodatabox" text="暂无数据" mode="list"></u-empty>
        </div>
    </div>
</template>

<script>
import { getPHDataList } from '@/api/iot/warning.js';
export default {
    name: 'PHRealtimeData',
    props: {
        currentscx: {
            type: String,
            default: ''
        },
        dateStr: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            tableData: [],
            otherEquipList: [], //其他设备
            curEquit: {}, //当前设备
            arrSendData: {}
        };
    },
    mounted() {
        uni.$on('sendData', (payload) => {
            this.$nextTick(() => {
                this.arrSendData = { ...payload.arrSendData };
                let { qtList } = payload.arrSendData;
                this.handlePayloadData(qtList);
                this.getQueryTime();
                this.queryPHData();
            });
        });
    },
    methods: {
        handlePayloadData(qtList = []) {
            qtList =
                qtList?.filter((e) => e.SBMC != '' && e.ZNSBLX === 'PH') || [];
            this.otherEquipList = qtList?.reverse();
            this.curEquit = this.otherEquipList.length
                ? this.otherEquipList[0]
                : {};
        },
        //查询ph设备电压和ph值的列表接口
        async queryPHData() {
            let startT = '';
            let endT = '';
            if (this.dateStr) {
                startT = this.$dayjs(this.dateStr).format(
                    'YYYY-MM-DD 00:00:00'
                );
                endT = this.$dayjs(this.dateStr)
                    .add(1, 'day')
                    .format('YYYY-MM-DD 00:00:00');
            } else {
                //获取预警时间里的值传参
                const { startT: queryStartT, endT: queryEndT } =
                    this.getQueryTime();
                startT = queryStartT;
                endT = queryEndT;
            }
            let params = {
                SBID: this.curEquit.SBXH,
                startT,
                endT
            };
            const { data = [] } = await getPHDataList(params);
            this.tableData = data;
        },
        //获取预警详情的开始时间和结束时间
        getQueryTime() {
            let startT = '';
            let endT = '';
            let PHEquipList = this.arrSendData.qtList.filter(
                (item) => item.SBMC === '' && item.ZNSBLX === 'PH'
            );

            if (PHEquipList.length) {
                let arrFirstListOne = PHEquipList[0].list;
                startT = arrFirstListOne[0].start;
                endT = arrFirstListOne[arrFirstListOne.length - 1].end;
            }
            return {
                startT,
                endT
            };
        },
        radioChange($event) {
            this.curEquit = $event.currentTarget.dataset.item;
            this.queryPHData();
        }
    },
    beforeDestroy() {
        uni.$off('sendData');
    }
};
</script>

<style lang="scss" scoped>
.board {
    background: #fff;
}
.ribao-table3 {
    table-layout: fixed;
    width: 100%;
}

.radiogroup {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 28rpx;
}

.radiogroup .left {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.radiogroup .radio {
    transform: scale(0.5);
}
.ribao-table3 td {
    line-height: 80rpx;
    height: 80rpx;
    text-align: center;
}

.table-head thead tr th {
    background: #daebff;
    color: #3a96ff;
    font-weight: 600;
    text-align: center;
    line-height: 80rpx;
    height: 80rpx;
    font-size: 28rpx;
}
.con {
    height: 790rpx;
    overflow-y: auto;
}
.qiye-board {
    padding: 0;
    margin: 0;
}
::v-deep .u-iconfont::before {
    font-size: 124rpx !important;
}
::v-deep .u-icon__label {
    font-size: 28rpx !important;
}
</style>
