<!-- @format -->

<template>
    <div>
        <div class="zy-form zy-detail-form">
            <div class="item">
                <p class="label star">放射源类型</p>
                <div class="rp pd-btn1" v-show="!isDetailPageShow()">
                    <button
                        type="button"
                        :class="form.FSYLX == item ? 'on' : ''"
                        v-for="(item, index) in arrRadioactiveType"
                        :key="index"
                        @click="changeRadioactiveType(item)"
                    >
                        {{ item }}
                    </button>
                </div>
                <div class="rp detail" v-show="isDetailPageShow()">
                    {{ form.FSYLX || '-' }}
                </div>
            </div>
            <div class="item">
                <p class="label star">绑定放射源</p>
                <div class="rp" v-show="!isDetailPageShow()">
                    <input
                        type="text"
                        v-model="form.FSYBH"
                        class="zy-input1"
                        placeholder="请扫描或手动输入放射源编号"
                        style="margin-right: 18rpx; font-size: 31rpx"
                    />
                    <image
                        src="@/static/app/images/sysic.png"
                        class="pd-sysbtn"
                        @click="getScanCode"
                    />
                </div>
                <div class="rp detail" v-show="isDetailPageShow()">
                    {{ form.FSYBH || '-' }}
                </div>
            </div>
            <div class="item">
                <p class="label star">绑定生产线</p>
                <div class="rp" v-show="!isDetailPageShow()">
                    <div class="zy-selectBox" @click="showScx()">
                        <p v-if="!form.SCXMC" class="res">请选择</p>
                        <p v-else class="res">{{ form.SCXMC }}</p>
                    </div>
                </div>
                <div class="rp detail" v-show="isDetailPageShow()">
                    <p class="res">{{ form.SCXMC || '-' }}</p>
                </div>
            </div>
            <div class="item">
                <p class="label star">设备安装时间</p>
                <div class="rp zy-selectBox" v-show="!isDetailPageShow()">
                    <p-mui-date-picker
                        @confirm="sdConfirm"
                        dateType="SECOND"
                        format="YYYY-MM-DD HH:mm:ss"
                    >
                        <input
                            style="width: 330rpx"
                            type="text"
                            v-model="form.AZSJ"
                            placeholder="请选择时间"
                            class="date-ipt res"
                            disabled
                        />
                    </p-mui-date-picker>
                </div>
                <div class="rp detail" v-show="isDetailPageShow()">
                    {{ form.AZSJ }}
                </div>
            </div>
            <div class="item">
                <p class="label star">放射源安装状态</p>
                <div class="rp pd-btn1" v-show="!isDetailPageShow()">
                    <button
                        type="button"
                        :class="form.YXZT == item.value ? 'on' : ''"
                        v-for="item in sbztList"
                        :key="item.value"
                        @click="changeYXZT(item)"
                    >
                        {{ item.label }}
                    </button>
                </div>
                <div class="rp detail" v-show="isDetailPageShow()">
                    {{ getEquipState() }}
                </div>
            </div>
            <div class="item">
                <p class="label star">放射源运行规律</p>
                <div class="rp pd-btn1" v-show="!isDetailPageShow()">
                    <button
                        type="button"
                        v-for="item in yxglList"
                        :key="item.value"
                        @click="changeYXGL(item)"
                        v-bind:class="form.YXGL.type == item.value ? 'on' : ''"
                    >
                        {{ item.label }}
                    </button>
                </div>
                <div class="rp detail" v-show="isDetailPageShow()">
                    {{ getEquipRegular() }}
                </div>
            </div>
            <div class="item" v-show="form.YXGL.type == '2'">
                <p class="label star">间歇周期</p>
                <div class="rp" v-show="!isDetailPageShow()">
                    <input
                        type="text"
                        v-model="form.YXGL.circle"
                        class="zy-input1"
                        placeholder="请填写间歇周期"
                    />
                </div>
                <div class="rp detail" v-show="isDetailPageShow()">
                    {{ form.YXGL.circle || '-' }}
                </div>
            </div>
            <div class="item" v-if="form.SFFC == '1'">
                <p class="label star" @click="togglePreventIllustration">
                    防拆功能
                    <image
                        src="@/static/app/images/whic.png"
                        class="pd-whic1"
                    />
                </p>
                <div class="rp pd-btn1">
                    <button
                        type="button"
                        :class="{ on: form.FCBJZT == '2' }"
                        @click="changePreventRemoveType('2')"
                    >
                        报警中
                    </button>
                    <button
                        type="button"
                        :class="{ on: form.FCBJZT == '1' }"
                        @click="changePreventRemoveType('1')"
                    >
                        启用
                    </button>
                    <button
                        type="button"
                        :class="{ on: form.FCBJZT == '0' }"
                        @click="changePreventRemoveType('0')"
                    >
                        未启用
                    </button>
                </div>
            </div>
        </div>
        <u-select
            value-name="SCXID"
            label-name="SCXMC"
            mode="single-column"
            :list="scxList"
            v-model="scxShow"
            @confirm="selectProduceLine"
        ></u-select>
    </div>
</template>

<script>
import { getScxList, getScxsbList } from '@/api/iot/enterprise.js';
export default {
    name: 'DataCollectionAppPHEquipInfo',
    props: {
        //父组件表单
        model: {
            type: Object,
            default: () => {}
        },
        //污染源数据
        info: {
            type: Object,
            default: () => {}
        },
        //页面类型
        pageType: {
            type: String,
            default: ''
        },
        //是否固定产线
        fixedLine: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            arrRadioactiveType: ['移动源', '固定源'],
            form: {
                FSYLX: '固定源', //放射类型
                FSYBH: '',
                SCXMC: '',
                SCXID: '',
                AZSJ: '', //安装时间
                YXZT: '1', //安装状态
                YXGL: {
                    type: '1',
                    circle: ''
                }, //运行规律
                SFFC: '0', //是否防拆SFFC 1展示0不展示
                FCBJZT: '0', //0不启用,  1  启用 ，默认启用 是否防拆
                CXMC: '',
                CXID: ''
            },
            canNotEditIMEI: false, //超过24小时不可以编辑IMEI
            scxShow: false,
            scxList: [], //生产线
            relativeEquips: [], //关联设备
            yxglList: [
                {
                    label: '平稳',
                    value: '1'
                },
                {
                    label: '间歇',
                    value: '2'
                }
            ],
            sbztList: [
                {
                    label: '工作',
                    value: '1'
                },
                {
                    label: '待机',
                    value: '2'
                },
                {
                    label: '关闭',
                    value: '3'
                }
            ]
        };
    },
	// watch: {
	// 	form: {
	// 		handler(newVal, oldVal) {
	// 			if (this.pageType == 'add') {
	// 				//this.backPage = true;
	// 				this.$emit("update:backPage",true)
	// 			}
	// 		},
	// 		deep: true,
	// 	},
	// },
    mounted() {
        //新增就获取当前时间
        if (this.pageType === 'add') {
            this.form.AZSJ = this.$dayjs().format('YYYY-MM-DD HH:mm:ss');
        }
        this.initProductLineList();
    },
    methods: {
        //放射源安装状态
        getEquipState() {
            let target = this.sbztList.find(
                (item) => item.value === this.form.YXZT
            );
            return target.label || '-';
        },
        //放射源运行规律
        getEquipRegular() {
            let target = this.yxglList.find(
                (item) => item.value === this.form.YXGL.type
            );
            return target.label || '-';
        },
        //详情显示，新增编辑隐藏
        isDetailPageShow() {
            return this.pageType === 'detail';
        },
        //修改防拆功能
        changePreventRemoveType(v) {
            if (this.pageType == 'add') {
                uni.showToast({
                    title: '新增设备防拆功能默认未开启',
                    icon: 'none'
                });
                return;
            }
            this.form.FCBJZT = v;
        },
        //扫码获取放射源编号
        async getScanCode() {
            try {
                uni.scanCode({
                    scanType: ['qrCode'],
                    success: (res) => {
                        console.log('res', res);
                        let result = res.result.split(';')[0];
                        this.form.FSYBH = result;
                    },
                    fail: (err) => {
                        uni.showToast({
                            title: '未识别到二维码！',
                            icon: 'none'
                        });
                    }
                });
            } catch (error) {}
        },
        //选择设备状态
        changeYXZT(item) {
            this.$set(this.form, 'YXZT', item.value);
        },
        //选择运行规律
        changeYXGL(item) {
            this.form.YXGL.type = item.value;
        },
        //切换放射源类型
        changeRadioactiveType(item) {
            this.form.FSYLX = item;
        },
        //初始化生产线
        initProductLineList() {
            let { WRYBH } = this.info;
            getScxList({
                ORGID: uni.getStorageSync('ORGID') || '',
                WRYBH: WRYBH
            }).then((res) => {
                if (res.data && res.data.length > 0) {
                    this.scxList = res.data;
                }
            });
        },
        //选择安装时间
        sdConfirm(obj) {
            this.form.AZSJ = obj.time;
        },
        //展示生产线
        showScx() {
			
            if (this.fixedLine) {
                uni.showToast({
                    title: '当前生产线已绑定，不可选择',
                    icon: 'none'
                });
                return;
            }
				
			if(!this.scxList.length){
				uni.showToast({
				    title: '该企业暂无生产线，请先添加企业生产线',
				    icon: 'none'
				});
			}
			console.log('this.scxList',this.scxList)
            this.scxShow = true;
        },
        //选择生产线
        async selectProduceLine(v) {
			
            try {
                await this.validIsHadEquipName(v[0].value);
                this.form.SCXID = v[0].value || '';
                this.form.SCXMC = v[0].label || '';
            } catch (error) {
                console.log('error');
                this.form.SCXID = '';
                this.form.SCXMC = '';
            }
        },
        //校验生产线是否已经存在设备名称
        validIsHadEquipName(scxid) {
            return new Promise((resolve, reject) => {
                //选择生产线前校验生产线上是否已经存在相同的名称的设备
                getScxsbList({
                    SCXID: scxid
                }).then((res) => {
                    this.relativeEquips = res.data || [];
                    let objSameNameEquip = this.relativeEquips.find(
                        (item) => item.SBMC === this.form.SBMC
                    );
                    if (objSameNameEquip) {
                        uni.showToast({
                            title: '该生产线已有设备重名，请重新输入',
                            icon: 'none'
                        });
                        return reject();
                    } else {
                        return resolve();
                    }
                });
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
