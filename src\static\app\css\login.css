@charset "utf-8";

/*common*/
/* @font-face {
    font-family: "DIN-Bold";
    src: url("~@/static/app/fonts/DIN-Bold.woff2") format("woff2"), url("~@/static/app/fonts/DIN-Bold.woff") format("woff"), url("~@/static/app/fonts/DIN-Bold.ttf") format("truetype"), url("~@/static/app/fonts/DIN-Bold.eot") format("embedded-opentype"), url("~@/static/app/fonts/DIN-Bold.svg") format("svg"), url("~@/static/app/fonts/DIN-Bold.otf") format("opentype");
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: "DIN-Medium";
    src: url("~@/static/app/fonts/DIN-Medium.woff2") format("woff2"), url("~@/static/app/fonts/DIN-Medium.woff") format("woff"), url("~@/static/app/fonts/DIN-Medium.ttf") format("truetype"), url("~@/static/app/fonts/DIN-Medium.eot") format("embedded-opentype"), url("~@/static/app/fonts/DIN-Medium.svg") format("svg"), url("~@/static/app/fonts/DIN-Medium.otf") format("opentype");
    font-weight: normal;
    font-style: normal;
} */
.gap{height: 18.11595rpx;}
.mask{position: fixed; left: 0; top: 0; right: 0; bottom: 0; background: rgba(0,0,0,.4); z-index: 1000; display: none;}
.header {
    background: linear-gradient(to bottom, #56b0fc, #62b9fb);
    position: relative;
    left: 0;
    right: 0;
    top: 0;
    z-index: 999;
    height: 79.71015rpx;
}
.pd-backbtn {
    position: absolute;
    left: 0;
    top: 0;
    width: 80.917875rpx;
    height: 79.71015rpx;
    background: url(~@/static/app/images/backic.png) no-repeat center;
        background-size: auto;
    background-size: 21.7391249rpx 36.2319rpx;
}
.title {
    text-align: center;
    font-size: 33.999975rpx;
    color: #fff;
    line-height: 79.71015rpx;
}
/*page*/
.loginbg{
    height: 100%;
    background: url(~@/static/app/images/loginbotbg.png) no-repeat center bottom;
    background-size: 100% auto;
}
.main{overflow-y: auto; overflow-x: hidden; -webkit-overflow-scrolling: touch; height: 100%;width: 100%;}
.inner {
	padding: 0rpx 0 0rpx;
}
.topbg{
    height: 100%;
    background: url(~@/static/app/images/topbg.png) no-repeat center top;
    background-size: 100% auto;

}
.logincell{
    padding: 182.36715rpx 37.5rpx 60.386475rpx;

}
.logincell .tit{
    color: #fff;
    font-size: 60.386475rpx;
    position: relative;
}
.tit::after{
    content: "";
    position: absolute;
    top: 94.5rpx;
    left: 0;
    height: 7.24635rpx;
    width: 73.9251rpx;
    background-color: #fff;
    border-radius: 7.5rpx;
}
.loginblock {
    margin: 30rpx;
    padding: 45rpx 30rpx;
    background-color: #fff;
    border-radius: 24.1545749rpx;
    color: #333;
    box-shadow: 0 5.4348rpx 27.77775rpx rgb(0, 0, 0, 0.2);
}
.pd-loginmod .inputwrap li {
    height: 81.999975rpx;
    position: relative;
    margin-bottom: 44.000025rpx;
}
.pd-loginmod .inputwrap .pd-ipt {
    height: 81.999975rpx;
    line-height: 81.999975rpx;
    display: block;
    width: 100%;
    background-color: #f1f2f6;
    border-radius: 8.454075rpx;
    padding-left: 72.367125rpx;
   /*  border-bottom: 1px solid #eee; */
    font-size: 32.000025rpx;
    color: #333;
    padding-right: 127.5rpx;
}
.pd-loginmod .inputwrap .pd-ipt.txt {
    background: #f1f2f6 url(~@/static/app/images/urseript.png) no-repeat 4% 24.75rpx;
        background-size: auto;
    background-size: 27.999975rpx 32.000025rpx;
}
.pd-loginmod .inputwrap li .pd-ipt.pwd {
    background: #f1f2f6 url(~@/static/app/images/passipt.png) no-repeat 4% 24.75rpx;
        background-size: auto;
    background-size: 27.999975rpx 32.000025rpx;
}
.iptclear2 {
    width: 29.589375rpx;
    height: 29.589375rpx;
    position: absolute;
    right: 12%;
    top: 57%;
    transform: translateY(-50%);
}
.pd-loginmod .inputwrap li img.eyepic {
    width: 31.401rpx;
    height: 20.5313999rpx;
    position: absolute;
    right: 4%;
    top: 57%;
    transform: translateY(-50%);
}
.low-login {
    height: 67.5rpx;
    line-height: 67.5rpx;
    font-size: 27.999975rpx;
    color: #666;
    margin-top: -30rpx;
    padding: 0 7.5rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.lfbox{
    display: flex;
    align-items: center;
}
.pass-checkbox1 {
    display: flex;
    padding: 12.077325rpx 0;
    align-items: center;
}
.pass-checkbox1 input {
    display: none;
}
.pass-checkbox1 i {
    width: 26.8236749rpx;
    height: 26.8236749rpx;
    background: url(~@/static/app/images/check-border.png) no-repeat center center;
    background-size: 100% 100%;
}
.pass-checkbox1 input:checked ~ i {
    background-image: url(~@/static/app/images/zy-check-yes.png);
}
.pass-checkbox1 .passfont {
    font-size: 26.8236749rpx;
    color: #b1b8c9;
    line-height: 21.7391249rpx;
    margin-left: 12.077325rpx;
}
.mr20{
    margin-right: 18.11595rpx;
}
.log-btn {
    width: 100%;
    height: 74.87925rpx;
    border-radius: 8.454075rpx;
    background: linear-gradient(to bottom, #4aa7fc, #62b9fb);
    color: white;
    font-size: 32.608725rpx;
    display: block;
    margin: 84.54105rpx auto 48.3091499rpx;
   /*  box-shadow: 0 5.4348rpx 27.77775rpx rgba(153,197,249,1); */
}
.agreement{
    margin: 60.386475rpx 30rpx;
}
.bluef{
    color: #4aa8fc;
}
.zy-data1 {
    background-color: #fff;
    padding: 9.999975rpx 0;
    padding-bottom: 9.999975rpx;
}
.zy-data1.rw-data1 {
    padding-bottom: 0;
}
.rw-data1 .rwrow1 {
    height: 90rpx;
    line-height: 90rpx;
    display: flex;
    justify-content: space-between;
    margin: 0 27rpx;
    margin-right: 0;
    padding-right: 27rpx;
    align-items: center;
   font-size: 27.77775rpx;
}
.rwrow1 .rw1{
    color: #2c323f;
}
.rwrow1 .des{
    color: #646c7f;
    height: 90rpx;
    line-height: 90rpx;

}
.border-b {
    border-bottom: solid 1px #ededed;
}
.rwnamebox {
    margin: 0 27rpx;
    margin-right: 0;
    padding-right: 27rpx;
    padding-bottom: 30.1932rpx;
}
.zy-data1 .rw1 {
    font-size: 27.77775rpx;
    color: #2c323f;
    height: 90rpx;
    line-height: 90rpx;
}
.jgbox {
    height: 100.24155rpx;
    line-height:  100.24155rpx;
    color: #646c7f;
    font-size: 27.77775rpx;
    padding: 27rpx 15rpx;
    background-color: #f3f5f9;
    border-radius: 9.999975rpx;
    position: relative;
}
.jgbox::after {
    content: '';
    border: 1px solid #f3f5f9;
    width: 8px;
    height: 8px;
    transform: rotate( 135deg);
    background-color: #f3f5f9;
    position: absolute;
    top: 0;
    left: 10%;
    z-index: 999;
    border-top: none;
    border-right: none;
    margin-top: -5px;
    margin-left: -4px;
}
.jgbox .fdes{
    height: 26.570025rpx;
    line-height: 26.570025rpx;
    padding-left: 28.0314rpx;
    font-size: 27.77775rpx;
    color: #646c7f;
    font-size: 27.77775rpx;
}
.addrico{
    background: url(~@/static/app/images/addr.png) no-repeat 0 center;
    background-size: 20.5313999rpx auto;
}
.zy-data1.rw-data1 .rwrow1 .arr1 {
    font-size: 27.77775rpx;
    color: #2c323f;
    line-height: 90rpx;
    padding-right: 33.999975rpx;
    background: url(~@/static/app/images/arwrt2.png) right center no-repeat;
        background-size: auto;
    background-size: 15.999975rpx;
}
.servebtn{
    padding: 0 4%;
}
.zy-map {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
}
.topdatas{
    background-color: #fff;
    padding: 2% 0;
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
}
.yy-posselect1{
    width: 20%;
    display: inline-block;
    padding: 0 30rpx 0 15rpx;
    font-size: 28.985475rpx;
    color: #333;
    line-height: 66.4251rpx;
    background: url(~@/static/app/images/data2-ic1.png) 80% center no-repeat;
    background-size: 25.3623rpx auto;
}
.yy-search1 input {
    width: 100%;
    height: 80.000025rpx;
    box-sizing: border-box;
    border-radius: 12.077325rpx;
    font-size: 32.000025rpx;
    color: #333;
    background: #f2f2f2 url(~@/static/app/images/ysearch.png) no-repeat 18.11595rpx center;
        background-size: auto;
    background-size: 35.000025rpx;
    padding-left: 90rpx;
}
.yy-search1{
    width: 76%;
}
.yy-site {
    position: absolute;
    top: 30%;
    left: 40%;
    width: 60rpx;
    height: 60rpx;
}
.yy-site img {
    width: 100%;
}
.yy-data4 {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 30rpx;
    background-color: #fff;
    border-top-left-radius: 30rpx;
    border-top-right-radius: 30rpx;
    height: 513.285rpx;
    overflow-y: auto;
}
.botaddrbox .item{
    font-size: 28.985475rpx;
    border-bottom: solid 1px #ededed;
    margin-bottom: 27rpx;
}
.botaddrbox .item:last-of-type{
    border-bottom: none;
}
.botaddrbox .sd{
    font-size: 28.985475rpx;
    color: #646c7f;
    background: url(~@/static/app/images/sdh.png) no-repeat 0 center;
    background-size: 25.7814rpx auto;
    padding-left: 33.81645rpx;
    margin-right: 18.11595rpx;
}
.botaddrbox .item .p1{
    font-size: 29.735475rpx;
    margin-bottom: 18.11595rpx;
    color: #2c323f;
}
.botaddrbox .item .p2{
    color: #646c7f;
    padding-bottom: 18.11595rpx;
}
