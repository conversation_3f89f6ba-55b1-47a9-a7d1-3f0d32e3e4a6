/** @format */

import axios from '@/common/ajaxRequest.js';
import { ULR_BASE } from '@/common/config.js';

export const getGgdmz = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/az/code',
        params: data
    });
};

// # 企业信息-应用场景
// {"DMJBH":"QYXX_YYCJ"}

// # 生产线-组织方式
// {"DMJBH":"SCX_ZZFS"}

// # 生产线-设备类型
// {"DMJBH":"SCX_SBLX"}

// # 产污设备-主要污染
// {"DMJBH":"CWSB_ZYWR"}

// # 产污设备-行业分类
// {"DMJBH":"CWSB_HYFL","FDM":"CWSB_HYFL"}

// # 产污设备--设备类型（将行业分类的值传给FDM，得到设备类型）
// {"DMJBH":"CWSB_HYFL","FDM":"SN"}

// # 治污设备-工艺类型
// {"DMJBH":"ZWSB_GYLX","FDM":"ZWSB_GYLX"}

// # 治污设备--设备类型（将工艺类型的值传给FDM，得到设备类型）
// {"DMJBH":"ZWSB_GYLX","FDM":"CCSS"}

// # 智能设备-错误码
// {"DMJBH":"ZNSB_ERR"}
