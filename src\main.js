/** @format */

import Vue from 'vue';
import App from './App';
import store from './store';
// import vueEsign from 'vue-esign'
import '@/common/logger.js';
import VueI18n from 'vue-i18n';
// import scan from './components/p-scan/scan.vue'
// import appWatch from '@/utils/app-watch.js';

// Vue.prototype.$appWatch = appWatch;

// Vue.component('scan',scan)
import initModal from '@/components/zhangxu-showModal/initModal.js';
import showModal from '@/components/zhangxu-showModal/show-modal';
initModal(Vue);
Vue.component('show-modal', showModal);



//#ifdef H5
// import AMap from 'vue-amap';
// Vue.use(AMap);
// AMap.initAMapApiLoader({
//     // 高德key
//     key: 'b698e8fc43715a16053e9fa156601706',
//     // 插件集合 （插件按需引入）
//     plugin: ['AMap.Geolocation']
// });
//#endif

import dayjs from './components/dayjs/dayjs.min.js';
import isSameOrAfter from './components/dayjs/plugin/isSameOrAfter/index';
dayjs.extend(isSameOrAfter);
Vue.prototype.$dayjs = dayjs;
import simpleDatePicker from './components/simple-date-picker/simple-date-picker.vue'
Vue.component('simple-date-picker',simpleDatePicker)


import Directives from "@/common/directives";
Vue.use(Directives);  
//引入p-chart
// import powerDataChart from 'p-charts';
// Vue.use(powerDataChart);

// 引入全局uView
//import uView from 'p-mui';
// import uView from './components/p-mui';
//Vue.use(uView);

import uView from "uview-ui";
Vue.use(uView);


// const echarts = require('echarts');
// Vue.prototype.$echarts = echarts;

// Vue.use(vueEsign)

Vue.prototype.$store = store;
App.mpType = 'app';

Vue.use(VueI18n);
const i18n = new VueI18n({
    locale: 'zh-CN',
    message: {
        'zh-CN': require('@/language/zh_CN.json'),
        'en-US': require('@/language/en_US.json')
    }
});



const app = new Vue({
    i18n,
    store,
    ...App
});
app.$mount();
