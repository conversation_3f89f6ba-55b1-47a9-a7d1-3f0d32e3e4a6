/*
 * @Author: your name
 * @Date: 2021-03-23 12:22:37
 * @LastEditTime: 2021-07-07 15:16:49
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /YDZF_APP/common/timePicker.js
 */
import dayjs from 'dayjs';

export default {
    //针对不同时间类型的情况选择
    timePicker(val){
        let timeInfo = {}
        let curMonth = dayjs().month() + 1
        if(val === 'week'){
            timeInfo.kssj = dayjs(new Date()).startOf('w').format('YYYY-MM-DD');
            timeInfo.jssj = dayjs(timeInfo.kssj).add(7, 'day').format('YYYY-MM-DD');
        }
        else if(val === 'month'){
            timeInfo.kssj = dayjs().startOf('month').format('YYYY-MM-DD');
            timeInfo.jssj = dayjs().endOf('month').format('YYYY-MM-DD')
        }
        else if(val === 'year'){
            timeInfo.kssj = dayjs().startOf('year').format('YYYY-MM-DD');
            timeInfo.jssj = dayjs().endOf('year').format('YYYY-MM-DD')
        }
        else if(val === 'season'){
            if(curMonth < 4){
                timeInfo.kssj = dayjs().month(0).startOf('month').format('YYYY-MM-DD');
                timeInfo.jssj = dayjs().month(2).endOf('month').format('YYYY-MM-DD');
            }
            else if(curMonth < 7){
                timeInfo.kssj = dayjs().month(3).startOf('month').format('YYYY-MM-DD');
                timeInfo.jssj = dayjs().month(5).endOf('month').format('YYYY-MM-DD');
            }
            else if(curMonth < 10){
                timeInfo.kssj = dayjs().month(6).startOf('month').format('YYYY-MM-DD');
                timeInfo.jssj = dayjs().month(8).endOf('month').format('YYYY-MM-DD');
            }
            else{
                timeInfo.kssj = dayjs().month(9).startOf('month').format('YYYY-MM-DD');
                timeInfo.jssj = dayjs().month(11).endOf('month').format('YYYY-MM-DD');
            }
        }else if(val === 'all'){
            timeInfo.kssj = ''
            timeInfo.jssj = ''
        }
        return timeInfo
    }
}