<!-- @format -->

<template>
	<div class="loginbg">
		<!-- <header class="header">
		<h1 class="title"></h1>
	</header> -->
		<section class="main">
			<div class="topbg"  :style="{'margin-top': marginTop+'px'}">
				<div class="logincell">
					<h2 class="tit">登录</h2>
				</div>
				<div class="loginblock">
					<dl class="pd-loginmod">
						<dd>
							<ul class="inputwrap">
								<li>
									<input type="text" class="pd-ipt txt" placeholder="用户名/账号/手机号" required clearable
										:focus="focusVal" v-model="username" />
									<image v-show="username" @click="username = ''" class="iptclear2"
										src="../../static/app/images/ipt_clear.png"></image>
								</li>
								<li>
									<!--  -->
									<input :type="passwordType" v-model="password" class="pd-ipt pwd psw"
										placeholder="请输入您的密码" />
									<image class="eyepic" src="../../static/app/images/eye_open.png"
										v-show="passwordType == 'text'" @click="passwordType = 'password'"></image>
									<image class="eyepic" src="../../static/app/images/eye_close.png"
										v-show="passwordType == 'password'" @click="passwordType = 'text'"></image>
								</li>
							</ul>
							<div class="low-login">
								<div class="lfbox">
									<!-- <span class="tickbox1"><input type="checkbox" class="checked">记住密码</span>
								<span class="tickbox1"><input type="checkbox">自动登录</span> -->
									<checkbox style="
                                            font-size: 46rpx;
                                            color: #aaa;
                                            zoom: 60%;
                                        " :checked="savePass" @click="checkChange()">
										记住密码
									</checkbox>
									<checkbox style="
                                            font-size: 46rpx;
                                            color: #aaa;
                                            zoom: 60%;
                                            margin-left: 40rpx;
                                        " :checked="isAutoLogin" @click="autoLogin()">
										自动登录
									</checkbox>
								</div>
								<!-- 	<div class="rt">
								<label class="fr"><a>忘记密码？</a></label>
							</div> -->
							</div>
							<button type="button" class="log-btn" @tap="loginByPwd"
								v-show="username && password">登录</button>

							<div class="disabled-btn" v-show="!username || !password">登录</div>
						</dd>
					</dl>
				</div>
				<p class="agreement" v-if="false">
					<checkbox style="font-size: 46rpx; color: #aaa; zoom: 60%" :checked="isAgree" @click="changeAgree">
						<span class="passfont">
							已阅读并同意
							<a class="bluef" @click="toPolicy(1)">《用户协议》</a>
							<a class="bluef" @click="toPolicy(2)">《隐私政策》</a>
						</span>
					</checkbox>
				</p>
			</div>
		</section>
		<view v-if="showUpgrade" class="flex-column-layout upgrade-tip-mask">
			<view class="flex-column-layout upgrade-progress-tip">
				<text style="width: 100%; text-align: left">正在更新升级数据</text>
				<view style="width: 100%"><progress :percent="upgradeProgress" active active-mode="forwards"
						stroke-width="3" show-info /></view>
			</view>
		</view>
	</div>
</template>

<script>
	import service from '../../service.js';
	import {cacheAreas} from '@/common/district.js'
	import {
		mapState,
		mapMutations
	} from 'vuex';
	import mInput from '../../components/m-input.vue';
	import loginService from '@/api/login-service.js';
	// #ifdef APP-PLUS
	import appService from '@/api/app-service.js';
	// #endif

	export default {
		components: {
			mInput
		},
		data() {
			return {
				focusVal: false,
				passwordType: 'password',
				loginType: 0,
				loginTypeList: ['密码登录', '免密登录'],
				mobile: '',
				code: '',
				providerList: [],
				hasProvider: false,
				username: '',
				password: '',
				isAgree: false,
				positionTop: 0,
				isDevtools: false,
				codeDuration: 0,
				savePass: false,
				isAutoLogin: false,
				showUpgrade: false,
				upgradeProgress: 0,
				screenHeight: 0,
				marginTop: 0
			};
		},

		created() {
			this.focusVal = this.username == '' ? true : false;
		},

		onLoad() {
			let _self = this;
			let promisePwd = new Promise((resolve, reject) => {
				uni.getStorage({
					key: 'save_password',
					success: function(r) {
						_self.savePass = r.data || false;
						if (_self.savePass) {
							uni.getStorage({
								key: 'password',
								success: function(r) {
									_self.password = r.data;
									resolve();
								}
							});
						}
					}
				});
			})

			let promiseName = new Promise((resolve, reject) => {
				uni.getStorage({
					key: 'username',
					success: function(r) {
						_self.username = r.data;

						_self.focusVal = _self.username == '' ? true : false;

						resolve();
					}
				});
			})


			// let promiseAgree = new Promise((resolve,reject)=>{
			// 	uni.getStorage({
			// 		key: 'isAgree',
			// 		success: function(r) {
			// 			_self.isAgree = r.data;
			// 			resolve();
			// 		}
			// 	});
			// })

			uni.getStorage({
				key: 'autoLogin',
				success: r => {
					_self.isAutoLogin = r.data;
				}
			});

			Promise.all([promisePwd, promiseName])
				.then(() => {
					if (_self.password && _self.username) {
						// 是否自动登录
						uni.getStorage({
							key: 'autoLogin',
							success: r => {
								if (r.data === true) {
									_self.loginByPwd();
								}
							}
						});
					}
				})


			// uni.getStorage({
			// 	key: 'autoLogin',
			// 	success: function(r) {

			// 	}
			// });


		},

		computed: {
			pageListStyle() {
				return {
					height: 'calc(100vh)'
				};
			}
			// mapState(["forcedLogin"])
		},

		onReady() {
			this.initPosition();

			// #ifdef MP-WEIXIN
			this.isDevtools = uni.getSystemInfoSync().platform === 'devtools';
			// #endif
		},

		mounted() {
			// #ifdef APP-PLUS
			const self = this;
			self.screenHeight = uni.getSystemInfoSync().screenHeight - plus.navigator.getStatusbarHeight();
			//监听键盘高度变化
			uni.onWindowResize((res) => {
				let windowHeight = res.size.windowHeight;
				
				if(windowHeight >= self.screenHeight){
					self.marginTop = 0
					return;
				}
				
				let queryDom = uni.createSelectorQuery().in(self) // 使用api并绑定当前组件
				queryDom
					.select('.psw')
					.boundingClientRect((res) => {
						if(!res){
							return;
						}
						console.log("尺寸变化", res)
						self.navTop = res.top + 160
						console.log(res.bottom)
						
						if(res.bottom > windowHeight ){
							self.marginTop = -(res.bottom - windowHeight + 44)
						} 
						

					})
					.exec()

			})
			// #endif

			uni.removeStorageSync('token');
			//检测版本更新；
			this.checkAppUpgrade();
		},

		methods: {
			...mapMutations(['login']),
			checkChange() {
				this.savePass = !this.savePass;
				uni.setStorage({
					key: 'save_password',
					data: this.savePass,
					success: function(r) {}
				});
			},
			initPosition() {
				/**
				 * 使用 absolute 定位，并且设置 bottom 值进行定位。软键盘弹出时，底部会因为窗口变化而被顶上来。
				 * 反向使用 top 进行定位，可以避免此问题。
				 */
				this.positionTop = uni.getSystemInfoSync().windowHeight - 100;
			},
			loginByPwd() {
				// 账号 testym
				// 密码 a.123456
				/**
				 * 客户端对账号信息进行一些必要的校验。
				 * 实际开发中，根据业务需要进行处理，这里仅做示例。
				 */
				// if (!this.isAgree) {
				// 	uni.showToast({
				// 		icon: 'none',
				// 		duration: 2000,
				// 		title: '请阅读并同意《用户协议》《隐私政策》'
				// 	});
				// 	return;
				// }
				if (this.username.length < 3) {
					uni.showToast({
						icon: 'none',
						duration: 2000,
						title: '账号最短为 3 个字符'
					});
					return;
				}

				uni.setStorage({
					key: 'username',
					data: this.username,
					success: function(r) {}
				});

				// uni.setStorage({
				// 	key: 'password',
				// 	data: this.password,
				// 	success: function(r) {}
				// });

				const data = {
					username: this.username,
					password: this.password
				};
				let _self = this;
				this.listenLogin();
				uni.showLoading({
					title: '登录中...'
				});
				loginService.loginByPassword(this.username, this.password);
			},

			listenLogin() {
				let _self = this;
				uni.$on('onLoginSuccess', params => {
					uni.$off('onLoginSuccess');
					uni.hideLoading();
					_self.onLoginSuccess();
				});

				uni.$on('onLoginFail', error => {
					console.log(error);
					uni.$off('onLoginFail');
					uni.hideLoading();
					let errorMsg = typeof error === 'object' ? error.error_msg : '登录出错，请检查网络';
					// uni.showModal({
					// 	title: `登录失败: ${errorMsg || "登录出错，请检查网络"}`,
					// });
					// this.$showModal({
					// 	title: '登录失败',
					// 	content: ` ${errorMsg || '登录出错，请检查网络'}`,

					// });
					uni.showToast({
						title: errorMsg,
						icon: 'none',
						duration: 2000
					});
				});
			},

			onLoginSuccess() {
				cacheAreas();
				this.$store.commit('login', this.username);
				uni.reLaunch({
					url: '/pages/enterprise/Index'
				});
				
				if(this.savePass){
					uni.setStorage({
						key: 'password',
						data: this.password,
						success: function(r) {}
					});
				}
				
			},

			onLoginFail(err) {},

			toMain(userName) {
				this.login(userName);
				/**
				 * 强制登录时使用reLaunch方式跳转过来
				 * 返回首页也使用reLaunch方式
				 */
				if (this.forcedLogin) {
					uni.reLaunch({
						url: '/pages/index/Index'
					});
				} else {
					uni.navigateBack();
				}
			},
			toPolicy(index) {
				if (index === 1) {
					uni.navigateTo({
						url: '/pages/login/userAgreement'
					})
				} else {
					uni.navigateTo({
						url: '/pages/login/privacyPolicy'
					})
				}
			},

			//获取版本信息
			checkAppUpgrade() {
				// #ifdef APP-PLUS
				let _self = this;
				appService.setProgressUpdater(percent => {
					_self.showUpgrade = true;
					_self.upgradeProgress = percent;
					if (percent === 100) {
						_self.showUpgrade = false;
					}
				});
				appService.checkMPAppUpgrade();
				// #endif
			},

			changeAgree() {
				this.isAgree = !this.isAgree;
				uni.setStorage({
					key: 'isAgree',
					data: this.isAgree,
					success: function(r) {}
				});
			},

			autoLogin() {
				this.isAutoLogin = !this.isAutoLogin;
				uni.setStorage({
					key: 'autoLogin',
					data: this.isAutoLogin,
					success: function(r) {}
				});
			}
		}
	};
</script>

<style scoped>
	
	@import url("../../static/app/css/login.css");
	.inputwrap .iptclear2 {
		width: 28rpx;
		height: 28rpx;
		position: absolute;
		right: 20rpx;
		top: 50%;
		margin-top: -14rpx;
		transform: none;
	}

	.inputwrap .eyepic {
		height: 24rpx;
		width: 28rpx;
		position: absolute;
		right: 20rpx;
		top: 50%;
		margin-top: -12rpx;
	}

	.upgrade-tip-mask {
		position: fixed;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		z-index: 9999;
		justify-content: flex-start;
	}

	.upgrade-progress-tip {
		width: 72%;
		padding: 20rpx;
		border-radius: 6rpx;
		background-color: #fff;
		box-shadow: 0 0 20rpx 0 #ccc;
		position: fixed;
		top: 70rpx;
		left: 50%;
		margin-left: -36%;
	}

	.disabled-btn {
		width: 100%;
		height: 74rpx;
		border-radius: 8rpx;
		background-color: #dddddd;
		color: #333;
		font-size: 32rpx;
		display: block;
		margin: 84rpx auto 48rpx;
		text-align: center;
		line-height: 74rpx;
	}

	.psw {
		outline: none;
		border: none;
	}

	/* .logincell {
		padding: 187px 18px 28px;
	} */
</style>
