<!-- @format -->

<template>
    <div style="background: #f1f1f1; padding-top: 80rpx">
        <header class="header">
            <i class="ic-back" @click="back"></i>
            <h1 class="title">{{ enterpriseName }}生产线</h1>
            <i class="header-delete" v-show="isCanDelLine" @click="delScx"
                >删除</i
            >
        </header>
        <section class="main">
            <div class="modwrap">
                <div class="yy-tit">
                    <h1>基本信息</h1>
                    <span class="edit" @click="editJbxx"></span>
                </div>
                <div class="zy-form">
                    <div class="item">
                        <p class="label">生产线名称</p>
                        <div class="rp">
                            <i class="rfont pd-txt2">{{
                                model.SCXMC || '-'
                            }}</i>
                        </div>
                    </div>
                    <div class="item remark">
                        <p class="label">污染物</p>
                        <div class="rp zy-textarea1">
                            <i class="rfont pd-txt2">{{ model.ZYWR || '-' }}</i>
                        </div>
                    </div>

                    <div class="item remark">
                        <p class="label">工艺流程</p>
                        <div class="rp zy-textarea1">
                            <i class="rfont pd-txt2">{{ model.GYLC || '-' }}</i>
                        </div>
                    </div>
                    <div class="item remark">
                        <p class="label">备注信息</p>
                        <div class="rp zy-textarea1">
                            <i class="rfont pd-txt2">{{ model.BZ || '-' }}</i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="gap"></div>
            <div class="gap"></div>
            <div class="modwrap">
                <div class="yy-tit">
                    <h1>已关联生产设备（{{ glsb.length }}个)</h1>
                    <span class="add" @click="addRelationSb"></span>
                </div>
                <div class="tablebox">
                    <view class="pd-tablelst1a">
                        <view class="thead">
                            <view class="tr">
                                <view
                                    style="flex: 1; padding-right: 24rpx"
                                    class="td"
                                    >设备名称</view
                                >
                                <view
                                    style="flex: 1; padding-right: 24rpx"
                                    class="td"
                                    >IMEI号</view
                                >
                                <view style="flex: 1" class="td"
                                    >最新数据上报</view
                                >
                                <view
                                    style="
                                        display: flex;
                                        justify-content: flex-end;
                                        align-items: center;
                                    "
                                    class="td"
                                >
                                    <!-- v-if="canDelete(item.CJSJ)" -->
                                    <p class="del" style="background: none"></p>
                                </view>
                            </view>
                        </view>
                        <view class="tbody">
                            <view
                                class="tr"
                                v-for="(item, index) in glsb"
                                :key="index"
                            >
                                <view
                                    class="td"
                                    style="
                                        color: #4874ff;
                                        flex: 1;
                                        padding-right: 24rpx;
                                    "
                                    @click="toSbDetail({ SBLX: '治污' }, item)"
                                >
                                    {{ item.SBMC }}
                                </view>
                                <view
                                    style="
                                        flex: 1;
                                        padding-right: 24rpx;
                                        word-break: break-all;
                                    "
                                    class="td"
                                >
                                    {{ item.IMEI }}</view
                                >
                                <view style="flex: 1" class="td">{{
                                    item.SBSJ
                                }}</view>
                                <view
                                    style="
                                        display: flex;
                                        justify-content: flex-end;
                                        align-items: center;
                                    "
                                    class="td"
                                >
                                    <p
                                        class="del"
                                        :class="
                                            canDelete(item.CJSJ)
                                                ? ''
                                                : 'no-icon'
                                        "
                                        @click="deleteSb(item)"
                                    ></p>
                                </view>
                            </view>
                        </view>
                    </view>
                </div>
            </div>
            <div class="gap"></div>
            <div class="gap"></div>
            <div class="modwrap">
                <div class="yy-tit">
                    <h1>已关联其他设备（{{ otherEquipList.length }}个)</h1>
                    <span class="add" @click="addRelationOtherEquip"></span>
                </div>
                <div class="tablebox" v-show="otherEquipList.length">
                    <view class="pd-tablelst1a">
                        <view class="thead">
                            <view class="tr">
                                <view
                                    style="flex: 1; padding-right: 24rpx"
                                    class="td"
                                    >设备名称</view
                                >
                                <view
                                    style="flex: 1; padding-right: 24rpx"
                                    class="td"
                                    >IMEI号</view
                                >
                                <view style="flex: 1" class="td"
                                    >最新数据上报</view
                                >
                                <view
                                    style="
                                        display: flex;
                                        justify-content: flex-end;
                                        align-items: center;
                                    "
                                    class="td"
                                >
                                    <!-- v-if="canDelete(item.CJSJ)" -->
                                    <p class="del" style="background: none"></p>
                                </view>
                            </view>
                        </view>
                        <view class="tbody">
                            <view
                                class="tr"
                                v-for="(item, index) in otherEquipList"
                                :key="index"
                            >
                                <view
                                    class="td"
                                    style="
                                        color: #4874ff;
                                        flex: 1;
                                        padding-right: 24rpx;
                                    "
                                    @click="toAddPhAndRadiation(item)"
                                >
                                    {{ item.SBMC }}
                                </view>
                                <view
                                    style="
                                        flex: 1;
                                        padding-right: 24rpx;
                                        word-break: break-all;
                                    "
                                    class="td"
                                >
                                    {{ item.IMEI }}</view
                                >
                                <view style="flex: 1" class="td">{{
                                    item.CJSJ
                                }}</view>
                                <view
                                    style="
                                        display: flex;
                                        justify-content: flex-end;
                                        align-items: center;
                                    "
                                    class="td"
                                >
                                    <p
                                        class="del"
                                        :class="
                                            canDelete(item.CJSJ)
                                                ? ''
                                                : 'no-icon'
                                        "
                                        @click="deleteSb(item)"
                                    ></p>
                                </view>
                            </view>
                        </view>
                    </view>
                </div>
            </div>
            <div class="gap"></div>
            <div class="gap"></div>
        </section>
    </div>
</template>

<script>
import { getGgdmz } from '@/api/iot/ggdmz.js';
import { getOtherList } from '@/api/iot/phAndRadiation.js';
import {
    scxInfo,
    getScxsbList,
    getRelationList,
    deleteRelation,
    deleteScsb,
    delScx,
    getProductContorlRelationeEditHistory
} from '@/api/iot/enterprise.js';
export default {
    data() {
        return {
            enterpriseName: '',
            enterpriseInfo: {},
            info: {},
            model: {
                ORGID: '',
                WRYBH: '',
                SCXMC: '', //生产线名
                ZYWR: '', //污染物
                GYLC: '', //工艺流程
                BZ: '' //备注内容
            },
            scxId: '',
            relationList: [],
            disabled: true,
            glsb: [],
            isLines: false,
            productionHeight: '',
            isHaveEditProductContorlRelationeEditHistory: false, //是否存在产治污关系配置记录
            isCanDelLine: false, //是否可以删除产线
            otherEquipList: [] //其他设备
        };
    },
    watch: {
        model(newval) {
            this.$nextTick(function () {
                this.getDescBox();
                //console.log('height',this.productionHeight);
            });
        }
    },
    onLoad(option) {
        this.enterpriseInfo = uni.getStorageSync('userInfo');
        this.info = JSON.parse(decodeURIComponent(option.info));
        this.enterpriseName = this.info.WRYMC;
        // uni.setNavigationBarTitle({
        //     title:this.info.WRYMC + '生产线'
        // });
        this.model.WRYBH = this.info.WRYBH;
        this.scxId = option.scxId;
    },

    mounted() {
        this.initFormData();
        // this.getRelationList();
        this.getScsbList();
        this.getOtherEquipList();
        this.queryProductContorlRelationeEditHistory();
    },
    methods: {
        //查询其他设备
        getOtherEquipList() {
            let { sjqx } = this.enterpriseInfo;
            let { WRYBH } = this.info;
            getOtherList({
                ORGID: sjqx,
                WRYBH: WRYBH
            }).then((res) => {
                this.otherEquipList = res.data || [];
            });
        },
        //添加其他设备
        addRelationOtherEquip() {
            let infoParams = {
                ...this.model,
                WRYBH: this.info.WRYBH
            };
            let url =
                '/pages/enterprise/phAndRadiation/PhAndRadiation?type=add&fixedLine=true&info=' +
                encodeURIComponent(JSON.stringify(infoParams));

            uni.navigateTo({
                url
            });
        },
        // 跳转其他设备详情
        toAddPhAndRadiation(item) {
            let infoParams = {
                ...this.model,
                WRYBH: this.info.WRYBH
            };
            let url =
                '/pages/enterprise/phAndRadiation/PhAndRadiation?type=detail&info=' +
                encodeURIComponent(JSON.stringify(infoParams)) +
                '&sbInfo=' +
                encodeURIComponent(JSON.stringify(item));

            uni.navigateTo({
                url: url
            });
        },
        //查询产治污关系编辑历史记录
        async queryProductContorlRelationeEditHistory() {
            const arrData = await getProductContorlRelationeEditHistory(
                this.info.WRYBH
            );
            this.isHaveEditProductContorlRelationeEditHistory =
                arrData.length > 0;
        },
        getDescBox() {
            uni.createSelectorQuery()
                .in(this)
                .select('#pollutants')
                .boundingClientRect((result) => {
                    if (result) {
                        this.productionHeight = result.height;
                    } else {
                        //this.getDescBox();
                    }
                })
                .exec();
        },
        initFormData() {
            let { sjqx } = this.enterpriseInfo;
            scxInfo({
                ORGID: sjqx,
                SCXID: this.scxId
            }).then((res) => {
                this.model = res.data;
                console.log('model', this.model);
            });
        },
        // getRelationList() {
        // 	let {
        // 		sjqx
        // 	} = this.enterpriseInfo;
        // 	getRelationList({
        // 		ORGID: sjqx,
        // 		SCXID: this.scxId
        // 	}).then((res) => {
        // 		this.relationList = res.data;
        // 	});
        // },
        getScsbList(callback) {
            getScxsbList({
                SCXID: this.scxId
            }).then((r) => {
                this.glsb = r.data;
                this.isCanDelLine = !this.glsb.length;
                callback?.();
            });
        },

        toSbDetail(v, item) {
            item.WRYBH = this.info.WRYBH;
            let url = `/pages/enterprise/cwsb/cwEquipmentDetail?info=${encodeURIComponent(
                JSON.stringify(item)
            )}&sbInfo=${encodeURIComponent(
                JSON.stringify(v)
            )}&enterpInfo=${encodeURIComponent(JSON.stringify(this.info))}`;

            uni.navigateTo({
                url
            });
        },
        deleteRelation(item) {
            let self = this;
            uni.showModal({
                title: '提示',
                content: '确定解除此治污线的绑定?',
                success: function (res) {
                    if (res.confirm) {
                        console.log('用户点击确定');
                        deleteRelation({
                            CZWID: item.CZWID
                        }).then((res) => {
                            uni.showToast({
                                title: '删除成功',
                                duration: 500
                            }).then(() => {
                                setTimeout(() => {
                                    self.getRelationList();
                                    //删除绑定后，要刷新企业详情的绑定列表
                                    let pages = getCurrentPages(); // 当前页面
                                    let beforePage = pages[pages.length - 2]; // 上一页

                                    if (beforePage.$vm) {
                                        beforePage.$vm.getRelationList();
                                    } else {
                                        beforePage.getRelationList();
                                    }
                                }, 500);
                            });
                        });
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                }
            });
        },
        deleteSb(item) {
            if (!this.canDelete(item.CJSJ)) {
                return;
            }
            let self = this;
            uni.showModal({
                title: '提示',
                content: '确定删除此设备?',
                success: function (res) {
                    if (res.confirm) {
                        console.log('用户点击确定');
                        deleteScsb({
                            SBID: item.SBID
                        }).then((res) => {
                            //如果设备类型是ph或者辐射
                            if (item.ZNSBLX === 'PH' || item.ZNSBLX === 'RAD') {
                                self.delOtherEquipCallback();
                            } else {
                                self.delProductEquipCallback();
                            }
                        });
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                }
            });
        },
        //删除关联其他设备
        delOtherEquipCallback() {
            let self = this;
            setTimeout(() => {
                self.getOtherEquipList();
                let pages = getCurrentPages(); // 当前页面
                let beforePage = pages[pages.length - 2]; // 上一页

                if (beforePage.$vm) {
                    beforePage.$vm.getOtherEquipList &&
                        beforePage.$vm.getOtherEquipList();
                } else {
                    beforePage.getOtherEquipList &&
                        beforePage.getOtherEquipList();
                }

                self.getScsbList(() => {
                    uni.showToast({
                        title: '删除成功',
                        duration: 500
                    }).then(() => {
                        // if (self.isHaveEditProductContorlRelationeEditHistory) {
                        //     setTimeout(() => {
                        //         //提示需重新配置产治污关系
                        //         uni.showModal({
                        //             title: '提示',
                        //             content: '需重新配置产治污关系',
                        //             showCancel: false
                        //         });
                        //     }, 800);
                        // }
                    });
                });
            }, 500);
        },

        //删除生产设备成功的回调
        delProductEquipCallback() {
            let self = this;
            setTimeout(() => {
                let pages = getCurrentPages(); // 当前页面
                let beforePage = pages[pages.length - 2]; // 上一页

                if (beforePage.$vm) {
                    beforePage.$vm.getScx && beforePage.$vm.getScx();
                    beforePage.$vm.getScsbList && beforePage.$vm.getScsbList();
                } else {
                    beforePage.$vm.getScx && beforePage.getScx();
                    beforePage.$vm.getScsbList && beforePage.getScsbList();
                }

                self.getScsbList(() => {
                    uni.showToast({
                        title: '删除成功',
                        duration: 500
                    }).then(() => {
                        if (self.isHaveEditProductContorlRelationeEditHistory) {
                            setTimeout(() => {
                                //提示需重新配置产治污关系
                                uni.showModal({
                                    title: '提示',
                                    content: '需重新配置产治污关系',
                                    showCancel: false
                                });
                            }, 800);
                        }
                    });
                });
            }, 500);
        },

        confirm() {
            this.zywrShow = false;
            this.model.ZYWR = this.selectWrwList.join(',');
        },

        getMore() {},
        editJbxx() {
            //跳转添加治污设备页面，页面治污线不可修改
            this.model = {
                ...this.model,
                WRYBH: this.info.WRYBH
            };
            uni.navigateTo({
                url:
                    './addProductionLine?type=edit&info=' +
                    encodeURIComponent(JSON.stringify(this.model))
            });
        },
        addRelationSb() {
            this.model = {
                ...this.model,
                WRYBH: this.info.WRYBH
            };
            uni.navigateTo({
                url:
                    '/pages/enterprise/cwsb/addCwEquipment?&type=addEquit&info=' +
                    encodeURIComponent(JSON.stringify(this.model))
            });
        },

        delScx() {
            let self = this;
            uni.showModal({
                title: '提示',
                content: `请确认删除产线：${self.enterpriseName}!`,
                success: function (res) {
                    if (res.confirm) {
                        console.log('用户点击确定');
                        delScx({
                            SCXID: self.scxId
                        }).then((res) => {
                            if (res.data.status === '000') {
                                uni.showToast({
                                    title: '删除成功',
                                    duration: 500
                                }).then(() => {
                                    setTimeout(() => {
                                        let pages = getCurrentPages(); // 当前页面
                                        let beforePage =
                                            pages[pages.length - 2]; // 上一页
                                        if (beforePage.$vm) {
                                            beforePage.$vm.getScx();
                                            beforePage.$vm.getScsbList();
                                        } else {
                                            beforePage.getScx();
                                            beforePage.getScsbList();
                                        }

                                        uni.navigateBack({
                                            delta: 1
                                        });
                                    }, 500);
                                });
                            } else {
                                uni.showToast({
                                    title: '删除失败',
                                    duration: 500
                                });
                            }
                        });
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                }
            });
        },
        back() {
            let pages = getCurrentPages(); // 当前页面
            let beforePage = pages[pages.length - 2]; // 上一页

            if (beforePage.$vm) {
                beforePage.$vm.getScx && beforePage.$vm.getScx();
                beforePage.$vm.getScsbList && beforePage.$vm.getScsbList();
            } else {
                beforePage.$vm.getScx && beforePage.getScx();
                beforePage.$vm.getScsbList && beforePage.getScsbList();
            }
            uni.navigateBack({
                delta: 1
            });
        },
        canDelete(time) {
            let outDate = this.$dayjs(time).add(1, 'day');

            return this.$dayjs().isBefore(outDate);
        },
        /**
         * 判断浏览器添加换行符
         */
        getSpaceBySystem() {
            var browser = navigator.appName;
            var b_version = navigator.appVersion;
            var version = b_version.split(';');
            var trim_Version = version[1].replace(/[ ]/g, '');
            if (
                browser == 'Microsoft Internet Explorer' &&
                (trim_Version == 'MSIE7.0' || trim_Version == 'MSIE8.0')
            ) {
                return '\r\n';
            } else {
                return '\n';
            }
        }
    }
};
</script>

<style scoped>
html {
    -webkit-tap-highlight-color: transparent;
    height: 100%;
}
body {
    -webkit-backface-visibility: hidden;
    height: 100%;
}
.pd-tablebx image {
    height: 30rpx;
}

.bznr {
    display: inline-block;
    height: 60rpx;
    line-height: 60rpx;
}

/deep/ .u-form-item--left__content__label {
    font-size: 30rpx;
    color: #2c323f;
}

/deep/ .u-input__textarea {
    border-radius: 8rpx;
    height: 100rpx;
    background-color: rgb(243, 245, 249);
    padding-left: 10rpx;
}

/deep/ .uni-textarea-wrapper {
    height: 100% !important;
}

/deep/ .u-list-item {
    margin: 0;
}

/deep/ .uni-input-placeholder {
    padding: 0 20rpx 0 0;
    /* text-align: right; */
    font-size: 26rpx;
}

.listWarp {
    width: 100%;
    height: 100%;
    overflow-y: scroll;
    padding-top: 80rpx;
}

.listWarp p {
    width: 100%;
    padding: 20rpx;
    text-align: center;
    border-bottom: 1px solid #efefef;
}

.opration {
    padding: 20rpx;
    display: flex;
    justify-content: space-between;
    position: absolute;
    width: 100%;

    background-color: #fff;
}

.confirm {
    color: rgb(60, 170, 255);
}

.on {
    color: rgb(60, 170, 255);
}

/deep/ .u-icon {
    padding: 0 0 0 10rpx;
}

.tip-title {
    text-align: center;
    border-bottom: 1px solid #efefef;
    padding: 20rpx;
    font-weight: 600;
}

.tip-content {
    padding: 20rpx;
}

.tip-content p {
    /* padding: 20rpx 0; */
    font-size: 26rpx;

    color: #666;
}

.tip-know {
    position: absolute;
    bottom: 0;
    padding: 20rpx;
    text-align: center;
    border-top: 1px solid #efefef;
    color: rgb(60, 170, 255);
    width: 100%;
}

/* .zy-textarea1 .rfont{
	text-align: left;
} */

.header-delete {
    position: absolute;
    right: 18rpx;
    top: 50%;
    color: #ffffff;
    transform: translateY(-50%);
    font-size: 27rpx;
}
</style>
