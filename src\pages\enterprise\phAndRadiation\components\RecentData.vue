<!-- @format -->
<!-- @format -->

<template>
    <div>
        <div class="zy-form">
            <div class="item">
                <p
                    class="label"
                    style="color: #4874ff; flex: auto"
                    @click="changeShowThreeList"
                >
                    查看最近三次采集结果
                </p>
                <div class="rp">
                    <p class="pd-txt2" v-show="showThreeList">
                        <span class="show-newData">已展示采集结果</span>
                        <u-icon
                            name="reload"
                            color="#ddd"
                            size="22"
                            @click="getList()"
                        ></u-icon>
                    </p>
                </div>
            </div>
        </div>
        <div class="gap"></div>
        <!-- 采集结果 -->
        <div v-show="showThreeList">
            <div class="pd-con1" v-if="equipType === 'PH'">
                <u-empty
                    mode="data"
                    v-if="!threeList || !threeList.length"
                    text="暂无数据"
                    font-size="26rpx"
                ></u-empty>
                <ul
                    class="pd-ullst1"
                    v-for="(item, index) in threeList"
                    :key="index"
                >
                    <li>
                        <em>检测时间</em><i>{{ item.JCSJ || '-' }}</i>
                    </li>
                    <li>
                        <em>基站当前经度</em><i>{{ Number(item.JD).toFixed(4) || '-' }}</i>
                    </li>
                    <li>
                        <em>基站当前纬度</em><i>{{ Number(item.WD).toFixed(4)  || '-' }}</i>
                    </li>
                    <li>
                        <em>设备电压</em
                        ><i
                            :class="{
                                abnormal: item.VOL_ZT != 0,
                                normal: item.VOL_ZT == 0
                            }"
                            >{{ item.VOL_ZT == 0 ? '正常' : '异常' }}</i
                        >
                    </li>
                    <li>
                        <em>信号强度</em
                        ><i
                            :class="{
                                abnormal: item.VOL_ZT != 0,
                                normal: item.VOL_ZT == 0
                            }"
                            >{{ item.VOL_ZT == 0 ? '正常' : '异常' }}</i
                        >
                    </li>
                    <li>
                        <em>信号质量</em
                        ><i
                            :class="{
                                abnormal: item.RSRQ_ZT != 0,
                                normal: item.RSRQ_ZT == 0
                            }"
                            >{{ item.RSRQ_ZT == 0 ? '正常' : '异常' }}</i
                        >
                    </li>
                    <li>
                        <em>信号功率</em
                        ><i
                            :class="{
                                abnormal: item.SP_ZT != 0,
                                normal: item.SP_ZT == 0
                            }"
                            >{{ item.SP_ZT == 0 ? '正常' : '异常' }}</i
                        >
                    </li>
                    <li>
                        <em>pH值</em><i>{{ item.POWER || '-' }}</i>
                    </li>
                </ul>
            </div>
            <div class="pd-con1" v-if="equipType === 'RAD'">
                <u-empty
                    mode="data"
                    v-if="!threeList || !threeList.length"
                    text="暂无数据"
                    font-size="28rpx"
                ></u-empty>
                <ul
                    class="pd-ullst1"
                    v-for="(item, index) in threeList"
                    :key="index"
                >
                    <li>
                        <em>检测时间</em><i>{{ item.JCSJ || '-' }}</i>
                    </li>
                    <li>
                        <em>设备状态</em
                        ><i
                            :class="{
                                abnormal: item.ERR != '正常',
                                normal: item.ERR == '正常'
                            }"
                            >{{ item.ERR || '-' }}</i
                        >
                    </li>

                    <li>
                        <em>电压1</em
                        ><i
                            :class="{
                                abnormal: item.VOL_ZT1 != 0,
                                normal: item.VOL_ZT1 == 0
                            }"
                            >{{ item.VOL_ZT1 == 0 ? '正常' : '异常' }}</i
                        >
                    </li>
                    <li>
                        <em>电压2</em
                        ><i
                            :class="{
                                abnormal: item.VOL_ZT2 != 0,
                                normal: item.VOL_ZT2 == 0
                            }"
                            >{{ item.VOL_ZT2 == 0 ? '正常' : '异常' }}</i
                        >
                    </li>
                    <li>
                        <em>当前使用电池编号</em
                        ><i>{{ item.BATTERYNUM || '-' }}</i>
                    </li>
                    <li>
                        <em>信号功率</em
                        ><i
                            :class="{
                                abnormal: item.SP_ZT != 0,
                                normal: item.SP_ZT == 0
                            }"
                            >{{ item.SP_ZT == 0 ? '正常' : '异常' }}</i
                        >
                    </li>
                    <li>
                        <em>信号质量</em
                        ><i
                            :class="{
                                abnormal: item.RSRQ_ZT != 0,
                                normal: item.RSRQ_ZT == 0
                            }"
                            >{{ item.RSRQ_ZT == 0 ? '正常' : '异常' }}</i
                        >
                    </li>

                    <li>
                        <em>辐射剂量率</em><i>{{ item.POWER || '-' }}</i>
                    </li>
                    <li>
                        <em>累计剂量率</em><i>{{ item.RADIATESUM || '-' }}</i>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script>
import { newStateList } from '@/api/iot/enterprise.js';
export default {
    name: 'DataCollectionAppGetLastedResult',
    props: {
        imei: {
            type: String,
            default: ''
        },
        equipType: {
            type: String,
            default: 'PH'
        }
    },
    data() {
        return {
            showThreeList: false,
            threeList: []
        };
    },
    mounted() {},

    methods: {
        //展示列表最新3条数据数据
        changeShowThreeList() {
            if (this.imei.length == '') {
                uni.showToast({
                    title: '请先扫码获取IMEI号imei',
                    icon: 'none'
                });
                return;
            }
            if (!this.threeList || !this.threeList.length) {
                this.getList();
            }
            this.showThreeList = !this.showThreeList;
        },
        //获取最新的3条数据
        getList() {
            this.threeList = [];
            newStateList({
                IMEI: this.imei
            }).then((res) => {
                this.threeList = [...res.data];
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.pd-ullst1 {
    margin-bottom: 20rpx;
    > li + li {
        border: none;
    }
    li {
        padding: 20rpx 12rpx 20rpx 0rpx;
    }
}
.pd-ullst1>li em{
	width:32%
}
.normal {
    color: #3ab918;
}
.abnormal {
    color: #ff0000;
}
</style>
