<template>
	<div>
		<div class="zy-form">
			<div class="item">
				<p class="label star">设备名称</p>
				<div class="rp">
					<input type="text" v-model="model.SBMC" class="zy-input1" placeholder="请填写设备名称" @click="hideTabbar"
						@focus="hideTabbar" @blur="showTabbar">
				</div>
			</div>
			<div class="item">
				<p class="label star">IMEI号</p>
				<div class="rp">
					<input type="text" :disabled="imeiShow" v-model="model.IMEI" class="zy-input1"
						placeholder="请扫描或手动输入后6位数" style="margin-right: 18rpx;font-size:31rpx;"
						@input="search(model.IMEI)" @click="hideTabbar" @focus="hideTabbar" @blur="showTabbar">
					<image src="../../../static/app/images/sysic.png" class="pd-sysbtn" @click="scanCode()" />
				</div>
			</div>
			<div class="item">
				<p class="label star">
					设备安装时间
				<div class="rp">
					<p-mui-date-picker @confirm="sdConfirm" dateType="SECOND" format="YYYY-MM-DD HH:mm:ss">
						<input type="text" v-model="model.AZSJ" placeholder="请选择时间" class="date-ipt" disabled />
					</p-mui-date-picker>
				</div>
			</div>
			<div class="item">
				<p class="label star">设备安装状态</p>
				<div class="rp pd-btn1">
					<button type="button" :class="model.YXZT == item.value ? 'on' : ''" v-for="(item,index) in sbztList"
						:key='item.value' @click="changeYXZT(item)">{{item.label}}</button>
				</div>
			</div>
			<div class="item">
				<p class="label star">设备类型</p>
				<div class="rp pd-btn1">
					<button type="button" :class="model.ZNSBLX == item.value ? 'on' : ''"
						v-for="(item,index) in equipTypeList" :key='item.value'
						@click="changeEquipType">{{item.label}}</button>
				</div>
			</div>
			<div class="item" v-if="model.ZNSBLX == 'ZD'">
				<p class="label">振动能量</p>
				<div class="rp pd-btn1">
					<button type="button" :class="model.ZDQD == item.value ? 'on' : ''"
						v-for="(item,index) in shakeList" :key='item.value'
						@click="changeShakeType(item)">{{item.label}}</button>
				</div>
			</div>
			<div class="item" v-if="model.ZNSBLX == 'ZD'">
				<p class="label star">是否受周边影响</p>
				<div class="rp pd-btn1">
					<button type="button" :class="model.SFSYX == item.value ? 'on' : ''"
						v-for="(item,index) in influenceList" :key='item.value'
						@click="changeInfluenceType(item)">{{item.label}}</button>
				</div>
			</div>

			<div class="item" v-if="model.ZNSBLX == 'ZD'">
				<p class="label star">设备运行规律</p>
				<div class="rp pd-btn1">
					<button type="button" v-for="(item,index) in yxglList" :key='item.value' @click="changeYXGL(item)"
						v-bind:class="model.type == item.value ? 'on' : ''">{{item.label}}
					</button>
				</div>
			</div>

			<div class="item" v-if="model.type == '2'">
				<p class="label star">间歇周期</p>
				<div class="rp">
					<input type="text" v-model="model.circle" class="zy-input1" placeholder="请填写间歇周期">
				</div>
			</div>
			<div class="item">
				<p class="label star">绑定治污线</p>
				<div class="rp">
					<div class="zy-selectBox" @click="showZwx">
						<p v-if="!model.ZWXMC" class="res">请选择</p>
						<p else class="res">{{model.ZWXMC}}</p>
					</div>
				</div>
			</div>

			<div class="item" v-if="model.SFFC == '1'">
				<p class="label star" @click="togglePreventIllustration">防拆功能 <image
						src="../../../static/app/images/whic.png" class="pd-whic1">
				</p>
				<div class="rp pd-btn1">
					<button type="button" :class="{on: model.FCBJZT  == '1' || model.FCBJZT  == '2'}"
						@click="changePreventRemoveType('1')">启用</button>
					<button type="button" :class="{on: model.FCBJZT  == '0'}"
						@click="changePreventRemoveType('0')">不启用</button>
				</div>
			</div>

			<div class="item nfx">
				<p class="label" @click="toggleInstallIllustration">安装照片<image src="../../../static/app/images/whic.png"
						class="pd-whic1">
				</p>
				<div class="gap"></div>
				<ul class="pd-ulpic1">
					<li v-for="(item,index) in azFileList">
						<div style='position: relative;width: 213.76815rpx;'>
							<image mode="scaleToFill" :src="getFileUrl(item)" alt=""
								@click="previewImage(azFileList,index)" />
							<image mode="scaleToFill" src="../../../static/app/images/cls.png" class="delImg"
								@click="delFile(item)" />
						</div>
					</li>
					<li>
						<image @click="addFile('ZDSB')" src="../../../static/app/images/addpic.png"
							class="pd-addpic1" />
					</li>
				</ul>
			</div>
			<div class="item remark">
				<p class="label">备注信息</p>
				<div class="rp">
					<textarea auto-height type="text" v-model="model.BZ" row="3" :class="model.BZ ? '':'two' "
						@click="hideTabbar" @focus="hideTabbar" @blur="showTabbar" class="zy-textarea1"
						placeholder="请输入备注信息如设备运行规律区别于“平稳”“间歇”，可特别说明下。"></textarea>
				</div>
			</div>

		</div>
		<div class="mymask" v-show="showMask"></div>
		<div class="pd-botdlg" v-show="showPreventIllustration">
			<i class="dlgcls" @click="togglePreventIllustration"></i>
			<div class="gap"></div>
			<div class="gap"></div>
			<div class="pd-con1">
				<div class="pd-tit1">防拆功能说明</div>
				<div class="gap"></div>
				<div class="gap"></div>

				<div class="gap"></div>
				<dl class="pd-dltxt1">
					<dt></dt>
					<dd>1、启用防拆功能后，如果防拆开关弹起将会触发防拆报警；</dd>
					<dd>2、新增设备防拆功能默认开启。</dd>

				</dl>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
			</div>
		</div>

		<div class="pd-botdlg" v-show="showInstallIllustration">
			<i class="dlgcls" @click="toggleInstallIllustration"></i>
			<div class="gap"></div>
			<div class="gap"></div>
			<div class="pd-con1">
				<div class="pd-tit1">拍照示例</div>
				<div class="gap"></div>
				<div class="gap"></div>
				<ul class="pd-ulpic1 sample">
					<li>
						<image mode="aspectFit" src="../../../static/images/sample/sb1.jpg" alt="">
							<p>近景</p>
					</li>
					<li>
						<image mode="aspectFit" src="../../../static/images/sample/sb3.jpg" alt="">
							<p>中景</p>
					</li>
					<li>
						<image mode="aspectFit" src="../../../static/images/sample/sb2.jpg" alt="">
							<p>远景</p>
					</li>
				</ul>
				<div class="gap"></div>
				<div class="gap"></div>
				<dl class="pd-dltxt1">
					<dt>说明：</dt>
					<dd>1、近景尽量把设备IMEI号拍出来；</dd>
					<dd>2、尽量把设备所安装的设备与设备的大概位置展示出来。</dd>
				</dl>
				<div class="gap"></div>
				<div class="gap"></div>
			</div>
		</div>
		<u-select value-name="ZWXID" label-name="ZWXMC" mode="single-column" :list="zwxList" v-model="zwxShow"
			@confirm="selectControlLine"></u-select>
		<u-select value-name="IMEI" label-name="IMEI" mode="single-column" :list="imeiList" v-model="imeiShow"
			@confirm="selectImei"></u-select>
	</div>
</template>

<script>
	import {
		getZwxList,
		addZwsb,
		newStateList,
		getFilelist,
		deletefile,
		znsb,
		saveCwsb,
		editZwsb,
		//	editScsb
		//getScxList,
		//addScsbList,

		commocode,
	} from '@/api/iot/enterprise.js';
	import {
		guid
	} from '@/common/uuid.js';
	import {
		DOWNLOAD_URLZDY,
		UPLOAD_URL,
		LOGIN_ULR_BASE,
	} from '@/common/config.js';

	export default {
		props: {
			formType: {
				type: String,
				//addProduct 添加生产设备 addEquitProduct锁定产线添加生产设备 editProduct编辑生产设备 detailProduct数据回显生产设备
				//addControl 添加治污设备 addEquitControl锁定产线添加治污设备 editControl编辑治污设备 detailControl数据回显治污设备
				default: 'addControl',
			},
			info: {
				type: Object,
				default: () => {} //企业信息
			}
		},
		data() {
			return {
				enterpriseInfo: {},//企业信息
				model: {
					SBID: '',
					SBMC: '', //设备名称
					IMEI: '', //IMEI
					YXZT: '1', //运行状态
					YXGL: {
						type: "",
					}, //运行规律
					SCXID: '', //生产线
					AZSJ: '', //安装时间
					BZ: '', //备注内容
					USER: '', //用户
					ORGID: '', //企业的ORGID,
					SCXMC: '', //生产线id
					circle: '', //间歇周期
					type: '1',
					ZDQD: '', //振动能量
					SFSYX: '1', //是否受周边影响
					ZNSBLX: '', //设备类型  （ZD-振动，DL-电流）
					SFFC: '0', //是否防拆SFFC 1展示0不展示
					FCBJZT: '1', //0不启用,  1  启用 ，默认启用
				},
				rules: {
					SBMC: {
						required: true,
						message: '请填写设备名称',
					},
					IMEI: {
						required: true,
						message: '请扫描获取IMEI'
					},
					YXZT: {
						required: true,
						message: '请选择设备安装状态'
					},
					SFSYX: {
						required: true,
						message: '请选择是否受周边影响'
					},
					YXGL: {
						required: true,
						message: '请选择运行规律'
					},
					circle: {
						required: false,
						message: '请填写间歇周期'
					},
					ZWXID: {
						required: true,
						message: '请选择治污线'
					},
					AZSJ: {
						required: true,
						message: '请获取安装时间'
					},

				},
				imeiShow: false,
				imeiList: [],
				isGetIMEI: false, //是否匹配到IMEI
				showMask: false,
				showInstallIllustration: false,
				showPreventIllustration: false,

				azFileList: [], // 安装上传图片
				shakeList: [ //振动能量
					// {
					// 	label:'弱',
					// 	value:'1'
					// },
					// {
					// 	label:'中',
					// 	value:'2'
					// },
					// {
					// 	label:'强',
					// 	value:'3'
					// },
				],
				influenceList: [ //周边影响
					// {
					// 	label:'是',
					// 	value:'1'
					// },
					// 	{
					// 		label:'否',
					// 		value:'0'
					// 	},
				],
				sbztList: [{
						label: "工作",
						value: "1"
					},
					{
						label: "待机",
						value: "2"
					},
					{
						label: "关闭",
						value: "3"
					}
				],
				yxglList: [{
						label: "平稳",
						value: "1"
					},
					{
						label: "间歇",
						value: "2"
					}
				],
				equipTypeList: [{
						value: 'ZD',
						label: '振动'
					},
					{
						value: 'DL',
						label: '电流'
					},
				],
				preventRemoveList: [{
						value: '1',
						label: '启用'
					},
					{
						value: '0',
						label: '不启用'
					},
				],
				zwxShow: false, //治污线show
				zwxList: [], //治污线

			}
		},
		watch: {
			'model.IMEI': {
				handler(newV, oldV) {
					if (newV == '') {
						//	this.threeList = []
						this.model.SFFC = '0'
						this.model.FCBJZT = '1'
					}
				},
				immediate: true

			}
		},
		mounted() {
			this.enterpriseInfo = uni.getStorageSync('userInfo');

			//治污设备 --- 从列表页进入新增页面
			if (this.formType == 'addControl') {
				// this.info = JSON.parse(decodeURIComponent(option.info));
				this.model = uni.getStorageSync('duePolluteDraft') || this.model;
				this.azFileList = uni.getStorageSync('duePolluteDraftFIleList') || this.azFileList
				//新增需要生成新得随机设备id
				this.model.SBID = guid();
				//获取公共代码
				this.getCommonCode()
				//加载治污线
				this.initContorlLines();
			}
		},
		methods: {
			// 获取治污线
			initContorlLines() {
				let {
					sjqx
				} = this.enterpriseInfo;
				let {
					WRYBH
				} = this.info;
				getZwxList({
					ORGID: sjqx,
					WRYBH: WRYBH
				}).then(res => {
					if (res.data && res.data.length > 0) {
						this.zwxList = res.data;
					}
				});
			},
			//获取公共代码
			getCommonCode() {
				//生产设备--振动能量
				commocode({
					code: 'ZDSB_ZDQD'
				}).then(res => {
					if (res.data && res.data.length) {
						this.shakeList = res.data;
					}
				})
				//生产设备--周边影响
				commocode({
					code: 'ZDSB_SFSYX'
				}).then(res => {
					if (res.data && res.data.length) {
						this.influenceList = res.data;
					}
				})
			},
			//选定时间
			sdConfirm(obj) {
				this.model.AZSJ = obj.time
			},
			//存草稿
			setDraft(type) {
				if (type == 'control') { //治污设备存草稿
					uni.setStorageSync('duePolluteDraft', this.model);
					if (this.azFileList.length > 0) {
						uni.setStorageSync('duePolluteDraftFIleList', this.azFileList);
					}
				}

			},
			//搜索
			search(imei) {
				if (imei && imei.length < 5) {
					if (this.timer) {
						clearTimeout(this.timer);
					}
					this.timer = setTimeout(() => {
						uni.showToast({
							title: '请先输入5位以上的IMEI号',
							icon: 'none'
						});
					}, 500);
					return;

				}
				if (imei && imei.length >= 5) {
					if (this.timer) {
						clearTimeout(this.timer);
					}
					this.timer = setTimeout(() => {
						this.getImei(imei);
					}, 500);
				}

			},
			getImei(imei) {
				znsb({
					IMEI: imei
				}).then((res) => {
					if (!res.data || res.data.length === 0) {
						uni.showToast({
							title: "未匹配到IMEI号，请检查输入！",
							icon: 'none'
						});
						this.isGetIMEI = false;
					} else {
						uni.hideKeyboard();
						this.imeiList = res.data;
						this.imeiShow = true;
						this.isGetIMEI = true;
					}
				})
			},
			selectImei(v) {
				this.model.IMEI = v[0].value;
				let obj = this.imeiList.find(e => e.IMEI == this.model.IMEI)
				this.model.ZNSBLX = obj.ZNSBLX;
				this.model.SFFC = obj.SFFC;
				this.model.FCBJZT = '1';
				this.changeShowThreeList()
			},
			scanCode() {
				uni.scanCode({
					scanType: ['qrCode'],
					success: res => {
						this.fromScan = true;
						let imei = res.result.split(';')[0];
						console.log("扫码", res);
						if(imei){
								this.search(imei);
						}

						// this.getnewStateList(this.model.IMEI)
					},
                    fail: (err) => {
                        uni.showToast({
                            title: '未识别到二维码！',
                            icon: 'none'
                        });
                    }
				});
			},
			//选择设备状态
			changeYXZT(item) {
				this.$set(this.model, 'YXZT', item.value)
			},
			//设备类型
			changeEquipType() {
				uni.showToast({
					title: '当前设备类型不可切换',
					icon: 'none'
				})

			},
			//选择振动能量
			changeShakeType(item) {
				// 非必填，如果当前没有值说明用户要选中
				// 如果当前有值但是不等于选中的值说明用户要切换状态
				if (this.model.ZDQD !== item.value) {
					this.$set(this.model, 'ZDQD', item.value)
				} else {
					this.$set(this.model, 'ZDQD', '')
				}
			},
			//是否受周边影响
			changeInfluenceType(item) {
				this.$set(this.model, 'SFSYX', item.value)
			},
			//选择运行规律
			changeYXGL(item) {
				this.$set(this.model, 'type', item.value)
				this.$set(this.model, 'YXGL', {
					type: item.value
				})
			},
			//治污线展示
			showZwx() {
				if (this.fixedLine) {
					uni.showToast({
						title: '当前治污线已绑定，不可选择',
						icon: 'none'
					});
					return;
				}
				this.zwxShow = true;
			},
			toggleInstallIllustration() {
				this.showMask = !this.showMask;
				this.showInstallIllustration = !this.showInstallIllustration;
			},
			togglePreventIllustration() {
				this.showMask = !this.showMask;
				this.showPreventIllustration = !this.showPreventIllustration;
			},
			//修改防拆功能
			changePreventRemoveType(v) {
				if (this.pageType == 'add' || this.pageType == 'addEquit') {
					uni.showToast({
						title: '新增设备防拆功能默认开启',
						icon: 'none'
					})
					return;
				}
				this.model.FCBJZT = v;
			},
			//选择治污线
			selectControlLine(v) {
				this.model.ZWXID = v[0].value;
				this.model.ZWXMC = v[0].label;
			},
			//校验治污表单
			validControl(){
				//设置运行规律，间歇周期
				if (this.model.type == '1') {
					this.$set(this.model, 'YXGL', {
						type: this.model.type
					})
				} else if (this.model.type == '2') {
					this.$set(this.model, 'YXGL', {
						type: this.model.type,
						circle: this.model.circle
					})
				}
				// 如果选择运行规律优先校验运行规律的必填项，间歇周期只有在选中了运行规律并且是间歇时才去校验
				this.rules.circle.required = this.model.type == "2" ? true : false
				//判断设备类型，重置相关校验规则,
				//如果振动，周边影响，运行规律，间歇周期都不是必填,并清空数据项
				if (this.model.ZNSBLX == 'ZD') {
					this.rules.SFSYX.required = true;
					this.rules.YXGL.required = true;
				} else if (this.model.ZNSBLX == 'DL') {
					this.rules.SFSYX.required = false;
					this.rules.YXGL.required = false;
					this.rules.circle.required = false;
					this.model.SFSYX = ''
					this.model.YXGL = ''
					this.model.circle = ''
				}

				this.model.USER = this.enterpriseInfo.id;
				this.model.ORGID = this.enterpriseInfo.sjqx;
				this.model.CJR = this.enterpriseInfo.name;

				let rules = Object.keys(this.rules);
				for (let i = 0; i < rules.length; i++) {
					let field = rules[i];
					let requires = this.rules[field];
					// 单独校验运行规律
					if (this.model.ZNSBLX == 'ZD' && field === 'YXGL' && !this.model['YXGL']['type']) {
						uni.showToast({
							icon: 'none',
							title: '请选择运行规律',
						});
						return
					}
					if (field !== 'YXGL' && (!this.model[field] || !this.model[
							field].length) && requires
						.required) {
						uni.showToast({
							icon: 'none',
							title: requires.message,
						});
						return;
					}
				}
				// 传给后端需要转格式
				this.model.YXGL = JSON.stringify(this.model.YXGL)
				//保存前判断是否匹配到IMEI
				if (!this.isGetIMEI && this.model.IMEI.length != 15) {
					uni.showToast({
						title: "未匹配到IMEI号，请检查输入！",
						icon: 'none'
					});
					return;
				}

				znsb({
					IMEI: this.model.IMEI
				}).then((res) => {
					if (!res.data || res.data.length === 0) {
						uni.showToast({
							title: "未匹配到IMEI号，无法保存，请检查输入！",
							icon: 'none'
						});
					} else {
						addZwsb(this.model).then(res => {
							if (res.data && res.data.status != '000') {
								uni.showToast({
									title: '添加失败',
									duration: 800
								});
								return;
							}
							uni.showToast({
								title: '添加成功',
								duration: 800
							}).then(() => {
								//保存成功清除草稿
								uni.removeStorageSync("duePolluteDraft");
								uni.removeStorageSync('duePolluteDraftFIleList');
								setTimeout(() => {
									let pages = getCurrentPages(); // 当前页面
									//刷新上一页面的设备列表
									let beforePage = pages[pages.length -
										2]; // 上一页
									if (beforePage.$vm || beforePage.$vm) {
										console.log(1)
										beforePage.$vm.getZwx && beforePage
											.$vm.getZwx();
										beforePage.$vm.getZwsbList &&
											beforePage.$vm.getZwsbList();
									} else {
										console.log(2)
										beforePage.getZwx && beforePage
											.getZwx();
										beforePage.getZwsbList &&
											beforePage.getZwsbList();
									}

									//从生产线详情进入时，需要刷新企业信息页面的生产线列表、生产设备列表
									if (this.fixedLine === true) {
										console.log(3)
										let parentPage = pages[pages
											.length - 3];
										if (parentPage.$vm) {
											parentPage.$vm.getZwx();
											parentPage.$vm.getZwsbList();
										} else {
											parentPage.getZwx();
											parentPage.getZwsbList();
										}
									}
									this.backPage = false;
									uni.navigateBack({
										delta: 1
									});
								}, 1000);
							});

						}).catch(err => {
							uni.showToast({
								title: '添加失败'
							});
						});
					}
				})
			},
			//获取文件路径
			getFileUrl(item) {
				return DOWNLOAD_URLZDY + item.WJID;
			},
			//添加文件
			addFile(zlx) {
				if (this.azFileList.length >= 3) {
					uni.showToast({
						title: "最多只能上传3张照片",
						icon: 'none'
					});
					return;
				}

				let self = this;
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
					success: function(res) {
						console.log('reschooseImage', res);
						//console.log(JSON.stringify(res.tempFilePaths));
						self.uploadFile(res, zlx);
					}
				});
			},
			//上传
			uploadFile(res, zlx) {
				console.log('resuploadFile', res);
				console.log('this.model.SBID', this.model.SBID);
				let f = res.tempFilePaths[0];
				let {size,type} = res.tempFiles[0];
				let self = this;
				uni.showLoading({
					title: "上传中"
				});
				uni.uploadFile({
					url: UPLOAD_URL,
					filePath: f,
					name: f.name,
					formData: {
						WJDX:size/1024,
						WJLX:type,
						LXDM: 'ZDFJ',
						ZLXDM: zlx,
						YWSJID: this.model.SBID,
						WJMC: f.name
					},
					timeout: 60000,
					success: function(r) {
						uni.showToast({
							title: '上传成功',
							icon: 'none'
						});
						//上传成功掉获取文件列表接口
						self.getFileList();
						uni.hideLoading();
					},
					fail: function(err) {
						uni.showToast({
							title: '上传失败',
							icon: 'none'
						})
						uni.hideLoading();
					}
				});
			},
			//获取上传后的文件列表，看接口文档要传什么参数,
			getFileList() {
				getFilelist({
					"pageSize": 100000,
					"pageNum": 1,
					"YWSJID": this.model.SBID,
					"LXDMS": "ZDFJ",
					"ZLXDMS": "WRYZP,GYLCT,ZDSB"
				}).then(res => {
					let fileData = res[0];
					if (fileData && fileData.zlxList && fileData.zlxList.length > 0) {
						fileData.zlxList.forEach(list => {
							if (list.ZLXDM == 'ZDSB') {
								this.azFileList = list.fileList;
							}
						});
					}
				});
			},
			//预览图片
			previewImage(fileList, index) {
				//将每一张图片的url都取出来传入uniapp
				let fileUrls = fileList.map(file => {

					return this.getFileUrl(file);
				});
				// 预览图片
				uni.previewImage({
					current: index,
					urls: fileUrls,
				});
			},
			//删除照片
			delFile(file) {
				let self = this;
				uni.showModal({
					title: '提示',
					content: '确认删除照片?',
					success: function(res) {
						if (res.confirm) {
							console.log('用户点击确定');
							deletefile(file.WJID).then(res => {
								uni.showToast({
									title: '删除成功',
									duration: 500
								}).then(() => {
									setTimeout(() => {
										self.getFileList();
									}, 500);

								});

							});
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},

		}
	};
</script>

<style>
</style>
