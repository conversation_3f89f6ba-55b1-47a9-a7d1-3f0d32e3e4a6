/*
 * @Author: your name
 * @Date: 2021-03-23 12:22:37
 * @LastEditTime: 2021-06-30 16:36:43
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: /YDZF_APP/common/net/reserve-params-injector.js
 */
import http from './http.js';

import {LOGIN_CACHE_KEYS} from '@/api/login-service.js';

http.interceptors.request.push((request, token) => {
	let params = request.params;
	let header = request.header || {};
	
	if(params){
		params.sim = params.sim || '17777392862';
		params.imei = params.imei || '111';
		params.os = params.os || 'Android';
	};
	
	let resolveToken = token || uni.getStorageSync(LOGIN_CACHE_KEYS.token);
	if(resolveToken){
		params.jwtToken = resolveToken;
		
		header.Authorization = `Bearer ${resolveToken}`;
	}
	let userId = uni.getStorageSync(LOGIN_CACHE_KEYS.userId);
	if(userId){
		params.userid = userId;
	}
	let password = uni.getStorageSync(LOGIN_CACHE_KEYS.password);
	if(password){
		params.pwd = password;
	}
});
