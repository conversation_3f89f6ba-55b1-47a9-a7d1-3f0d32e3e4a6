<!-- @format -->

<template>
	<div>
		<u-empty text="暂无证明资料" mode="list" v-if="!isCanEdit && list.length == 0">
		</u-empty>

		<ul class="ul-pic1" v-show="!tempVideoUrl">
			<li class="imgli" v-for="(item, index) in list" :key="item.WJID">
				<div class="innerbox">
					<video v-if="getFileType(item.WJMC) == 'video'" class="img" id="myVideo" :src="getVideoUrl(item)"
						:controls="false">
						<cover-view @tap="previewVideo(item, index)" style="position: relative;">
							<image  src="https://i.postimg.cc/xC6HNmy9/play.png" mode="widthFix" />
							<cover-image v-if="isCanEdit" mode="aspectFill" src="../../static/app/images/cls.png"
								class="delImg" @click="delFile(item)" />
						</cover-view>

					</video>

					<image v-if="getFileType(item.WJMC) == 'image'" class="img" mode="scaleToFill"
						:src="getFileUrl(item)" alt="" @click="previewImage(list, item)" />
					<image v-if="isCanEdit" mode="aspectFill" src="../../static/app/images/cls.png" class="delImg"
						@click="delFile(item)" />
				</div>
			</li>

			<li v-if="isCanEdit">
				<div class="innerbox">
					<image @click="chooseMediaType" src="../../static/app/workbench/images/addtu.png" class="addpic" />
				</div>
			</li>
		</ul>

		<div v-if="tempVideoUrl" class="mask1">
			<image @tap="tempVideoUrl=''" class="_root" src="../../static/app/images/cls.png" mode="widthFix"></image>
			<div class="block" @tap.stop>
				<video autoplay :src="tempVideoUrl" id="video_play"></video>
			</div>
		</div>
	</div>
</template>

<script>
	import {
		getFilelist,
		deletefile,
		downloadFile
	} from '@/api/iot/appendix.js';
	import {
		API_LOGIN_SERVICE_URL,
		LOGIN_ULR_BASE,
		UPLOAD_URL,
		DOWNLOAD_URLZDY,
		PREVIEW_FILE_URL
	} from '@/common/config.js';
	export default {
		name: '',
		props: {
			list: {
				type: Array,
				default: () => {}
			},
			isCanEdit: {
				type: Boolean,
				default: false
			},
			childTypeKeyword: {
				type: String,
				default: ''
			},
			uploadId: {
				type: String,
				default: ''
			},
			isShowVideo: {
				type: Boolean,
				default: true
			}
		},
		data() {
			return {
				tempVideoUrl: ''
			};
		},
		onLoad(options) {},
		mounted() {},
		methods: {
			//获取文件list
			getFileList() {
				let obj = {
					pageSize: 100000,
					pageNum: 1,
					YWSJID: this.uploadId,
					LXDMS: 'ZDFJ',
					ZLXDMS: this.childTypeKeyword
				};
				getFilelist(obj)
					.then((res) => {
						console.log('res11111', res);
						let fileData = res[0];
						if (
							fileData &&
							fileData.zlxList &&
							fileData.zlxList.length > 0
						) {
							fileData.zlxList.forEach((list) => {
								console.log('list', list);
								if (list.ZLXDM == this.childTypeKeyword) {
									this.$emit('update:list', list.fileList);
								}
							});
						}
					})
					.catch((err) => {
						console.log('err', err);
					});
			},

			//删除文件
			delFile(file) {
				if (this.timer) {
					clearTimeout(this.timer);
				}
				this.timer = setTimeout(() => {
					let self = this;
					uni.showModal({
						title: '提示',
						content: '确认删除?',
						success: function(res) {
							if (res.confirm) {
								console.log('用户点击确定');
								deletefile(file.WJID).then((res) => {
									uni.showToast({
										title: '删除成功',
										duration: 500
									}).then(() => {
										setTimeout(() => {
											self.getFileList();
										}, 500);
									});
								});
							} else if (res.cancel) {
								console.log('用户点击取消');
							}
						}
					});
				}, 500);
			},
			//选择媒体类型弹窗
			chooseMediaType() {
				this.$emit('chooseMediaType', this.childTypeKeyword);
			},
			//添加文件
			addFile(zlx, mediaType) {
				if (zlx == this.childTypeKeyword && this.list.length >= 6) {
					uni.showToast({
						title: '最多只能上传6张照片',
						icon: 'none'
					});
					return;
				}
				let lenCount = 6;

				lenCount = 6 - this.list.length;

				let self = this;
				if (mediaType == 'image') {
					uni.chooseImage({
						sourceType: ['camera'],
						count: lenCount, //默认9
						sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
						success: function(res) {
							self.uploadFile(res, zlx, 'image');
						},
						fail: function(err) {
							console.log('errImage', err);
						}
					});
				} else if (mediaType == 'video') {
					uni.chooseVideo({
						sourceType: ['camera'],
						count: lenCount, //默认9
						sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
						success: function(res) {
							console.log('res', res);
							self.uploadFile(res, zlx, 'video')
						},
						fail: function(err) {
							console.log('errVideo', err);
						}
					})
				}
			},
			//上传
			uploadFile(res, zlx, mediaType) {
				let f = mediaType === 'image' ? res.tempFilePaths[0] : res.tempFilePath;
				let self = this;
				// console.log(self.info.BBID)
				uni.showLoading({
					title: '上传中'
				});
				uni.uploadFile({
					url: UPLOAD_URL,
					filePath: f,
					name: 'file',
					formData: {
						pageSize: 100000,
						pageNum: 1,
						YWSJID: self.uploadId,
						LXDM: 'ZDFJ',
						ZLXDM: zlx
					},
					timeout: 6000,
					success: function(res) {
						uni.showToast({
							title: '上传成功',
							icon: 'none'
						});
						console.log('上传成功');
						self.getFileList();
						uni.hideLoading();
					},
					fail: function(err) {
						console.log('上传失败');
						console.log('err===>', err);
						uni.showToast({
							title: '上传失败',
							icon: 'none'
						});
						uni.hideLoading();
					}
				});
			},
			getFileType(fileName) {
				let suffix = ''; // 后缀获取
				let result = ''; // 获取类型结果
				if (fileName) {
					const flieArr = fileName.split('.'); // 根据.分割数组
					suffix = flieArr[flieArr.length - 1]; // 取最后一个
				}
				if (!suffix) return false; // fileName无后缀返回false
				suffix = suffix.toLocaleLowerCase(); // 将后缀所有字母改为小写方便操作
				console.log('后缀', suffix);
				// 匹配图片
				const imgList = ['png', 'jpg', 'jpeg', 'bmp', 'gif']; // 图片格式
				result = imgList.find((item) => item === suffix);
				if (result) return 'image';
				// 匹配txt
				const txtList = ['txt'];
				result = txtList.find((item) => item === suffix);
				if (result) return 'txt';
				// 匹配excel
				const excelList = ['xls', 'xlsx'];
				result = excelList.find((item) => item === suffix);
				if (result) return 'excel';
				// 匹配word
				const wordList = ['doc', 'docx'];
				result = wordList.find((item) => item === suffix);
				if (result) return 'word';
				// 匹配pdf
				const pdfList = ['pdf'];
				result = pdfList.find((item) => item === suffix);
				if (result) return 'pdf';
				// 匹配ppt
				const pptList = ['ppt', 'pptx'];
				result = pptList.find((item) => item === suffix);
				if (result) return 'ppt';
				// 匹配zip
				const zipList = ['rar', 'zip', '7z'];
				result = zipList.find((item) => item === suffix);
				if (result) return 'zip';
				// 匹配视频
				const videoList = [
					'mp4',
					'm2v',
					'mkv',
					'rmvb',
					'wmv',
					'avi',
					'flv',
					'mov',
					'm4v'
				];
				result = videoList.find((item) => item === suffix);
				if (result) return 'video';
				// 匹配音频
				const radioList = ['mp3', 'wav', 'wmv'];
				result = radioList.find((item) => item === suffix);
				if (result) return 'radio';
				// 其他文件类型
				return 'other';
			},
			//获取视频路径
			getVideoUrl(item) {
				console.log('item', item)
				console.log(PREVIEW_FILE_URL + item.MLSY.slice(35));
				return PREVIEW_FILE_URL + item.MLSY.slice(35);
				//return "https://img.cdn.aliyun.dcloud.net.cn/guide/uniapp/%E7%AC%AC1%E8%AE%B2%EF%BC%88uni-app%E4%BA%A7%E5%93%81%E4%BB%8B%E7%BB%8D%EF%BC%89-%20DCloud%E5%AE%98%E6%96%B9%E8%A7%86%E9%A2%91%E6%95%99%E7%A8%8B@20200317.mp4"
			},
			//获取文件路径
			getFileUrl(item) {
				return DOWNLOAD_URLZDY + item.WJID;
			},
			//预览图片
			previewImage(fileList, item) {
				let fileUrls = fileList
					.filter(item => this.getFileType(item.WJMC) == 'image')
					.map(file => this.getFileUrl(file))
				// 预览图片
				const idx = fileUrls.findIndex(i => i === this.getFileUrl(item))
				uni.previewImage({
					current: idx,
					urls: fileUrls,
					loop: true,
				});
			},
			/**
			 * @method 预览视频
			 * @param {Object} item 当前视频
			 * @param {Object} index 当前点击索引
			 */
			previewVideo(item, index) {
				this.tempVideoUrl = this.getVideoUrl(item) || '';
				// 获取 video 上下文 videoContext 对象
				this.videoContext = uni.createVideoContext('video_play');
				// 进入全屏状态
				this.videoContext.requestFullScreen();
			}
		},
	}
</script>

<style lang="less" scoped>
	.ul-pic1 {
		flex-wrap: wrap;
		align-items: space-between;
		display: flex;
	}

	.ul-pic1 li {
		position: relative;
		width: 33.3%;
		margin-left: 0;
		margin-top: 0px;
		padding: 8rpx;
		border-top: 0;
	}

	.ul-pic1 li.imgli {
		padding: 0;
	}

	.ul-pic1 li .delImg {
		position: absolute;
		right: 0;
		top: 0;
		width: 48rpx;
		height: 48rpx;
	}

	.pd-pic {
		height: 360rpx;
	}

	.ul-pic1 li.imgli {
		background: none;
		border-top: none;
		width: 30%;
		margin: 6rpx;
	}

	.ul-pic1 li .innerbox {
		width: 100%;
		background-color: #f1f1f1;
		padding: 16rpx;
		height: 160rpx;
	}

	.ul-pic1 li.imgli .innerbox {
		padding: 0rpx;
		background: #fff;
	}

	.ul-pic1 li .img {
		width: 100%;
		height: 100%;
	}

	.addpic {
		width: 100%;
		height: 100%;
		margin: auto;
		display: block;
	}

	.mask1 {
		height: 100vh;
		background-color: #000;
		position: fixed;
		top: 0%;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: 999999 !important;

		.block {
			padding: 0 30rpx;
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 100%;

			video {
				width: 100%;
				height: 1200rpx;
			}
		}

		._root {
			display: block;
			width: 60rpx;
			height: 60rpx;
			position: absolute;
			left: 40rpx;
			top: 40rpx;
			overflow: hidden;

		}
	}
</style>
