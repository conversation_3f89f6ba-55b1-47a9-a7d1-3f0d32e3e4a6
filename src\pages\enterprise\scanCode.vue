<template>
	<scan @getCode="getScanCode" />
</template>

<script>
import IMEI_store from './enterprise.store.js';
export default {
	name:'ScanCode',
	data() {
		return {
			IMEI: IMEI_store.state
		};
	},
	onLoad() {},
	methods: {
		getScanCode(v) {
			if (v) {
				this.IMEI.IMEI = v.split(';')[0];
				uni.navigateBack({
					delta: 1
				});
			}
		}
	}
};
</script>

<style scoped>
	html { -webkit-tap-highlight-color: transparent; height: 100%; }
	body { -webkit-backface-visibility: hidden; height: 100%;}
</style>
