<!-- @format -->

<template>
    <view class="power-page">
        <!-- #ifdef H5 || APP-PLUS -->
        <view class="status-bar" />
        <slot name="bar">
            <NaviBar
                style="position: fixed"
                :title="title"
                :naviBack="naviBack"
            />
        </slot>
        <!-- #endif -->

        <view
            id="powerPageMain"
            class="power-page-main"
            ref="content"
            :style="contentStyle"
        >
            <slot />
        </view>
    </view>
</template>

<script>
import NaviBar from '@/pages/component/NaviBar.vue';

import styleUtil from '@/common/style.js';

export default {
    name: 'Page',
    components: {
        NaviBar
    },

    props: {
        title: {
            type: String,
            default: ''
        },

        naviBack: {
            type: Boolean,
            default: true
        },

        padding: {
            type: Boolean,
            default: true
        },

        mainStyle: {
            type: Object,
            default: () => {
                return {
                    'background-color': '#f4f4f4'
                };
            }
        }
    },

    computed: {
        contentStyle: function () {
            let style = {
                width: `calc(100% - ${this.padding === true ? '16px' : '0'})`,
                height: `calc(100% - ${this.padding === true ? '16px' : '0'})`,
                padding: this.padding === true ? '8px' : '0'
            };
            Object.assign(style, this.mainStyle);
            return styleUtil.styleObjectToString(style);
        }
    },

    mounted() {
        let _self = this;
        styleUtil.getNodeLayout(this, '#powerPageMain').then((layout) => {
            _self.$emit('layoutAware', layout);
        });
    }
};
</script>

<style scoped>
.status-bar {
    width: 100%;
    height: var(--status-bar-height);
}

.power-page {
    display: flex;
    flex-flow: column;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    height: 100%;
}

/* #ifdef H5 || APP-PLUS */
.power-page-main {
    flex: 1;
    width: 100%;
    height: calc(100% - 96rpx);
    margin-top: 96rpx;
    background-color: #f4f4f4;
}

/* #endif */

/* #ifdef MP */
.power-page-main {
    flex: 1;
    padding: '16rpx';
    width: 'calc(100% - 32rpx)';
    height: 100%;
    background-color: #f4f4f4;
}

/* #endif */
</style>
