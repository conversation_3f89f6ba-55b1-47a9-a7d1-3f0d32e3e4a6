<template>
	<div class="page-qiye">
		<header class="header">
			<i class="ic-back" @click="back"></i>
			<h1 class="title">{{ enterpriseInfo.WRYMC}}</h1>
		</header>
		<div class="qiye-tabs1">
			<span :class="{'cur':item.value === curType}" v-for="(item,index) in tabArr" :key="item.value"
				@click="changeTab(item)">
				{{item.name}}
				<image class="icon" mode="widthFix" v-if="index === 2 && enterpriseState === '2'"
					src="@/static/app/enterpriseDetail/images/qiye-lamp2.png" alt="">
			</span>
		</div>

		<!-- 监管信息 -->
		<RegulatoryInfo ref="regulatoryInfo" v-show="curType == 'regulatoryInfo'" :enterpriseData="enterpriseData"
			:enterpriseState="enterpriseState" :wrybh="wrybh">
		</RegulatoryInfo>

		<!-- 实时监控 -->
		<RealTimeMonitor ref="realTimeMonitor" v-show="curType == 'realTimeMonitor'" :enterpriseData="enterpriseData">
		</RealTimeMonitor>

		<!-- 预警信息 -->
		<EarlyWarningInfo ref="earlyWarningInfo" v-show="curType == 'earlyWarningInfo'" :enterpriseData="enterpriseData"
			:wrybh="wrybh"></EarlyWarningInfo>

	</div>
</template>

<script>
	import RegulatoryInfo from './RegulatoryInfo.vue';
	import RealTimeMonitor from './RealTimeMonitor.vue';
	import EarlyWarningInfo from './EarlyWarningInfo.vue';
	import {
		yjxxInfo,
		getEnterpirseInfo
	} from '@/api/iot/enterpeiseWarnInfo.js';
	// import {
	// 	getInfo
	// } from '@/api/iot/realtime.js';
	export default {
		components: {
			RegulatoryInfo,
			RealTimeMonitor,
			EarlyWarningInfo,
		},
		data() {
			return {
				tabArr: [{
						name: '监管信息',
						value: 'regulatoryInfo'
					},
					{
						name: '实时监控',
						value: 'realTimeMonitor'
					},
					{
						name: '预警信息',
						value: 'earlyWarningInfo'
					}
				],
				curType: 'regulatoryInfo',
				enterpriseData: {}, //企业信息
				enterpriseInfo: {}, //企业基本信息
				wrybh: '', //污染源编号
				xzqhdm: '',
				enterpriseState: '', //企业状态
			};
		},
		onLoad(option) {
			let userinfo = uni.getStorageSync('user_info');
			this.xzqhdm = userinfo.orgid;
			this.wrybh = option.WRYBH;
			this.initInfo();
			//5分钟更新一次状态
			// setInterval(() => {
			// 	this.initInfo();
			// }, 300000);
		},
		created() {},
		methods: {
			//tab切换
			changeTab(item) {
				this.curType = item.value;
				switch (item.value) {
					case 'regulatoryInfo':
						this.$refs.regulatoryInfo.getlineInfo();
						this.initInfo();
						break;
					case 'realTimeMonitor':
						uni.$emit('changeTab');
						break;
					case 'earlyWarningInfo':
						this.$refs.earlyWarningInfo.getWarningEventList();
						break;
					default:
						break;
				}
			},
			//获取企业信息
			initInfo() {
				getEnterpirseInfo({
					WRYBH: this.wrybh,
					XZQHDM: this.xzqhdm
				}).then(res => {
					console.log('EnterpirseInfo', res.data);
					this.enterpriseData = res.data;
					this.enterpriseInfo = res.data.qyjbxx;
					this.enterpriseState = res.data.qyzt;
				});
			},
			//获取企业状态
			// getEnterpriseState() {
			// 	getEnterpirseInfo({
			// 		WRYBH: this.wrybh,
			// 		XZQHDM: this.xzqhdm
			// 	}).then(res => {
			// 		this.enterpriseState = res.data.qyzt;
			// 		this.enterpriseInfo = res.data.qyjbxx;
			// 	});
			// },

			back() {
				uni.navigateBack({
					delta: 1
				});
			}
		}
	};
</script>

<style scoped>
	@import '@/static/app/enterpriseDetail/css/common.css';
	@import '@/static/app/enterpriseDetail/css/reset.css';
	@import '@/static/app/enterpriseDetail/css/zy08-20.css';

	html {
		-webkit-tap-highlight-color: transparent;
		height: 100%;
	}

	body {
		-webkit-backface-visibility: hidden;
		height: 100%;
	}

	.icon {
		width: 30rpx;
		top: -14rpx;
	}

	.title {
		text-align: center;
		font-size: 18px;
		color: #fff;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		line-height: 60px;
		padding-left: 30px;
	}

	.qiye-tabs2 {
		flex-wrap: wrap;
	}

	.page-qiye {
		height: 100%;

	}
</style>
