<template>
	<div style="background-color: #f1f1f1;height:100vh;">
		<header class="header blue2" id="header">
			<h1 class="title">报备管理</h1>
		</header>
		<div class="navtop" id="header2">
			<div class="yy-line1" style="border-bottom: none;">
				<input type="text" class="inpsear1" v-model.trim="keyword" placeholder="输入关键字查询" @confirm="getList">
				<div class="addic" @click="addFiling">
					<image src="../../static/app/images/addbtn.png" class="pic">
				</div>
			</div>
			<ul class="navtab1">
				<li :class="{on:curTab == 'DSH'}" @click="changeTab('DSH')">待审核({{notReviewList.length || '0'}})</li>
				<li :class="{on:curTab == 'YSH'}" @click="changeTab('YSH')">已审核({{reviewedList.length || '0'}})</li>
			</ul>
		</div>
		<section class="main">
			<div class="inner bbgl-inner">
				<div class="nodatabox" v-if="!list.length">
					<div>
						<image mode="heightFix" src="@/static/app/workbench/images/nodata.png" alt=""
							class="nodatatu">
							<p>暂无报备记录</p>
					</div>
				</div>
                <div v-else>
                    <div class="gap"></div>
                    <div v-for="(item,index) in list" :key="index" v-if="list.length" @click="toDetail(item)">
                        <div class="zd-mod">
                            <div class="zd-hd flx1 ac jb" >
                                <h1 class="t1" :class="{
                                    't1-width':item.SHZT == '1'
                                }">{{item.WRYMC || '-'}}</h1>
                                <!-- 0-未提交、1-已提交待审核，2-审核不通过、3-审核通过 -->
                                <span class="passbtn unpass" v-if="item.SHZT == 2">未通过</span>
                                <span class="passbtn" v-if="item.SHZT == 3">通过</span>
                                <!-- <span class="duebtn" v-if="item.SHZT == 1">处理</span> -->
                            </div>
                            <div class="zd-bd">
                                <div class="gap"></div>
                                <table class="zy-table1">
                                    <colgroup>
                                        <col width="30%">
                                        <col width="">
                                        <col>
                                    </colgroup>
                                    <tbody>
                                        <tr>
                                            <td class="color6">报备时间：</td>
                                            <td>{{item.BBSJ || '-'}}</td>
                                        </tr>
                                        <tr>

                                            <td class="color6">报备原因：</td>
                                            <td>{{item.BBYY || '-'}}</td>
                                        </tr>
                                        <tr v-if="item.SHZT != 1">
                                            <td class="color6">报备期限：</td>
                                            <!-- <td>{{item.KSSJ + '~' + item.JZSJ || '-'}} -->
                                            <td>{{getReportTime(item)}}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div class="gap"></div>
                            </div>
                        </div>
                        <div class="gap"></div>
                    </div>
                    <div class="gap"></div>
                    <div class="gap"></div>
                </div>
			</div>
		</section>
	</div>
</template>

<script>
	import {
		getQybbList
	} from '@/api/iot/filing.js';
	export default {
		name: '',

		computed: {
			// 报备时间计算属性
			getReportTime() {
				return item => {
					if(!item.JZSJ || item.JZSJ == null || item.JZSJ=='null') item.JZSJ ='永久'
					return item.KSSJ + '~' + item.JZSJ;
				};
			}
		},
		data() {
			return {
				userinfo: {},
				curTab: 'DSH',
				tab: 'BB',

				list: [],
				keyword: '',
				notReviewList: [],
				reviewedList: [],
                // mainHeight:0
			};
		},
        onShow(){
     
	     let userinfo = uni.getStorageSync('user_info');
			this.userinfo = userinfo;
			this.getList()

        },
		mounted() {

		},
		methods: {
			addFiling() {
				this.moveTo('/pages/filing/FilingDetail?type=add')
			},
			toDetail(item) {
				this.moveTo('/pages/filing/FilingDetail?type=detail&reviewState=' + this.curTab + '&info=' +
					encodeURIComponent(JSON.stringify(item)))
			},
			async getList() {
				let params = {
					ALLSEARCH: this.keyword
				}
				const {
					data
				} = await getQybbList(params)
				data.list.forEach(item => {
					item.CJSJ = item.CJSJ && item.CJSJ.slice(0, 16)
					item.JZSJ = item.JZSJ && item.JZSJ.slice(0, 16)
					item.KSSJ = item.KSSJ && item.KSSJ.slice(0, 16)
					item.SHSJ = item.SHSJ && item.SHSJ.slice(0, 16)
					item.BBSJ = item.BBSJ && item.BBSJ.slice(0, 16)
				})
				// console.log(data.list)
				//0-未提交、1-已提交待审核，2-审核不通过、3-审核通过
				this.notReviewList = data.list.filter(item => item.SHZT == 1)
				this.reviewedList = data.list.filter(item => item.SHZT == 2 || item.SHZT == 3)
				if (this.curTab == 'DSH') {
					this.list = this.notReviewList
				} else if (this.curTab == 'YSH') {
					this.list = this.reviewedList
				}
			},
			changeTab(v) {
				this.curTab = v
				this.list = []
				switch (this.curTab) {
					case 'DSH':
						this.list = this.notReviewList
						break;
					case 'YSH':
						this.list = this.reviewedList
						break;
				}
			},
			moveTo(url) {
				uni.navigateTo({
					url: url
				});
			},

		}
	};
</script>


<style scoped>


	.zd-hd .t1 {
		padding: 36rpx 0rpx 30rpx 50rpx;
		font-size: 34rpx;
		color: #333;
		font-weight: bold;
		line-height: 48rpx;
		height: auto;
		width: 450rpx;
		overflow: hidden;
		/*隐藏*/
		white-space: nowrap;
		/*不换行*/
		text-overflow: ellipsis;
		/* 超出部分省略号 */
	}

	.zd-hd .t1-width {
		width: auto
	}

	.nodatabox {
		height: 100%;
		background: #fff;
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 0;
	}

	.addic {
		background: #fff;
		border-radius: 12px;
	}

	.main {
		padding-top: 304rpx;
		overflow: hidden;
	}

	.inner {
		height:100%;
		padding: 0;
		overflow: scroll;
	}

	.pic {
		width: 100%;
		height: 100%;
	}

	.zy-table1 td {
		text-align: left;
		word-break: break-all;
	}

	.zd-hd .duebtn {
		color: #2b6cf9;
		background-color: #d0e8fb;
		width: 52px;
		height: 24px;
		line-height: 24px;
		text-align: center;
		font-size: 14px;
		display: inline-block;
	}
    .navtop {
        position: absolute;
        top:79.7101rpx;
        left: 0;
        right: 0;
        z-index: 999;
    }
    ::v-deep .uni-input-placeholder,::v-deep .uni-textarea-placeholder {
       color: #fffbfb;
    }
</style>
