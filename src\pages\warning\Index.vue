<!-- @format -->

<template>
	<!-- 预警列表数据 -->
	<body class="pd-pg1a" style="background: #f1f1f1; display: flex; flex-direction: column;height:100%;">
		<header class="header">
			<div class="pd-row aic">
				<input type="text" class="pd-inpsrh" placeholder="输入关键字查询" @confirm="search" v-model="serchtext" />
			</div>
		</header>
		<ul class="time-tab">
			<li :class="currentTab == 'TODAY' ? 'on' : ''" @click="change('TODAY')">
				今天
			</li>
			<li :class="currentTab == 'YESTERDAY' ? 'on' : ''" @click="change('YESTERDAY')">
				昨天
			</li>
			<li :class="currentTab == 'WEEK' ? 'on' : ''" @click="change('WEEK')">
				近一周
			</li>
			<li :class="currentTab == 'MONTH' ? 'on' : ''" @click="change('MONTH')">
				近一月
			</li>
			<li :class="currentTab == 'CUSTOM' ? 'on' : ''" @click="change('CUSTOM')">
				自定义
			</li>
		</ul>
		<section class="main" style="flex: 1; position: relative">
			<div class="inner" style="
                    padding-left: 0;
                    padding-right: 0;
                    display: flex;
                    flex-direction: column;
                ">
				<div class="pd-txt4">共{{ total || 0}}条记录,其中正在发生的预警共{{warnCount || 0}}条</div>
				<scroll-view v-if="!nodata" class="scroll-region" scroll-y="true" @scrolltolower="getMore">
					<div v-for="item in list" :key="item.YJID">
						<div class="yj-mod">
							<div class="hd ac jb">
								<div class="lf flx1 ac">
									<!-- 非拆除预警 -->
									<div class="yjic-btn" v-if="item.YJLX!== 'CC' && item.YJZT == 1">
										<image src="@/static/app/images/yjic.png" alt="" class="yjic">
											<p>预警中</p>
									</div>
									<!-- 非拆除预警 -->
									<div class="yjic-btn" style="background-color: #2b6cf9;"
										v-if="item.YJLX!== 'CC' && item.YJZT == 0">
										<image src="@/static/app/images/yjic.png" alt="" class="yjic">
											<p>预警解除</p>
									</div>
									<!-- 拆除预警 -->
									<div class="yjic-btn yjic-btn-cc" v-if="item.YJLX == 'CC'">
										<image src="@/static/app/images/yjic.png" alt="" class="yjic1">
									</div>
									<h1 class="til" @click="toEnterprise(item)">{{item.WRYMC || "-"}}</h1>
								</div>
								<span class="rtarr"></span>
							</div>
							<div class="bd">
								<div class="gap"></div>
								<ul class="yj-info">
									<li>
										<p class="p1">预警类型：</p>
										<p class="p2">{{getContent(item.YJLX)}}</p>
									</li>
									<li>
										<p class="p1">发生时间：</p>
										<p class="p2">{{item.FSSJ&&item.FSSJ.slice(0,16) || '-'}}</p>
									</li>
									<li>
										<p class="p1">预警时间：</p>
										<p class="p2">
											{{item.YJSJ&&item.YJSJ.slice(0,16) || '-'}}（第{{item.YJCS || '-'}}次）
										</p>
									</li>
									<li v-if="item.YJLX!== 'CC'">
										<p class="p1">结束时间：</p>
										<p class="p2">
											<span v-if="item.YJZT == 1" style="color:#ff4848;">预警中</span>
											<span
												v-if="item.YJZT == 0">{{item.JCSJ&&item.JCSJ.slice(0,16) || '-'}}</span>
										</p>
									</li>
									<li v-if="item.YJLX!== 'CC'">
										<p class="p1">持续时间：</p>
										<p class="p2">{{item.CXSJ_CH || '-'}}</p>
									</li>
								</ul>
								<ul class="yjcxbox" @click="toWarningDetail(item)">
									<li class="f1 chanxian-1">
										<em class="item-1">预警产线：</em>
										<!-- 治污预警 ZW -->
										<p class="item-2" v-if="item.YJLX == 'ZW'">{{item.SCXMC}} → {{item.ZWXMC}}</p>
										<!-- 停产预警 TC-->
										<p class="item-2" v-if="item.YJLX == 'TC'">{{item.SCXMC}}</p>
										<!-- 拆除预警 CC、故障预警 GZ -->
										<p class="item-2" v-if="item.YJLX == 'CC' || item.YJLX == 'GZ'">
											{{getWarnEquip(item.SCXMC,item.ZWXMC)}}
										</p>
									</li>
									<li class="f1 chanxian" v-if="item.YJLX!== 'CC'">
										<em class="item-1">生产设备：</em>
										<p class="item-2">【{{ item.SCSBMC || '-' }}】</p>
										<image class="yxic" src="@/static/app/images/now-yx.png" alt="">
									</li>
									<li class="f1 chanxian" v-if="item.YJLX!== 'TC'">
										<em class="item-1">治污设备：</em>
										<p class="item-2">【{{ item.ZWSBMC || '-'}}】</p>
										<image src="@/static/app/images/stop-yx.png" alt="" class="yxic">
									</li>
									<!-- 拆除预警 CC -->
									<li class="f1 chanxian-1" v-if="item.YJLX == 'CC'">
										<em class="item-1">预警设备：</em>
										<p class="item-">{{getWarnEquip(item.SCSBMC,item.ZWSBMC)}}</p>
									</li>
									<li class="f1 chanxian-1">
										<em class="item-1">预警结论：</em>
										<p class="item-2">{{item.YJNR_JY || '-'}}</p>
									</li>
								</ul>
								<div class="gap"></div>
							</div>


					</div>
                    </div>
						<div class="gap"></div>
						<div class="gap"></div>
						<div class="gap"></div>

				</scroll-view>
				<u-empty mode="data" v-else text="暂无预警记录"></u-empty>
			</div>
			<div class="inner" v-if="showDatePicker" style="
                    padding-left: 0;
                    padding-right: 0;
                    position: absolute;
                    left: 0;
                    top: 0;
                    right: 0;
					background: #f1f1f1;
                ">
				<div class="gap"></div>
				<div class="gap"></div>
				<ul class="pd-ultbs2">
					<li :class="{ on: timeType == 'Date' }" @click="changeTimeType">
						按日选择
					</li>
					<li :class="{ on: timeType == 'Month' }" @click="changeTimeType">
						按月选择
					</li>
				</ul>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="pd-dateopt1" v-if="timeType == 'Date'">
					<text class="time" :class="{ on: timeStart }" @click="switchTime">{{ startT }}</text>
					<i>至</i>
					<text class="time" :class="{ on: !timeStart }" placeholder="结束时间"
						@click="switchTime">{{ endT ? endT : '结束时间' }}</text>
				</div>
				<div class="pd-dateopt1" v-if="timeType == 'Month'">
					<input type="text" disabled="true" :value="month" />
				</div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
				<simple-date-picker key="start" v-if="timeType == 'Date' && timeStart" :date="startT" ref="picker"
					:type="timeType" @change="onStartChange">
				</simple-date-picker>

				<simple-date-picker key="end" v-if="timeType == 'Date' && !timeStart" :date="endT"
					:startDate="startDate" :type="timeType" @change="onEndChange">
				</simple-date-picker>

				<simple-date-picker key="month" v-if="timeType == 'Month'" :date="month" type="MONTH"
					@change="onMonthChange">
				</simple-date-picker>

				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
				<div class="gap"></div>
			</div>
		</section>
		<footer class="footer footer-zdy" v-if="showDatePicker">
			<div class="gap"></div>
			<div class="gap"></div>
			<ul class="pd-ulbtn1 ots">
				<li class="on" @click="cancel"><i>取消</i></li>
                <li class="cur"><i @click="confirm">确定</i></li>
			</ul>
			<div class="gap"></div>
			<div class="gap"></div>
		</footer>
		<filterBox :showFilterBox="showFilterBox" @getFilterParams="getFilterParams"></filterBox>

	</body>
</template>

<script>
	import {
		getXzqh
	} from '@/api/iot/xzqh.js';
	import {
		getList,
		getWarnList
	} from '@/api/iot/warning.js';
	import {
		getYjjl,
		getCode
	} from '@/api/iot/realtime.js';
	import filterBox from './filter-box.vue';

	export default {
		components: {
			filterBox,
		},
		data() {
			return {
				warningType: [],
				serchtext: '',
				list: [],
				zwycNum: 0,
				currentTab: 'TODAY',
				timer: null,
				pageSize: 20,
				pagNum: 1,
                isLastPage:false,
				showFilterBox: false,
				xzqhdm: '',
				fdm: '',
				total: '',
				xzqhList: [],
				userinfo: {},
				nodata: false,
				request: null,
				currentUrl: 'qd',
				showDatePicker: false,
				timeType: 'Date',
				timeStart: true,
				month: '',
				startT: '',
				endT: '',
				ZT: '',
				timeFormat: 'YYYY-MM-DD',
				startDate: '',
				userinfo: null,
				tab: 'YJ',
				warnCount: 0,
			};
		},
		watch: {
			timeStart(newValue) {}
		},
		onShow() {

		},
		mounted() {
            	this.userinfo = uni.getStorageSync('user_info');
			if (this.userinfo && this.userinfo.orgid.length > 6) {
				//区县级
				this.xzqhdm = this.userinfo.orgid;
			} else if (this.userinfo && this.userinfo.orgid.length == 6) {
				//区县级
				this.xzqhdm = this.userinfo.orgid;
				this.fdm = this.userinfo.orgid.substr(0, 4);
			} else {
				//市级
				this.fdm = this.userinfo.orgid || '';
			}
			this.getCode();
			this.initTime();
			// this.userinfo = uni.getStorageSync('user_info');
			// if (this.userinfo && this.userinfo.orgid.length > 6) {
			// 	//区县级
			// 	this.xzqhdm = this.userinfo.orgid;
			// } else if (this.userinfo && this.userinfo.orgid.length == 6) {
			// 	//区县级
			// 	this.xzqhdm = this.userinfo.orgid;
			// 	this.fdm = this.userinfo.orgid.substr(0, 4);
			// } else {
			// 	//市级
			// 	this.fdm = this.userinfo.orgid || '';
			// }
			// this.getCode();
			// this.initTime();
			// this.initList();
			// this.initRegion();
		},

		methods: {
			//拼接生产设备治污设备
			getWarnEquip(productStr, controlPolluStr) {
				let str = ''
				if (productStr) {
					if (controlPolluStr) {
						str = productStr + ',' + controlPolluStr
					} else {
						str = productStr
					}
				} else {
					if (controlPolluStr) {
						str = controlPolluStr
					} else {
						str = ''
					}

				}
				return str
			},
			//获取
			getLine(SCXMC, ZWXMC) {
				let str = '';
				let first = '';
				if (SCXMC) {
					first = SCXMC + ',';
				} else {
					first = '';
				}
				return str = first + ZWXMC;
			},
			// tab切换
			change(v) {
				this.currentTab = v;
				if (this.currentTab == 'CUSTOM') {
					this.showDatePicker = true;
					// this.$refs.picker.renderData(this.timeStart ? this.startT : this.endT);
					return;
				} else {
					this.showDatePicker = false;
				}
				//重置列表数据、数量
				this.reset();
				this.initTime();
				this.initList();
			},


			// 搜索
			search() {
				uni.hideKeyboard();
				if (this.timer) {
					clearTimeout(this.timer);
				}
				this.timer = setTimeout(() => {
					this.reset();
					this.initList();
				}, 500);
			},
			formatContent(row) {
				let content = row.YJNR.replace(`【${row.WRYMC}】`, '');
				return content;
			},
			formatTime(row) {
				let time = parseInt(row.CXSJ);
				let hours = Math.floor(time / 60);
				let minutes = time % 60;
				return `${hours}小时${minutes}分钟`;
			},
			cancel() {
				this.showDatePicker = false;
			},
			confirm() {
				if (this.timeType == 'Month') {
					this.startT = this.month + '-01';
					this.endT = this.$dayjs(this.startT)
						.endOf('month')
						.format('YYYY-MM-DD');
				}

				if (!this.dateCompare(this.startT, this.endT)) {
					uni.showToast({
						title: '结束时间不能早于开始时间',
						icon: 'none'
					});
					return;
				}
				this.showDatePicker = false;
				this.initList();
			},
			/**
			 * 比较日期大小
			 */
			dateCompare(startDate, endDate) {
				// // 计算截止时间
				startDate = new Date(startDate.replace('-', '/').replace('-', '/'));
				// 计算详细项的截止时间
				endDate = new Date(endDate.replace('-', '/').replace('-', '/'));
				if (startDate <= endDate) {
					return true;
				} else {
					return false;
				}
			},
			// toBottom: function(e) {
			// 	// console.log('到达底部');
			// 	this.getMore();
			// },
			getMore() {
                if(this.isLastPage){
	                uni.showToast({
						title: '没有更多了',
						icon: 'none'
					});
					return;
                }else{
                    this.pagNum++;
                    this.initList();
                }
			},
			initTime() {
				switch (this.currentTab) {
					case 'TODAY':
						this.startT = this.$dayjs().format('YYYY-MM-DD');
						this.endT = this.$dayjs().format('YYYY-MM-DD');
						break;
					case 'YESTERDAY':
						this.startT = this.$dayjs()
							.subtract(1, 'day')
							.format('YYYY-MM-DD');
						this.endT = this.$dayjs()
							.subtract(1, 'day')
							.format('YYYY-MM-DD');
						break;
					case 'WEEK':
						this.startT = this.$dayjs()
							.subtract(7, 'day')
							.format('YYYY-MM-DD');
						this.endT = this.$dayjs().format('YYYY-MM-DD');
						break;
					case 'MONTH':
						this.startT = this.$dayjs()
							.subtract(1, 'month')
							.format('YYYY-MM-DD');
						this.endT = this.$dayjs().format('YYYY-MM-DD');
						break;
					default:
						break;
				}
			},
			// 初始化列表数据
			async initList(v) {
				if (v) {
					this.ZT = v;
				}
				// 列表数据
				let xzqhdm = this.xzqhdm;
				if (!xzqhdm) {
					xzqhdm = this.fdm;
				}
				if (this.request && this.request.cancel && this.pagNum == 1) {
					this.request.cancel();
				}

				let self = this;
				let length = 0;
				let res = await getWarnList({
					WRYMC: this.serchtext,
					SJQX: this.userinfo.sjqx,
					startT: this.startT + ' 00:00:00',
					endT: this.endT + ' 23:59:59',
					pageSize: this.pageSize,
					pageNum: this.pagNum,
					XZQHDM: '',
					YJLX: this.ZT,
					BMDH: this.userinfo.orgid
				})
				self.request = null;
				if (self.pagNum > 1) {
					self.list = self.list.concat(res.data.list);
				} else {
					self.list = res.data.list;
					self.nodata = false;
					if (!res.data.list || !res.data.list.length) {
						self.nodata = true;
					}
					//正在发生的预警
					let warnList = self.list.filter(v => v.YJZT == 1);
					self.warnCount = warnList.length;
				}
                self.total = res?.data?.total || 0;
                self.isLastPage = res?.data?.isLastPage || false;
				length = this.list.length;
				return Promise.resolve(length)
			},
			getCode() {
				getCode({
					code: 'YJGZ_YJLX'
				}).then(async (res) => {
					this.warningType = res.data;
					this.ZT = this.warningType
						.map((item) => {
							return item.value;
						})
						.join(',');

					let tabArr = ['TODAY', 'YESTERDAY', 'WEEK', 'MONTH']
					for (var i = 0; i < tabArr.length; i++) {
						this.currentTab = tabArr[i];
						this.initTime();
						let length = await this.initList(this.ZT);
						if (length != 0) {
							return
						}
					}

				});
			},
			switchTime() {
				this.timeStart = !this.timeStart;
				this.startDate = this.timeStart ? '' : this.startT;
				// this.$refs.picker.renderData(this.timeStart ? this.startT : this.endT);
			},
			onDateChange(obj) {
				let date = this.$dayjs(obj).format(this.timeFormat);

				if (this.timeType == 'Date') {
					if (this.timeStart) {
						this.startT = date;
					} else {
						this.endT = date;
					}
				} else {
					this.month = date;
					this.startT = this.month + '-01';
					this.endT = this.$dayjs(obj)
						.endOf('month')
						.format('YYYY-MM-DD');
				}
			},
			onStartChange(obj) {
				let date = this.$dayjs(obj).format(this.timeFormat);
				this.startT = date;
			},
			onMonthChange(obj) {
				let date = this.$dayjs(obj).format(this.timeFormat);
				this.month = date;
				// this.startT = this.month + '-01';
				// this.endT = this.$dayjs(obj).endOf('month').format('YYYY-MM-DD');
			},
			onEndChange(obj) {
				let date = this.$dayjs(obj).format(this.timeFormat);
				this.endT = date;
			},
			changeTimeType() {
				let timeType = this.timeType;
				this.timeType = timeType == 'Date' ? 'Month' : 'Date';
				this.timeFormat =
					this.timeType == 'Date' ? 'YYYY-MM-DD' : 'YYYY-MM';

				// if (this.timeType == 'Month') {
				// 	this.startDate = ""
				// 	this.onDateChange(this.$dayjs(this.month || new Date()))
				// } else {
				// 	if (timeType == 'Month') {
				// 		this.$refs.picker.renderData(this.timeStart ? this.startT : this.endT);
				// 	}
				// }
			},
			getFilterParams(params) {
				this.reset();
				this.xzqhdm = params.XZQHDM;
				this.fdm = params.FDM;
				this.initList(params.ZT);
			},
			getFilePath(v) {
				return v == '0' ?
					require('../../static/app/images/mk3a.png') :
					v == '1' ?
					require('../../static/app/images/mk1a.png') :
					v == '2' ?
					require('../../static/app/images/mk2a.png') :
					require('../../static/app/images/tingzhi.png');
			},
			// 到企业详情页
			toEnterprise(options) {
				uni.navigateTo({
					url: '/pages/enterpriseWarnInfo/detail/Layout?WRYBH=' + options.WRYBH
				});
			},
			toWarningDetail(options) {
				if (options.YJLX != 'CC') {
					uni.navigateTo({
						url: `/pages/warningRecord/Layout?YJID=${options.YJID}`
					});
				}
			},

			getContent(yjlx) {
				for (let i = 0; i < this.warningType.length; i++) {
					let item = this.warningType[i];
					if (item.value == yjlx) {
						return item.label;
					}
				}
				return '';
			},
			toMine() {
				uni.navigateTo({
					url: '../mine/Index'
				});
			},
			reset() {
				this.pagNum = 1;
				this.list = [];
				this.total = 0;
                this.isLastPage = false;
			},
			//初始化行政区域列表
			initRegion() {
				this.xzqhList = [];
				//省级用户查询地市
				if (this.xzqhdm.length == 2) {
					getXzqh({
						FDM: this.xzqhdm
					}).then((res) => {
						this.xzqhList = res.data;
					});
				}
				//市级用户查询区县
				//区县级用户查地市,查找父级市
				if (this.xzqhdm.length == 4 || this.xzqhdm.length == 6) {
					getXzqh({
						FDM: this.xzqhdm.substr(0, 2) + '0000'
					}).then((res) => {
						let data = res.data;
						data.forEach((item) => {
							if (
								item.XZQHDM.substr(0, 4) == this.xzqhdm.substr(0, 4)
							) {
								this.xzqhList.push(item);
								return;
							}
						});
					});
				}
			},

			// 时间转换
			changeTime(v) {
				let remainder = Number(v) % 60;
				let value = Math.floor(Number(v) / 60);

				return value > 0 ?
					value + '小时' + remainder + '分钟' :
					remainder + '分钟';
			}
		}
	};
</script>

<style scoped lang="less">
.pd-pg1a .header {
    height: 110.90825rpx;
    padding: 10.289875rpx 28.985475rpx 0;
    box-sizing: border-box;
}
	html {
		-webkit-tap-highlight-color: transparent;
		height: 100%;
	}

	body {
		-webkit-backface-visibility: hidden;
		height: 100%;
	}

	.main {
		// height: calc(100% - 80rpx);
		overflow-y: hidden;
	}

	.header {
		position: initial;
	}

	.header.pd-hd1 {
		padding: 28.985475rpx;
	}

	.pd-filter {
		width: 38.64735rpx;
		height: 41.6667rpx;
		margin-left: 12rpx;
	}

	.pd-ultbs1 {
		position: initial;
		z-index: 666;
	}

	.pd-pg1a .inner {
		padding-top: 0;
	}

	.inner.pd-con1 {
		padding-top: 270rpx;
	}

	.scroll-region {
		height: 100%;
	}

	.yj-mod {
		border-radius: 0;
	}

	.tab {
		width: 100%;
		display: flex;
		justify-content: space-between;
	}

	.on {
		color: red;
	}

	.pd-dateopt1 .time {
		border-bottom: 1px solid #ddd;
		padding-bottom: 12px;
	}

	.pd-dateopt1 .time.on {
		border-bottom: 1px solid #4874ff;
		color: #4874ff;
	}

	.pd-dlbx1 {
		margin-bottom: 16rpx;
	}

	.pd-ultxt2 {
		font-size: 26rpx;

		li {
			border-bottom: 1px solid #eee;
			height: 70rpx;
			align-items: center;
			font-size: 26rpx;

			span {
				min-width: 130rpx;
				text-align: right;
			}
		}
	}

	.footer-zdy {
		z-index: 999999;
	}

	.time-tab {
		display: flex;
		justify-content: space-evenly;
		flex-direction: row;
		padding: 20rpx 0;
		font-size: 30rpx;
		background: #fff;
	}

	.time-tab li {
		background-color: #dae3ff;
		color: #4b76ff;
		border-radius: 36rpx;
		padding: 0 24rpx;
		line-height: 56rpx;
	}

	.time-tab li.on {
		background-color: #4874ff;
		color: #fff;

	}

	.yjcxbox {
		margin-top: 10rpx;
	}

	.yjcxbox .chanxian {
		display: flex;
	}

	.yjcxbox .chanxian .item-1 {
		width: 150rpx
	}

	.yjcxbox .chanxian .item-2 {
		width: calc(100% - 150rpx);
		padding-right: 140rpx;
		text-indent: -10rpx;
	}

	.yjcxbox .chanxian-1 .item-1 {
		width: 150rpx;
	}

	.yjcxbox .chanxian-1 .item-2 {
		width: calc(100% - 150rpx);
	}

	.flx1 {
		width: 96%;
	}

	.rtarr {
		width: 4%;
	}

	.yjic-btn-cc {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.yjic1 {
		width: 15px;
		height: 18px;
	}

	.pd-ultbs2 {
		display: flex;
		justify-content: center;
	}

	.pd-ultbs2 li {
		padding: 0 24.1545749rpx;
		line-height: 60.386475rpx;
		border-radius: 9.057975rpx;
		font-size: 27.77775rpx;
		color: #333;
	}

	.pd-ultbs2 li.on {
		background: #4874ff;
		color: #fff;
	}

	.pd-ultbs2 li+li {
		margin-left: 24.1545749rpx;
	}

	.pd-dateopt1 {
		display: flex;
		justify-content: center;
	}

	.pd-dateopt1 i {
		font-size: 33.81645rpx;
		color: #3d3d3d;
		padding: 0 60.386475rpx;
	}

	.pd-dateopt1 input {
		padding-bottom: 24.1545749rpx;
		font-size: 33.81645rpx;
		color: #3d3d3d;
		border-bottom: 1px solid #ddd;
		background-color: transparent;
		width: 25%;
		text-align: center;
	}

	.pd-dateopt1 input::-webkit-input-placeholder {
		color: #aaa;
	}

	.pd-dateopt1 input:focus {
		color: #4874ff;
		border-bottom-color: #4874ff;
	}

	.pd-ulbtn1 {
		display: flex;
		justify-content: space-around;
		padding: 0 40rpx;

	}

	.pd-ulbtn1 li {
		width: 270rpx;
	}

	.pd-ulbtn1.ots li i {
		line-height: 74.502425rpx;
	}

	.pd-ulbtn1.ots li.cur i {
		border-color: #4874ff;
		background: #4874ff;
		color: #fff;
	}

	.pd-ulbtn1 li i {
		display: block;
		line-height: 60.386475rpx;
		border-radius: 300px;
		font-size: 27.77775rpx;
		color: #666;
		text-align: center;
		background: #f1f1f1;
		border: 1px solid transparent;
	}

	.pd-ulbtn1 li.on i {
		border-color: #4874ff;
		background: transparent;
		color: #4874ff;
	}

	.footer {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 9999;
		background: #fff;
		box-shadow: 0 -1px 5px 0 rgb(0 0 0 / 3%);
	}

	.pd-txt4 {
		background-color: #dae3ff;
		color: #4b76ff;
		padding: 6rpx 10rpx;
		text-align: center;
	}
</style>
