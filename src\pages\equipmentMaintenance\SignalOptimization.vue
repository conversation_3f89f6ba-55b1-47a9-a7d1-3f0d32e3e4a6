<!-- @format -->
<template>
    <!-- 信号优化 -->
    <section class="main" style="padding-bottom: 0; margin-bottom: 30.1932rpx">
        <div class="inner">
            <div class="gap"></div>
            <div class="zy-form">
                <!-- 设备基本信息 -->
                <GetIMEIMsg
                    @updateInfo="updateInfo"
                    ref="refGetIMEIMsg"
                    :options="options"
                ></GetIMEIMsg>
                <div class="item">
                    <p class="label">优化时间</p>
                    <div class="rp" :class="{ 'detail-type': isDisabled }">
                        <p-mui-date-picker
                            v-show="!isDisabled"
                            :disabled="isDisabled"
                            @confirm="confirmTime"
                            dateType="SECOND"
                            format="YYYY-MM-DD HH:mm:ss"
                        >
                            <span class="date-ipt res">
                                {{ info.ywmx.yhsj }}
                            </span>
                        </p-mui-date-picker>
                        <span class="date-ipt" v-show="isDisabled">
                            {{ info.ywmx.yhsj }}
                        </span>
                    </div>
                </div>
                <div class="item">
                    <p class="label">原err占比</p>
                    <div class="rp">
                        <input
                            type="text"
                            disabled
                            v-model="info.ywmx.yerrzb"
                            class="zy-input1"
                            placeholder="扫码后获取原err占比"
                        />
                    </div>
                </div>
                <div class="item">
                    <p class="label">现err占比</p>
                    <div class="rp">
                        <input
                            type="text"
                            disabled
                            v-model="info.ywmx.xerrzb"
                            class="zy-input1"
                            placeholder="扫码后获取现err占比"
                        />
                    </div>
                </div>
                <div class="item">
                    <p class="label">原信号功率</p>
                    <div class="rp">
                        <input
                            type="text"
                            disabled
                            v-model="info.ywmx.yxhgl"
                            class="zy-input1"
                            placeholder="扫码后获取原信号功率"
                        />
                    </div>
                </div>
                <div class="item">
                    <p class="label">现信号功率</p>
                    <div class="rp">
                        <input
                            type="text"
                            disabled
                            v-model="info.ywmx.xxhgl"
                            class="zy-input1"
                            placeholder="扫码后获取现信号功率"
                        />
                    </div>
                </div>
                <div class="item">
                    <p class="label">原小区基站</p>
                    <div class="rp">
                        <input
                            type="text"
                            disabled
                            v-model="info.ywmx.yxqjz"
                            class="zy-input1"
                            placeholder="扫码后获取原小区基站"
                        />
                    </div>
                </div>
                <div class="item">
                    <p class="label">现小区基站</p>
                    <div class="rp">
                        <input
                            type="text"
                            disabled
                            v-model="info.ywmx.xxqjz"
                            class="zy-input1"
                            placeholder="扫码后获取现小区基站"
                        />
                    </div>
                </div>
            </div>
            <UploadImageList
                ref="refUploadImageList"
                :options="options"
                fileTypeKeyword="YWMX"
                childTypeKeyword="XHYH"
                :uploadId="info.mxid"
                :isDisabled="isDisabled"   
                title="现场拍照"
            >
                <ul class="pd-ulpic1 sample">
                    <li>
                        <image
                            mode="aspectFit"
                            src="@/static/equipmentMaintenance/images/signal1.jpg"
                            alt=""
                        />
                        <p>移动运维照</p>
                    </li>
                    <li>
                        <image
                            mode="aspectFit"
                            src="@/static/equipmentMaintenance/images/signal2.jpg"
                            alt=""
                        />
                        <p>现场运维照</p>
                    </li>
                </ul>
                <div class="gap"></div>
                <div class="gap"></div>
                <dl class="pd-dltxt1">
                    <dt>说明：</dt>
                    <dd>
                        <p>1、拍下移动技术人员现场运维照片；</p>
                    </dd>
                    <dd>
                        <p>2、拍下运维人员现场运维照片。</p>
                    </dd>
                </dl>
            </UploadImageList>
            <!-- 采集结果 -->
            <GetLastedResult
                :imei="info.imei"
                ref="refGetLastedResult"
            ></GetLastedResult>

            <div class="gap"></div>
            <div class="gap"></div>
            <div class="zy-bot-btn1" v-show="!isDisabled" @click="save">
                保存
            </div>
            <div class="gap"></div>
            <div class="gap"></div>
        </div>
    </section>
</template>

<script>
import { maintenanceDetail } from '@/api/iot/equipmentMaintenance';
import { guid } from '@/utils/uuid.js';
import GetIMEIMsg from './components/GetIMEIMsg';
import GetLastedResult from './components/GetLastedResult';
import UploadImageList from './components/UploadImageList';
import useSaveForm from './hook/useSaveForm';
const { handleSave } = useSaveForm();
export default {
    data() {
        return {
            options: {},
            info: {
                imei: '',
                mxid: '', //明细id
                ywid: '', //运维id
                ywlx: '', //运维类型
                ywmx: {
                    yhsj: '',
                    yerr: '',
                    xerr: '',
                    yxhgl: '',
                    xxhgl: '',
                    yxqjz: '',
                    xxqjz: ''
                }
            },
            arrUploadType: [
                {
                    name: '现场照片',
                    LXDM: 'YWMX', //类型代码
                    ZLXDM: 'XHYH' //子类型代码
                }
            ],
            isDisabled: false, //是否不可编辑
            fileList: []
        };
    },
    components: {
        GetIMEIMsg,
        GetLastedResult,
        UploadImageList
    },
    watch: {},
    onLoad(options) {
        this.options = options;
    },
    onHide() {},
    onReady() {},
    created() {},
    mounted() {
        if (this.options.type === 'add') {
            this.info.ywlx = this.options.ywlx;
            this.info.ywid = this.options.ywid;
            this.info.ywmx.yhsj = this.$dayjs().format('YYYY-MM-DD HH:mm:ss');
            this.info.mxid = guid();
        }
        if (this.options.type === 'detail') {
            uni.setNavigationBarTitle({
                title: '运维明细详情'
            });
            this.isDisabled = true;
            this.getMaintenanceDetail();
        }
    },
    onBackPress() {},
    methods: {
        //请求明细
        async getMaintenanceDetail(id) {
            let data = await maintenanceDetail(this.options.mxid);
            for (const key in data) {
                if (key == 'ywmx') {
                    let innerData = data['ywmx'];
                    for (const key1 in innerData) {
                        this.$set(
                            this.info.ywmx,
                            key1,
                            innerData[key1] === '' || innerData[key1] == 'null'
                                ? '-'
                                : innerData[key1]
                        );
                    }
                } else {
                    this.$set(
                        this.info,
                        key,
                        data[key] === '' || data[key] == 'null'
                            ? '-'
                            : data[key]
                    );
                }
            }
            let index = this.info.ywmx?.qtyz?.indexOf('#') || 0;
            this.info.qtyz = this.info.ywmx?.qtyz?.slice(0, index) || '-';
            this.$refs.refGetIMEIMsg.updateInfo(this.info);
            this.$nextTick(() => {
                this.$refs.refUploadImageList.getImageFileList();
            });
        },
        //更新表单数据
        updateInfo(payload) {
            this.info = {
                ...this.info,
                ...payload,
                ywmx: { ...payload.ywmx, yhsj: this.info.ywmx.yhsj }
            };
        },
        confirmTime(obj) {
            this.info.ywmx.yhsj = obj.time;
        },

        //保存
        async save() {
            let objRules = {};
            let objValiData = {};
            Object.assign(objRules, this.$refs.refGetIMEIMsg.rules, this.rules);
            Object.assign(
                objValiData,
                this.info,
                this.info.ywmx,
                this.$refs.refGetIMEIMsg.info
            );
            let arrUploadImage = this.$refs.refUploadImageList.arrUploadImage;
            let pages = getCurrentPages(); // 当前页面
            handleSave(
                objRules,
                objValiData,
                this.info,
                pages,
                true,
                arrUploadImage,
                false
            );
        }
    }
};
</script>

<style scoped lang="less">
section {
    background: #f1f1f1;
}
</style>
