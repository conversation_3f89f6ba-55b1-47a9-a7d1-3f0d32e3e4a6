<!-- @format -->

<template>
    <section class="main" style="background: #f1f1f1">
        <div class="inner" style="padding-top: 0">
            <div class="gap"></div>
            <div class="zy-form">
                <div class="item">
                    <p class="label star greyf">企业名称</p>
                    <div class="rp w70">
                        <input
                            type="text"
                            v-model="model.WRYMC"
                            class="zy-input1"
                            placeholder="请填写污染源名称"
                            @click="hideTabbar"
                            @focus="hideTabbar"
                            @blur="showTabbar"
                        />
                    </div>
                </div>
                <div class="item">
                    <p class="label greyf">所属行业</p>
                    <div class="rp w70 zy-selectBox">
                        <m-uni-data-picker
                            v-model="model.HYLX"
                            :level="industryLevel"
                            :localdata="industryList"
                            placeholder="请选择所属行业"
                            popup-title="请选择所属行业"
                            @change="onchangeIndustry"
                            @changeData="changeIndustryData"
                        >
					<span class="res gray-tip" v-show="!model.HYLX">请选择所属行业</span>
					<span class="deep-gray" v-show="model.HYLX">{{currentIndustry || '-'}}</span>
                        </m-uni-data-picker>
						
                    </div>
                </div>
                <div class="item">
                    <p class="label greyf">信用代码</p>
                    <div class="rp w70">
                        <input
                            type="text"
                            v-model="model.TYSHXYDM"
                            class="zy-input1"
                            placeholder="请填写社会统一信用代码"
                            @click="hideTabbar"
                            @focus="hideTabbar"
                            @blur="showTabbar"
                        />
                    </div>
                </div>
                <div class="item">
                    <p class="label star greyf">企业地址</p>
                    <div class="rp w70">
                        <textarea
                            class="zy-input1"
                            auto-height="true"
                            placeholder=""
                            v-model="model.DWDZ"
                            @click="hideTabbar"
                            @focus="hideTabbar"
                            @blur="showTabbar"
                        ></textarea>
                    </div>
                </div>
                <div class="item">
                    <p class="label greyf">经纬度</p>
                    <div class="rp w70">
                        <input
                            type="number"
                            class="zy-input1 center jdipt mr20"
                            placeholder="请输入经度"
                            v-model="model.JD"
                            @click="hideTabbar"
                            @focus="hideTabbar"
                            @blur="showTabbar"
                        />
                        <input
                            type="number"
                            class="zy-input1 center jdipt"
                            placeholder="请输入纬度"
                            v-model="model.WD"
                            @click="hideTabbar"
                            @focus="hideTabbar"
                            @blur="showTabbar"
                        />
                        <i
                            class="adrr"
                            style="margin-left: 10rpx"
                            @click="toMap()"
                        ></i>
                    </div>
                </div>
                <div class="item">
                    <p class="label greyf">所属区域</p>
                    <div class="rp">
                        <div class="zy-selectBox">
                            <m-uni-data-picker
                                v-model="model.XZQHDM"
                                :level="areaLevel"
                                :localdata="areaList"
                                placeholder="请选择所属区域"
                                popup-title="请选择所属区域"
                                @change="onchange"
                                @changeData="changeAreaData"
                            >
                            </m-uni-data-picker>
                        </div>
                    </div>
                </div>
                <div class="item">
                    <p class="label" style="flex: 4">污染源重点监管类型</p>
                    <div class="rp">
                        <div
                            class="zy-selectBox"
                            @click="isShowSeletorSupervise = true"
                        >
                            <p class="res placeholder" v-if="!model.WRYJGLX">
                                请选择源重点监管类型(可多选)
                            </p>
                            <p class="res input pd-txt2" v-if="model.WRYJGLX">
                                {{ model.WRYJGLX }}
                            </p>
                        </div>
                    </div>
                </div>
                <div class="item">
                    <p class="label">非现场监管类型</p>
                    <div class="rp">
                        <div class="zy-selectBox" @click="isShowOffSite = true">
                            <p class="res placeholder" v-if="!model.FXCJGLX">
                                请选择非现场监管类型(可多选)
                            </p>
                            <p class="res input pd-txt2" v-if="model.FXCJGLX">
                                {{ model.FXCJGLX }}
                            </p>
                        </div>
                    </div>
                </div>
                <div class="item">
                    <p class="label star greyf">环保联系</p>
                    <div class="rp w70">
                        <input
                            type="text"
                            v-model="model.HBLXR"
                            class="zy-input1"
                            placeholder="请填写环保联系"
                            value=""
                            @click="hideTabbar"
                            @focus="hideTabbar"
                            @blur="showTabbar"
                        />
                    </div>
                </div>
                <div class="item">
                    <p class="label star greyf">联系电话</p>
                    <div class="rp w70">
                        <input
                            type="text"
                            v-model="model.HBLXRDH"
                            class="zy-input1"
                            placeholder="请填写联系电话"
                            @click="hideTabbar"
                            @focus="hideTabbar"
                            @blur="showTabbar"
                            value=""
                        />
                    </div>
                </div>
                <div class="item">
                    <p class="label greyf">监管联系</p>
                    <div class="rp w70">
                        <input
                            type="text"
                            v-model="model.JGLXR"
                            class="zy-input1"
                            placeholder="请填写监管联系"
                            @click="hideTabbar"
                            @focus="hideTabbar"
                            @blur="showTabbar"
                            value="陈小龙"
                        />
                    </div>
                </div>
                <div class="item">
                    <p class="label greyf">联系电话</p>
                    <div class="rp w70">
                        <input
                            :adjust-position="true"
                            type="text"
                            v-model="model.JGLXRDH"
                            class="zy-input1"
                            placeholder="请填写联系电话"
                            @click="hideTabbar"
                            @focus="hideTabbar"
                            @blur="showTabbar"
                            value=""
                        />
                    </div>
                </div>
                <div class="item">
                    <p class="label star greyf">安装人员</p>
                    <div class="rp w70">
                        <input
                            :adjust-position="true"
                            type="text"
                            v-model="model.AZLXR"
                            class="zy-input1"
                            placeholder="请填写安装人员"
                            value=""
                            @click="hideTabbar"
                            @focus="hideTabbar"
                            @blur="showTabbar"
                        />
                    </div>
                </div>
                <div class="item">
                    <p class="label star greyf">联系电话</p>
                    <div class="rp w70">
                        <input
                            :adjust-position="true"
                            type="text"
                            v-model="model.AZLXRDH"
                            class="zy-input1"
                            placeholder="请填写联系电话"
                            @click="hideTabbar"
                            @focus="hideTabbar"
                            @blur="showTabbar"
                            value=""
                        />
                    </div>
                </div>
                <div class="item remark">
                    <p class="label greyf">备注</p>
                    <div class="rp w70">
                        <textarea
                            :adjust-position="true"
                            auto-height="true"
                            maxlength="-1"
                            :class="model.BZ ? '' : 'two'"
                            class="zy-textarea1"
                            v-model="model.BZ"
                            rows="3"
                            @focus="hideTabbar"
                            @click="hideTabbar"
                            @blur="showTabbar"
                            placeholder="请填写备注信息如污染源上班时间、多个联系人等"
                        ></textarea>
                    </div>
                </div>
            </div>
            <div class="gap"></div>
            <div class="gap"></div>
            <div class="zy-bot-btn1" @click="save">保存</div>
            <div class="gap"></div>
            <div class="gap"></div>
        </div>

        <!-- 污染源重点监管类型 -->
        <MultipleChoicePopup
            :arrSelectList="arrSupervise"
            :isShow.sync="isShowSeletorSupervise"
            @confirm="confirmSupervise"
            :strCurrentSeleted="model.WRYJGLX"
        ></MultipleChoicePopup>

        <!-- 非现场监管类型 -->
        <MultipleChoicePopup
            :arrSelectList="arrSupervise"
            :isShow.sync="isShowOffSite"
            @confirm="confirmOffSite"
            :strCurrentSeleted="model.FXCJGLX"
        ></MultipleChoicePopup>
    </section>
</template>

<script>
import enterprise_store from './enterprise.store.js';
import { guid } from '@/common/uuid.js';
import { getAreaTree, getAreaTreeFour } from '@/common/district.js';
import { checkSocialCreditCode } from '@/common/checkSocialCreditCode.js';
import { addQyxx, updataQyxx, getCommoHylx } from '@/api/iot/enterprise.js';
import MultipleChoicePopup from '@/pages/component/MultipleChoicePopup';

export default {
    watch: {
        JWD: {
            handler: function (v) {
                this.model.JD = v.JWD.longitude;
                this.model.WD = v.JWD.latitude;
                // this.model.DWDZ = v.JWD.DZ;
            },
            deep: true //深度监听
        }
    },
    components: {
        MultipleChoicePopup
    },
    data() {
        return {
            type: 'edit',
            enterpriseInfo: {},
            JWD: enterprise_store.state,
            border: false, //input 是否显示边框, 默认false
            model: {
                WRYBH: '',
                WRYMC: '',
                DWDZ: '',
                JD: '',
                WD: '',
                SSSF: '', //中文
                SSDS: '', //中文
                SSQX: '', //中文
                SSJD: '',
                XZQHDM: '',
                HBLXR: '',
                HBLXRDH: '',
                JGLXR: '',
                JGLXRDH: '',
                BZ: '',
                XGR: '',
                AZLXR: '', //安装联系人
                AZLXRDH: '', //安装联系人电话
                HYLX: '', //所属行业
                TYSHXYDM: '' //社会统一信用代码
            },
            labelWidth: 200,
            labelPosition: 'left', //表单域提示文字的位置，left-左侧，top-上方
            errorType: ['border-bottom'], //错误的提示方式，数组形式, 可选值为：
            DWDZ: '',
            rules: {
                WRYMC: {
                    required: true,
                    message: '填写企业名称',
                    trigger: 'change'
                },
                HYLX: {
                    required: false,
                    message: '填写所属行业',
                    trigger: 'change'
                },

                TYSHXYDM: {
                    required: false,
                    message: '填写社会统一信用代码',
                    trigger: 'change'
                },
                DWDZ: {
                    required: true,
                    message: '填写企业地址',
                    trigger: 'change'
                },
                JD: {
                    required: false,
                    message: '请获取经度',
                    trigger: 'change'
                },
                WD: {
                    required: false,
                    message: '请获取纬度',
                    trigger: 'change'
                },
                XZQHDM: {
                    required: false,
                    message: '请选择行政区划',
                    trigger: 'change'
                },
                HBLXR: {
                    required: true,
                    message: '填写环保联系人',
                    trigger: 'change'
                },
                HBLXRDH: {
                    required: true,
                    message: '填写环保联系人电话',
                    trigger: 'change'
                },
                AZLXR: {
                    required: true,
                    message: '填写安装联系人',
                    trigger: 'change'
                },
                AZLXRDH: {
                    required: true,
                    message: '填写安装联系人电话',
                    trigger: 'change'
                },
                JGLXR: {
                    required: false,
                    message: '请填写监管联系人',
                    trigger: 'change'
                },
                JGLXRDH: {
                    required: false,
                    message: '请填写监管联系人电话',
                    trigger: 'change'
                }
            },
            yycjShow: false,
            yycjList: [],
            areaList: [],
            showBtn: true,
            windowHeight: 0,
            areaLevel: '3',
            backPage: true,
            clientHight: '',
            footerHight: '',
            hasFocus: false,
            timer: null,
            industryList: [], //行业列表
            industryLevel: '3',
            arrSupervise: [
                {
                    label: '涉气',
                    value: '涉气'
                },
                {
                    label: '涉水',
                    value: '涉水'
                }
            ],
            //显示污染源重点监管类型
            isShowSeletorSupervise: false,
            //显示非现场监管类型
            isShowOffSite: false,
			//当前行业
			currentIndustry:'',
        };
    },

    onLoad(option) {
        this.enterpriseInfo = uni.getStorageSync('userInfo');
        this.model.ORGID = uni.getStorageSync('ORGID') || '';
        this.type = option.type;
        if (option.type == 'edit') {
            uni.setNavigationBarTitle({
                title: '修改污染源信息'
            });
            this.model = JSON.parse(decodeURIComponent(option.info));
            for (let key in this.model) {
                if (this.model[key] == null) {
                    this.model[key] = '';
                }
            }
        } else if (option.type == 'add') {
            this.model.WRYBH = guid();
        }

        let areaList = getAreaTree();

        if (this.model.SSJD && this.model.SSJD != 'null') {
            //this.areaLevel = 4;
            this.areaLevel = '4';
            areaList = getAreaTreeFour();
        }
        // this.areaList = this.areaList.filter(item => {
        // 	return item.XZH.substr(0, 2) == this.enterpriseInfo.orgid.substr(0, 2) || item.FDM.substr(0, 2) ==
        // 		this.enterpriseInfo.orgid.substr(0, 2)
        // })
    },
    //onReady() {
    //this.getClientHight();
    //},
    created() {
        this.getArea();
        //获取行业信息
        uni.getStorage({
            key: 'storage_industryData',
            success: (res) => {
                this.industryList = res.data;
            }
        });

        // #ifdef APP-PLUS
        uni.onWindowResize((res) => {
            if (this.windowHeight == 0) {
                this.windowHeight = res.size.windowHeight;
                return;
            }

            if (this.windowHeight > res.size.windowHeight) {
                this.showBtn = false;
            } else {
                this.showBtn = true;
            }
        });
        // #endif
    },
    mounted() {
        uni.$on('sentEnterprisePosition', (position) => {
            let arrPosition = position.split(',');
            this.model.JD = arrPosition[0];
            this.model.WD = arrPosition[1];
        });
		if (this.type == 'edit') {
		
			this.getCurrentIndustry(this.model.HYLX)
		} 
    },
    destroyed() {
        uni.offWindowResize(() => {
            console.log('取消监听窗口尺寸变化事件');
        });
        uni.$off('sentEnterprisePosition');
    },
    onBackPress(e) {
        // uni.addInterceptor('navigateBack', {
        //     invoke(args) {
        //         // request 触发前拼接 url
        //         args.mark = '污染源-列表-修改污染信息-未保存';
        //         args.model = '污染源-列表';
        //         console.log(args, 'navigateBack');
        //     }

        // });

        if (this.type !== 'add') {
            return false;
        }
        let self = this;
        if (this.backPage) {
            uni.showModal({
                title: '提示',
                content: '当前内容未保存，是否离开?',
                success: function (res) {
                    if (res.confirm) {
                        console.log('用户点击确定');
                        self.backPage = false;

                        let pages = getCurrentPages(); // 当前页面
                        let beforePage = pages[pages.length - 2]; // 上一页
                        if (beforePage.$vm) {
                            //beforePage.$vm.getEnterpriseInfo();
                        } else {
                            // beforePage.initList();
                            // beforePage.goTop();
                            beforePage.getEnterpriseInfo();
                        }
                        setTimeout(() => {
                            uni.navigateBack({
                                delta: 1,
                                mark: '污染源-列表-修改污染信息-未保存',
                                model: '污染源-列表'
                            });
                        }, 400);
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                        self.backPage = true;
                    }
                }
            });
        }
        uni.hideKeyboard();
        return this.backPage;
    },
    methods: {
        //详情页回显行业
		getCurrentIndustry(YHLX){
		   let label = 	this.findLabelByValue(YHLX,this.industryList)
		   this.currentIndustry = label;
		},
	    findLabelByValue(value, arr) {
		  for (let i = 0; i < arr.length; i++) {
		    if (arr[i].value === value) {
		      return arr[i].label
		    } else if (arr[i].children && arr[i].children.length > 0) {
		      const label = this.findLabelByValue(value, arr[i].children)
		      if (label) {
		        return label
		      }
		    }
		  }
		  return null
		},
        // 选中污染源重点监管类型
        confirmSupervise(v) {
            this.model.WRYJGLX = v.join(',');
        },
        // 选中非现场监管类型
        confirmOffSite(v) {
            this.model.FXCJGLX = v.join(',');
        },
        onchangeIndustry(e) {
            console.log(111, e);
			this.currentIndustry =e.detail.value[2].text;
        },
        changeIndustryData(e) {
            console.log(222, e);
        },
        confirmIndustry(v) {
            this.model.HYLX = v[0].label;
        },
        async getArea() {
            let areaList = await this.getAreaTreePromise();
            this.areaList = areaList.filter((item) => {
                return (
                    item.XZH.substr(0, 2) ==
                        uni.getStorageSync('XZQHDM')?.substr(0, 2) ||
                    item.FDM.substr(0, 2) ==
                        uni.getStorageSync('XZQHDM')?.substr(0, 2)
                );
            });
        },
        //获取地域树
        getAreaTreePromise() {
            return new Promise(async function (resolve, reject) {
                let areaList = await getAreaTree();
                resolve(areaList);
            });
        },
        // getClientHight() {
        // 	let that = this;
        // 	uni.getSystemInfo({
        // 		success(res) {
        // 			console.log(res.screenHeight); //获取手机设备屏幕高度
        // 			that.clientHight = res.screenHeight;
        // 			that.footerHight = that.clientHight * 0.1;
        // 		}
        // 	})
        // },
        getYycj(v) {
            this.model.YYCJ = v[0].value;
        },
        save() {
            if (this.timer) {
                clearTimeout(this.timer);
            }
            this.timer = setTimeout(() => {
                this.model.USER = this.enterpriseInfo.name;
                let rules = Object.keys(this.rules);

                let showMessage = false;
                for (let i = 0; i < rules.length; i++) {
                    let field = rules[i];
                    let requires = this.rules[field];
                    if (
                        (!this.model[field] || !this.model[field].length) &&
                        requires.required
                    ) {
                        showMessage = true;
                        let message = requires.message;

                        // #ifdef APP-PLUS
                        plus.nativeUI.toast(message);
                        // #endif

                        // #ifndef APP-PLUS
                        uni.showToast({
                            icon: 'none',
                            title: message,
                            duration: 1000
                        });

                        // #endif
                        break;
                    }
                }

                if (showMessage) {
                    return;
                }

                //校验联系人电话，社会信用代码
                if (this.model.HBLXRDH !== '') {
                    if (!this.isPhone(this.model.HBLXRDH, '环保联系人电话')) {
                        return;
                    }
                } else if (this.model.AZLXRDH !== '') {
                    if (!this.isPhone(this.model.JGLXRDH, '安装联系人电话')) {
                        return;
                    }
                } else if (this.model.JGLXRDH !== '') {
                    if (!this.isPhone(this.model.JGLXRDH, '监管联系人电话')) {
                        return;
                    }
                } else if (this.model.TYSHXYDM !== '') {
                    //校验社会统一信用代码
                    let result = checkSocialCreditCode(this.model.TYSHXYDM);
                    if (!result.isPass) {
                        // #ifdef APP-PLUS
                        plus.nativeUI.toast(result.errorMessage);
                        // #endif

                        // #ifndef APP-PLUS
                        uni.showToast({
                            icon: 'none',
                            title: result.errorMessage,
                            duration: 1000
                        });
                        // #endif
                        return;
                    }
                }

                if (this.type == 'add') {
                    addQyxx(this.model).then((res) => {
                        if (
                            res.data.msg == 'fail' ||
                            res.data.status == '500'
                        ) {
                            uni.showToast({
                                icon: 'none',
                                title: '污染源已存在请勿重复添加'
                            });
                        } else {
                            uni.showToast({
                                title: '添加成功',
                                duration: 1000
                            })
                                .then(() => {
                                    setTimeout(() => {
                                        // uni.navigateTo({
                                        // 	url: './saveScxSuccess?scxName='+this.model.SCXMC+'&info='+ encodeURIComponent(JSON.stringify(this.info))
                                        // });
                                        // uni.setStorageSync('isAddScx', true);
                                        let pages = getCurrentPages(); // 当前页面
                                        let beforePage =
                                            pages[pages.length - 2]; // 上一页
                                        if (beforePage.$vm) {
                                            //beforePage.$vm.getEnterpriseInfo();
                                        } else {
                                            // beforePage.initList();

                                            beforePage.getEnterpriseInfo();
                                        }
                                        this.backPage = false;
                                        uni.navigateBack({
                                            delta: 1,
                                            mark: '污染源-列表-修改污染信息-新增',
                                            model: '污染源-列表'
                                        });
                                    }, 500);
                                })
                                .catch((err) => {
                                    uni.showToast({
                                        title: '添加失败'
                                    });
                                });
                        }
                    });
                } else {
                    updataQyxx(this.model)
                        .then((res) => {
                            uni.showToast({
                                title: '操作成功'
                            }).then(() => {
                                setTimeout(() => {
                                    let pages = getCurrentPages(); // 当前页面
                                    let beforePage = pages[pages.length - 2]; // 上一页
                                    let parentPage = pages[pages.length - 3]; // 上两页
                                    if (beforePage.$vm) {
                                        beforePage.$vm.getEnterpriseInfo();
                                    } else {
                                        beforePage.getEnterpriseInfo();
                                    }
                                    if (parentPage.$vm) {
                                        parentPage.$vm.initList();
                                    } else {
                                        parentPage.initList();
                                    }
                                    // uni.navigateTo({
                                    // 	url: './saveScxSuccess?scxName='+this.model.SCXMC+'&info='+ encodeURIComponent(JSON.stringify(this.info))
                                    // });
                                    // uni.setStorageSync('isAddScx', true);
                                    this.backPage = false;
                                    uni.navigateBack({
                                        delta: 1,
                                        mark: '污染源-列表-修改污染信息-保存',
                                        model: '污染源-列表'
                                    });
                                }, 300);
                            });
                        })
                        .catch((err) => {
                            uni.showToast({
                                title: '操作失败'
                            });
                        });
                }
            }, 500);
        },

        toMap() {
            //         uni.navigateTo({
            // url:`./map?address=${encodeURIComponent(this.model.DWDZ)}`
            //         });
            uni.navigateTo({
                url: `/pages/enterprise/Location`
            });
            // uni.chooseLocation({
            // 	success: function (res) {
            // 		console.log('位置名称：' + res.name);
            // 		console.log('详细地址：' + res.address);
            // 		console.log('纬度：' + res.latitude);
            // 		console.log('经度：' + res.longitude);
            // 	}
            // });
        },
        back() {
            uni.navigateBack({
                delta: 1
            });
        },
        onchange(e) {
            const regions = e.detail.value;
            console.log('区域选择', regions);
            let len = regions.length;
            if (len == 3) {
                this.model.XZQHDM = len ? regions[2].value : '';
            } else {
                this.model.XZQHDM = len ? regions[3].value : '';
                this.model.SSJD = len ? regions[3].text : '';
            }
            this.model.SSSF = len ? regions[0].text : '';
            this.model.SSDS = len ? regions[1].text : '';
            this.model.SSQX = len ? regions[2].text : '';
        },
        changeAreaData() {
            let areaList =
                this.areaLevel == 3 ? getAreaTreeFour() : getAreaTree();
            this.areaList = areaList.filter((item) => {
                return (
                    item.XZH.substr(0, 2) ==
                        uni.getStorageSync('ORGID')?.substr(0, 2) ||
                    item.FDM.substr(0, 2) ==
                        uni.getStorageSync('ORGID')?.substr(0, 2)
                );
            });

            this.areaLevel = this.areaLevel == 3 ? 4 : 3;
        },
        showTabbar(event) {
            setTimeout(() => {
                if (!this.hasFocus) {
                    console.log('失去焦点');
                    this.hasFocus = false;
                    this.showBtn = true;
                }
            }, 10);
        },
        hideTabbar() {
            console.log('获得焦点');
            this.hasFocus = true;
            this.showBtn = false;
        },
        isPhone(reg_tel, message) {
            var reg = /^\d{11}$/;
            if (!reg.test(reg_tel)) {
                uni.showToast({
                    title: '请输入有效的' + message,
                    icon: 'none',
                    duration: 1000
                });

                return false;
            }
            return true;
        }
    }
};
</script>

<style scoped>
/** @format */

html {
    -webkit-tap-highlight-color: transparent;
    height: 100%;
}

body {
    -webkit-backface-visibility: hidden;
    height: 100%;
    overflow: scroll;
}

/deep/ .u-form {
    font-size: 26rpx;
}

/deep/ .u-input__textarea {
    border-radius: 8rpx;
    height: 100rpx;
    padding-left: 40rpx;
    background: url(../../static/app/images/addr.png) no-repeat;
    background-color: rgb(243, 245, 249);
    background-size: 20rpx auto;
    background-position: 12rpx 16rpx;
}

/deep/ .input-value-border {
    border: none;
    line-height: 54.34785rpx;
}

/deep/ .input-arrow {
    border-left: none;
    border-right: 1px solid #999;
}

/deep/ .arrow-area {
    margin-bottom: 0;
}

/deep/ .selected-area {
    font-size: 26.570025rpx;
}

.uni-textarea-placeholder {
    white-space: pre-wrap;
    overflow: unset;
}

.fix-btn {
    position: static;
}

.btn-box {
    position: absolute;
    width: 100%;
    padding: 0rpx 30rpx;
}

.hideBtn {
    bottom: calc(-50vh);
}
.gray-tip{
	color:#c1c1c1;
}
.deep-gray{
	color:#666;
}
</style>
