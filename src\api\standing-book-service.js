/** @format */

import http from '@/common/net/http.js';

/**
 * 获取系统中已配置的不同类型台账列表
 */
const getBookList = (pageIndex = 1) => {
    return http.post(
        `${http.url}/platform/tz/tzconfig/tzconfigcontroller/querytzconfigs`,
        {
            pageNum: pageIndex,
            pageSize: 10
        }
    );
};

/**
 * 获取指定台账ID的台账目录树
 */
const getBookCatalogueTree = (bookId, count = 'false', optionParams) => {
    let params = {
        SFYX: '1',
        isCount: count,
        XTXH: bookId,
        isFilterZero: 'false'
    };
    Object.assign(params, optionParams);
    return http.post(`${http.url}/mobile/base/querytzmenutreeconfigs`, params);
};

const getCatalogueMenuCount = (bookId, optionParams) => {
    return getBookCatalogueTree(bookId, 'true', optionParams);
};

/**
 * 获取台账目录节点配置
 */
const getBookCatalogueConfig = (catalogueId, optionParams) => {
    let params = {
        XH: catalogueId
    };
    if (optionParams) {
        Object.assign(params, optionParams);
    }
    return http.post(
        `${http.url}/platform/tz/tzconfig/tzmenuconfigcontroller/gettzmenuconfig`,
        params
    );
};

const getDynamicListConfig = (configId) => {
    return new Promise((resolve, reject) => {
        http.post(`${http.url}/mobile/base/configquery`, {
            XH: configId
        })
            .then((config) => {
                //因服务平台历史原因，这个服务接口本该只返回一个JSON的，但是使用了分页，实际数据项只有一条
                let actualConfig = JSON.parse(config.list[0].PZLB);
                let detailConfig = config.list[0].KZPZ || {
                    FORMID: '',
                    CS: 'recordId=rid'
                };
                let resolveConfig = {
                    columnsConfig: actualConfig,
                    detailConfig
                };
                resolve(resolveConfig);
            })
            .catch((error) => {
                reject(error);
                console.log(`动态列表配置查询出错：${error}`);
            });
    });
};

const getDynamicListIsShowSearch = (configId) => {
    return new Promise((resolve, reject) => {
        http.post(`${http.url}/mobile/base/configquery`, {
            XH: configId
        })
            .then((config) => {
                //因服务平台历史原因，这个服务接口本该只返回一个JSON的，但是使用了分页，实际数据项只有一条
                let configData = JSON.parse(config.list[0].PZCX);
                let isShow = false;
                if (configData.quickMatch.length > 0) {
                    isShow = true;
                }
                let resolveConfig = {
                    isShow: isShow
                };
                resolve(resolveConfig);
            })
            .catch((error) => {
                reject(error);
                console.log(`动态列表配置查询出错：${error}`);
            });
    });
};

const getDynamicListData = (configId, pageIndex, search, params) => {
    let resolveParams = {
        quickMatch: search,
        pageNum: pageIndex,
        pageSize: 10,
        xh: configId
    };
    Object.assign(resolveParams, params);
    return http.post(`${http.url}/mobile/base/query`, resolveParams);
};

export default {
    getBookList,
    getBookCatalogueTree,
    getBookCatalogueConfig,
    getCatalogueMenuCount,
    getDynamicListConfig,
    getDynamicListIsShowSearch,
    getDynamicListData
};
