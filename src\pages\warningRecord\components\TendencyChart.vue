<!-- @format -->

<template>
    <view>
        <view id="chart" v-show="curEquit && curEquit.SBMC != ''">
            <!-- #ifdef APP-PLUS || H5 -->
            <view
                :prop="option"
                :change:prop="echarts.updateEcharts"
                :name="curEquit && curEquit.SBMC"
                id="echarts"
                class="echarts"
                ref="echarts"
            ></view>
            <!-- #endif -->
            <!-- #ifndef APP-PLUS || H5 -->
            <view>非 APP、H5 环境不支持</view>
            <!-- #endif -->
        </view>
        <div
            v-show="curEquit && curEquit.SBMC == ''"
            style="color: #999; text-align: center"
        >
            暂无数据
        </div>
    </view>
</template>

<script>
import { getZdqs, ycsbqj } from '@/api/iot/warning.js';
import { debounce } from 'lodash';
export default {
    props: {
        dateStr: {
            type: String,
            default: ''
        },
        curEquit: {
            type: Object,
            default: () => {}
        },
        queryTime: {
            type: Object,
            default: () => {}
        },
        curIndex: {
            type: Number,
            default: 0
        },
        warnStart: {
            type: String,
            default: ''
        },
        warnEnd: {
            type: String,
            default: ''
        },
        x: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            trendData: [],
            fullScreen: false,
            colorArr: [
                '#9799f3',
                '#0fd0b7',
                '#ffd351',
                '#6ebffb',
                '#f56b6d',
                '#40c057',
                '#6a89e2',
                '#ff8e43',
                '#f60000',
                '#94004b',
                '#0032c9',
                '#f6a400',
                '#62320a',
                '#454344',
                '#23a801',
                '#b6c800',
                '#d19573',
                '#817f80',
                '#c80002',
                '#351f72',
                '#3d73af',
                '#b1b1b1',
                '#f3cd00',
                '#d83b4c',
                '#335500',
                '#d0d0d0',
                '#c63aa5',
                '#0147c3',
                '#6c72be',
                '#e5e5e7',
                '#0082c0',
                '#e57102',
                '#5cb9a5'
            ],
            colorArrReverse: [
                '#5cb9a5',
                '#e57102',
                '#0082c0',
                '#e5e5e7',
                '#6c72be',
                '#0147c3',
                '#c63aa5',
                '#d0d0d0',
                '#335500',
                '#d83b4c',
                '#f3cd00',
                '#b1b1b1',
                '#3d73af',
                '#351f72',
                '#c80002',
                '#817f80',
                '#d19573',
                '#b6c800',
                '#23a801',
                '#454344',
                '#62320a',
                '#f6a400',
                '#0032c9',
                '#94004b',
                '#f60000',
                '#ff8e43',
                '#6a89e2',
                '#40c057',
                '#f56b6d',
                '#6ebffb',
                '#ffd351',
                '#0fd0b7',
                '#9799f3'
            ],
            option: {
                color: [
                    '#409EFF' //'#E88511', '#1C8B14', '#14AACF', '#A825C9', '#781929', '#2C8B14'
                ],
                legend: {
                    show: false,
                    y: 'bottom',
                    type: 'scroll',
                    data: [
                        '振动能量' //'X轴', 'Y轴', 'Z轴', 'X轴主频', 'Y轴主频', 'Z轴主频'
                    ],
                    selected: {
                        振动能量: true
                    }
                },
                grid: {
                    top: '60',
                    left: '40',
                    right: '36',
                    bottom: '10%',
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    extraCssText: 'z-index: 9',
                    formatter: function (params) {
                        var res = '振动时间：' + params[0].name;
                        for (var i = 0; i < params.length; i++) {
                            res +=
                                '<br>' +
                                params[i].seriesName +
                                '：' +
                                params[i].data;
                        }
                        return res;
                    }
                },
                // dataZoom: [{
                // 	show: true,
                // 	realtime: true,
                // 	start: 0,
                // 	end: 100,
                // 	xAxisIndex: [0, 1],
                // 	bottom: '10%'
                // }],
                xAxis: [
                    {
                        name: '时\n间',
                        scale: true,
                        type: 'category',
                        data: [],
                        axisLabel: {
                            show: true,
                            textStyle: {
                                color: '#666'
                            },
                            formatter: function (value) {
                                console.log('value', value);
                                value =
                                    ' ' +
                                    value.slice(0, 6) +
                                    '\n' +
                                    source.slice(6);
                                return value;
                            }
                        }
                        // axisLabel: {
                        //     interval: 2,
                        //     rotate: -20,
                        //     formatter: function (val) {
                        //         return val;
                        //     }
                        // }
                    },
                    {
                        axisTick: {
                            show: false // 不显示坐标轴刻度线
                        },
                        axisLine: {
                            show: false // 不显示坐标轴线
                        },
                        axisLabel: {
                            show: false // 不显示坐标轴上的文字
                        },
                        name: '',
                        scale: true,
                        type: 'category',
                        data: []
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        name: '能         \n量         ',
                        nameLocation: 'center',
                        nameGap: 8,
                        nameRotate: 0,
                        nameTextStyle: {
                            fontSize: 14
                        },
                        //默认以千分位显示，不想用的可以在这加一段
                        axisLabel: {
                            //调整左侧Y轴刻度， 直接按对应数据显示
                            show: true,
                            showMinLabel: true,
                            showMaxLabel: true,
                            formatter: function (value) {
                                return '启停阈值' + value;
                            }
                        }
                    },
                    {
                        type: 'value',
                        name: '　　　　主\n　　　　频',
                        nameLocation: 'center',
                        nameGap: 10,
                        nameRotate: 0,
                        nameTextStyle: {
                            fontSize: 16
                        },
                        //默认以千分位显示，不想用的可以在这加一段
                        axisLabel: {
                            //调整左侧Y轴刻度， 直接按对应数据显示
                            show: true,
                            showMinLabel: true,
                            showMaxLabel: true,
                            formatter: function (value) {
                                return value;
                            }
                        }
                    }
                ],
                series: [
                    {
                        yAxisIndex: 0,
                        markLine: {
                            symbol: ['circle', 'none'],
                            data: [
                                {
                                    silent: false,
                                    lineStyle: {
                                        type: 'dashed',
                                        color: '#9134ff'
                                    },
                                    label: {
                                        position: 'end',
                                        color: '#9134ff'
                                    },
                                    yAxis: 0
                                    // symbol:'none'
                                }
                            ]
                        },
                        data: [],
                        areaStyle: {
                            opacity: 0.1
                        },
                        name: '振动能量',
                        type: 'line'
                    },
                    {
                        xAxisIndex: 1,
                        itemStyle: {
                            normal: {
                                lineStyle: {
                                    color: '#409EFF'
                                }
                            }
                        },
                        data: [],
                        areaStyle: {
                            opacity: 0.1
                        },
                        name: '',
                        type: 'line'
                    }
                ]
            },
            allData: [],
            visualMapData: [],
            debouncedGetTrendPic: null
        };
    },
    created() {
        this.debouncedGetTrendPic = debounce(this.getTrendPic, 1500); // 设置防抖延迟时间为300毫秒
    },
    watch: {
        curEquit: {
            handler: function (newVal) {
                this.$nextTick(() => {
                    if (this.allData && this.allData.length) {
                        //遍历所有数据寻找设备id和设备序号相同的
                        const item = this.allData.find(
                            (item) => item.SBID === newVal.SBXH
                        );
                        if (item) {
                            // 如果找到了，则说明之前存储过，直接拿到缓存数据去初始化图表
                            this.initChart(
                                item.LIST,
                                item.YZ,
                                item.visualMapData || []
                            );
                        } else {
                            // 未找到就发起请求重新添加
                            this.debouncedGetTrendPic();
                        }
                    } else {
                        this.debouncedGetTrendPic();
                    }
                });
            },
            deep: true
        }
    },

    methods: {
        //获取延迟上报时段
        async getDelayTime() {
            let params = {
                sbid: this.curEquit?.SBXH || '',
                startT: this.queryTime?.startT || '',
                endT: this.queryTime?.endT || ''
            };
            const { data } = await ycsbqj(params);
            this.visualMapData = data || [];
            return Promise.resolve();
        },
        /**
         * @param {Object} source 要插入的字符串
         * @param {Object} start 开始位置
         * @param {Object} newStr 插入的字符串
         */
        insertStr(source, start, newStr) {
            return source.slice(0, start) + newStr + source.slice(start);
        },
        /**
         * @method 根据某个字段对整个数组对象去重
         * @parmas arr:要去重的数组
         * @params field:字段名
         */
        unique(arr, field) {
            const map = new Map();
            return arr.filter(
                (item) => !map.has(item[field]) && map.set(item[field], 1)
            );
        },
        //获取振动趋势图
        async getTrendPic() {
            try {
                let obj = {
                    SBID: this.curEquit?.SBXH || '',
                    startT: this.queryTime?.startT || '',
                    endT: this.queryTime?.endT || ''
                };
                let res = await getZdqs(obj);
                if (res.data && res.data[0].LIST) {
                    this.trendData = res.data[0].LIST;
                    let trendType = res.data[0].ZNSBLX;
                    let yAxis = res.data[0].YZ;
                    await this.getDelayTime();
                    res.data[0].visualMapData = this.visualMapData;

                    this.allData.push(res.data[0]);
                    // 根据设备id对allData去重，防止重复push
                    this.allData = this.unique(this.allData, 'SBID');

                    this.$nextTick(() => {
                        this.initChart(
                            this.trendData,
                            yAxis,
                            trendType,
                            (this.visualMapData = [])
                        );
                    });
                } else {
                    this.option.xAxis.data = [];
                }
            } catch (e) {
                console.log(e);
            }
        },
        /**
         * @param {Object} data
         * @param {Object} yAxis y轴数据
         * @param {Object} trendType 设备类型
         * @param {Object} visualMapData 延迟上报时段
         */
        initChart(data, yAxis, trendType, visualMapData = []) {
            // console.log('visualMapData',visualMapData)
            // visualMapData = [
            // 	{
            // 		startT:"2023-05-22 14:30",
            // 		endT:"2023-05-24 16:30"
            // 	},
            // 	{
            // 		startT:"2023-05-26 09:30",
            // 		endT:"2023-05-27 10:10"
            // 	}
            // ]

            let self = this;
            // 时间轴
            this.option.xAxis[0].data = [];
            this.option.series[0].data = [];
            this.option.yAxis[0].max = null;
            let max = 0;
            for (let i = 0; i < data.length; i++) {
                const row = data[i];
                let time = this.$dayjs(
                    row.JCSJ.slice(5, 16).replace(/-/g, '/')
                ).format('MM/DD HH:mm');
                this.option.xAxis[0].data.push(time);
                if (row.NL - max > 0) {
                    max = row.NL;
                }
                this.option.series[0].data.push(row.NL);
            }

            this.option.xAxis[1].data = [...this.x];

            if (visualMapData.length) {
                let xAxisData = data.map((item) => item.JCSJ);
                let visualMapResult = [];
                for (let i = 0; i < visualMapData.length; i++) {
                    let gt = null;
                    let lt = null;
                    let startDiff = Infinity;
                    let endDiff = Infinity;
                    for (let j = 0; j < xAxisData.length; j++) {
                        let start = this.$dayjs(visualMapData[i].startT).unix();
                        let end = this.$dayjs(visualMapData[i].endT).unix();
                        let current = this.$dayjs(xAxisData[j]).unix();
                        let diffStart = Math.abs(current - start);
                        let diffEnd = Math.abs(current - end);
                        if (diffStart < startDiff) {
                            gt = j;
                            startDiff = diffStart;
                        }
                        if (diffEnd < endDiff) {
                            lt = j;
                            endDiff = diffEnd;
                        }
                    }
                    visualMapResult.push({
                        gt,
                        lt,
                        color: 'red'
                    });
                }
                this.option.visualMap = [
                    {
                        show: false,
                        dimension: 0,
                        seriesIndex: 0,
                        pieces: visualMapResult,
                        outOfRange: {
                            color: '#3873ff'
                        }
                    }
                ];
                //console.log('visualMapResult',visualMapResult)
            }

            let nameWarnStart = this.warnStart.substring(5);
            let nameWarnEnd = this.warnEnd.substring(5);
            let xAxisWarnStart =
                ' ' + this.insertStr(this.warnStart.substring(5), 6, '\n');
            let xAxisWarnEnd =
                ' ' + this.insertStr(this.warnEnd.substring(5), 6, '\n');
            this.option.series[1].markArea = {
                itemStyle: {
                    color: 'rgba(255, 173, 177, .6)'
                },
                data: [
                    [
                        {
                            name: `预警时间段\n  ${nameWarnStart}~\n${nameWarnEnd}`,
                            //xAxis:' 03/20 \n19:48',
                            xAxis: nameWarnStart
                        },
                        {
                            xAxis: nameWarnEnd
                        }
                    ]
                ],
                label: {
                    show: true,
                    color: '#c6403c' // set the text color to red
                }
            };
            this.option.series[0].markLine.data[0].yAxis = yAxis;
            this.option.series[0].name =
                trendType == 'DL'
                    ? this.curEquit?.SBMC + '电流量'
                    : this.curEquit?.SBMC + '振动能量';
            this.option.yAxis[0].name =
                trendType == 'DL' ? '电流量（A）' : '能量';
            this.option.yAxis[0].nameTextStyle.padding =
                trendType == 'DL' ? [0, 0, 10, 14] : [0, 36, 10, 0];
            if (yAxis - max > 0) {
                this.option.yAxis[0].max = yAxis;
            }
            this.option.tooltip.backgroundColor = this.isControlEquit
                ? this.colorArrReverse[this.curIndex]
                : this.colorArr[this.curIndex]; //设置背景图片 rgba格式
            this.option.tooltip.borderColor = this.isControlEquit
                ? this.colorArrReverse[this.curIndex]
                : this.colorArr[this.curIndex]; //设置边框颜色
            this.option.color = this.isControlEquit
                ? this.colorArrReverse[this.curIndex]
                : this.colorArr[this.curIndex];

        },
        toFullScreen() {
            uni.navigateTo({
                url: `/pages/realtime/detail/FullScreenTendencyChart?dateStr=${
                    this.dateStr
                }&curEquit=${encodeURIComponent(JSON.stringify(this.curEquit))}`
            });
        },
        timeChange(v) {
            this.initChartData();
        }
    }
};
</script>
<script module="echarts" lang="renderjs">
let myChart
export default {
	data() {
		return {
			chartw: "",
			charth: '',
			flag: false,
			name: '',
		}
	},
	mounted() {
		if (typeof window.echarts === 'function') {
			this.initEcharts()
		} else {
			// 动态引入较大类库避免影响页面展示
			const script = document.createElement('script')
			// view 层的页面运行在 www 根目录，其相对路径相对于 www 计算
			script.src = 'static/echarts.js'
			script.onload = this.initEcharts.bind(this)
			document.head.appendChild(script)
		}
	},
	methods: {
		initEcharts() {
			let myDom = document.getElementById('echarts');
			myDom.setAttribute("style", "display:block;height:500px,width:100%;");
			myChart = echarts.init(myDom)
			// 观测更新的数据在 view 层可以直接访问到
			myChart && myChart.setOption(this.option)
		},
		updateEcharts(newValue, oldValue, ownerInstance, instance) {
			let myDom = document.getElementById('echarts');
			myDom.setAttribute("style", "display:block;height:500px,width:100%;");
			if (!newValue) {
				return;
			}
			let option = JSON.parse(JSON.stringify(newValue));
			let insertStr = function(source, start, newStr) {
				//source 要插入的字符串
				//start 开始位置
				// newStr 插入的字符串
				return source.slice(0, start) + newStr + source.slice(start)
			}
			option.xAxis[0].axisLabel.formatter = function(value) {
				value = '  ' + insertStr(value, 6, ' \n')
				return value
			}
			option.tooltip.formatter = function(params) {
				var res = '<div style="font-size:12px">振动时间：' + params[0].name;
				for (var i = 0; i < params.length; i++) {
					res += '<br>' + params[i].seriesName + '：' + params[i].data;
				}
				return res + '<div>';
			}
			option.tooltip.confine = true;
			// option.tooltip.formatter = function(obj) {
			// 	let data = obj.data;
			// 	return `<div style="background:${data.statusLabel== '停止'?'#ff7054':(data.statusLabel== '开启'?'#7dcd27':'#ccc')};color:#fff;padding:6px;opacity:0.9;border-radius:6px;border:1px solid #ddd;">          ${data.name}</br>
			//        开始时间：${data.start.slice(0,16)} </br>
			//        结束时间：${data.end} </br>
			//        状态：${data.statusLabel} </br>
			//      </div>`;
			// }
			// 监听 service 层数据变更
			myChart && myChart.clear()
			myChart && myChart.setOption(option)
			myChart && myChart.resize()
		},
	}
}
</script>
<style scoped>
/* #chart {
	height: calc(100vh - 200rpx);
} */
.echarts {
    width: 100%;
    height: 500rpx;
}

.ic-full {
    z-index: 1;
}

.icon {
    display: flex;
    justify-content: flex-end;
}

.quanping {
    width: 30rpx;
}

.pic {
    width: 130rpx;
    border-radius: 20rpx;
    position: relative;
    bottom: -8rpx;
}

.qiye-tu .pingmian {
    width: 100%;
}
</style>
