/*
 * @Author: your name
 * @Date: 2021-05-07 17:44:23
 * @LastEditTime: 2021-06-30 16:33:55
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: /YDZF_APP/common/document.js
 */
export const open = (path) => {
	if(typeof path === 'undefined' || path === null || path === '') {
		return
	}
	let File = plus.android.importClass('java.io.File')
	let Intent = plus.android.importClass('android.content.Intent')
	
	let wordFile = new File(path)
	if(wordFile.exists()){
		let openIntent = new Intent(Intent.ACTION_VIEW)
		openIntent.setPackage('cn.wps.moffice_eng')
		let Uri = plus.android.importClass("android.net.Uri")
		let FileProvider = plus.android.importClass('io.dcloud.common.util.DCloud_FileProvider')
		
		let mainActivity = plus.android.runtimeMainActivity()
		let packName = mainActivity.getPackageName()
		let fileUri = FileProvider.getUriForFile(mainActivity, `${packName}.dc.fileprovider`, wordFile)
		openIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
		openIntent.setDataAndType(fileUri, 'application/msword')
		mainActivity.startActivity(openIntent)
	} else {
	}
}