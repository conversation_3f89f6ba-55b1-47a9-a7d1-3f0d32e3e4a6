<!--
 * @Author: your name
 * @Date: 2021-04-06 15:11:47
 * @LastEditTime: 2021-05-19 18:07:57
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /UNI_APP_ShanDong/pages/component/image-print.vue
-->
<template>
	<view class="imageprint"
		  @click.stop="onPreviewAttach()">
		<swiper style="height: 1200rpx;"
				:indicator-dots="true"
				:current='0'
				:autoplay="false">
			<swiper-item v-for="(pic, index) in imageUrl"
						 item-id='0'
						 :key="index">
				<image mode='widthFix'
					   :src="pic" />
			</swiper-item>
		</swiper>
	</view>
</template>

<script>
	export default {
		props: {
			imageUrl: [String, Array]
		},

		watch: {
			imageUrl(val) {
				this.current = 0
			}
		},

		data() {
			return {
				current: 0
			}
		},

		methods: {
			onPreviewAttach() {
				uni.previewImage({
					urls: this.imageUrl
				})
			}
		},
	};
</script>

<style scoped>
	.imageprint {
		width: 100%;
		height: 100%;
	}

	.imageprint image {
		width: 100%;
	}
</style>
