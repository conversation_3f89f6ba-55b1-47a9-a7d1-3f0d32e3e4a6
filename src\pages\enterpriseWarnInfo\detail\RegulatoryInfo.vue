<template>
	<!-- 监管信息 -->
	<div class="tabs1-con">
		<ul class="qiye-info">
			<li>
				<div class="lp">
					<p class="p1">企业状态：</p>
				</div>
				<div class="rp">
					<p>
						<image class="pic" mode="widthFix" :src="getFilePath(enterpriseState)"></image>
					</p>
				</div>
			</li>
			<li>
				<div class="lp">
					<p class="p1">环保联系：</p>
				</div>
				<div class="rp">
					<p class="p2">{{enterpriseInfo.HBLXR}}</p>
				</div>
			</li>
			<li>
				<div class="lp">
					<p class="p1">联系方式：</p>
				</div>
				<div class="rp">
					<p class="p2 tel" @click="call(enterpriseInfo.HBLXRDH)">{{enterpriseInfo.HBLXRDH || '-'}}</p>
				</div>
			</li>
			<li>
				<div class="lp">
					<p class="p1">企业地址：</p>
				</div>
				<div class="rp">
					<p class="p2 local" @click="opLocaltion">
						{{ enterpriseInfo.DWDZ&&enterpriseInfo.DWDZ.slice(0, 18) || '-' }}{{ enterpriseInfo.DWDZ&&enterpriseInfo.DWDZ.length > 13 ? '...' : '' }}
					</p>
				</div>
			</li>
		</ul>

		<div class="gap"></div>
		<div class="qiye-board">
			<ul class="qiye-data1">
				<li>
					<div class="ic">
						<image class="icon" mode="widthFix"
							src="@/static/app/enterpriseDetail/images/qiye-data1-ic1.png" alt="">
					</div>
					<div class="rp">
						<p class="p1">生产线</p>
						<p class="p2">{{lineInfo.SCX_COUNT || 0}}</p>
					</div>
				</li>
				<li>
					<div class="ic">
						<image class="icon" mode="widthFix"
							src="@/static/app/enterpriseDetail/images/qiye-data1-ic2.png" alt="">
					</div>
					<div class="rp">
						<p class="p1">治污线</p>
						<p class="p2">{{lineInfo.ZWX_COUNT || 0}}</p>
					</div>
				</li>
				<li>
					<div class="ic">
						<image class="icon" mode="widthFix"
							src="@/static/app/enterpriseDetail/images/qiye-data1-ic3.png" alt="">
					</div>
					<div class="rp">
						<p class="p1">生产设备</p>
						<p class="p2">{{lineInfo.SCSB_COUNT || 0}}</p>
					</div>
				</li>
				<li>
					<div class="ic">
						<image class="icon" mode="widthFix"
							src="@/static/app/enterpriseDetail/images/qiye-data1-ic4.png" alt="">
					</div>
					<div class="rp">
						<p class="p1">治污设备</p>
						<p class="p2">{{lineInfo.ZWSB_COUNT || 0}}</p>
					</div>
				</li>
			</ul>
		</div>

		<div class="gap"></div>
		<div class="qiye-board">
			<div class="zy-form">
				<div class="item nfx">
					<div class="zy-line">
						<p class="qiye-til1">企业大门图</p>
					</div>
					<ul class="pd-ulpic1">
						<li v-for="(item, index) in wryFileList" :key="item.WJID">
							<div class="picbox">
								<image class="picture" :src="getFileUrl(item)" alt="" @click="
										previewImage(wryFileList, index)
									" />
							</div>
						</li>
					</ul>
					<div class="no-data" v-if="wryFileList.length == 0">暂无数据</div>
				</div>
				
				
				<div class="item nfx">
					<div class="zy-line">
						<p class="qiye-til1">企业平面图</p>
					</div>
				
					<ul class="pd-ulpic1">
						<li v-for="(item, index) in arrPlanDispose" :key="item.pmtxh">
							<div class="picbox">
								<image class="picture" :src="getPmtFileUrl(item)" alt="" @click="toPmtFullScreen(item)" />
								<div style="position:absolute;bottom:0;left:0;background:rgba(0,0,0,.6);
									color:#fff;white-space: nowrap; 
									  overflow: hidden;          
									  text-overflow: ellipsis;
										 text-align: center;
										 line-height:50rpx;
										  height:50rpx;
										  width:100%;
										  font-size:24rpx;
										  border-radius: 0 0 6rpx 6rpx;
									">
									{{item.pmtmc}}
								</div>
							</div>
						</li>
					</ul>
					<div class="no-data" v-if="arrPlanDispose.length == 0">暂无数据</div>
				</div>
				
				
<!-- 				<div class="item nfx">
					<div class="zy-line">
						<p class="qiye-til1">企业平面图</p>
					</div>

					<ul class="pd-ulpic1">
						<li v-for="(item, index) in enterpriseFileList" :key="item.WJID">
							<div class="picbox">
								<image class="picture" :src="getFileUrl(item)" alt="" @click="
										previewImage(enterpriseFileList, index)
									" />
							</div>
						</li>
					</ul>
					<div class="no-data" v-if="enterpriseFileList.length == 0">暂无数据</div>
				</div>
				 -->
				
				
				
				<div class="item nfx">
					<div class="zy-line">
						<p class="qiye-til1">生产车间全景图</p>
					</div>

					<ul class="pd-ulpic1">
						<li v-for="(item, index) in productWorkshopFileList" :key="item.WJID">
							<div class="picbox">
								<image class="picture" :src="getFileUrl(item)" alt="" @click="
										previewImage(productWorkshopFileList, index)
									" />
							</div>
						</li>
					</ul>
					<div class="no-data" v-if="productWorkshopFileList.length == 0">暂无数据</div>
				</div>
				<div class="item nfx">
					<div class="zy-line">
						<p class="qiye-til1">治污线全景图</p>
					</div>
					<ul class="pd-ulpic1">
						<li v-for="(item, index) in pollutControlFileList" :key="item.WJID">
							<div class="picbox">
								<image class="picture" :src="getFileUrl(item)" alt="" @click="
										previewImage(pollutControlFileList, index)
									" />
							</div>
						</li>
					</ul>
					<div class="no-data" v-if="pollutControlFileList.length == 0">暂无数据</div>
				</div>
			</div>
			<!-- 		<div class="zy-line ac jb">
				<p class="qiye-til1">企业平面图</p>
				<i class="qiye-quanping">
					<image v-if="enterpriseData.QYPMT_WJID" class="quanping" mode="widthFix"
						src="@/static/app/enterpriseDetail/images/qiye-quanping.png" @click="goFullScreen" alt="">
				</i>
			</div> -->
			<!-- <div class="gap"></div> -->
			<!-- 企业平面图轮播 -->
			<!-- 			<view class="uni-margin-wrap">
				<swiper v-if="picList.length != 0"  class="swiper" circular 
				 :indicator-dots="indicatorDots" :autoplay="autoplay"
					:interval="interval" :duration="duration">
					<swiper-item v-for="(item,index) in picList" :key="index">
						<view class="swiper-item uni-bg-red">
							<image class="pingmian" mode="aspectFit" 
							style="width:100%;height:100%;"
								:src="getFileUrl(item)" />

						</view>
					</swiper-item>
				</swiper>
			</view> -->

			<!-- <div class="qiye-tu">
				<image v-if="picList.length == 0" mode="widthFix"
				 class="emptypic"
					src="@/static/app/enterpriseDetail/images/empty.png" alt="">
			</div>
 -->
		</div>

		<div class="gap"></div>
		<div class="qiye-board">
			<div class="zy-line">
				<p class="qiye-til1">工艺流程图</p>
			</div>
			<div class="gap"></div>
			<div class="qiye-tabs2">
				<div class="item" :class="currentscx==item.SCXID?'cur':''" @click="changeProductLine(item,index)"
					v-for="(item, index) in scxlist">
					<p>{{ item.SCXMC }}</p>
					<!-- <image v-if="item.ISYC == 'true'" src="@/static/app/enterpriseDetail/images/qiye-lamp.png" class="lamp"> -->
				</div>
			</div>
			<div class="gap"></div>
			<div class="qiye-tu">
				<image @click="toFullScreen(webviewSrc)" v-if="webviewSrc" mode="widthFix"
					style="width:100%;height:auto;" :src="webviewSrc" alt="">
					<image v-if="!webviewSrc" mode="widthFix"
						style="width:40%;margin:40rpx auto 80rpx auto;display: block;"
						src="@/static/app/enterpriseDetail/images/empty.png" alt="">
			</div>
		</div>

		<div class="gap"></div>
	</div>

</template>

<script>
	import {
		getFilelist,
	} from '@/api/iot/appendix.js';
	import {
		getBasecount,
		getDwzb,
	} from '@/api/iot/runningData.js';
	import {
		queryPmtxx
	} from '@/api/iot/planeFigure.js';
	import {
		DOWNLOAD_URLZDY
	} from '@/common/config.js';
	export default {
		props: {
			enterpriseData: {
				type: Object,
				default: () => {}
			},
			enterpriseState: {
				type: String,
				default: ''
			},
			wrybh: {
				type: String,
				default: ''
			},

		},
		watch: {
			wrybh: {
				handler: function(nv) {
					this.getlineInfo()
				},
			},

		},
		data() {
			return {
				picList: [],
				indicatorDots: true,
				autoplay: true,
				interval: 2000,
				duration: 500,
				tabArr: [{
						name: '监管信息',
						value: 'regulatoryInfo'
					},
					{
						name: '实时监控',
						value: 'realTimeMonitor'
					},
					{
						name: '预警信息',
						value: 'earlyWarningInfo'
					}
				],
				curType: 'regulatoryInfo',
				enterpriseInfo: {}, //企业基本信息
				lineInfo: {}, //生产线信息
				previewList: [], //预览列表
				scxlist: [], //生产线
				webviewSrc: '',
				currentscx: '', //当前生产线
				flowPicList: [], //工艺流程图
				wryFileList: [], //企业大门图
				enterpriseFileList: [], //企业平面图
				productWorkshopFileList: [], //生产车间全景图
				pollutControlFileList: [] ,//治污线全景图
				arrPlanDispose:[],

			};
		},
		watch: {
			enterpriseData: {
				handler: function(newVal) {
					console.log('enterprise', newVal)
					this.enterpriseInfo = newVal.qyjbxx;
					this.wrybh = this.enterpriseInfo.WRYBH;
					this.previewList.push(this.enterpriseData.QYPMT_WJID);
					//this.enterpriseData.QYPMT_WJID = "1662359787917021307392,1662359787917021307392,1662359787917021307392"
					this.wryFileList = this.enterpriseData.WRYZP_WJID && this.enterpriseData.WRYZP_WJID.split(',') ||
					[]
					this.enterpriseFileList = this.enterpriseData.QYPMT_WJID && this.enterpriseData.QYPMT_WJID.split(
						',') || []
					this.productWorkshopFileList = this.enterpriseData.SCQJT_WJID && this.enterpriseData.SCQJT_WJID
						.split(',') || []
					this.pollutControlFileList = this.enterpriseData.ZWQJT_WJID && this.enterpriseData.ZWQJT_WJID
						.split(',') || []
					this.getflowPic();
					this.scxlist = newVal.scxList;
					let scxLen = newVal.scxList.length;
					if (scxLen) {
						this.currentscx = newVal.scxList[0].SCXID;
						this.currentscxname = newVal.scxList[0].SCXMC;
						let url = '';
						if (this.scxlist[0].GYLCT_WJID) {
							url = this.getFileUrl(this.scxlist[0].GYLCT_WJID)
							this.webviewSrc = url
						}

					}
					this.queryPmtList(this.wrybh)
				},
				deep: true
			}

		},
		onLoad(option) {
			let userinfo = uni.getStorageSync('user_info');
		},
		created() {
			this.getlineInfo()
		},
		methods: {
			//平面图全屏
			toPmtFullScreen(item){
				uni.navigateTo({
					url: `/pages/planeFigure/FullScreenPlaneFigureCanvas?pmtxh=${item.pmtxh}&pmtmc=${item.pmtmc}&wrybh=${this.wrybh}`
				});
			},
			//查询平面图数组信息
			async queryPmtList(wrybh) {
				const data = await queryPmtxx(wrybh);
				data.forEach((e) => {
					e.dwsj = JSON.parse(e.dwsj);
				});
				this.arrPlanDispose = data;
				if (this.arrPlanDispose.length) {
					this.pmtxh = this.arrPlanDispose[0].pmtxh;
					// this.$refs.planeFigureCanvasRef.init(this.pmtxh);
				}
				 console.log('this.arrPlanDispose', this.arrPlanDispose);
			},
			//获取图片路径
			getPmtFileUrl(item) {
					return DOWNLOAD_URLZDY + item.pmtxh;
			},
			//获取图片路径
			getFileUrl(item) {
					return DOWNLOAD_URLZDY + item.WJID;
				
			},

			//预览
			previewImage(fileList, index) {
				let fileUrls = fileList.map((file) => {
					return this.getFileUrl(file);
				});
				// 预览图片
				uni.previewImage({
					current: index,
					urls: fileUrls
				});
			},
			//切换生产线
			changeProductLine(val, index) {
				this.currentscx = val.SCXID
				this.currentscxname = val.SCXMC
				let url = '';
				if (this.scxlist[0].GYLCT_WJID) {
					url = this.getFileUrl(this.scxlist[index].GYLCT_WJID)
					this.webviewSrc = url
				}

			},
			//平面图全屏
			goFullScreen() {
				let picId = ''
				if (this.enterpriseData.QYPMT_WJID) {
					picId = this.enterpriseData.QYPMT_WJID
				}
				uni.navigateTo({
					url: `/pages/realtime/detail/FullScreenPlanPic?PICID=${picId}`
				})
			},
			//工艺全屏
			toFullScreen(src) {
				uni.navigateTo({
					url: `/pages/realtime/detail/FullScreenGongyiPic?SRC=${src}`
				})
			},
			//获取平面图点位
			getflowPic() {
				if (this.enterpriseData.QYPMT_WJID) {
					let obj = {
						WJID: this.enterpriseData.QYPMT_WJID
					}
					getDwzb(obj).then(res => {})
				} else {
					return;
				}

			},

			//获取图片路径
			getFileUrl(item) {
				return DOWNLOAD_URLZDY + item;
			},
			//预览
			previewImage(fileList, index) {
				let fileUrls = fileList.map(file => {
					return this.getFileUrl(file);
				});
				// 预览图片
				uni.previewImage({
					current: index,
					urls: fileUrls
				});
			},
			//获取状态图片
			getFilePath(v) {
				return v == '0' ?
					require('@/static/app/images/mk3a.png') :
					v == '1' ?
					require('@/static/app/images/mk1a.png') :
					v == '2' ?
					require('@/static/app/images/mk2a.png') :
					require('@/static/app/images/tingzhi.png');
			},
			//生产线，生产设备信息
			getlineInfo() {
				let obj = {
					WRYBH: this.wrybh
				}
				getBasecount(obj).then(res => {
					this.lineInfo = res.data[0];
				})
			},
			//获取样式
			getStateClass(state) {
				switch (state) {
					case '1':
						return 'p2'
						break;
					case '2':
						return 'p2'
						break;
					default:
						return 'p3'
				}
			},
			// 唤起系统导航app进行导航
			opLocaltion() {
				uni.getLocation({
					success: res => {
						uni.openLocation({
							latitude: parseFloat(this.enterpriseInfo.WD),
							longitude: parseFloat(this.enterpriseInfo.JD),
							name: this.enterpriseInfo.WRYMC,
							scale: 8
						});
					}
				});
			},
			call(phone) {
				console.log('传入的电话', phone);
				const res = uni.getSystemInfoSync();

				// ios系统默认有个模态框
				if (res.platform == 'ios') {
					uni.makePhoneCall({
						phoneNumber: phone,
						success() {
							console.log('拨打成功了');
						},
						fail() {
							console.log('拨打失败了');
						}
					})
				} else {
					//安卓手机手动设置一个showActionSheet
					uni.showActionSheet({
						itemList: [phone, '呼叫'],
						success: function(res) {
							console.log(res);
							if (res.tapIndex == 1) {
								uni.makePhoneCall({
									phoneNumber: phone,
								})
							}
						}
					})
				}

			},
			//tab切换
			changeTab(item) {
				this.curType = item.value;
			},
			back() {
				uni.navigateBack({
					delta: 1
				});
			}
		}
	}
</script>

<style lang="less">
	@import '@/static/app/enterpriseDetail/css/common.css';
	@import '@/static/app/enterpriseDetail/css/reset.css';
	@import '@/static/app/enterpriseDetail/css/zy08-20.css';

	/deep/ .uni-margin-wrap {
		width: 800rpx;
		width: 100%;
	}

	.swiper {
		height: 500rpx;
	}

	.swiper-item {
		display: block;
		height: 500rpx;
		line-height: 500rpx;
		text-align: center;
	}

	.swiper-list {
		margin-top: 40rpx;
		margin-bottom: 0;
	}

	.uni-common-mt {
		margin-top: 60rpx;
		position: relative;
	}

	.info {
		position: absolute;
		right: 20rpx;
	}

	.uni-padding-wrap {
		width: 550rpx;
		padding: 0 100rpx;
	}

	.ic .icon {
		width: 90rpx;
	}



	.swiper /deep/ .uni-swiper-dot {
		background: rgba(61, 107, 240, .4);
	}

	.swiper /deep/ .uni-swiper-dot-active {
		background-color: #3d6bf0;
	}

	.qiye-quanping .quanping {
		width: 30rpx;
	}

	.pic {
		width: 130rpx;
		border-radius: 20rpx;
		position: relative;
		bottom: -8rpx;
	}

	.qiye-tu .pingmian {
		width: 100%;
	}

	.qiye-tu .emptypic {
		width: 40%;
		margin: 30rpx auto 80rpx auto;
		display: block;
	}

	.pd-ulpic1 {
		flex-wrap: wrap;
		align-items: flex-start;
	}

	.pd-ulpic1 li {
		position: relative;
		width: 33.33%;
		margin-left: 0;
		margin-top: 12rpx;
	}

	.pd-ulpic1 li .delImg {
		position: absolute;
		right: 0;
		top: 0;
		width: 48rpx;
		height: 48rpx;
	}

	.pd-pic {
		height: 360rpx;
	}

	.zy-form .item {
		display: block;
		// justify-content: space-between;
		padding: 10px 0;
		border-bottom: 1px solid #eee;
		// flex-direction: column;
	}

	.pd-ulpic1 {
		flex-wrap: wrap;
		align-items: flex-start;
	}

	.pd-ulpic1 li {
		position: relative;
		width: 33.33%;
		height: 150rpx;
		margin-left: 0;
		margin-top: 6px;
		overflow: hidden;
		background: #fff;
		padding: 10rpx;
	}

	.pd-ulpic1 li .picture {
		display: block;
		width: 100%;
		height: 100%;
		border-radius: 10rpx;
	}

	.pd-ulpic1 li .picbox {
		border-radius: 10rpx;
		width: 100%;
		height: 100%;
		position: relative;
	}

	.no-data {
		text-align: center;
		color: #999;
		line-height: 50rpx;
		font-size: 24rpx;
	}
</style>
