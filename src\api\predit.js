/*
 * @Author: your name
 * @Date: 2021-03-23 12:22:37
 * @LastEditTime: 2021-05-24 15:30:09
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /UNI_APP_ShanDong/api/predit.js
 */
import axios from '@/common/ajaxRequest.js'
import {ULR_BASE} from '@/common/config.js'

export const queryXcjcRwsl = data => {
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: {
		  service : 'QUERY_XCJCRWSL_SERVICE'
	  }
    });
};

//查询预警信息
export const queryYjxx = data => {
	data.service = 'QUERY_YWXJ_YJXX_SERVICE';
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};

//查询预警信息的汇总数据
export const queryFltj = data => {
	data.service = 'QUERY_YWXJ_FLTJ_SERVICE';
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};

// 预警信息确认 
export const queryYjxxQr = data => {
	data.service = 'QUERY_YWXJ_YJQR_SERVICE';
    return axios.request({
      method: 'post',
      url: ULR_BASE,
      data: data
    });
};