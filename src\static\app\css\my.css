.yy-tit {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24.1545749rpx;
    background-color: #fff;
    height: 84.54105rpx;
    line-height: 84.54105rpx;
    border-bottom: 1px solid #eee;
}

.yy-tit h1 {
    font-size: 28.985475rpx;
    color: #333;
    background: url(~@/static/app/images/linebg.png) no-repeat left center;
    padding-left: 22.5rpx;
    background-size: 10.8696rpx auto;
    font-weight: bold;
}

.yy-tit .edit {
    width: 26.570025rpx;
    height: 26.570025rpx;
    background: url(~@/static/app/images/edit.png) no-repeat center center;
    background-size: 100% 100%;
}

.yy-tit .add {
    width: 27.77775rpx;
    height: 27.77775rpx;
    background: url(~@/static/app/images/addbtn.png) no-repeat center center;
    background-size: 100% 100%;
}

.del {
    width: 27.77775rpx;
    height: 27.77775rpx;
    background: url(~@/static/app/images/delbtn.png) no-repeat center center;
    background-size: 100% 100%;
}

.adrr {
    width: 30.7971rpx;
    height: 30.7971rpx;
    background: url(~@/static/app/images/adress.png) no-repeat center center;
    background-size: 100% 100%;
}

.zy-input1.center {
    text-align: center;
}

.zy-input1.adript {
    padding-right: 22.5rpx;
}

.zy-form .item .rfont {
    font-size: 27.77775rpx;
    color: #666;
    width: 100%;
    text-align: right;
}

.zy-form .item {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.w70 {
    width: 70%;
}

.pd-tablelst1a {
    min-width: 100%;
    overflow-x: scroll;
}

.pd-tablelst1a tr td,
.pd-tablelst1a .tr .td {
    background: #fff;
    font-size: 27.77775rpx;
    color: #333;
    text-align: left;
    height: 72.4638rpx;
}

.pd-tablelst1a .tr {
    display: flex;
    border-bottom: 1px solid #eee
}

.pd-tablelst1a .thead {
    padding-left: 22.5rpx;
    padding-right: 24.1545749rpx;
}

.pd-tablelst1a .tbody {
    background-color: #FFFFFF;
    padding-left: 22.5rpx;
    padding-right: 24.1545749rpx;
}

.pd-tablelst1a .tbody .tr {
    display: flex;
    border-bottom: 1px solid #eee;
}

.pd-tablelst1a .tr .td {
    /* box-sizing: content-box; */
    display: flex;
    align-items: center;
}

.pd-tablelst1a tr td:first-child {
    padding-left: 22.5rpx;
}

.pd-tablelst1a thead tr td,
.pd-tablelst1a .thead,
.pd-tablelst1a .thead .tr .td {
    background: #f0f9ff;
    color: #42677f;
    height: 84.54105rpx;
}

.pd-tablelst1a .thead .tr .td {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.pd-tablelst1a thead tr td {}

.zy-form .item .rp {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.zy-form .item .label {
    width: 30%;
}

.zy-input1 {
    width: 100%;
}

.userbg {
    height: 386.473425rpx;
    background-color: #4874ff;
}

.userlogo {
    text-align: center;
    padding-top: 105rpx;
}

.userlogo>img {
    display: inline-block;
    width: 138.8889rpx;
    height: 138.8889rpx;
    margin: auto;
}

.userlogo .name {
    color: #fff;
    font-size: 28.985475rpx;
    margin-top: 22.5rpx;
    font-weight: 600;
}

.arr {
    font-size: 28rpx;
    color: #999;
    line-height: 39.855075rpx;
    padding: 0 30.1932rpx;
    background: url(~@/static/app/images/select-right.png) right center no-repeat;
    background-size: 11.473425rpx;
}

.quitbtn {
    background-color: #fff;
    font-size: 33.81645rpx;
    color: #333;
    text-align: center;
    width: 100%;
    height: 84.54105rpx;
    line-height: 84.54105rpx;
    margin-top: 60.386475rpx;
}

.jdipt {
    width: 180rpx;
    height: 54.34785rpx;
    line-height: 54.34785rpx;
    text-align: center;
    background-color: #f1f1f1;
    border-radius: 6.038625rpx;
}

.mr20 {
    margin-right: 18.11595rpx;
}

.zy-form .item.remark {
    align-items: flex-start;
}

.zy-form .item .label.remark {
    color: #666;
}

.zy-form .item .label {
    flex: 1;
}

.zy-form.info .item .label {
    width: 30%;
    flex: unset;
}

.inner {
    padding-top: 0;
}

.userbg {
    height: 386.473425rpx;
    background-color: #4874ff;
}

.userlogo {
    text-align: center;
    padding-top: 105rpx;
}

.userlogo>image {
    display: inline-block;
    width: 138.8889rpx;
    height: 138.8889rpx;
    margin: auto;
}

.userlogo .name {
    color: #fff;
    font-size: 28.985475rpx;
    margin-top: 22.5rpx;
    font-weight: 600;
}

.arr {
    font-size: 28rpx;
    color: #999;
    line-height: 39.855075rpx;
    padding: 0 30.1932rpx;
    background: url(~@/static/app/images/select-right.png) right center no-repeat;
    background-size: 11.473425rpx;
}

.quitbtn {
    background-color: #fff;
    font-size: 33.81645rpx;
    color: #333;
    text-align: center;
    width: 100%;
    height: 84.54105rpx;
    line-height: 84.54105rpx;
    margin-top: 60.386475rpx;
}

.zy-form {
    padding-left: 30.1932rpx;
    background-color: #fff;
}

.zy-form .item {
    display: flex;
    justify-content: space-between;
    padding: 21.1353rpx 0;
    padding-right: 30.1932rpx;
    border-bottom: 1px solid #eee;
}

.zy-form .item .label {
    font-size: 28rpx;
    color: #333;
    line-height: 39.855075rpx;
    position: relative;
}

.zy-form .item .label.star::after {
    content: "*";
    position: absolute;
    left: -12.077325rpx;
    top: 0;
    color: red;
}

.zy-form .item .rp {
    display: flex;
}

.zy-form .item .rfont {
    font-size: 27.77775rpx;
    color: #666;
    width: 100%;
    text-align: right;
}
