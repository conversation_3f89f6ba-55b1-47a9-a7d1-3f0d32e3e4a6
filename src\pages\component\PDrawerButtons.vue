<template>
	<view class="power-drawer-buttons">
		<text class="power-drawer-text power-drawer-reset"
			  @click="reset">重置</text>
		<text class="power-drawer-text power-drawer-confirm"
			  @click="clickConfirm()">确定</text>
	</view>
</template>

<script>
	export default {
		data() {
			return {

			}
		},
		methods: {
			reset() {
				this.$emit('reset');
			},

			clickConfirm() {
				this.$emit('confirm')
			}
		}
	}
</script>

<style scoped>
	.power-drawer-buttons {
		display: flex;
		flex-flow: row nowrap;
		justify-content: center;
		align-items: center;
		width: 100%;
	}

	.power-drawer-text {
		font-size: 16px;
		line-height: 36px;
		text-align: center;
	}

	.power-drawer-reset {
		flex: 1;
		color: #333;
		border-top: 1px solid #999;
		border-bottom: 1px solid #999;
	}

	.power-drawer-confirm {
		flex: 1;
		color: #fff;
		background-color: #1e8eef;
		border-right: 1px solid #1e8eef;
		border-top: 1px solid #1e8eef;
		border-bottom: 1px solid #1e8eef;
	}
</style>
