import axios from '@/common/ajaxRequest.js'
//import {ULR_BASE} from '@/common/config.js'

let ULR_BASE = 'http://yapi.powerdata.com.cn:31665/mock/437/plat';

// 今日超标企业
export const queryJrcbqy = data => {
    return axios.request({
      method: 'get',
      url: ULR_BASE + '/bi/zxjc/queryJrcbqy',
      params: data
    });
};


// 各地市监控企业和超标企业
export const queryGdsjkqyhcbqy = data => {
    return axios.request({
      method: 'get',
      url: ULR_BASE + '/bi/zxjc/queryGdsjkqyhcbqy',
      params: data
    });
};


// 企业信息
export const queryQyxi = data => {
    return axios.request({
      method: 'get',
      url: ULR_BASE + '/bi/zxjc/queryQyxi',
      params: data
    });
};


// 公司基本信息排口信息
export const gsjbxxpk = data => {
    return axios.request({
      method: 'get',
      url: ULR_BASE + '/bi/zxjc/gsjbxxpk',
      params: data
    });
};


// 排口监测数据
export const pkjcsj = data => {
    return axios.request({
      method: 'get',
      url: ULR_BASE + '/bi/zxjc/pkjcsj',
      params: data
    });
};

// 各因子最近监测数据
export const queryGyzzjjcsj = data => {
    return axios.request({
      method: 'get',
      url: ULR_BASE + '/bi/zxjc/queryGyzzjjcsj',
      params: data
    });
};
