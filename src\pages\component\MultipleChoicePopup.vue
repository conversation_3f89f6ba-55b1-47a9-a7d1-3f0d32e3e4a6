<!-- @format -->

<template>
    <div>
        <u-popup
            v-model="isShow"
            mode="bottom"
            width="100%"
            height="50%"
            border-radius="20"
            @close="$emit('update:isShow', false)"
        >
            <div class="opration">
                <span @click="cancel" class="cancel">取消</span>
                <span @click="confirm" class="confirm">确定</span>
            </div>
            <div class="listWarp">
                <p
                    v-for="item in arrSelectList"
                    :key="item.value"
                    @click="select(item)"
                    :class="
                        arrSelectedItems.includes(item.value || item.label)
                            ? 'on'
                            : ''
                    "
                >
                    {{ item.label }}
                </p>
            </div>
        </u-popup>
    </div>
</template>

<script>
export default {
    name: 'DataCollectionAppMultipleChoicePopup',
    props: {
        //是否显示
        isShow: {
            type: Boolean,
            default: false
        },
        //选项数据
        arrSelectList: {
            type: Array,
            default: () => []
        },
        //从外部传入的当前选中数据
        strCurrentSeleted: {
            type: String,
            default: ''
        }
    },
    watch: {
        isShow(newVal, oldVal) {
            if (newVal && this.strCurrentSeleted) {
                this.matchCurrentSeleted();
            }
        }
    },
    data() {
        return {
            //组件选中项
            arrSelectedItems: []
        };
    },

    mounted() {},

    methods: {
        confirm() {
            this.$emit('update:isShow', false);
            this.$emit('confirm', this.arrSelectedItems);
        },
        select(item) {
            let index = this.arrSelectedItems.findIndex((el) => {
                return el == item.value;
            });
            if (index != -1) {
                this.arrSelectedItems.splice(index, 1);
            } else {
                this.arrSelectedItems.push(item.value);
            }
        },
        cancel() {
            this.arrSelectedItems = [];
            this.$emit('update:isShow', false);
            this.$emit('cancel');
        },
        //打开时高亮当项目
        matchCurrentSeleted() {
            this.arrSelectedItems = this.strCurrentSeleted.split(',');
        }
    }
};
</script>

<style lang="scss" scoped>
.listWarp {
    width: 100%;
    height: 100%;
    overflow-y: scroll;
}
.listWarp p {
    width: 100%;
    padding: 20rpx;
    text-align: center;
    border-bottom: 1px solid #efefef;
}
.opration {
    padding: 20rpx;
    display: flex;
    justify-content: space-between;
    width: 100%;
    background-color: #fff;
}
.confirm {
    color: rgb(60, 170, 255);
}
.on {
    color: rgb(60, 170, 255);
}
</style>
