/** @format */
import originalAxios from 'axios-miniprogram';
import axios from '@/common/ajaxRequest.js';
import { ULR_BASE,ULR_BASE1,ULR_BASE2,BASE_URL,URL_BASE_ENTERPRISE_DETAIL} from '@/common/config.js';

// 设备运行状态
export const sbsxzt = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/monitor/cxzt',
        params: data
    });
};

//状态日历
export const qyztrl = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE1 + '/sjcl/api/20179/zhyj/qyztrl',
        params: data
    });
};


//状态日历
export const getScrl = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/monitor/scx/scrl',
        params: data
    });
};

//振动趋势
export const getZdqs = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/baseinfo/zdqs',
        params: data
    });
};

//生产线、治污线、生产设备、治污设备数量
export const getBasecount = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/statistics/basecount',
        params: data
    });
};

//预警事件接口
export const getWarningEventList = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/warning/event/list',
        params: data
    });
};

//平面图、工艺流程图设备点位坐标
export const getDwzb = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/monitor/dwzb',
        params: data
    });
};