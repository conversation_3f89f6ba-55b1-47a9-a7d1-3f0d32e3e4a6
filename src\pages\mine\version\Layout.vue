<template>
	<div class="zy-form">
		<div class="item" :class="{'item-1':index == 0}" v-for="(item, index) in list" :key="index">
			<div class="left" @click="toDetail(item)">
				<p class="label tit">
					版本{{item.BBMC || '-' }}主要更新
				</p>
				<p class="label date">
					{{item.CJSJ&&item.CJSJ.slice(0,11) || '-' }}
				</p>
			</div>
			<div class="imgbox" @click="toDetail(item)">
				<image src="@/static/app/images/arwrt1.png" class="pd-arw1" />
			</div>
		</div>
	</div>
</template>

<script>
	import {
		appVersion
	} from '@/api/iot/version.js';
	export default {
		data() {
			return {
				list: []
			};
		},
		onLoad() {

		},
		mounted() {
			this.getAppVersion()
		},
		methods: {
			async getAppVersion() {
				let params = {
					APPID: 'device'
				}
				const {
					data
				} = await appVersion(params)
				this.list = data
			},
			toDetail(item) {
				uni.navigateTo({
					url: '/pages/mine/version/Detail?item=' + encodeURIComponent(JSON.stringify(item))
				})
			}
		}
	};
</script>


<style scoped>
	.zy-form {
		padding: 0rpx;
	}

	.zy-form .item {
		padding: 26rpx 20rpx 40rpx 20rpx;
	}

	.left {
		width: 300rpx;
	}

	.zy-form .item .label {
		width: 100%;
		line-height: 40rpx;
	}

	.zy-form .item .date {
		color: #999;
		font-size: 30rpx;
	}

	.zy-form .item .tit {
		font-size: 31rpx;
		color: #333;
		line-height: 80rpx;
	}

	.zy-form .item-1 .tit {
		font-weight: bold;
	}

	.zy-form .item .imgbox {
		width: 34rpx;
		height: 34rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
</style>
