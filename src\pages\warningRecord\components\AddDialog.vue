<!-- @format -->

<template>
	<div style="width: 100%; height: 100%" >
		<div class="mask" style="display: block" @click="$emit('hide')"></div>
		<div class="yj-alert">
			<h3>运维反馈</h3>
           <div class="form-content">
			   <div class="choosevalue">
			   	<p class="p1 is-valid"><label class="redx">*</label>是否有效: </p>
			   	<radio-group class="radio-group" @change="radioChange">
			   		<label class="uni-list-cell uni-list-cell-pd" v-for="(item, index) in isValidList"
			   			:key="item.value">
			   			<view>
			   				<radio :value="item.value" :checked="index === current" />
			   			</view>
			   			<view>{{item.name}}</view>
			   		</label>
			   	</radio-group>
			   </div>
			   <p class="p1"><label class="redx">*</label>反馈人员:</p>
			   <input v-model="model.FKR" placeholder="请输入核实人员" placeholder-style="color:#ccc;"></input>
			   <p class="p1"><label class="redx">*</label>联系电话:</p>
			   <input v-model="model.FKRDH" placeholder="请输入联系电话" placeholder-style="color:#ccc;"></input>
			   <p class="p1"><label class="redx">*</label>反馈内容:</p>
			   <textarea v-model.trim="model.FKNR" class="textarea1" placeholder="请输入问题发生原因及处理方案" style="text-align: left;"
			   	placeholder-style="color:#ccc;" :maxlength='50'></textarea>
			   <p class="p1"><label class="redx">*</label>处理建议:</p>
			   <textarea v-model.trim="model.CZCS" class="textarea1" placeholder="请输入下一步处理建议" style="text-align: left;"
			   	placeholder-style="color:#ccc;" :maxlength='50'></textarea>
			   <div class="nfx">
			   	<em class="zmzl">上传附件：
			   		<!-- <span v-if="pageType == 'add' ">（上传照片/视频）</span> -->
			   	</em>
			   	<div class="gap"></div>
			   	<AppendixImage ref="appendix" :list.sync="fileList" fileTypeKeyword="YJFK" childTypeKeyword="YJFK"
			   		:isCanEdit="true" :uploadId="model.FKID"></AppendixImage>
			   </div>
			 
		   </div>
		   <div class="zy-line ac jc">
		   	<p class="btn1" @click="$emit('hide')">取消</p>
		   	<p class="btn2" @click="FeedbackAdd">确定</p>
		   </div>
		</div>
		<u-top-tips ref="uTips"></u-top-tips>
	</div>
</template>

<script>
	import AppendixImage from '@/pages/component/AppendixImage'
	import {
		getWarningFeedbackAdd
	} from '@/api/iot/warningRecord.js';

	import {
		guid
	} from '@/common/uuid.js';

	export default {
		components: {
			AppendixImage
		},
		props: {
			warning: {
				type: Object,
				default: () => {
					return null
				}
			},
		},
		data() {
			return {
				model: {
					FKDX: 4, //反馈对象（运维端这里都写4）
					FKID: '', //反馈记录ID（在前端生成）
					FKR: '', //反馈人，运维端需手动填入
					FKRDH: '', //联系电话
					FKNR: '', //处置情况
					CZCS: '', //处置措施
					SFYX:'',//是否有效
				},
				rules: {
					SFYX: {
						required: true,
						message: '请填写反馈是否有效'
					},
					FKR: {
						required: true,
						message: '请填写反馈人员'
					},
					FKRDH: {
						required: true,
						message: '请填写联系电话'
					},
					FKNR: {
						required: true,
						message: '请填写反馈内容'
					},
					CZCS: {
						required: true,
						message: '请选择处理建议'
					},
				},
				isValidList: [{
						value: '1',
						name: "有效"
					},
					{
						value: '0',
						name: "无效"
					},
				],
				curValid: '',
				current: '',
				info: {},
				fileList: [],
				userinfo: {}
			};
		},
		mounted() {
			let userinfo = uni.getStorageSync('user_info');
			this.userinfo = userinfo;
			this.model.FKR = this.userinfo.name;
			this.model.FKRDH = this.userinfo.mobile;
			this.model.FKID = guid()
		},
		methods: {
			//预警反馈录入
			FeedbackAdd() {
				let rules = Object.keys(this.rules);
				for (let i = 0; i < rules.length; i++) {
					let field = rules[i];
					let requires = this.rules[field];
				
					if (
						(!this.model[field] || !this.model[field].length) &&
						requires.required
					) {
						uni.showToast({
							icon: 'none',
							title: requires.message
						});
						return;
					}
				}
				
				if (!this.isPhone(this.model.FKRDH, "联系电话")) {
					return;
				}
				
				const params = {
					YJID: this.warning.ID, //预警ID
					YJBH: this.warning.YJBH, //预警编号
					YJSJID: this.warning.YJSJID,
				};
				Object.assign(params, this.model)
				getWarningFeedbackAdd(params).then((res) => {
					if (res.data.status == '000') {
						this.$refs.uTips.show({
							title: '提交成功',
							type: 'success',
							duration: '1000'
						});
						setTimeout(() => {
							this.$emit('hide');
						}, 300);
					}
				});
			},
			radioChange(v) {
				this.model.SFYX= v.target.value;
				console.log('SFYX',this.model.SFYX)
			},
			isPhone(reg_tel, message) {
				var reg = /^\d{11}$/;
				if (!reg.test(reg_tel)) {
					uni.showToast({
						title: '请输入有效的' + message,
						icon: 'none',
						duration: 1000
					})
			
					return false
				}
				return true;
			
			},
			
		}
	};
</script>

<style scoped>
	.yj-alert h3{
		margin-bottom: 5px;
	}
	.yj-alert {
		height: 1000rpx;
		overflow-y: scroll;
		padding:40rpx 20rpx;
		width:620rpx;
	}
	
	.yj-alert input {
		width: 100%;
		background: #FCFCFC;
		border-radius: 4rpx;
		border: 2rpx solid #E3E3E3;
		font-size: 30rpx;
		color: #333;
		line-height: 60rpx;
		height: 60rpx;
		box-sizing: border-box;
		padding: 0rpx 20rpx;
		resize: none;
		margin-bottom: 30rpx;
		font-family: "Microsoft YaHei";
	}
	

	.uni-label-pointer {
		cursor: pointer;
		display: flex;
		flex-direction: row;
		margin-right: 60rpx;
	}

	.radio-group {
		display: flex;
	}

	.radio-group .uni-list-cell {
		margin-right: 10rpx;
	}

	radio {
		transform: scale(0.7)
	}

	/deep/.uni-textarea-textarea {
		text-align: left;
	}

	.yj-alert .p1 {
		margin-top: 20rpx;
	}

	.yj-alert .textarea1 {
		margin-bottom: 30rpx;
	}

	.choosevalue {
		display: flex;
		align-items: center;
	}

	.radio-group {
		position: relative;
		top: 7rpx;
		left: 10rpx;
	}
	.redx {
	    color: #eb4444;
	    width: 10px;
	    display: inline-block;
		margin-right: 4rpx;
	}
	.is-valid{
		position: relative;
		top: 5rpx;
		font-size: 29.166675rpx;
	}
	.form-content{
		height:calc(100% - 150rpx);overflow-y: scroll;
	}
	.zy-line{
		padding-top: 
		12rpx;
	}
</style>
