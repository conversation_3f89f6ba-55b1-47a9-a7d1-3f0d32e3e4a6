/** @format */
import originalAxios from 'axios-miniprogram';
import axios from '@/common/ajaxRequest.js';
import { ULR_BASE } from '@/common/config.js';

// 企业详情-产治污线
export const getCzwxList = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/baseinfo/czwx/list',
        params: data
    });
};

// 治污设备列表
export const getZwsbList = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/az/zwsb/list',
        params: data
    });
};

// 新增报备接口
export const getQybbAdd = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/qybb/add',
        params: data
    });
};

// 报备列表接口
export const getQybbList = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/qybb/list',
        params: data
    });
};

// 企业报备审核提交接口
export const getExamine = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/qybb/examine',
        params: data
    });
};

// 查询设备数据
export const getZdsbList = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/baseinfo/zdsb/list',
        params: data
    });
};
