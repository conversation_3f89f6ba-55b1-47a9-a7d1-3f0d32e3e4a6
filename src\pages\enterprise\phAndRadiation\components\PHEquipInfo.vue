<!-- @format -->

<template>
    <div>
        <div class="zy-form">
            <div class="item">
                <p class="label star">设备安装时间</p>
                <div class="rp zy-selectBox" v-show="!isDetailPageShow()">
                    <p-mui-date-picker
                        @confirm="sdConfirm"
                        dateType="SECOND"
                        format="YYYY-MM-DD HH:mm:ss"
                    >
                        <input
                            style="width: 330rpx"
                            type="text"
                            v-model="form.AZSJ"
                            placeholder="请选择时间"
                            class="date-ipt res"
                            disabled
                        />
                    </p-mui-date-picker>
                </div>
                <div class="rp detail" v-show="isDetailPageShow()">
                    {{ form.AZSJ }}
                </div>
            </div>
            <div class="item">
                <p class="label star">
                    绑定生产线
                    <image
                        src="@/static/app/images/whic.png"
                        class="pd-whic1"
                        @click="showRemindModal"
                    >
                    </image>
                </p>
                <div class="rp" v-show="!isDetailPageShow()">
                    <div class="zy-selectBox" @click="showScx()">
                        <p v-if="!form.SCXMC" class="res">请选择</p>
                        <p else class="res">{{ form.SCXMC }}</p>
                    </div>
                </div>
                <div class="rp detail res" v-show="isDetailPageShow()">
                    {{ form.SCXMC || '-' }}
                </div>
            </div>
            <div class="item zy-selectBox">
                <p class="label star">报警阈值</p>
                <div
                    class="rp detail res"
                    @click="$emit('setShowSetPh')"
                    style="height: 40rpx"
                ></div>
            </div>
        </div>
        <u-select
            value-name="SCXID"
            label-name="SCXMC"
            mode="single-column"
            :list="scxList"
            v-model="scxShow"
            @confirm="selectProduceLine"
        ></u-select>
        <RemindModal
            v-show="isShowRemindModal"
            @close="isShowRemindModal = false"
            :content="content"
			height="330rpx"
        ></RemindModal>
    </div>
</template>

<script>
import { getScxList, getScxsbList } from '@/api/iot/enterprise.js';
import RemindModal from '@/pages/component/RemindModal.vue';
export default {
    name: 'DataCollectionAppPHEquipInfo',
    components: {
        RemindModal
    },
    props: {
        //父组件表单
        model: {
            type: Object,
            default: () => {}
        },
        //污染源数据
        info: {
            type: Object,
            default: () => {}
        },
        //页面类型
        pageType: {
            type: String,
            default: ''
        },
        //是否固定产线
        fixedLine: {
            type: Boolean,
            default: false
        }
    },
	// watch: {
	// 	form: {
	// 		handler(newVal, oldVal) {
	// 			if (this.pageType == 'add') {
	// 				//this.backPage = true;
	// 				this.$emit("update:backPage",true)
	// 			}
	// 		},
	// 		deep: true,
	// 	},
	// },
    data() {
        return {
            form: {
                AZSJ: '',
                SCXMC: '',
                SCXID: '',
                CXMC: '',
                CXID: '',
                QTYZ: '0#0'
            },
            canNotEditIMEI: false, //超过24小时不可以编辑IMEI
            scxShow: false,
            scxList: [], //生产线
            relativeEquips: [], //关联设备
            isShowRemindModal: false,
            content: ''
        };
    },
    mounted() {
        //新增就获取当前时间
        if (this.pageType === 'add') {
            this.form.AZSJ = this.$dayjs().format('YYYY-MM-DD HH:mm:ss');
        }

        this.initProductLineList();
    },

    methods: {
        showRemindModal() {
            this.isShowRemindModal = true;
            this.content =
                '1.绑定产线后如该生产线正在生产，pH值若出现超标（超出上限值或下限值），则会进行预警。 2.绑定产线后如该产线没有生产，pH值若出现超标（超出上限值或下限值），不会进行预警。';
        },

        //详情显示，新增编辑隐藏
        isDetailPageShow() {
            return this.pageType === 'detail';
        },
        initProductLineList() {
            let { WRYBH } = this.info;
            getScxList({
                ORGID: uni.getStorageSync('ORGID') || '',
                WRYBH: WRYBH
            }).then((res) => {
                if (res.data && res.data.length > 0) {
                    this.scxList = res.data;
                }
            });
        },
        sdConfirm(obj) {
            this.form.AZSJ = obj.time;
        },
        showScx() {
            if (this.fixedLine) {
                uni.showToast({
                    title: '当前生产线已绑定，不可选择',
                    icon: 'none'
                });
                return;
            }
            this.scxShow = true;
        },
        //选择生产线
        async selectProduceLine(v) {
            try {
                await this.validIsHadEquipName(v[0].value);
                this.form.SCXID = v[0].value;
                this.form.SCXMC = v[0].label;
            } catch (error) {
                console.log('error');
                this.form.SCXID = '';
                this.form.SCXMC = '';
            }
        },
        //校验生产线是否已经存在设备名称
        validIsHadEquipName(scxid) {
            return new Promise((resolve, reject) => {
                //选择生产线前校验生产线上是否已经存在相同的名称的设备
                getScxsbList({
                    SCXID: scxid
                }).then((res) => {
                    this.relativeEquips = res.data || [];
                    let objSameNameEquip = this.relativeEquips.find(
                        (item) => item.SBMC === this.form.SBMC
                    );
                    if (objSameNameEquip) {
                        uni.showToast({
                            title: '该生产线已有设备重名，请重新输入',
                            icon: 'none'
                        });
                        return reject();
                    } else {
                        return resolve();
                    }
                });
            });
        },
        setForm(form) {
            Object.assign(this.form, form);
            console.log('this.form', this.form);
        }
    }
};
</script>

<style lang="scss" scoped></style>
