<!--
 * @Author: your name
 * @Date: 2021-03-23 12:22:37
 * @LastEditTime: 2021-06-10 11:35:51
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: /YDZF_APP/components/no-data.vue
-->
<template>
	<view>
		<div class="box-region">
			<image v-if="type == 'message'" :src="nomessageIcon" style="width:451rpx;height:344rpx; display: inline-block;"></image>
			<image v-if="type == 'data'" :src="nodataIcon" style="width:451rpx;height:344rpx; display: inline-block;"></image>
		</div>
	</view>
</template>

<script>
import nodataIcon from '@/static/images/no-data.png'
import nomessageIcon from '@/static/images/none-message.png'
	export default {
		props: {
			type: {
				type: String,
				default: 'data' // message data
			}
		},
		data(){
			return {
				nodataIcon,
				nomessageIcon
			}
		}
	}
</script>

<style>
	.box-region{
		display: flex;
		justify-content: center; /* 水平居中 */
		align-items: center;     /* 垂直居中 */
		width: 100%;
		height: 75vh;
	}
</style>
