<!-- @format -->

<template>
	<div>
		<section class="main1">
			<div class="head">
				<div class="date-form">
					<div class="left-label">优化日期：</div>
					<span class="green">
						{{ lastOptimizTime || '-'}}</span>
				</div>
				<image mode="widthFix" src="~@/static/app/images/update.png" class="update" @click="getData" />
			</div>

			<div style="height: 100%; position: relative; overflow-y: scroll">
				<div class="head">
					<div class="cs-xlbo" v-show="!isLandScape">
						<div class="left-label">选择日期：</div>
						<div class="cs-xlbo-right" @click="showDatePicker = true">
							<u-icon name="calendar" class="calendar"></u-icon>
							{{ curDate }}
						</div>
					</div>
				</div>
				<div class="head">
					<div class="cs-xlbo" v-show="!isLandScape">
						<div class="left-label">当前设备：</div>
						<div class="wh-slec1" @click="show = !show">
							{{ curEquip }}
							<u-icon name="arrow-right" class="arrow"></u-icon>
						</div>
					</div>

				</div>

				<div class="yy-mod">
					<div class="yy-tablebx1">
						<div>
							<table class="yy-tablelst2a" cellpadding="0">
								<colgroup>
									<col width="160rpx" />
									<col width="520rpx" />
									<col width="400rpx" />
									<col width="300rpx" />
									<col width="400rpx" />
									<col width="400rpx" />
								</colgroup>
								<thead>
									<tr>
										<td>序号</td>
										<td>时间</td>
										<td>信号功率</td>
										<td>信噪比</td>
										<td>信号质量</td>
										<td>小区基站</td>
									</tr>
								</thead>
							</table>
							<div class="yy-tableinner" :style="{
                                    height: isLandScape
                                        ? 'calc(100vh - 200rpx)'
                                        : 'calc(100vh - 340rpx)'
                                }">
								<table class="yy-tablelst2a" cellpadding="0">
									<colgroup>
										<col width="160rpx" />
										<col width="520rpx" />
										<col width="400rpx" />
										<col width="300rpx" />
										<col width="400rpx" />
										<col width="400rpx" />
									</colgroup>
									<tbody>
										<tr v-for="(item, index) in tableData" :key="index">
											<td>{{ index + 1 }}</td>
											<td>{{ item.SBSJ || '-' }}</td>
											<td>{{ item.SP || '-' }}</td>
											<td>{{ item.SINR || '-' }}</td>
											<td>{{ item.RSRQ || '-' }}</td>
											<td>{{ item.PCI || '-' }}</td>
										</tr>
									</tbody>
								</table>
								
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>
		<div class="mask" style="display: block" v-if="showSelect" @click="closeMask"></div>
		<div class="pd-botalt" v-if="showSelect">
			<div class="pd-altbd">
				<div class="pd-tit1">预警时间</div>
				<ul class="pd-ulbtn2a">
					<li :class="{ on: item == typeVal }" v-for="(item, index) in typeList" :key="index"
						@click="typeChange(item)">
						<p>{{ item }}</p>
					</li>
				</ul>
				<div class="pd-tit1">自定义</div>
				<div class="canderbox" @click="showCalender = !showCalender">
					<text>{{ startT || '超时时间' }}</text>
					<label>至</label>
					<text>{{ endT || '终止时间' }}</text>
				</div>
			</div>
			<ul class="pd-botbtn1a">
				<li class="czbtn" @click="reset">
					<!--     <image
                        src="~@/static/app/workbench/images/resetic.png"
                        alt=""
                        class="resetic"
                    /> -->
					<p>重置</p>
				</li>
				<li class="confirmbtn" @click="closeMask">确定</li>
			</ul>
		</div>

		<template v-if="show">
			<u-select v-model="show" :list="workshopList" @confirm="equipConfirm" :default-selector="curSelectArr"
				value-name="SBID" label-name="SBMC"></u-select>
		</template>

		<u-calendar v-model="showDatePicker" :mode="mode" @change="changeCalender"></u-calendar>
	</div>
</template>

<script>
	import {
		zdsblist,
		ztxxlist
	} from '@/api/iot/weakSignal.js';
	export default {
		name: '',
		data() {
			return {
				showSelect: false, //筛选是否显示
				show: false, //picker显示
				showCalender: false, //日历显示
				curEquip: '', //picker选中值
				typeList: [], //设备列表
				curSelectArr: [0], //当前选中设备
				keyords: '',
				tableData: [],
				isLandScape: false,
				showDatePicker: false,
				curDate: this.$dayjs().format('YYYY-MM-DD'),
				mode: 'date',
				workshopList: [],
				curSBID: '', //当前设备id
				info: {},
				lastOptimizTime: '' //最近优化时间
			};
		},
		onLoad(option) {
			//当前设备
			let equipObj = JSON.parse(decodeURIComponent(option.equip));
			this.curSBID = equipObj.SBID;
			this.curEquip = equipObj.SBMC;
			this.getData();
			//企业信息
			this.info = JSON.parse(decodeURIComponent(option.info));
			this.getWorkshopData();
		},
		mounted() {},
		// 监听尺寸变化
		onResize() {
			let _this = this;
			uni.getSystemInfo({
				success: function(res) {
					if (res.windowWidth > res.windowHeight) {
						// 横屏
						_this.isLandScape = true;
					} else {
						// 竖屏
						_this.isLandScape = false;
					}
				}
			});
		},
		methods: {
			//获取车间信息
			getWorkshopData() {
				let obj = {
					WRYBH: this.info.WRYBH,
					FXID: this.info.FXID
				};
				zdsblist(obj).then((res) => {
					if (res.status === '000') {
						this.workshopList = res.data;
					}
				});
			},
			updateTime(v) {
				this.startT = v.startT;
				this.endT = v.endT;
				this.getData();
			},
			// 筛选按钮点击
			handleSelect() {
				this.showDatePicker = !this.showSelect;
			},

			// 筛选中预警时间切换
			typeChange(v) {
				this.typeVal = v;
				this.setTime();
			},

			// 生产设备选中
			equipConfirm(v) {
				console.log('v', v);
				console.log('workshopList', this.workshopList);
				let index = this.workshopList.findIndex(
					(item) => item.SBID === v[0].value
				);
				console.log('index', index);
				this.curSelectArr = [index];
				console.log('curSelectArr', this.curSelectArr);
				this.curEquip = v[0].label;
				this.curSBID = v[0].value;
				this.show = false;
				this.getData();
			},
			//获取表格数据
			getData() {
				uni.hideKeyboard();
				this.tableData = [];
				ztxxlist({
					SBID: this.curSBID,
					DATE: this.curDate
				}).then((res) => {
					this.tableData = res.data.list;
					this.lastOptimizTime = this.tableData[0].SBSJ;
				});
			},
			// 日历选择
			changeCalender(v) {
				console.log('v', v);
				this.curDate = v.result;
				this.getData();
			}
		}
	};
</script>

<style lang="less" scoped>
		html { -webkit-tap-highlight-color: transparent; height: 100%; } 
		body { -webkit-backface-visibility: hidden; height: 100%;}
	.date-form{
		display: flex;
	}

	.head {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 15rpx;
	}

	.input-box {
		position: relative;
		width: 573.6111rpx;
	}

	.wh-slec1 {
		width: calc(100% - 180rpx);
		border: 1px solid #4874ff;
		border-radius: 4px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		color:#333;
	}

	.yy-tableinner {
		height: calc(100vh - 380rpx);
		overflow: auto;
		box-sizing: border-box;
		-ms-overflow-style: none;
		scrollbar-width: none;
		padding: 0;
		width: 100%;

		&::-webkit-scrollbar {
			display: none;
		}
	}

	.yy-tablelst2a {
		// table-layout: fixed;
		// width: 100%;
	}

	.yy-tablelst2a {
		// width: 100%;
	}

	.yy-tablelst2a tr td {
		font-size: 27.173925rpx;
		color: #333;
		text-align: left;
		height: 84.54105rpx;
		padding: 0 12.077325rpx;
	}

	.yy-tablelst2a thead tr td {
		background: #daebff;
		color: #3a96ff;
		font-weight: 600;
		text-align: center;
	}

	.yy-tablelst2a tbody tr td {
		color: #28618b;
		background: #f5f9ff;
		text-align: center;
		border-bottom: 1px dashed #ddd;
	}

	.search-btn {
		width: 70rpx;
		height: 70rpx;
		position: absolute;
		top: 20rpx;
		right: 180rpx;
	}

	.yy-mod {
		padding-left: 0;
	}
	
	.cs-xlbo_landScape {
		height: 50rpx;
		line-height: 50rpx;
		padding: 0 10rpx;
		margin-right: 20rpx;
	}

	.update {
		width: 40rpx;
	}

	.cs-xlbo {
		display: flex;
		width: 100%;
	}
.left-label{color:#999;width:180rpx;}
	.cs-xlbo-right {
		flex:1;
		border: 1px solid #4874ff;
		border-radius: 4px;
		color:#333;
	}

	.calendar {
		margin-right: 10rpx;
		color: #4874ff;
	}

	.arrow {
		color: #4874ff;
		font-size: 24rpx;
	}

	.green {
		color: rgb(63, 193, 97);
	}
</style>
