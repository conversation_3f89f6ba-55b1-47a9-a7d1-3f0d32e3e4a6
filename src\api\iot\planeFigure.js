/** @format */
import axios from '@/common/ajaxRequest.js';
const LOGIN_ULR_BASE = 'http://iot-manage.iotdi.com.cn/iotManage'
import { ULR_BASE, BASE_URL,URL_BASE_ENTERPRISE_DETAIL,UPLOAD_URL,IOTMANAGE_URL} from '@/common/config.js';

//查询点位信息
export const queryPmtxx = (wrybh) => {
    return axios.request({
        url: IOTMANAGE_URL + '/pmt/queryPmtxx/' + wrybh,
        method: 'get',
        params: {}
    });
};

//设置周期
export const pubCycle = (data) => {
    return axios.request({
        method: 'post',
        url: IOTMANAGE_URL + '/api/pub/cycle',
        data: data,
		headers: { 'Content-Type': 'application/json; charset=utf-8' }
    });
};
//设置阈值
export const pubthreshold = (data) => {
    return axios.request({
        method: 'post',
        url: IOTMANAGE_URL + '/api/pub/threshold',
        data: data,
		headers: { 'Content-Type': 'application/json; charset=utf-8' }
    });
};

//请求设备阈值与周期
export const getZnsbInfo = (data) => {
    return axios.request({
        method: 'GET',
        url: ULR_BASE + '/baseinfo/znsb/info',
        params: data
    });
};

//获取素材图例列表
export const getMaterialLegendList = (params) => {
    return axios.request({
        url: IOTMANAGE_URL + '/gylct/scktllb',
        method: 'get',
        params
    });
};

//设置设备与图例
export const saveEquipLegnend = (data) => {
    return axios.request({
        url: IOTMANAGE_URL + '/gylct/setSbytl',
        method: 'post',
        data,
		headers: { 'Content-Type': 'application/json; charset=utf-8' }
    });
};

//设置设备与图例
export const matchEquipPicture = (params) => {
    return axios.request({
        url: IOTMANAGE_URL + '/gylct/queryTlBySszl',
        method: 'get',
        params,
    });
};
