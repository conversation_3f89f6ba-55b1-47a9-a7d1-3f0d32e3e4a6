@charset "utf-8";

/* -- flex布局-- */
.flx1 {
    display: flex;
}

.flx1.ac {
    align-items: center;
}

.flx1.jc {
    justify-content: center;
}

.flx1.jb {
    justify-content: space-between;
}

.flx1.ja {
    justify-content: space-around;
}

.flx1.start {
    justify-content: flex-start;
}

.flx1.end {
    justify-content: flex-end;
}

.flx1.ev {
    justify-content: space-evenly;
}

.yy0706-topbg1 {
    height: 100%;
    background: #F6F7F9 url(~@/static/equipmentMaintenance/images/yy0706-topbg1.png) 0 0 no-repeat;
    background-size: 100% 262.5rpx;
    padding-top: 60rpx;
    box-sizing: border-box;
}

.yy0706-line1 {
    padding: 20.83335rpx 33.3333rpx;
    display: flex;
    align-items: center;
}

.inpsear1 {
    flex: 1;
    height: 73.611075rpx;
    line-height: 73.611075rpx;
    color: #fff;
    background: rgba(255, 255, 255, 0.35) url(~@/static/equipmentMaintenance/images/yy0706-sear.png) no-repeat 96% center;
    background-size: 32.638875rpx auto;
    padding-right: 65.97225rpx;
    border-radius: 34.722225rpx;
    font-size: 29.166675rpx;
}

.yy0706-topic {
    width: 44.444475rpx;
    height: 44.444475rpx;
    margin-left: 30rpx;
}

.yy0706-navtab1 {
    /*! background: #fff; */
    display: flex;
    justify-content: space-evenly;
    height: 84.7222499rpx;
}

.yy0706-navtab1 li {
    font-size: 31.94445rpx;
    color: rgba(255, 255, 255, 0.8);
    line-height: 84.7222499rpx;
    position: relative;
}

.yy0706-navtab1 li.on {
    color: #fff;
    font-weight: bold;
}

.yy0706-navtab1 li.on::after {
    content: "";
    display: block;
    width: 37.5rpx;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
    text-align: center;
    height: 5.55555rpx;
    border-radius: 2.08335rpx;
    background-color: #fff;
}

.yy0706cont-wrap {
    height: calc(100% - 250rpx);
    overflow-y: auto;
}

.yy0706-hd {
    position: relative;
}

.yy0706-hd .f1 {
    font-size: 31.94445rpx;
    color: #333;
    font-weight: bold;
    line-height: 83.333325rpx;
    height: 83.333325rpx;
}

.yydeal-btn {
    padding: 0 13.888875rpx;
    height: 48.6110999rpx;
    line-height: 48.6110999rpx;
    border-radius: 5.55555rpx;
    display: inline-block;
    font-size: 26.3889rpx;
}

.yydeal-btn.done {
    background: rgba(37, 189, 93, 0.1);
    color: #25BD5D;
}

.yydeal-btn.deal {
    background: rgba(255, 190, 35, 0.1);
    color: #FFA541;
}

.yy0706-table1 td {
    font-size: 29.166675rpx;
    height: 60rpx;
    vertical-align: top;
}

.yy0706-table1 td.color9 {
    color: #999;
}

.yy0706-ic1 {
    width: 29.166675rpx;
    height: 29.166675rpx;
    display: inline-block;
}

.yy0706-ic2 {
    width: 36.111075rpx;
    height: 36.111075rpx;
    display: inline-block;
}

.yy0706-tip {
    width: 31.94445rpx;
    height: 31.94445rpx;
    display: inline-block;
}

.yy0706-tipbox {
    background: #FFF0E5;
    padding: 0 22.5rpx;
    border-radius: 11.1111rpx;
    white-space: nowrap;
    font-size: 24.999975rpx;
    color: #A25D25;
    position: absolute;
    top: -101%;
    left: 11%;
    z-index: 999;
}

.yy0706-tipbox::before {
    content: '';
    border: 1px solid #FFF0E5;
    width: 13.888875rpx;
    height: 13.888875rpx;
    transform: rotate(-45deg);
    background-color: #FFF0E5;
    position: absolute;
    top: 100%;
    left: 50%;
    z-index: 999;
    border-top: none;
    border-right: none;
    margin-top: -7.5rpx;
    margin-left: -15rpx;
}

.yy0706-alert1 {
    position: absolute;
    left: 66.666675rpx;
    right: 66.666675rpx;
    top: 0;
    bottom: 0;
    margin: auto;
    height: 900.000025rpx;
    background: #FFFFFF;
    border-radius: 24.3055499rpx;
    z-index: 1000;
}

.yy0706-alert1 .hd {
    height: 105rpx;
    line-height: 105rpx;
}

.yy0706-alert1 .tit1 {
    font-size: 34.722225rpx;
    font-weight: bold;
    color: #1F1F39;
    text-align: center;
    text-align: center;
}

.yy0706-cls {
    width: 37.5rpx;
    height: 37.5rpx;
    display: inline-block;
    position: absolute;
    top: 34.722225rpx;
    right: 37.5rpx;
}

.yy0706-alert1 .bd {
    padding: 37.5rpx 34.722225rpx 0;
}

.yy0706-srow .f1 {
    font-size: 30.555525rpx;
    color: #666;
    width: 30%;
    display: inline-block;
}

.yy0706-srow .pd-inptxt1 {
    font-size: 30.555525rpx;
    color: #333;
    text-align: right;
    width: 3rem;
}

.yy0706-srow .dw {
    color: #333333;
    font-size: 31.94445rpx;
    margin-left: 15rpx;
    display: inline-block;
}

.yy0706-srow+.yy0706-srow {
    margin-top: 30rpx;
}

.yy0706-photof {
    font-size: 31.94445rpx;
    color: #333;
    margin-top: 34rpx;
}

.yy0706-uptu {
    width: 144.44445rpx;
    height: 144.44445rpx;
    display: inline-block;
    margin-top: 34.722225rpx;
}

.yy0706-confirmbtn {
    width: 222.222225rpx;
    margin: 0 auto;
    text-align: center;
    height: 79.861125rpx;
    line-height: 79.861125rpx;
    background: #357FFF;
    color: #fff;
    border-radius: 11.1111rpx;
    font-size: 31.94445rpx;
    font-weight: bold;
    position: absolute;
    bottom: 41.6667rpx;
    left: 50%;
    transform: translateX(-50%);
}


/* 2023/07/07 */
.yy0707-zkmod {
    background: #FFFFFF;
    border-radius: 18.055575rpx;
    margin-bottom: 20.83335rpx;
}

.yy0707-zkmod .hd {
    height: 93.75rpx;
    padding: 0 24.3055499rpx;
}

.yy0707-zkmod .bd {
    padding: 0 24.3055499rpx 0;
}

.yy0707-tit1 {
    font-size: 34.722225rpx;
    color: #333;
    /*  font-weight: bold; */
    padding-left: 27.77775rpx;
    position: relative;
}

.yy0707-tit1::before {
    content: '';
    position: absolute;
    left: 0;
    top: 51%;
    width: 9.7222499rpx;
    height: 33.3333rpx;
    background: #4874ff;
    border-radius: 5.55555rpx;
    transform: translateY(-50%);
}

.yy0707-arric {
    width: 22.9167rpx;
    height: 13.888875rpx;
    display: inline-block;
    background: url(~@/static/equipmentMaintenance/images/yy0707-down.png) no-repeat center center;
    background-size: 22.2222rpx auto;
}

.pd35 {
    padding: 116.6667rpx 24.3055499rpx;
}

.yy0707-row .p1 {
    font-size: 31.94445rpx;
    color: #333;
    font-weight: bold;
}

.yy0707-cbox+.yy0707-cbox {
    border-top: solid 1px #eee;
    padding-top: 22.5rpx;
}

.yy0707-zkmod.on .yy0707-arric {
    background: url(~@/static/equipmentMaintenance/images/yy0707-up.png) no-repeat center center;
    background-size: 22.2222rpx auto;
}

.yy0707-twrap1 {
    margin-top: 15rpx;
}
