<template>
	<!-- 阈值弹窗 -->
	<div class="dialog-box" >
		<div class="head">
			<image @click="showThresholdDialog = !showThresholdDialog" class="gray-icon"
				src="@/static/app/images/gray-arrow.png" mode="widthFix"></image>
			<div class="title">设置实际阈值</div>
			<span class="send" @click="sendThreshold">发送</span>
		</div>
		<div class="input-box">
			<input class='input-msg' type="number" v-model="setThreshold" placeholder="请输入实际阈值" />
			<div class="describe-box">
				实际阈值：设置判断被监控设备运行状态的阈值范围
			</div>
		</div>
		<div class="historybox" v-if="arrHistoryThreshold.length">
			<div class="update">
				<image class="icon" mode="widthFix" src="@/static/app/images/columnicon.png"></image>
				发送记录
			</div>
			<ul v-for="item in arrHistoryThreshold">
				<li>
					<view class="left-text">设备IMEI：</view>
					<view>{{item.IMEI || '-'}}</view>
				</li>
				<li>
					<view class="left-text">上报阈值：</view>
					<view>{{item.setThreshold || '-'}}</view>
				</li>
				<li>
					<view class="left-text">提交时间：</view>
					<view>{{item.setSendTime || '-'}}</view>
				</li>
			</ul>
		</div>
	</div>
	
</template>

<script>
	import ThresholdDialog from './components/ThresholdDialog'
	import appWatch from '@/utils/app-watch.js';
	import {
		pubCycle,
		pubthreshold,
		getZnsbInfo
	} from '@/api/iot/planeFigure.js'
	export default {
		components:{
			 ThresholdDialog
		 },
		data() {
			return {
				IMEI: '',
				barTitle: '参数设置',
				threshold: '', //阈值
				sendCycle: '', //上报周期
				sleepTime: '', //检测周期
				setThreshold: '', //阈值
				setSendCycle: '', //上报周期
				setSleepTime: '', //检测周期
				showThresholdDialog: false,
				showCycleDialog: false,
				arrHistoryThreshold: [

				], //历史阈值提交记录
				arrHistoryCycle: [], //历史周期提交记录
				userInfo: {}
			}
		},
		onLoad(option) {
			this.IMEI = option.IMEI;
			this.barTitle = this.IMEI + '参数设置'
			this.userInfo = uni.getStorageSync('userInfo');
			
			let arrHistoryThreshold = uni.getStorageSync('arrHistoryThreshold') || []
			let arrHistoryCycle = uni.getStorageSync('arrHistoryCycle') || []
			this.arrHistoryThreshold = arrHistoryThreshold.filter(item => item.IMEI === this.IMEI)
			this.arrHistoryCycle = arrHistoryCycle.filter(item => item.IMEI === this.IMEI)
		},
		mounted() {
			this.getZnsbInfo()
		},
		methods: {
			//设置默认值
			setValue(){
				this.setSleepTime = 300;
				this.setSendCycle = 120;
			},
			async getZnsbInfo() {
				let params = {
					IMEI: this.IMEI
				}
				const {
					data: {
						SLEEPTIME,
						SENDCYCLE,
						THRESHOLD
					}
				} = await getZnsbInfo(params)
				this.sleepTime = SLEEPTIME;
				this.sendCycle = SENDCYCLE;
				this.threshold = THRESHOLD.substr(0, THRESHOLD.indexOf('#'));
			},
			//设置阈值
			async sendThreshold() {
				if (this.setThreshold == '') {
					uni.showToast({
						title: '请填写阈值',
						icon: "none",
						duration: 1000
					});
					return;
				} else {
					let params = {
						"name": this.userInfo.name,
						"imeiList": [
							this.IMEI
						],
						"bz": "",
						"threshold": this.setThreshold + '#' + this.setThreshold
					}
					const res = await pubthreshold(params).catch(err => {
						console.log(err)
					})
					let historyObj = {
						IMEI: this.IMEI,
						setSendTime: this.$dayjs().format('YYYY-MM-DD HH:mm'),
						setThreshold: this.setThreshold
					}
					
					this.arrHistoryThreshold.unshift(historyObj)
					
					let arrHistoryThreshold = uni.getStorageSync('arrHistoryThreshold') || []
					arrHistoryThreshold.unshift(historyObj)
					uni.setStorageSync('arrHistoryThreshold', arrHistoryThreshold)
					
					uni.showToast({
						title: '发送成功'
					}).then(res => {
						this.showThresholdDialog = false;
					})
				}
			},
			//设置周期
			async sendNewCycle() {
				if (this.setSendCycle == '') {
					uni.showToast({
						title: '请填写上报周期',
						icon: "none",
						duration: 1000
					});
					return;
				} else if (this.setSleepTime == '') {
					uni.showToast({
						title: '请填写检测周期',
						icon: "none",
						duration: 1000
					});
					return;
				} else {
					let params = {
						"name": this.userInfo.name,
						"imeiList": [
							this.IMEI
						],
						"bz": "",
						"sendCycle": this.setSendCycle,
						"sleepTime": this.setSleepTime
					}
					const res = await pubCycle(params).catch(err => {
						console.log(err)
					})
					let historyObj = {
						IMEI: this.IMEI,
						setSendTime: this.$dayjs().format('YYYY-MM-DD HH:mm'),
						setSleepTime: this.setSleepTime,
						setSendCycle: this.setSendCycle,
					}
					
					this.arrHistoryCycle.unshift(historyObj)
					
					let arrHistoryCycle = uni.getStorageSync('arrHistoryCycle') || []
					arrHistoryCycle.unshift(historyObj)
					uni.setStorageSync('arrHistoryCycle', arrHistoryCycle)
					uni.showToast({
						title: '发送成功'
					}).then(res => {
						this.showCycleDialog = false;
					})
				}
			},
			//设置阈值弹窗
			toThresholdDialog() {
				this.showThresholdDialog = true;
				this.setThreshold = this.threshold;
			},
			//设置周期弹窗
			toCycleDialog() {
				this.showCycleDialog = true;
				this.setSleepTime = this.sleepTime;
				this.setSendCycle = this.sendCycle;
				appWatch.getClickWatchAPP({
				   url:'pages/realtime/SetImeiCircle',
				   clickModule:'设备周期设置'
				}); 
				
			},
		}
	}
</script>

<style>
</style>