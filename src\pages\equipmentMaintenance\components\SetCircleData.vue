<!-- @format -->
<template>
    <div>
        <div class="item circle">
            <image
                mode="widthFix"
                class="choose"
                src="@/static/images/choose.png"
                v-show="options.type === 'add'"
            />
            <p class="label" :class="{ star: options.type === 'add' }">
                发送检测周期指令
                <image
                    src="@/static/app/images/whic.png"
                    class="pd-whic1"
                    @click="toShowReminderPop('jczq')"
                >
                </image>
            </p>
            <div class="rp">
                <span v-show="isDisabled">{{ info.ywmx.jczq }}</span>
                <input
                    v-show="!isDisabled"
                    type="number"
                    class="zy-input1"
                    v-bind:value="info.ywmx.jczq"
                    @input="updateDetailData($event, 'jczq')"
                    :class="{ 'search-num': getFontColor('jczq') }"
                    :placeholder="info.ywmx.jczq.toString()"
                />
                <span class="unit"> 秒 </span>
            </div>
        </div>
        <div class="item circle">
            <image
                mode="widthFix"
                class="choose"
                src="@/static/images/choose.png"
                v-show="options.type === 'add'"
            />
            <p class="label" :class="{ star: options.type === 'add' }">
                发送上报周期指令
                <image
                    src="@/static/app/images/whic.png"
                    class="pd-whic1"
                    @click="toShowReminderPop('bsjg')"
                >
                </image>
            </p>

            <div class="rp">
                <span v-show="isDisabled">{{ info.ywmx.bsjg }}</span>
                <input
                    v-show="!isDisabled"
                    type="number"
                    v-bind:value="info.ywmx.bsjg"
                    @input="updateDetailData($event, 'bsjg')"
                    class="zy-input1"
                    :class="{ 'search-num': getFontColor('bsjg') }"
                    :placeholder="info.ywmx.bsjg.toString()"
                />
                <span class="unit"> 分 </span>
            </div>
        </div>
        <div class="item circle">
            <image
                mode="widthFix"
                class="choose"
                src="@/static/images/choose.png"
                v-show="options.type === 'add'"
            />
            <p class="label" :class="{ star: options.type === 'add' }">
                发送设置阈值指令
                <image
                    src="@/static/app/images/whic.png"
                    class="pd-whic1"
                    @click="toShowReminderPop('qtyz')"
                >
                </image>
            </p>
            <div class="rp">
                <span v-show="isDisabled">{{ info.ywmx.qtyz }}</span>
                <input
                    v-show="!isDisabled"
                    type="number"
                    v-bind:value="info.qtyz"
                    :disabled="isDisabled"
                    @input="updateData($event, 'qtyz')"
                    class="zy-input1"
                    :class="{ 'search-num': getFontColor('qtyz') }"
                    :placeholder="info.qtyz.toString()"
                />
            </div>
        </div>
        <ReminderPop
            v-show="isShowReminderPop"
            @close="isShowReminderPop = false"
            :content="content"
        ></ReminderPop>
    </div>
</template>

<script>
import ReminderPop from './ReminderPop';
import { getIMEIData } from '@/api/iot/equipmentMaintenance.js';
import { znsb } from '@/api/iot/enterprise.js';
export default {
    props: {
        info: {
            type: Object,
            default: () => {}
        },
        isDisabled: {
            type: Boolean,
            default: false
        },
        options: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            isShowReminderPop: false,
            imeiShow: false,
            imeiList: [],
            ywmx: {
                jczq: 0,
                bsjg: 0,
                qtyz: '0#0'
            },
            arrInitStyle: [
                {
                    name: 'jczq',
                    reminder: '默认显示原监控仪的检测周期，支持修改。',
                    isInit: true
                },
                {
                    name: 'bsjg',
                    reminder: '默认显示原监控仪的上报周期，支持修改。',
                    isInit: true
                },
                {
                    name: 'qtyz',
                    reminder: '默认显示原监控仪的阈值，支持修改。',
                    isInit: true
                }
            ],
            content: ''
        };
    },
    components: {
        ReminderPop
    },
    watch: {},
    onHide() {},
    created() {},
    mounted() {},
    onBackPress() {},
    methods: {
        //显示说明弹窗
        toShowReminderPop(v) {
            this.isShowReminderPop = true;
            let objTarget = this.arrInitStyle.find((item) => item.name === v);
            this.content = objTarget.reminder;
        },
        //绑定输入的值文字颜色
        getFontColor(name) {
            let targerObj = this.arrInitStyle.find((item) => item.name == name);
            return targerObj.isInit;
        },
        //输入新值
        updateDetailData(e, v) {
            console.log(e, v);
            this.arrInitStyle.forEach((item) => {
                if (item.name == v) {
                    item.isInit = false;
                }
            });
            let inputValue = e.target.value;
            this.$emit('update:info', {
                ...this.info,
                ywmx: { ...this.info.ywmx, [v]: inputValue }
            });
        },
        updateData(e, v) {
            this.arrInitStyle.forEach((item) => {
                if (item.name == v) {
                    item.isInit = false;
                }
            });
            console.log(e, v);
            let inputValue = e.target.value;
            this.$emit('update:info', {
                ...this.info,
                [v]: inputValue
            });
        },
        //更新表单
        updateInfo(newInfo) {
            for (const key in this.info) {
                this.$set(this.info, key, newInfo[key]);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.circle {
    .label {
        flex: 5 !important;
    }
    .rp {
        flex: 5 !important;
    }
    .choose {
        width: 34rpx;
        height: 34rpx;
        position: relative;
        z-index: 1;
        margin-right: 16rpx;
        margin-left: -8rpx;
    }
}
.search-num {
    color: #999;
}
.unit {
    margin-left: 10rpx;
}
</style>
