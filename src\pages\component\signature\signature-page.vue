<template>
	<Page :mainStyle="mainStyle" >
		<template v-slot:bar>
			<view></view>
		</template>
		<signature
			ref="signature"
			:width="width"
			:height="height"
			:hiddenCanvasHeight="hiddenCanvasHeight"
		/>
		<view class="flex-row-layout">
			<view class="power-button power-button-primary clear-button2" @click="onClickClear"><span>清除</span></view>
			<view class="power-button power-button-primary clear-button" @click="goBack"><span>取消</span></view>
			<view class="power-button power-button-primary" @click="onClickConfirm"><span>保存</span></view>
		</view>
	
	</Page>
</template>

<script>
	import Page from '@/pages/component/Page.vue';
	import signature from './signature.vue';
	import NaviBar from '@/pages/component/NaviBar.vue';
	
	import styleUtil from '@/common/style.js';
	
	export default {
		components: {
			Page, signature,NaviBar
		},
		
		data() {
			return {
				width: 300,
				height: 220,
				horizontalHeight:220,
				pageHeight: 700,
				hiddenCanvasHeight:1,
				signatureKey:''
			}
		},
		
		computed: {
			mainStyle: function() {
				let style = {
					width: 'calc(100% - 20px)',
					height: `${this.pageHeight}px`,
					padding: '0',
					'background-color': '#fff',
					'margin-top': '0',
					display: 'flex',
					'flex-direction': 'row-reverse',
					'align-items': 'center'
				}
				return style;
			}
		},
		
		onUnload(){
			// //设置竖屏
			// plus.screen.lockOrientation("portrait-primary");
			// //先跳转到一个空页面，解决下次页面跳转字体变大bug
			// uni.navigateTo({
			// 	url: '/pages/component/signature/signature-page-blank',
			// 	animationType: 'none',
			// 	animationDuration: 0
			// })
		},
		
		onShow() {
			
		},

		onLoad(options){
			//设置横屏
			// plus.screen.lockOrientation("landscape-primary");
			
			this.signatureKey = options.id
		},
			
		mounted() {
			let _self = this;
			styleUtil.getScreenLayout()
				.then(layout => {
					_self.width = layout.width - 20;
					_self.pageHeight = layout.height;
				})
		},
		
		methods: {
			goBack(){
				uni.navigateBack({
					delta: 1
				})
			},
			
			onClickConfirm() {
				uni.showLoading({
				    title: '生成中',
					mask:true
				});
				
				this.hiddenCanvasHeight = this.pageHeight
				setTimeout(()=>{
					this.getSignature()
				},500)
				
				
			},
			//生成签名
			getSignature(){
				this.$refs.signature.generate()
					.then(image => {
						uni.$emit(this.signatureKey, image);
						uni.hideLoading();
						uni.navigateBack({
							delta: 1
						})
					})
					.catch(() => {
						uni.hideLoading();
						this.hiddenCanvasHeight = 1
						uni.showToast({
							title: '请签名',
							duration: 2000,
							icon: 'none'
						});
					})
			},
			
			onClickClear() {
				this.$refs.signature.reset();
			}
		}
	}
</script>

<style scoped>
	.clear-button {
		background-color: #cccccc;
		color: #fff;
		border: 1px solid #cccccc;
	}
	.clear-button2 {
		background-color: #ff007f;
		color: #fff;
		border: 1px solid #ff007f;
	}
	.power-button{
		line-height: 140rpx;
		padding: 0;
		width: 80rpx;
		height: 340rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.power-button span{
		transform: rotate(90deg);
		display: block;
	}
	.flex-row-layout{
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 80px;
		flex-direction: column;
		height: 90%;
	}
</style>
