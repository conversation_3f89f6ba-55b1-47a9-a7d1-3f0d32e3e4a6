<template>
	<bottom-sheet ref="dialog"
				  @cancel="onCancel">
		<view class="flex-row-layout picker-option-layout">
			<text class="picker-option-button"
				  @click="onCancelClick">取消</text>
			<view style="margin-left: auto; margin-right: 20rpx;"
				  class="picker-option-button picker-option-confirm"
				  @click="onConfirmClick">
				确认
			</view>
		</view>
		<!-- 		在钉钉小程序里，picker-view下只能放一个picker-view-column，否则会报错，
		所以改用一个view包裹的方式 -->
		<view v-if="showMonth"
			  class="year-month-picker">
			<picker-view :value="indexs"
						 @change="onTimeChange">
				<picker-view-column>
					<view v-for="(y, index) in years"
						  :key="index">{{y}}年</view>
				</picker-view-column>
				<picker-view-column>
					<view v-for="(m, index) in months"
						  :key="index">{{m}}月</view>
				</picker-view-column>
			</picker-view>
		</view>
		<view v-if="!showMonth"
			  class="year-month-picker">
			<picker-view :value="indexs"
						 @change="onTimeChange">
				<picker-view-column>
					<view v-for="(y, index) in years"
						  :key="index">{{y}}年</view>
				</picker-view-column>
			</picker-view>
		</view>
	</bottom-sheet>
</template>

<script>
	import number from '@/common/number.js';

	import bottomSheet from '@/pages/component/bottom-sheet.vue';

	const MODE_MONTH = 'month';

	export default {
		name: 'YearMonthPicker',
		components: {
			bottomSheet
		},

		props: {
			fields: {
				type: String,
				default: MODE_MONTH
			},

			value: String
		},

		data() {
			const today = new Date();
			let year = today.getFullYear();
			let monthIndex = today.getMonth();

			if (this.value && this.value !== '请选择') {
				if (this.showMonth) {
					year = parseInt(this.value.split('-')[0]);
					monthIndex = parseInt(this.value.split('-')[1]) - 1;
				} else {
					year = this.value;
				}
			}
			let years = [];
			let endYear = year + 20;
			let startYear = 1970;
			while (startYear < endYear) {
				years.push(startYear);
				startYear += 1;
			}

			let months = [];
			for (let m = 1; m < 13; m++) {
				months.push(number.alignZeroPadding(m));
			}

			let indexs = [];
			indexs.push(years.indexOf(year));
			if (MODE_MONTH === this.fields) {
				indexs.push(monthIndex);
			}

			return {
				years,
				year,
				months,
				month: number.alignZeroPadding(monthIndex + 1),
				indexs
			}
		},

		computed: {
			showMonth: function() {
				return MODE_MONTH === this.fields;
			},

			time: function() {
				if (this.showMonth) {
					return `${this.year}-${this.month}`;
				} else {
					return this.year;
				}
			}
		},

		methods: {
			onTimeChange(event) {
				let selectedIndexs = event.detail.value;
				let yearIndex = selectedIndexs[0];

				this.year = this.years[yearIndex];
				this.$set(this.indexs, 0, yearIndex);
				if (this.showMonth) {
					let monthIndex = selectedIndexs[1];
					this.month = this.months[monthIndex];
					this.$set(this.indexs, 1, monthIndex);
				}

				this.$emit('change', this.time);
			},

			show() {
				this.$refs.dialog.show();
			},

			dismiss() {
				this.$refs.dialog.dismiss();
			},

			onConfirmClick() {
				this.dismiss();
				if (this.time !== this.value) {
					this.$emit('confirm', this.time);
				}
			},

			onCancelClick() {
				this.dismiss();
				this.resetYearAndMonth();
			},

			onCancel() {
				this.resetYearAndMonth();
			},

			resetYearAndMonth() {
				if (this.value && this.value !== '请选择') {
					if (this.showMonth) {
						this.year = parseInt(this.value.split('-')[0]);
						this.month = this.value.split('-')[1];
					} else {
						this.year = this.value;
					}
				} else {
					const today = new Date();
					this.year = today.getFullYear();
					if (this.showMonth) {
						this.month = number.alignZeroPadding(today.getMonth() + 1);
					}
				}

				this.$set(this.indexs, 0, this.years.indexOf(this.year));
				if (this.showMonth) {
					this.$set(this.indexs, 1, this.months.indexOf(this.month));
				}
			}
		}
	}
</script>

<style scoped>
	.picker-option-layout {
		width: 100%;
		height: 96rpx;
		box-shadow: 0 2rpx 2rpx #ccc;
	}

	.year-month-picker {
		width: 100%;
		height: 400rpx;
		margin-top: 20rpx;
	}

	.picker-option-button {
		height: 68rpx;
		line-height: 68rpx;
		padding: 4rpx 32rpx 0 32rpx;
		font-size: 32rpx;
	}

	.picker-option-button:active {
		color: #fff;
		background-color: #1E8EEF;
	}

	.picker-option-confirm {
		background-color: #0FAEFF;
		border-radius: 6rpx;
		color: #fff;
	}
</style>
