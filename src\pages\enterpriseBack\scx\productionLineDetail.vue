<template>
	<body style="background-color: #f1f2f6;" class="warp">
		<header class="header">
			<i class="ic-back" @click="back"></i>
			<div class="title">生产线详情</div>
			<i class="pd-edtbtn" v-if='disabled' @click="disabled=false">编辑</i>
			<i class="pd-edtbtn" v-if='!disabled' @click="disabled=true">关闭</i>
		</header>
		<scroll-view scroll-y style="height: calc(100vh - 78rpx)" @scrolltolower="getMore">
			<section class="main">
				<div class="inner">
					<div class="pd-tit1"><strong>基本信息</strong></div>
					<u-form :model="model" :rules="rules" ref="uForm" :errorType="errorType">
						<u-form-item :label-position="labelPosition" label="生产线名称" required prop="SCXMC" :label-width="labelWidth">
							<u-input :border="border" v-model="model.SCXMC" :disabled="disabled" placeholder="请填写生产线"></u-input>
						</u-form-item>

						<u-form-item required :label-position="labelPosition" label="工艺名称" prop="GYMC" :label-width="labelWidth">
							<u-input :disabled="disabled" :border="border" placeholder="请填写工艺名称" v-model="model.GYMC" />
						</u-form-item>

						<u-form-item required :label-position="labelPosition" label="工艺特征" prop="GYTZ" :label-width="labelWidth">
							<u-input :disabled="disabled" :border="border" placeholder="请填写工艺特征" v-model="model.GYTZ" />
						</u-form-item>

						<u-form-item required :label-position="labelPosition" label="主要污染" prop="ZYWR" :label-width="labelWidth">
							<u-input v-if="disabled" :disabled="disabled" :border="border" placeholder="请填写主要污染物" v-model="model.ZYWR" />
							<u-input v-else type="select" @click="zywrShow = true" :select-open="zywrShow" :border="border" placeholder="请填写主要污染物" v-model="model.ZYWR" />
							<u-icon name="question-circle" color="transparent" size="32"></u-icon>
						</u-form-item>
						<u-form-item :label-position="labelPosition" label="产污设备组织方式" required prop="CWSBZZFS" :label-width="labelWidth + 80">
							<u-input v-if="disabled" :disabled="disabled" :border="border" v-model="CWSBZZFS" placeholder="请选择产污设备组织方式"></u-input>
							<u-input
								v-else
								:disabled="disabled"
								:border="border"
								type="select"
								:select-open="cwsbzzfsShow"
								v-model="CWSBZZFS"
								placeholder="请选择产污设备组织方式"
								@click="cwsbzzfsShow = true"
							></u-input>
							<u-icon name="question-circle" color="#909399" size="32" @click="showTips"></u-icon>
						</u-form-item>
						<u-form-item :label-position="labelPosition" label="治污设备组织方式" required prop="CWSBZZFS" :label-width="labelWidth + 80">
							<u-input v-if="disabled" :disabled="disabled" :border="border" v-model="ZWSBZZFS" placeholder="请选择治污设备组织方式"></u-input>

							<u-input
								v-else
								:disabled="disabled"
								:border="border"
								type="select"
								:select-open="zwsbzzfsShow"
								v-model="ZWSBZZFS"
								placeholder="请选择治污设备组织方式"
								@click="zwsbzzfsShow = true"
							></u-input>
							<u-icon name="question-circle" color="#909399" size="32" @click="showTips"></u-icon>
						</u-form-item>

						<u-form-item label-position="top" label="备注内容" prop="BZNR" :label-width="labelWidth">
							<u-input :disabled="disabled" type="textarea" :border="border" height="200" placeholder="填写备注内容" v-model="model.BZ" />
						</u-form-item>
					</u-form>
					<div class="pd-tit1" style="border-bottom: none;"><strong>关联设备</strong></div>
					<div class="pd-tablebx">
						<table cellpadding="0" class="pd-tablelst1">
							<thead>
								<tr>
									<td>设备名称</td>
									<td>数据最新上报</td>
									<td></td>
								</tr>
							</thead>
							<tbody>
								<tr v-for="(item,index) in glsb" :key='index'>
									<td>{{item.SBMC||'-'}}</td>
									<td>{{item.SBSJ||'-'}}</td>
									<td><image src="../../../static/images/delic.png" class="pd-delic"></image></td>
								</tr>
							</tbody>
						</table>
						<div class="gap"></div>
						<div class="gap"></div>
					</div>
					<ul class="pd-ulbtn1">
						<li class="on" @click="save">保存</li>
					</ul>
				</div>

				<u-popup v-model="zywrShow" mode="bottom" width="100%" height="50%" border-radius="20">
					<div class="opration">
						<span @click="cancel" class="cancel">取消</span>
						<span @click="confirm" class="confirm">确定</span>
					</div>
					<div class="listWarp">
						<p v-for="(item, index) in zywrList" @click="selectWrw(item)" :class="selectWrwList.includes(item.DM) ? 'on' : ''">{{ item.DMNR }}</p>
					</div>
				</u-popup>
				<u-select value-name="DM" label-name="DMNR" mode="single-column" :list="cwsbzzfsList" v-model="cwsbzzfsShow" @confirm="getCwsbzzfs"></u-select>
				<u-select value-name="DM" label-name="DMNR" mode="single-column" :list="zwsbzzfsList" v-model="zwsbzzfsShow" @confirm="getZwsbzzfs"></u-select>
				<u-popup v-model="showTip" mode="center" width="70%" height="30%" border-radius="10">
					<div class="tip-title">生产状态判断方式说明</div>
					<div class="tip-content">
						<p>N备1用 : 该生产线中所有产污设备只要有一个开启，即判断为生产状态</p>
						<div class="gap"></div>
						<p>同时开启 : 该生产线内所有产污设备同时开启，才判定为生产状态</p>
					</div>
					<div class="tip-know" @click="showTips()">我知道了</div>
				</u-popup>
			</section>
		</scroll-view>
	</body>
</template>

<script>
import { getGgdmz } from '@/api/iot/ggdmz.js';
import { getScx, getScxsb, scxUpdata } from '@/api/iot/enterprise.js';
export default {
	data() {
		return {
			enterpriseInfo: {},
			info: {},
			model: {
				ORGID: '',
				WRYBH: '',
				CJR: '',
				SCXMC: '', //生产线名
				GYMC: '', //工艺名称
				GYTZ: '', //工艺特征
				ZYWR: '', //主要污染
				CWSBZZFS: '', //产污设备组织方式
				ZWSBZZFS: '', //治污设备组织方式
				BZ: '' //备注内容
			},
			CWSBZZFS: '', //产污设备组织方式
			ZWSBZZFS: '', //治污设备组织方式
			showTip: false,
			selectWrwList: [], //选中的污染物list
			cwsbzzfsShow: false,
			cwsbzzfsList: [],
			zwsbzzfsShow: false,
			zwsbzzfsList: [],
			zywrShow: false,
			zywrList: [],
			rules: {
				SCXMC: [
					{
						required: true,
						message: '请填写生产线',
						trigger: 'change'
					}
				],
				GYMC: [
					{
						required: true,
						message: '请填写工艺名称'
					}
				],
				GYTZ: [
					{
						required: true,
						message: '请填写工艺特征'
					}
				],
				ZYWR: [
					{
						required: true,
						message: '请选择主要污染物'
					}
				],
				CWSBZZFS: [
					{
						required: true,
						message: '请选择产污设备组织方式'
					}
				],
				ZWSBZZFS: [
					{
						required: true,
						message: '请选择治污设备组织方式'
					}
				]
			},
			border: false, //input 是否显示边框, 默认false
			labelWidth: 200,
			selectShow: false,
			labelPosition: 'left', //表单域提示文字的位置，left-左侧，top-上方
			errorType: ['border-bottom'], //错误的提示方式，数组形式, 可选值为：
			copy: false,
			scxId: '',
			disabled: true,
			glsb:[]
		};
	},
	onLoad(option) {
		this.enterpriseInfo = uni.getStorageSync('userInfo');
		this.info = JSON.parse(decodeURIComponent(option.info));
		this.scxId = option.scxId;
		this.initFormData();
		this.getGgdmz();
	},
	onHide() {},
	mounted() {
		this.$refs.uForm.setRules(this.rules);
	},
	methods: {
		initFormData() {
			let { orgid } = this.enterpriseInfo;
			getScx({ ORGID: orgid, SCXID: this.scxId }).then(res => {
				if (res.data_array && res.data_array.length > 0) {
					this.model = res.data_array[0];
					getGgdmz({ DMJBH: 'SCX_ZZFS' }).then(ret => {
						if (ret.data_array && ret.data_array.length >= 0) {
							let len = ret.data_array.length
							for(let i = 0 ;i<len;i++){
								if(this.model.ZWSBZZFS == ret.data_array[i].DM){
									this.ZWSBZZFS = ret.data_array[i].DMNR
								}
								if(this.model.CWSBZZFS == ret.data_array[i].DM){
									this.CWSBZZFS = ret.data_array[i].DMNR
								}
							}
						}
					});
					getScxsb({SCXID: res.data_array[0].SCXID}).then(r=>{
						if(r.data_array && r.data_array.length > 0){
							this.glsb=r.data_array
						}
					})
				}
				
			});
		},
		getGgdmz() {
			// 主要污染
			getGgdmz({ DMJBH: 'CWSB_ZYWR' }).then(res => {
				if (res.data_array && res.data_array.length > 0) {
					this.zywrList = res.data_array;
				}
			});
			// 组织方式
			getGgdmz({ DMJBH: 'SCX_ZZFS' }).then(res => {
				if (res.data_array && res.data_array.length > 0) {
					this.zwsbzzfsList = res.data_array;
					this.cwsbzzfsList = res.data_array;
				}
			});
			// 组织方式
			getGgdmz({ DMJBH: 'SCX_ZZFS' }).then(res => {
				if (res.data_array && res.data_array.length > 0) {
					this.zwsbzzfsList = res.data_array;
					this.cwsbzzfsList = res.data_array;
				}
			});
		},

		// 产污设备组织方式 u-select @confirm 事件
		getCwsbzzfs(v) {
			this.model.CWSBZZFS = v[0].value;
			this.CWSBZZFS = v[0].label;
		},
		// 治污设备组织方式 u-select @confirm 事件
		getZwsbzzfs(v) {
			this.model.ZWSBZZFS = v[0].value;
			this.ZWSBZZFS = v[0].label;
		},
		confirm() {
			this.zywrShow = false;
			this.model.ZYWR = this.selectWrwList.join(',');
		},
		// 污染物  事件
		selectWrw(wrw) {
			let index = this.selectWrwList.findIndex(item => {
				return item == wrw.DM;
			});
			if (index != -1) {
				this.selectWrwList.splice(index, 1);
			} else {
				this.selectWrwList.push(wrw.DM);
			}
		},
		cancel() {
			this.zywrShow = false;
			this.selectWrwList = [];
		},

		showTips() {
			this.showTip = !this.showTip;
		},

		getMore() {},

		save() {
			this.model.ORGID = this.enterpriseInfo.orgid;
			this.model.WRYBH = this.info.WRYBH;
			this.model.XGR = this.enterpriseInfo.name;
			this.$refs.uForm.validate(valid => {
				if (valid) {
					scxUpdata(this.model).then(res => {
						uni.showToast({
							title: '修改成功',
							duration: 2000
						})
					});
					setTimeout(()=>{
						uni.navigateTo({
							url: './saveScxSuccess?scxName='+this.model.SCXMC+'&info='+ encodeURIComponent(JSON.stringify(this.info))+'&xg=' + 1
						});
					},2000)
				}
			});
		},
		back() {
			uni.navigateBack({
				delta: 1
			});
		}
	}
};
</script>

<style scoped>
.pd-tablebx image {
	height: 30rpx;
}
.bznr {
	display: inline-block;
	height: 60rpx;
	line-height: 60rpx;
}
/deep/ .u-form-item--left__content__label {
	font-size: 30rpx;
	color: #2c323f;
}

/deep/ .u-input__textarea {
	border-radius: 8rpx;
	height: 100rpx;
	background-color: rgb(243, 245, 249);
	padding-left: 10rpx;
}
/deep/ .uni-textarea-wrapper {
	height: 100% !important;
}
/deep/ .u-list-item {
	margin: 0;
}
/deep/ .uni-input-placeholder {
	padding: 0 20rpx 0 0;
	/* text-align: right; */
	font-size: 26rpx;
}
.listWarp {
	width: 100%;
	height: 100%;
	overflow-y: scroll;
}
.listWarp p {
	width: 100%;
	padding: 20rpx;
	text-align: center;
	border-bottom: 1px solid #efefef;
}
.opration {
	padding: 20rpx;
	display: flex;
	justify-content: space-between;
	position: absolute;
	width: 100%;

	background-color: #fff;
}
.confirm {
	color: rgb(60, 170, 255);
}
.on {
	color: rgb(60, 170, 255);
}
/deep/ .u-icon {
	padding: 0 0 0 10rpx;
}
.tip-title {
	text-align: center;
	border-bottom: 1px solid #efefef;
	padding: 20rpx;
	font-weight: 600;
}
.tip-content {
	padding: 20rpx;
}
.tip-content p {
	/* padding: 20rpx 0; */
	font-size: 26rpx;

	color: #666;
}
.tip-know {
	position: absolute;
	bottom: 0;
	padding: 20rpx;
	text-align: center;
	border-top: 1px solid #efefef;
	color: rgb(60, 170, 255);
	width: 100%;
}
</style>
