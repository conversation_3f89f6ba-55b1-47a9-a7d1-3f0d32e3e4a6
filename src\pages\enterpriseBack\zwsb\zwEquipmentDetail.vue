<!-- @format -->

<template>
    <body style="background-color: #f1f2f6" class="warp">
        <header class="header">
            <div class="title">编辑治污设备</div>
            <i class="ic-back" @click="back"></i>
            <i class="pd-edtbtn" v-if="disabled" @click="disabled = false"
                >编辑</i
            >
            <i class="pd-edtbtn" v-if="!disabled" @click="disabled = true"
                >关闭</i
            >
        </header>
        <ul class="zy-tabs1">
            <li
                :class="dataType == item ? 'cur' : ''"
                v-for="(item, index) in dataTypeArr"
                :key="index"
                @click="change(item)"
            >
                <p>{{ item }}</p>
            </li>
        </ul>
        <scroll-view
            scroll-y
            style="height: calc(100vh - 156rpx)"
            @scrolltolower="getMore"
        >
            <section class="main">
                <div class="inner" style="padding: 20rpx 18rpx 0px">
                    <div v-show="dataType == '设备信息'">
                        <div class="pd-tit1"><strong>基本信息</strong></div>
                        <u-form
                            :model="model"
                            :rules="rules"
                            ref="uForm"
                            :errorType="errorType"
                        >
                            <!-- 	<u-form-item :label-position="labelPosition" label="所属企业" required prop="SSQY" :label-width="labelWidth">
								<u-input :border="border" v-model="model.SSQY" placeholder="请填写所属企业"></u-input>
							</u-form-item> -->

                            <u-form-item
                                required
                                :label-position="labelPosition"
                                label="设备名称"
                                prop="SBMC"
                                :label-width="labelWidth"
                            >
                                <u-input
                                    :disabled="disabled"
                                    :border="border"
                                    placeholder="请填写设备名称"
                                    v-model="model.SBMC"
                                />
                            </u-form-item>

                            <u-form-item
                                required
                                :label-position="labelPosition"
                                label="所属生产线"
                                prop="SSSCX"
                                :label-width="labelWidth"
                            >
                                <u-input
                                    v-if="disabled"
                                    disabled
                                    :border="border"
                                    placeholder="请填写所属生产线"
                                    v-model="SSSCX"
                                />
                                <u-input
                                    v-if="!disabled"
                                    type="select"
                                    :select-open="scxShow"
                                    @click="scxShow = true"
                                    :border="border"
                                    placeholder="请填写所属生产线"
                                    v-model="SSSCX"
                                />
                            </u-form-item>
                            <u-form-item
                                :label-position="labelPosition"
                                label="工艺类型"
                                required
                                prop="GYLX"
                                :label-width="labelWidth"
                            >
                                <u-input
                                    v-if="disabled"
                                    disabled
                                    v-model="model.GYLX"
                                    placeholder="请选择行业分类"
                                ></u-input>
                                <u-input
                                    v-if="!disabled"
                                    :border="border"
                                    type="select"
                                    :select-open="gylxShow"
                                    v-model="model.GYLX"
                                    placeholder="请选择行业分类"
                                    @click="gylxShow = true"
                                ></u-input>
                            </u-form-item>

                            <u-form-item
                                :label-position="labelPosition"
                                label="设备类型"
                                required
                                prop="GYLX_SBLX"
                                :label-width="labelWidth"
                            >
                                <u-input
                                    v-if="disabled"
                                    disabled
                                    :border="border"
                                    v-model="model.GYLX_SBLX"
                                    placeholder="请选择治污设备组织方式"
                                ></u-input>
                                <u-input
                                    v-if="!disabled"
                                    :border="border"
                                    type="select"
                                    :select-open="sblxShow"
                                    v-model="model.GYLX_SBLX"
                                    placeholder="请选择治污设备组织方式"
                                    @click="selectSblx()"
                                ></u-input>
                            </u-form-item>

                            <u-form-item
                                required
                                :label-position="labelPosition"
                                label="设备位置"
                                prop="SBWZ"
                                :label-width="labelWidth"
                            >
                                <u-input
                                    disabled
                                    :border="border"
                                    placeholder="请获取位置"
                                    v-model="SBWZ"
                                />
                                <u-icon
                                    v-if="!disabled"
                                    name="map"
                                    @click="getJwd"
                                ></u-icon>
                            </u-form-item>

                            <u-form-item
                                :label-position="labelPosition"
                                label="绑定智能设备"
                                prop="IMEI"
                                :label-width="labelWidth"
                            >
                                <u-input
                                    v-if="disabled"
                                    disabled
                                    :border="border"
                                    placeholder="可扫码获取设备id"
                                    v-model="model.IMEI"
                                />
                                <u-input
                                    v-if="!disabled"
                                    :border="border"
                                    placeholder="可扫码获取设备id"
                                    v-model="model.IMEI"
                                />
                                <u-icon
                                    v-if="!disabled"
                                    name="scan"
                                    @tap="scanCode"
                                ></u-icon>
                            </u-form-item>

                            <u-form-item
                                label-position="top"
                                label="备注内容"
                                prop="BZNR"
                                :label-width="labelWidth"
                            >
                                <u-input
                                    type="textarea"
                                    :border="border"
                                    height="200"
                                    placeholder="填写备注内容"
                                    v-model="model.BZ"
                                />
                            </u-form-item>

                            <u-form-item
                                :label-position="labelPosition"
                                label-width="150"
                            >
                                <u-upload
                                    :before-remove="beforeRemove"
                                    @on-list-change="onListChange"
                                    width="160"
                                    height="160"
                                    :action="uploadUrl"
                                    ref="uUpload"
                                    :max-count="disabled ? 0 : maxCount"
                                    :auto-upload="true"
                                    :show-progress="true"
                                    :header="{
                                        jwtToken: token
                                    }"
                                    name="up"
                                    :before-upload="beforeUpload"
                                    :form-data="{
                                        LXDM: 'ZDFJ',
                                        ZLXDM: 'ZDFJLB',
                                        YWSJID: ''
                                    }"
                                    :deletable="!disabled"
                                ></u-upload>
                            </u-form-item>
                        </u-form>
                        <ul class="pd-ulbtn1">
                            <li class="on" @click="save">保存</li>
                        </ul>
                    </div>
                    <ReportData
                        :sbInfo="sbInfo"
                        :info="info"
                        v-if="dataType == '上报数据'"
                    ></ReportData>
                    <ReportStatus
                        :sbInfo="sbInfo"
                        :info="info"
                        v-if="dataType == '上报状态'"
                    ></ReportStatus>
                </div>

                <u-select
                    value-name="SCXID"
                    label-name="SCXMC"
                    mode="single-column"
                    :list="scxList"
                    v-model="scxShow"
                    @confirm="getScx"
                ></u-select>
                <u-select
                    value-name="DM"
                    label-name="DMNR"
                    mode="single-column"
                    :list="gylxList"
                    v-model="gylxShow"
                    @confirm="getGylx"
                ></u-select>
                <u-select
                    value-name="DM"
                    label-name="DMNR"
                    mode="single-column"
                    :list="sblxList"
                    v-model="sblxShow"
                    @confirm="getSblx"
                ></u-select>
            </section>
        </scroll-view>
    </body>
</template>

<script>
import { getGgdmz } from '@/api/iot/ggdmz.js';
import {
    getScx,
    getZwsb,
    updateZwsb,
    getFilelist,
    deletefile
} from '@/api/iot/enterprise.js';
import ReportData from './reportData.vue';
import ReportStatus from './reportStatus.vue';
import {
    DOWNLOAD_URLZDY,
    UPLOAD_URL,
    DELETFILE_URLZDY
} from '@/common/config.js';
import enterprise_store from '../enterprise.store.js';
export default {
    components: { ReportData, ReportStatus },
    watch: {
        JWD: {
            handler: function (v) {
                this.model.JD = v.JWD.longitude;
                this.model.WD = v.JWD.latitude;
                this.SBWZ = `${v.JWD.longitude} ~ ${v.JWD.latitude}`;
                console.log(v);
            },
            deep: true //深度监听
        }
    },
    data() {
        return {
            JWD: enterprise_store.state,
            autoUpload: false,
            uploadUrl: UPLOAD_URL,
            token: '',
            dataTypeArr: ['设备信息', '上报数据', '上报状态'],
            dataType: '设备信息',
            disabled: true,
            enterpriseInfo: {},
            sbInfo: {},
            info: {},
            model: {
                SBMC: '',
                ORGID: '',
                CJR: '',
                SCXID: '',
                GYLX: '',
                GYLX_SBLX: '',
                IMEI: '',
                JD: '',
                WD: '',
                BZ: '', //备注内容
                XGR: '',
                SBID: ''
            },
            GYLX: '', //产污设备组织方式
            SBLX: '', //治污设备组织方式
            SSSCX: '',
            SBWZ: '',
            gylxShow: false,
            gylxList: [],
            sblxShow: false,
            sblxList: [],
            scxShow: false,
            scxList: [],
            rules: {
                SSQY: [
                    {
                        required: true,
                        message: '请选择所属企业',
                        trigger: 'change'
                    }
                ],
                SCXID: [
                    {
                        required: true,
                        message: '请选择所属生产线'
                    }
                ],
                GYLX: [
                    {
                        required: true,
                        message: '请选择工艺类型'
                    }
                ],
                SBLX: [
                    {
                        required: true,
                        message: '请选择设备类型'
                    }
                ]
            },
            border: false, //input 是否显示边框, 默认false
            labelWidth: 200,
            selectShow: false,
            labelPosition: 'left', //表单域提示文字的位置，left-左侧，top-上方
            errorType: ['border-bottom'], //错误的提示方式，数组形式, 可选值为：
            fileArr: [],
            maxCount: 3
        };
    },
    onLoad(option) {
        this.enterpriseInfo = uni.getStorageSync('userInfo');
        this.info = JSON.parse(decodeURIComponent(option.info));
        this.sbInfo = JSON.parse(decodeURIComponent(option.sbInfo));
        this.token = uni.getStorageSync('token');
    },
    onHide() {},
    mounted() {
        this.$refs.uForm.setRules(this.rules);
        this.getGgdmz();
    },
    methods: {
        beforeUpload() {
            this.$refs.uUpload.formData.YWSJID = this.sbInfo.SBID;
            // this.$refs.uUpload.formData.file = this.$refs.uUpload.lists[0].file.path;
            // console.log(this.$refs.uUpload.lists);
        },
        // 当内部文件列表被加入文件、移除文件，或手动调用clear方法时触发
        onListChange(lists) {},
        beforeRead(file, lists, name) {
            console.log(file, lists, name);
        },
        // 手动移除列表的某一个图片
        beforeRemove(index, lists) {
            let WJID = this.fileArr[index].WJID;
            deletefile(WJID).then((res) => {
                this.getZwsb();
            });
        },
        getGgdmz() {
            // 工艺类型
            getGgdmz({ DMJBH: 'ZWSB_GYLX', FDM: 'ZWSB_GYLX' }).then((res) => {
                if (res.data_array && res.data_array.length > 0) {
                    this.gylxList = res.data_array;
                }
            });
            // 组织方式
            let { orgid } = this.enterpriseInfo;
            let { WRYBH } = this.info;
            let { SBID } = this.sbInfo;
            let { SCXID } = this.sbInfo;
            getScx({
                ORGID: orgid,
                pageSize: 666,
                orderBy: 'CJSJ',
                orderWay: 'DESC',
                WRYBH: WRYBH
            }).then((res) => {
                if (res.data_array && res.data_array.length > 0) {
                    this.scxList = res.data_array;
                    for (let i = 0; i < res.data_array.length; i++) {
                        if (res.data_array[i].SCXID == SCXID) {
                            this.SSSCX = res.data_array[i].SCXMC;
                        }
                    }
                }
            });
            this.getZwsb();
        },
        getZwsb() {
            let { orgid } = this.enterpriseInfo;
            let { SBID } = this.sbInfo;
            let { SCXID } = this.sbInfo;

            // 查询一条治污设备
            getZwsb({ ORGID: orgid, SBID: SBID, SCXID: SCXID }).then((res) => {
                if (res.data_array && res.data_array.length > 0) {
                    let data = res.data_array[0];
                    this.SBWZ = `${Number(data.JD).toFixed(2)} ~ ${Number(
                        data.WD
                    ).toFixed(2)}`;
                    this.model = res.data_array[0];
                    let GYLX = res.data_array[0].GYLX;
                    getFilelist({
                        YWSJID: res.data_array[0].SBID,
                        LXDMS: 'ZDFJ',
                        ZLXDMS: 'ZDFJLB'
                    }).then((res) => {
                        this.$refs.uUpload.lists = [];
                        this.fileArr = [];
                        if (res.length > 0) {
                            let file = res[0].zlxList[0].fileList;

                            for (let i = 0; i < file.length; i++) {
                                this.fileArr.push(file[i]);
                                this.$refs.uUpload.lists.push({
                                    file: {
                                        path: DOWNLOAD_URLZDY + file[i].WJID
                                    },
                                    url: DOWNLOAD_URLZDY + file[i].WJID
                                });
                            }
                        }
                    });

                    getGgdmz({ DMJBH: 'ZWSB_GYLX', FDM: 'ZWSB_GYLX' }).then(
                        (r) => {
                            if (r.data_array && r.data_array.length > 0) {
                                let GYLXDM = '';
                                for (let i = 0; i < r.data_array.length; i++) {
                                    if (r.data_array[i].DMNR == GYLX) {
                                        GYLXDM = r.data_array[i].DM;
                                    }
                                }
                                getGgdmz({
                                    DMJBH: 'ZWSB_GYLX',
                                    FDM: GYLXDM
                                }).then((res) => {
                                    if (
                                        res.data_array &&
                                        res.data_array.length > 0
                                    ) {
                                        this.sblxList = res.data_array;
                                    }
                                });
                            }
                        }
                    );
                }
            });
        },

        // 产污设备组织方式 u-select @confirm 事件
        getGylx(v) {
            this.model.GYLX = v[0].label;
            this.model.GYLX_SBLX = '';
            getGgdmz({ DMJBH: 'ZWSB_GYLX', FDM: v[0].value }).then((res) => {
                if (res.data_array && res.data_array.length > 0) {
                    this.sblxList = res.data_array;
                }
            });
        },
        // 治污设备组织方式 u-select @confirm 事件
        getSblx(v) {
            this.model.GYLX_SBLX = v[0].label;
        },
        selectSblx() {
            if (!this.model.GYLX) {
                uni.showToast({
                    title: '请先选择工艺类型'
                });
                return;
            }
            this.sblxShow = true;
        },
        getScx(v) {
            this.model.SCXID = v[0].value;
            this.SSSCX = v[0].label;
        },

        getJwd() {
            uni.navigateTo({
                url: '../map'
            });
            // uni.getLocation({
            // 	type: 'wgs84',
            // 	success: res => {
            // 		this.model.JD = res.longitude;
            // 		this.model.WD = res.latitude;
            // 		this.SBWZ = `${res.longitude.toFixed(2)} ~ ${res.latitude.toFixed(2)}`;
            // 	}
            // });
        },

        scanCode() {
            uni.scanCode({
                scanType: ['qrCode'],
                success: (res) => {
                    this.model.IMEI = res.result.split(';')[0];
                },
                fail: (err) => {
                    uni.showToast({
                        title: '未识别到二维码！',
                        icon: 'none'
                    });
                }
            });
        },
        change(v) {
            this.dataType = v;
        },
        getMore() {},

        save() {
            this.model.ORGID = this.enterpriseInfo.orgid;
            this.model.XGR = this.enterpriseInfo.name;
            this.model.SBID = this.sbInfo.SBID;
            this.model.SCXID = this.sbInfo.SCXID;
            this.$refs.uForm.validate((valid) => {
                if (valid) {
                    updateZwsb(this.model).then((res) => {
                        uni.showToast({
                            title: '修改成功',
                            duration: 2000
                        });
                        // uni.setStorageSync('isAddEquipment',true)
                        setTimeout(() => {
                            uni.navigateTo({
                                url:
                                    './saveZwsbSuccess?zwsbName=' +
                                    this.model.SBMC +
                                    '&info=' +
                                    encodeURIComponent(
                                        JSON.stringify(this.info)
                                    ) +
                                    '&xg=' +
                                    1
                            });
                        }, 2000);
                    });
                }
            });
        },
        back() {
            uni.navigateBack({
                delta: 1
            });
        }
    }
};
</script>

<style scoped>
.pd-tablebx image {
    height: 30rpx;
}
.bznr {
    display: inline-block;
    height: 60rpx;
    line-height: 60rpx;
}
/deep/ .u-form-item--left__content__label {
    font-size: 30rpx;
    color: #2c323f;
}

/deep/ .u-input__textarea {
    border-radius: 8rpx;
    height: 100rpx;
    background-color: rgb(243, 245, 249);
    padding-left: 10rpx;
}
/deep/ .uni-textarea-wrapper {
    height: 100% !important;
}
/deep/ .u-list-item {
    margin: 0;
}
/deep/ .uni-input-placeholder {
    padding: 0 20rpx 0 0;
    /* text-align: right; */
    font-size: 26rpx;
}
.listWarp {
    width: 100%;
    height: 100%;
    overflow-y: scroll;
}
.listWarp p {
    width: 100%;
    padding: 20rpx;
    text-align: center;
    border-bottom: 1px solid #efefef;
}
.opration {
    padding: 20rpx;
    display: flex;
    justify-content: space-between;
    position: absolute;
    width: 100%;

    background-color: #fff;
}
.confirm {
    color: rgb(60, 170, 255);
}
.on {
    color: rgb(60, 170, 255);
}
/deep/ .u-icon {
    padding: 0 0 0 10rpx;
}
.tip-title {
    text-align: center;
    border-bottom: 1px solid #efefef;
    padding: 20rpx;
    font-weight: 600;
}
.tip-content {
    padding: 20rpx;
}
.tip-content p {
    /* padding: 20rpx 0; */
    font-size: 26rpx;

    color: #666;
}
.tip-know {
    position: absolute;
    bottom: 0;
    padding: 20rpx;
    text-align: center;
    border-top: 1px solid #efefef;
    color: rgb(60, 170, 255);
    width: 100%;
}
.pd-tit1 {
    border-radius: 18rpx 18rpx 0 0;
}
/deep/ .u-form {
    border-radius: 0 0 18rpx 18rpx;
}
</style>
