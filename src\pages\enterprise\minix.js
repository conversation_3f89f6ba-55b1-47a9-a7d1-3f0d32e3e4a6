export default {
	data(){
		return{
			showBtn: true,
			windowHeight: 0,
			hasFocus: false
		}
	},
	created() {
		uni.onWindowResize((res) => {
			console.log(this.windowHeight, res.size.windowHeight);
			if (this.windowHeight == 0) {
				this.windowHeight = res.size.windowHeight;
				return
			}
	
			if (this.windowHeight > res.size.windowHeight) {
				this.showBtn = false;
			} else {
				this.showBtn = true;
			}
		})
	},
	methods:{
		showTabbar(event) {
		
			setTimeout(() => {
				if (!this.hasFocus) {
					console.log("失去焦点")
					this.hasFocus = false;
					this.showBtn = true;
				}
			}, 10);
		
		},
		hideTabbar() {
			this.hasFocus = true;
			this.showBtn = false;
		},
	}
}