/** @format */

import axios from 'axios-miniprogram';
// import axios from 'axios';
import store from '../store';
import logService from '@/api/log-service.js';
import loginService from '@/api/login-service.js';
import checkToken from './checkToken.js'
// 当第一次请求 显示loading  剩下的时候就不调用了
// 当都请求完毕后 隐藏loading
class AjaxRequest {
	// baseURL
	constructor() {
		// 请求的路径
		this.baseURL = '';
		this.timeout = 5 * 1000; // 超时时间
		this.queue = {}; // 存放每次的请求 TODO 重复一样的请求会有问题 by 黄冠豪
	}
	merge(options) {
		return {
			...options,
			baseURL: this.baseURL,
			timeout: this.timeout
		};
	}
	setInterceptor(instance, options) {
		//每次请求时 都会加一个loading效果
		// 更改请求头
		instance.interceptors.request.use(async (config) => {
			let userinfo = uni.getStorageSync('user_info')
			let userId = uni.getStorageSync('user_id')
			let password = uni.getStorageSync('password')
			let customORGID = uni.getStorageSync('ORGID') || ''
			if (checkToken()) {
				//说明token要过期了。重新请求拿到token再发起后续请求
				uni.setStorageSync('IS_LOGIN', false);
				await loginService.loginByPassword(userId, password)
			}
			config.headers.common['cas_token'] = uni.getStorageSync('token') || '';
			if (config.method == 'post' || config.method == 'POST') {
				config.data = config.data || {}
				config.data.jwtToken = uni.getStorageSync('token');
				config.data.version = '1';
				config.data.SJQX = customORGID || userinfo.sjqx;
				config.data.ORGID = customORGID || userinfo.orgid;
				config.data.APPID = 'device'
			}
			if (config.method == 'get' || config.method == 'GET') {
				config.params = config.params || {}
				config.params.SJQX = customORGID || userinfo.sjqx;
				config.params.ORGID = customORGID || userinfo.orgid;
				config.params.APPID = 'device'
			}
			// config.headers.token = uni.getStorageSync('token');
			if (Object.keys(this.queue).length === 0 && options.showLoading) {
				store.commit('showLoading');
			}
			this.queue[options.url] = options.url;
			return config;
		});
		// 如果上一个promise 返回了一个常量 会作为下一个promise的输入
		instance.interceptors.response.use(
			(res) => {
				delete this.queue[options.url]; // 每次请求成功后 都删除队列里的路径
				if (Object.keys(this.queue).length === 0) {
					store.commit('hideLoading');
				}
				// 状态码拦截器判断，如果是000的话，就返回
				if (
					res.data.status_code == '0' ||
					res.data.success ||
					res.data.result === 'success' ||
					res.data.code == '000' ||
					res.data.hasOwnProperty('datas') ||
					res.data.code === '200' ||
					res.data.count ||
					res.data.length >= 0 ||
					res.data.status === '000' ||
					res.data.status === '500' ||
					res.status === 200 ||
					res.data instanceof Array ||
					res.data instanceof Object ||
					res === [] ||
					res.data === ''
				) {
					return res.data;
				} else {
					if (!options.noShowError) {
						let errorMsg =
							res.data.msg ||
							res.data.errMsg ||
							res.data.error_msg ||
							'请求超时，稍后尝试';
						try {
							let service = options.data.service;
							logService
								.submitExceptionLog(service, options, res)
								.then((submitResp) => {});
						} catch (error) {}

						store.commit('errorMsg', {
							msg: errorMsg,
							res
						});
					}
					return Promise.reject(res.data);
				}
			},
			(error) => {
				delete this.queue[options.url]; // 每次请求成功后 都删除队列里的路径
				if (Object.keys(this.queue).length === 0) {
					store.commit('hideLoading');
				}
				if (axios.isCancel(error)) {
					console.log('Request canceled', error.message);
					error.isCanceled = true;
					return Promise.reject(error);
				}

				try {
					let service = options.data.service;
					logService
						.submitExceptionLog(service, options, error)
						.then((submitResp) => {});
				} catch (error) {}

				let errorMsg = error.message || '请求超时，稍后尝试';
				if (
					error.message == '网络错误' ||
					error.message == 'Network Error'
				) {
					errorMsg = '网络请求失败，请检查你的网络设置';
				}

				if (!options.noShowError) {
					store.commit('errorMsg', {
						msg: errorMsg,
						error
					});
				}
				return Promise.reject(error);
			}
		);
	}

	request(options) {
		options = Object.assign({
				showLoading: true
			},
			options
		);
		let post = {
			// 'Content-Type': 'application/json; charset=utf-8'
			'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8'
		};
		if (options.post) {
			post = options.post;
		}
		// url,method
		let instance = axios.create({
			headers: {
				common: {
					Accept: 'application/json, test/plain, */*',
				},
				post: post
			}
		}); // 通过axios库创建一个axios实例
		// 将请求数据转换成功 formdata 接收格式
		// instance.defaults.transformRequest = (data) => {
		//   return stringify(data)
		// }
		this.setInterceptor(instance, options);
		let config = this.merge(options);
		return instance(config); // axios执行后返回的是一个promise
	}
}
export default new AjaxRequest();