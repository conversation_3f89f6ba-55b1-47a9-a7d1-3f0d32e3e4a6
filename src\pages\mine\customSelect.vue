<template>
	<div style="height:100%;overflow: hidden;">
		<header class="blue-header">
			<i class="ic-back" @click="back"></i>
			<h1 class="lf-title">切换客户</h1>
		</header>
		<section class="main">
			<div class="inner yyinner">
				<div class="zm-srhbx1">
					<input type="text" placeholder="请输入关键字查询" v-model="keyword" @input.trim="filterArrCustom()" />
					<button type="button" @click="filterArrCustom"></button>
				</div>
				<ul class="zm17-item1">
					<li class="bg2" :class="{
						'bg1':item.value == curCustom
					}" v-for="item in arrCustom" :key="item.value" @click="changeCustom(item)">
						<p class="p1" v-if="item.label">{{item.label&&item.label.substring(0, 1)}}</p>
						<p class="p2">{{item.label}}</p>
					</li>
				</ul>
				<button type="button" class="zm18-btn" @click="changeORGID">切换</button>
			</div>
		</section>
	</div>
</template>

<script>
	import {
		userCustoms
	} from '@/api/iot/version.js';
	export default {
		data() {
			return {
				arrCustom: [],
				customList: [],
				curCustom: uni.getStorageSync('ORGID') || '',//当前orgid
				keyword: '',
				curCustomName:uni.getStorageSync('ORGID_LABEL') || ''
			};
		},

		mounted() {
			this.getCustomList();
		},
		onLoad(options) {},

		methods: {
			back() {
				uni.navigateBack({
					delta: 1
				});
			},
			async getCustomList() {
				const {
					data
				} = await userCustoms({})
				this.arrCustom = data
				this.customList = data
			},
			filterArrCustom() {
				const keyword = this.keyword;
				const filteredArray = this.customList.filter(function(element) {
					return element.label.includes(keyword); 
				});
				this.arrCustom = filteredArray
			},
			changeCustom(item){
				this.curCustom = item.value;
				this.curCustomName = item.label;
			},
			changeORGID(){
				uni.setStorageSync('ORGID', this.curCustom);
				uni.setStorageSync('ORGID_LABEL', this.curCustomName);
				uni.showToast({
				    title: '切换成功',
				    duration: 1000,
				});
				let objMatch = this.customList.find(obj => obj.ORGID === this.curCustom);
				uni.setStorageSync('XZQHDM',objMatch.XZQHDM)
				uni.switchTab({
				    url: `/pages/mine/Index`
				});
			}
		}
	};
</script>


<style>
</style>
