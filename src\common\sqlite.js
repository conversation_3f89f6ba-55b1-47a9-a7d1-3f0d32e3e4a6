import tables from '@/store/tables.js'

const DB_NAME = 'ecology'

//所有已创建的表，用于判断哪些表已创建
const ALL_TABLES = new Set()

export const initDatabase = () => {
	let path = `_doc/${DB_NAME}.db`
	if(plus.sqlite.isOpenDatabase(DB_NAME, path)) {
		loadCreatedTables(() => {
			initTables()
		})
	} else {
		plus.sqlite.openDatabase({
			name: DB_NAME,
			path,
			 success() {
				console.log(`打开数据库成功`)
				loadCreatedTables(() => {
					initTables()
				})
			 },
			 fail(error) {
				 console.log(`打开数据库出错：${JSON.stringify(error, null, 4)}`)
			 }
		})
	}
}

/**
 * 加载所有已创建的表
 */
const loadCreatedTables = (callback) => {
	let queryTableSql = `SELECT name FROM sqlite_master WHERE TYPE='table'`
	plus.sqlite.selectSql({
		name: DB_NAME,
		sql: queryTableSql,
		success(rows) {
			(rows || []).forEach(row => {
				ALL_TABLES.add(row.name)
			})
			console.log(`查询表结果：${JSON.stringify(rows, null, 4)}`)
			callback()
		},
		fail(error) {
			console.log(`加载已创建表失败${JSON.stringify(error, null, 4)}`)
		}
	})
}

const isTableExist = (tableName) => {
	return ALL_TABLES.has(tableName.toUpperCase())
}

const initTables = () => {
	console.log(`初始化表`)
	for(let tableConfig of tables) {
		console.log(`表配置：${JSON.stringify(tableConfig, null, 4)}`)
		if(!isTableExist(tableConfig.name)) {
			continue
		}
		
		let executeSqls = generateCreateTableSql(tableConfig)
		plus.sqlite.executeSql({
			name: DB_NAME,
			sql: executeSqls,
			success() {
				console.log(`创建表【${tableConfig.name}】成功`)
			},
			fail(error) {
				console.log(`创建表【${tableConfig.name}】失败`)
			}
		})
	}
}

/**
 * 生成创建表的SQL
 */
export const generateCreateTableSql = (tableConfig) => {
	let sqlSyntaxColumns = []
	let primaryKey = tableConfig.primaryKey
	if(isString(primaryKey)) {
		primaryKey = {
			name: primaryKey.toUpperCase(),
			type: 'TEXT',
			autoIncrement: falseyi 
		}
	}
	
	tableConfig.columns.forEach(column => {
		let structureColumn = column
		if(isString(column)) {
			structureColumn = {
				name: column.toUpperCase(),
				type: 'TEXT'
			}
		}
		let syntaxColumn = `${structureColumn.name} ${structureColumn.type}`
		if(structureColumn.name === primaryKey.name) {
			syntaxColumn += ` PRIMARY KEY`
			if(primaryKey.autoIncrement) {
				syntaxColumn += ' AUTOINCREMENT'
			}
			syntaxColumn += ' NOT NULL'
		} else {
			if(structureColumn.notNull) {
				syntaxColumn += ' NOT NULL'
			}
		}
		sqlSyntaxColumns.push(syntaxColumn)
	})
	
	let uniqueKeys = tableConfig.uniqueKeys || []
	if(uniqueKeys.length > 0) {
		let uniqueColumns = `UNIQUE (${uniqueKeys.join(',')})`
		sqlSyntaxColumns.push(uniqueColumns)
	}
	
	let executeSqls = []
	let tableName = tableConfig.name.toUpperCase()
	let createTableSql = `CREATE TABLE ${tableName} (${sqlSyntaxColumns.join(',')})`
	executeSqls.push(createTableSql)
	
	let indexKeys = tableConfig.indexKeys || []
	if(indexKeys.length > 0) {
		createIndexSql = `CREATE INDEX INX_${tableName} ON ${tableName} (${indexKeys.join(',')})`
		executeSqls.push(createIndexSql)
	}
	console.log(`表创建语句：${JSON.stringify(executeSqls, null, 4)}`)
	return executeSqls
}

/**
 * 变量是对象还是字符串
 */
const isString = (target) => {
	return typeof target === 'string'
}

