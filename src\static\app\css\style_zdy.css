/** 最新切图 **/

@charset "utf-8";

/*common*/
.gap {
    height: 24.1545749rpx;
}

.mask {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, .4);
    z-index: 1000;
}

/*page*/
.header {
    background: #4874ff;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    z-index: 999;
    height: 79.71015rpx;
}

.title {
    text-align: center;
    font-size: 30.608725rpx;
    color: #fff;
    line-height: 79.71015rpx;
}

.main {
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    height: 100%;
}

.inner {
    padding: 79.71015rpx 0 90.579675rpx;
}

.footer {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
}

.pd-pg1a .header {
    height: 141.90825rpx;
    padding: 45.289875rpx 28.985475rpx 0;
    box-sizing: border-box;
}

.pd-pg1a .pd-ultbs1 {
    top: 111.90825rpx;
}

.pd-pg1a .inner {
    padding: 220.4106rpx 28.985475rpx 90.579675rpx;
}

.pd-inpsrh {
    width: 100%;
    height: 66.4251rpx;
    border-radius: 9.057975rpx;
    background: #fff url(~@/static/app/images/srhic1.png) no-repeat 18.11595rpx center;
    text-indent: 57.367125rpx;
    font-size: 25.3623rpx;
    color: #333;
    line-height: 66.4251rpx;
    background-size: 24.1545749rpx;
}

.pd-inpsrh::-webkit-input-placeholder {
    color: #c1c1c1;
}

.searchbox {
    position: relative;
}

.searchbox .pd-inpsrh {
    background-position: 97% center;
    text-indent: 20rpx;
}

.searchbox .searchbtn {
    position: absolute;
    right: 0;
    top: 0px;
    width: 40px;
    height: 34px;
}

.pd-ultbs1 {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    background: #fff;
    display: flex;
    justify-content: space-evenly;
    height: 78.502425rpx;
}

.pd-ultbs1 li {
    font-size: 28.985475rpx;
    color: #666;
    line-height: 78.502425rpx;
    width: 50%;
    text-align: center;
}

.pd-ultbs1 li.on {
    color: #4874ff;
    background: url(~@/static/app/images/botbar.png) no-repeat center bottom;
    background-size: 84.54105rpx;
    font-weight: bold;
}

.pd-ultbs1 li sub {
    font-size: 22.94685rpx;
}

.pd-ultbs1 li.on sub,
.pd-ultbs1 li.on p {
    font-weight: bold;
}

.pd-ullst1 {
    background: #fff;
    border-radius: 12.077325rpx;
    padding: 24.1545749rpx;
}

.pd-ullst1 li+li {
    padding-top: 24.1545749rpx;
}

.pd-ullst1 li {
    display: flex;
}

.pd-ullst1 li em {
    font-size: 28rpx;
    color: #999;
    width: 22%;
}

.pd-ullst1 li i {
    font-size: 28rpx;
    color: #333;
}

.pd-menu {
    background: #fff;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    height: 90.579675rpx;
    box-shadow: 0 -6.0386251rpx 24.1545749rpx rgba(0, 0, 0, .1);
}

.pd-menu li i {
    height: 36.8357249rpx;
    display: block;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}

.pd-menu li.on p {
    color: #4874ff;
}

.pd-menu li p {
    font-size: 20.5313999rpx;
    color: #333;
    text-align: center;
    padding-top: 6.038625rpx;
}

.pd-menu li i.muic1 {
    background-image: url(~@/static/app/images/muic1.png);
}

.pd-menu li i.muic2 {
    background-image: url(~@/static/app/images/muic2.png);
}

.pd-menu li i.muic3 {
    background-image: url(~@/static/app/images/muic3.png);
}

.pd-menu li.on i.muic1 {
    background-image: url(~@/static/app/images/muic1s.png);
}

.pd-menu li.on i.muic2 {
    background-image: url(~@/static/app/images/muic2s.png);
}

.pd-menu li.on i.muic3 {
    background-image: url(~@/static/app/images/muic3s.png);
}

.pd-row.aic {
    align-items: center;
}

.pd-row {
    display: flex;
}

.pd-add1 {
    width: 54.951675rpx;
    margin-left: 54.34785rpx;
}

.pd-add2 {
    width: 27.77775rpx;
    height: 27.77775rpx;
}

.pd-dlbx1 {
    background: #fff;
    border-radius: 12.077325rpx;
}

.pd-dlbx1 dt {
    height: 81.5217749rpx;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24.1545749rpx;
}

.pd-dlbx1 dt strong {
    font-size: 28.985475rpx;
    color: #333;
    background: url(~@/static/app/images/dotic1.png) no-repeat left center;
    padding-left: 36.2319rpx;
    background-size: 20.5313999rpx;
    font-weight: bold;
}

.pd-dlbx1 dd {
    padding: 24.1545749rpx;
}

.pd-dlbx1 dd p {
    font-size: 28rpx;
    color: #666;
    background-repeat: no-repeat;
    background-position: left center;
    padding-left: 36.2319rpx;
    background-size: 17.51205rpx;
}

.pd-dlbx1 dd p+p {
    margin-top: 24.1545749rpx;
}

.pd-dlbx1 dd p.p1 {
    background-image: url(~@/static/app/images/aic1.png);
}

.pd-dlbx1 dd p.p2 {
    background-image: url(~@/static/app/images/aic2.png);
}

.pd-dlbx1 dd p.p3 {
    background-image: url(~@/static/app/images/aic3.png);
}

.pd-dlbx1 dd p.p4 {
    background-image: url(~@/static/app/images/aic4.png);
    background-size: 18.719775rpx;
}

.pd-arw1 {
    width: 11.473425rpx;
    height: 19.9274999rpx;
}

.pd-sysbtn {
    width: 25.3623rpx;
    height: 25.3623rpx;
    /* margin-top: 9.057975rpx; */
}

.pd-btn1 button+button {
    margin-left: 18.11595rpx;
}

.pd-btn1 button {
    height: 53.140125rpx;
    line-height: 53.140125rpx;
    border-radius: 300px;
    font-size: 28rpx;
    color: #999;
    background: #f1f1f1;
    border: none;
    padding: 0 32rpx;
}

.pd-btn1 button.on {
    background: #4874ff;
    color: #fff;
}

.pd-txt1 {
    font-size: 28rpx;
    color: #4874ff;
}

.pd-txt2 {
    font-size: 28rpx;
    color: #666;
}

.pd-whic1 {
    width: 26.570025rpx;
    vertical-align: -5%;
    margin-left: 9.057975rpx;
    height: 26.570025rpx;
}

.zy-form .item.nfx {
    display: block;
}

.pd-addpic1 {
    width: 213.76815rpx;
    height: 142.512075rpx;
}

.pd-con1 {
    padding: 0 28.985475rpx;
}

.zy-bot-btn1.nofx {
    position: static;
    margin: 0 28.985475rpx;
}

.pd-botdlg {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 30.1932rpx 30.1932rpx 0 0;
    background: #fff;
    z-index: 1000;
}

.pd-botdlg .dlgcls {
    background: url(~@/static/app/images/dlgcls.png) no-repeat center;
    background-size: 18.11595rpx;
    width: 18.11595rpx;
    height: 18.719775rpx;
    position: absolute;
    right: 36.2319rpx;
    top: 36.2319rpx;
}

.pd-tit1 {
    font-size: 36.2319rpx;
    color: #333;
    font-weight: bold;
}

.pd-ulpic1 {
    display: flex;
}

.pd-ulpic1 li+li {
    margin-left: 24.1545749rpx;
}

.pd-ulpic1 li image {
    display: block;
    width: 213.76815rpx;
    height: 142.512075rpx;
    margin: 0 auto;
    border-radius: 16rpx;
}

.pd-ulpic1 li p {
    font-size: 27.77775rpx;
    color: #888;
    padding-top: 24.1545749rpx;
    text-align: center;
}

.pd-dltxt1 dt,
.pd-dltxt1 dd {
    font-size: 27.77775rpx;
    color: #888;
    white-space: nowrap;
    line-height: 1.5;
}

.pd-modhd {
    height: 83.937225rpx;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 28.985475rpx;
    background: #fff;
}

.pd-modhd strong {
    font-size: 28.985475rpx;
    font-weight: bold;
    color: #333;
    position: relative;
    padding-left: 28.985475rpx;
}

.pd-modhd strong:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 10.8696rpx;
    height: 28.985475rpx;
    border-radius: 300px;
    background: #4874ff;
    transform: translateY(-50%);
}

.pd-modhd i {
    font-size: 28.985475rpx;
    color: #4874ff;
    margin-left: 8rpx;
}

.pd-edt1 {
    width: 25.9662rpx;
    height: 25.9662rpx;
}

.pd-txt3 {
    background: #f8f5ed url(~@/static/app/images/tsic1.png) no-repeat 28.985475rpx 45%;
    background-size: 22.94685rpx;
    padding: 12.077325rpx 28.985475rpx 12.077325rpx 66.4251rpx;
    font-size: 24.1545749rpx;
    color: #efa31e;
    line-height: 1.5;
}

.pd-tip12 {
    display: flex;
    align-items: center;
    background: #f8f5ed;
    /* padding: 12.077325rpx 28.985475rpx 12.077325rpx 28.985475rpx;font-size: 24.1545749rpx; color: #efa31e; */
    line-height: 28rpx;
}

.pd-tip12 text {
    font-size: 24.1545749rpx;
    color: #efa31e;
}

.pd-tip12 image {
    width: 22.94685rpx;
    height: 22.94685rpx;
}

.pd-delt {
    width: 27.77775rpx;
    height: 27.77775rpx;
    margin-left: 18.11595rpx;
}

.pd-pic {
    width: 677.5362rpx;
    display: block;
}


.pd-ulpic1 li .delImg {
    position: absolute;
    right: 0;
    top: 0;
    width: 48rpx;
    height: 48rpx;
}
