import axios from '@/common/ajaxRequest';
import { ULR_BASE, LOGIN_ULR_BASE,URL_BASE_ENTERPRISE_DETAIL} from '@/common/config.js';



// 设备运行状态
export const sbsxzt = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/monitor/cxzt',
        params: data
    });
};


//振动趋势
export const getZdqs = (data) => {
    return axios.request({
        method: 'get',
        url: URL_BASE_ENTERPRISE_DETAIL + '/sjcl/api/datasharte/baseinfo/zdqs',
        params: data
    });
};

//振动趋势区域时间颜色
export const ycsbqj = (data) => {
    return axios.request({
        method: 'get',
        url: URL_BASE_ENTERPRISE_DETAIL + '/sjcl/api/datasharte/baseinfo/ycsbqj',
        params: data
    });
};

// 预警内容页
export const yjxxInfo = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/waringPush/yjxx/info',
        params: data
    });
};

//近24小时振动监控数据：（治污预警和停产预警）
export const monitorCxzt = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/waringPush/cxzt',
        params: data
    });
};

//近24小时振动监控数据：（故障预警）
export const monitorZdsj = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/monitor/sbxq/zdsj',
        params: data
    });
};

//查询设备图片接口
export const monitorPicture = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/waringPush/sbxx/picture/list',
        params: data
    });
};

// 近24小时振动监控数据：（故障预警）
export const getZdsj = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/monitor/sbxq/zdsj',
        params: data
    });
};


//根据预警ID查询24小时状态图
export const warnStateData = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/waringPush/znsb/cxzt',
        params: data
    });
};

//近24小时振动监控数据：（治污预警和停产预警）
export const monitorCxztByID = (data) => {
    return axios.request({
        method: 'get',
        url: ULR_BASE + '/waringPush/znsb/cxzt',
        params: data
    });
};