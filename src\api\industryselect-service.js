import http from '@/common/net/http.js';
import districtList from './district.json';
import { deepCopyObject } from '@/common/merge.js';

const CACHE_PREFFIX = 'district';

const createCacheKey = (code) => {
	if(code){
		return `${CACHE_PREFFIX}@${code}`;
	} else {
		return `${CACHE_PREFFIX}@root`;
	}
}

/**
 * 从服务端同步行业类型数据
 */
const syncIndustryData = (code) => {
	return new Promise((resolve, reject) => {
		http.post(`${http.url}`, {
			HYLXDM: "",
			service: "SEARCH_HYLX",
			version: "1"
		}).then(resp => {
			let district = resp.map(item => {
				return {
					code: item.HYLXDM,
					name: item.HYLX,
					parent: item.HYDMXZ || 'root',
					level: item.order,
					order: parseInt(item.HYLXDM)
				}
            });
            let mockRoot = {
                code: 'root',
                name: 'virtual root node',
                order: 0
            }
            district.push(mockRoot);
			saveToLocal('industryselect-data', district);
			resolve(deepCopyObject(district));
		})
		.catch(error => {
			reject(error)
		});
	});
}

/**
 * 从本地缓存加载行业分类数据
 */
const loadIndustryFromLocal = (code) => {
	let cacheKey = createCacheKey(code);
	uni.removeStorageSync(cacheKey);
	return uni.getStorageSync('industryselect-data');
}

const saveToLocal = (code, data) => {
	uni.setStorage({
		key: 'industryselect-data',
		data: data
	})
}

/**
 * 加载全部行业类型数据
 * @param {Object} code
 */
const loadIndustrySource = (code) => {
	return new Promise((resolve, reject) => {
		let localCache = loadIndustryFromLocal(code);
		if(localCache){
			resolve(deepCopyObject(localCache));
		} else {
			resolve(syncIndustryData(code));
		}
	});
}

/**
 * 检索从根节点到到指定行政区划代码的所有层次节点
 * 比如：根据01检索结果为：
 * [
		{code: '01', ...},
		{code: '011', ...},
		{code: '0113', ...}
	]
 */
const retrieveIndustryChainByCode = (code) => {
	return new Promise((resolve, reject) => {
		loadIndustrySource(code)
			.then(district => {
				let root = resolveRootDistrict(code, district);
				if(root){
					let chain = [];
					recursiveDistrictChain(code, root, chain)
					resolve(chain);
				}else{
					resolve(null);
				}
			})
			.catch(error => {
				reject(error);
			})
	});
}

/**
 * 获取根节点去划分接下来的行业类型
 * @param {Object} code
 * @param {Object} districtSource
 */
const resolveRootDistrict = (code, districtSource) => {
	if(districtSource === null || typeof districtSource === 'undefined'){
		return;
	}
	if(Array.isArray(districtSource)){//类似/common/city.data.js这样的数据
		if(districtSource.length > 0){
			let matchDistrict = []
			districtSource.forEach(element => {
				if(element.code === code){
					matchDistrict.push(element)
				}
			});
			// let matchDistrict = districtSource.filter(item => {
			// 	let preffix = trimZero(item.code);
			// 	return code.startsWith(preffix);
			// });
			matchDistrict.sort((aDistrict, another) => {
				return parseInt(aDistrict.code) - parseInt(another.code);
			});
			return matchDistrict.length > 0 ? matchDistrict[0] : null;
		}
		return null;
	} else {
		//从`api/appservercontroller/getXzqhTree`这个接口返回的树状结构数据
		let preffix = trimZero(districtSource.code);
		return code.startsWith(preffix) ? districtSource : null;
	}
}

/**
 * 去掉首尾的0，仅限于行政区划，实际使用中不能去除
 */
export const trimZero = (code) => {
	// let preffix = code.replace(/0/g, ' ').trim().replace(/[ ]/g, '0');
	return code;
}

/**
 * 根据行业代码分类的层级规则提取指定行行业编码@param code的各层级行业编码
 * 如32是321的上级，320是3205的上级
 */
const recursiveDistrictChain = (code, district, chain) => {
	let preffix = trimZero(district.code);
	if(code.startsWith(preffix)){
		chain.push({
			code: district.code,
			name: district.name,
			parent: district.parent,
			order: district.order
		});
		if(district.children && district.children.length > 0){
			for(let i = 0; i < district.children.length; i++){
				let child = district.children[i];
				let childPreffix = trimZero(child.code);
				if(code.startsWith(childPreffix)){
					recursiveDistrictChain(code, child, chain);
					break;
				}
			}
		}
	}
}

/**
 * 获取指定代码的行业类型
 */
const getIndustryByCode = (code) => {
	return new Promise((resolve, reject) => {
		loadIndustrySource(code)
			.then(districtSource => {
				let result = null;
				if(districtSource){
					let filterDistricts = districtSource.filter(item => {
						return item.code === code;
					});
					result = filterDistricts.length > 0 ? filterDistricts[0] : null;
				}
				resolve(result);
			})
			.catch(error => {
				
			})
	});
}

/**
 * 获取指定代码的行业类型
 */
const getIndustryByCodeSync = (code, districtSource) => {
	let result = null;
	if(districtSource){
		let filterDistricts = districtSource.filter(item => {
			return item.code === code;
		});
		result = filterDistricts.length > 0 ? filterDistricts[0] : null;
	}
	return result;
}

const recursiveFindDistrict = (code, parent) => {
	if(code === parent.code){
		return parent;
	}
	
	let preffix = trimZero(parent.code);
	if(code.startsWith(preffix)){
		let children = parent.children;
		if(children && children.length > 0){
			for(let i = 0; i < children.length; i++){
				let child = children[i];
				let childPreffix = trimZero(child.code);
				if(code.startsWith(childPreffix)){
					return recursiveFindDistrict(code, child);
				}
			}
		}
	}else{
		return null;
	}
}

/**
 * 获取根节点
 */
const getRootIndustry = () => {
	return new Promise((resolve, reject) => {
		loadIndustrySource()
			.then(districtSource => {
				let result = null;
				if(districtSource && districtSource.length > 0){
                    let matches = districtSource.filter(item => {
                        return item.code === 'root'
                    })
                    if(matches.length > 0){
                        result = matches[0];
                    }
                }
				resolve(result);
			})
			.catch(error => {
				reject(error);
			})
	});
}

/**
 * 获取子节点行业编号
 */
const getChildIndustry = (parent) => {
	return new Promise((resolve, reject) => {
		loadIndustrySource(parent)
			.then(districtSource => {
				let children = getChildIndustrySync(parent, districtSource);
				for(let district of children){
					district.hasChild = getChildIndustrySync(district, districtSource).length > 0;
				}
				resolve(children);
			})
			.catch(error => {
				reject(error);
			})
	});
}

/**
 * 同步获取子节点
 */
const getChildIndustrySync = (parent, districtSource) => {
	let children = null;
	if(districtSource){
		let parentCode = parent.code || parent;
		children = districtSource.filter(item => {
			return item.parent === parentCode;
		});
	}
	return children;
}

const selectIndustry = (multi, selected) => {
	uni.navigateTo({
		url: `/pages/component/tree/industry-tree?multi=${multi}&selected=${selected}`
	})
}

export default {
	loadIndustrySource,
	retrieveIndustryChainByCode,
	getIndustryByCode,
	getIndustryByCodeSync,
	getRootIndustry,
	getChildIndustry,
	getChildIndustrySync,
	selectIndustry
};

